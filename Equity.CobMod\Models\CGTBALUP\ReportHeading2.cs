using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportHeading2 Data Structure

public class ReportHeading2
{
    private static int _size = 132;
    // [DEBUG] Class: ReportHeading2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler131, is_external=, is_static_class=False, static_prefix=
    private string _Filler131 ="";
    
    
    
    
    // [DEBUG] Field: Filler132, is_external=, is_static_class=False, static_prefix=
    private string _Filler132 ="    BALANCES UPDATE REPORT";
    
    
    
    
    
    // Serialization methods
    public string GetReportHeading2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler131.PadRight(52));
        result.Append(_Filler132.PadRight(80));
        
        return result.ToString();
    }
    
    public void SetReportHeading2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 52 <= data.Length)
        {
            string extracted = data.Substring(offset, 52).Trim();
            SetFiller131(extracted);
        }
        offset += 52;
        if (offset + 80 <= data.Length)
        {
            string extracted = data.Substring(offset, 80).Trim();
            SetFiller132(extracted);
        }
        offset += 80;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportHeading2AsString();
    }
    // Set<>String Override function
    public void SetReportHeading2(string value)
    {
        SetReportHeading2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller131()
    {
        return _Filler131;
    }
    
    // Standard Setter
    public void SetFiller131(string value)
    {
        _Filler131 = value;
    }
    
    // Get<>AsString()
    public string GetFiller131AsString()
    {
        return _Filler131.PadRight(52);
    }
    
    // Set<>AsString()
    public void SetFiller131AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler131 = value;
    }
    
    // Standard Getter
    public string GetFiller132()
    {
        return _Filler132;
    }
    
    // Standard Setter
    public void SetFiller132(string value)
    {
        _Filler132 = value;
    }
    
    // Get<>AsString()
    public string GetFiller132AsString()
    {
        return _Filler132.PadRight(80);
    }
    
    // Set<>AsString()
    public void SetFiller132AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler132 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}