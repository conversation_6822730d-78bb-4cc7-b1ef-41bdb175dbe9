using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// <Section> Class for Fvar
public class Fvar
{
public Fvar() {}

// Fields in the class


// [DEBUG] Field: D1Record, is_external=, is_static_class=False, static_prefix=
private D1Record _D1Record = new D1Record();




// [DEBUG] Field: D2Record, is_external=, is_static_class=False, static_prefix=
private D2Record _D2Record = new D2Record();




// [DEBUG] Field: D3Record, is_external=, is_static_class=False, static_prefix=
private D3Record _D3Record = new D3Record();




// [DEBUG] Field: D4Record, is_external=, is_static_class=False, static_prefix=
private D4Record _D4Record = new D4Record();




// [DEBUG] Field: D5Record, is_external=, is_static_class=False, static_prefix=
private D5Record _D5Record = new D5Record();




// [DEBUG] Field: D6Record, is_external=, is_static_class=False, static_prefix=
private D6Record _D6Record = new D6Record();




// [DEBUG] Field: D7Record, is_external=, is_static_class=False, static_prefix=
private D7Record _D7Record = new D7Record();




// [DEBUG] Field: D8Record, is_external=, is_static_class=False, static_prefix=
private D8Record _D8Record = new D8Record();




// [DEBUG] Field: D8_FULL_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string D8_FULL_SCHEDULE = "F ";




// [DEBUG] Field: D8_SEDOLS_SUPPRESSED, is_external=, is_static_class=False, static_prefix=
public const string D8_SEDOLS_SUPPRESSED = "SS";




// [DEBUG] Field: D8_TRANCHES_SUPPRESSED, is_external=, is_static_class=False, static_prefix=
public const string D8_TRANCHES_SUPPRESSED = "ST";




// [DEBUG] Field: D8_MINIMUM_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string D8_MINIMUM_SCHEDULE = "M ";




// [DEBUG] Field: D9Record, is_external=, is_static_class=False, static_prefix=
private D9Record _D9Record = new D9Record();




// [DEBUG] Field: D10Record, is_external=, is_static_class=False, static_prefix=
private D10Record _D10Record = new D10Record();




// [DEBUG] Field: D11Record, is_external=, is_static_class=False, static_prefix=
private D11Record _D11Record = new D11Record();




// [DEBUG] Field: D12Record, is_external=, is_static_class=False, static_prefix=
private D12Record _D12Record = new D12Record();




// [DEBUG] Field: D17Record, is_external=, is_static_class=False, static_prefix=
private D17Record _D17Record = new D17Record();




// [DEBUG] Field: D18Record, is_external=, is_static_class=False, static_prefix=
private D18Record _D18Record = new D18Record();




// [DEBUG] Field: D19Record, is_external=, is_static_class=False, static_prefix=
private D19Record _D19Record = new D19Record();




// [DEBUG] Field: D20Record, is_external=, is_static_class=False, static_prefix=
private D20Record _D20Record = new D20Record();




// [DEBUG] Field: D21Record, is_external=, is_static_class=False, static_prefix=
private D21Record _D21Record = new D21Record();




// [DEBUG] Field: D22Record, is_external=, is_static_class=False, static_prefix=
private D22Record _D22Record = new D22Record();




// [DEBUG] Field: D23Record, is_external=, is_static_class=False, static_prefix=
private D23Record _D23Record = new D23Record();




// [DEBUG] Field: D24Record, is_external=, is_static_class=False, static_prefix=
private D24Record _D24Record = new D24Record();




// [DEBUG] Field: D25Record, is_external=, is_static_class=False, static_prefix=
private D25Record _D25Record = new D25Record();




// [DEBUG] Field: D26Record, is_external=, is_static_class=False, static_prefix=
private D26Record _D26Record = new D26Record();




// [DEBUG] Field: D27Record, is_external=, is_static_class=False, static_prefix=
private D27Record _D27Record = new D27Record();




// [DEBUG] Field: D28Record, is_external=, is_static_class=False, static_prefix=
private D28Record _D28Record = new D28Record();




// [DEBUG] Field: D29Record, is_external=, is_static_class=False, static_prefix=
private D29Record _D29Record = new D29Record();




// [DEBUG] Field: D30Record, is_external=, is_static_class=False, static_prefix=
private D30Record _D30Record = new D30Record();




// [DEBUG] Field: D31Record, is_external=, is_static_class=False, static_prefix=
private D31Record _D31Record = new D31Record();




// [DEBUG] Field: D33Record, is_external=, is_static_class=False, static_prefix=
private D33Record _D33Record = new D33Record();




// [DEBUG] Field: D32Record, is_external=, is_static_class=False, static_prefix=
private D32Record _D32Record = new D32Record();




// [DEBUG] Field: D34Record, is_external=, is_static_class=False, static_prefix=
private D34Record _D34Record = new D34Record();




// [DEBUG] Field: D35Record, is_external=, is_static_class=False, static_prefix=
private D35Record _D35Record = new D35Record();




// [DEBUG] Field: D36Record, is_external=, is_static_class=False, static_prefix=
private D36Record _D36Record = new D36Record();




// [DEBUG] Field: D38Record, is_external=, is_static_class=False, static_prefix=
private D38Record _D38Record = new D38Record();




// [DEBUG] Field: D39Record, is_external=, is_static_class=False, static_prefix=
private D39Record _D39Record = new D39Record();




// [DEBUG] Field: D40Record, is_external=, is_static_class=False, static_prefix=
private D40Record _D40Record = new D40Record();




// [DEBUG] Field: D41Record, is_external=, is_static_class=False, static_prefix=
private D41Record _D41Record = new D41Record();




// [DEBUG] Field: D41RecHed, is_external=, is_static_class=False, static_prefix=
private D41RecHed _D41RecHed = new D41RecHed();




// [DEBUG] Field: D41RecEnd, is_external=, is_static_class=False, static_prefix=
private D41RecEnd _D41RecEnd = new D41RecEnd();




// [DEBUG] Field: D42Record, is_external=, is_static_class=False, static_prefix=
private D42Record _D42Record = new D42Record();




// [DEBUG] Field: D43Record, is_external=, is_static_class=False, static_prefix=
private D43Record _D43Record = new D43Record();




// [DEBUG] Field: D44Record, is_external=, is_static_class=False, static_prefix=
private D44Record _D44Record = new D44Record();




// [DEBUG] Field: MAX_NO_OF_COSTS, is_external=, is_static_class=False, static_prefix=
public const int MAX_NO_OF_COSTS = 200;




// [DEBUG] Field: MAX_MASTER_RECORD_LEN, is_external=, is_static_class=False, static_prefix=
public const int MAX_MASTER_RECORD_LEN = 16270;




// [DEBUG] Field: D45Record, is_external=, is_static_class=False, static_prefix=
private D45Record _D45Record = new D45Record();




// [DEBUG] Field: D45BalAcqDispRecord, is_external=, is_static_class=False, static_prefix=
private D45BalAcqDispRecord _D45BalAcqDispRecord = new D45BalAcqDispRecord();




// [DEBUG] Field: D46Record, is_external=, is_static_class=False, static_prefix=
private D46Record _D46Record = new D46Record();




// [DEBUG] Field: D46RecordN, is_external=, is_static_class=False, static_prefix=
private D46RecordN _D46RecordN = new D46RecordN();




// [DEBUG] Field: D46HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D46HeaderRecord _D46HeaderRecord = new D46HeaderRecord();




// [DEBUG] Field: D46TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D46TrailerRecord _D46TrailerRecord = new D46TrailerRecord();




// [DEBUG] Field: D49Record, is_external=, is_static_class=False, static_prefix=
private D49Record _D49Record = new D49Record();




// [DEBUG] Field: D50Record, is_external=, is_static_class=False, static_prefix=
private D50Record _D50Record = new D50Record();




// [DEBUG] Field: D51Record, is_external=, is_static_class=False, static_prefix=
private D51Record _D51Record = new D51Record();




// [DEBUG] Field: D51HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D51HeaderRecord _D51HeaderRecord = new D51HeaderRecord();




// [DEBUG] Field: D51TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D51TrailerRecord _D51TrailerRecord = new D51TrailerRecord();




// [DEBUG] Field: D68Record, is_external=, is_static_class=False, static_prefix=
private D68Record _D68Record = new D68Record();




// [DEBUG] Field: D68HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D68HeaderRecord _D68HeaderRecord = new D68HeaderRecord();




// [DEBUG] Field: D68TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D68TrailerRecord _D68TrailerRecord = new D68TrailerRecord();




// [DEBUG] Field: D69Record, is_external=, is_static_class=False, static_prefix=
private D69Record _D69Record = new D69Record();




// [DEBUG] Field: D70Record, is_external=, is_static_class=False, static_prefix=
private D70Record _D70Record = new D70Record();




// [DEBUG] Field: D70HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D70HeaderRecord _D70HeaderRecord = new D70HeaderRecord();




// [DEBUG] Field: D70TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D70TrailerRecord _D70TrailerRecord = new D70TrailerRecord();




// [DEBUG] Field: D71Record, is_external=, is_static_class=False, static_prefix=
private D71Record _D71Record = new D71Record();




// [DEBUG] Field: D72Record, is_external=, is_static_class=False, static_prefix=
private D72Record _D72Record = new D72Record();




// [DEBUG] Field: D73Record, is_external=, is_static_class=False, static_prefix=
private D73Record _D73Record = new D73Record();




// [DEBUG] Field: D74Record, is_external=, is_static_class=False, static_prefix=
private D74Record _D74Record = new D74Record();




// [DEBUG] Field: D75Record, is_external=, is_static_class=False, static_prefix=
private D75Record _D75Record = new D75Record();




// [DEBUG] Field: D76Record, is_external=, is_static_class=False, static_prefix=
private D76Record _D76Record = new D76Record();




// [DEBUG] Field: D77Record, is_external=, is_static_class=False, static_prefix=
private D77Record _D77Record = new D77Record();




// [DEBUG] Field: D78Record, is_external=, is_static_class=False, static_prefix=
private D78Record _D78Record = new D78Record();




// [DEBUG] Field: D79Record, is_external=, is_static_class=False, static_prefix=
private D79Record _D79Record = new D79Record();




// [DEBUG] Field: D80Record, is_external=, is_static_class=False, static_prefix=
private D80Record _D80Record = new D80Record();




// [DEBUG] Field: D81Record, is_external=, is_static_class=False, static_prefix=
private D81Record _D81Record = new D81Record();




// [DEBUG] Field: D82Record, is_external=, is_static_class=False, static_prefix=
private D82Record _D82Record = new D82Record();




// [DEBUG] Field: D83Record, is_external=, is_static_class=False, static_prefix=
private D83Record _D83Record = new D83Record();




// [DEBUG] Field: D83HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D83HeaderRecord _D83HeaderRecord = new D83HeaderRecord();




// [DEBUG] Field: D83TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D83TrailerRecord _D83TrailerRecord = new D83TrailerRecord();




// [DEBUG] Field: D84Record, is_external=, is_static_class=False, static_prefix=
private D84Record _D84Record = new D84Record();




// [DEBUG] Field: D85Record, is_external=, is_static_class=False, static_prefix=
private D85Record _D85Record = new D85Record();




// [DEBUG] Field: D86Record, is_external=, is_static_class=False, static_prefix=
private D86Record _D86Record = new D86Record();




// [DEBUG] Field: D87Record, is_external=, is_static_class=False, static_prefix=
private D87Record _D87Record = new D87Record();




// [DEBUG] Field: D88Record, is_external=, is_static_class=False, static_prefix=
private D88Record _D88Record = new D88Record();




// [DEBUG] Field: D89Record, is_external=, is_static_class=False, static_prefix=
private D89Record _D89Record = new D89Record();




// [DEBUG] Field: D89HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D89HeaderRecord _D89HeaderRecord = new D89HeaderRecord();




// [DEBUG] Field: D89TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D89TrailerRecord _D89TrailerRecord = new D89TrailerRecord();




// [DEBUG] Field: D90Record, is_external=, is_static_class=False, static_prefix=
private D90Record _D90Record = new D90Record();




// [DEBUG] Field: D91Record, is_external=, is_static_class=False, static_prefix=
private D91Record _D91Record = new D91Record();




// [DEBUG] Field: D91HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D91HeaderRecord _D91HeaderRecord = new D91HeaderRecord();




// [DEBUG] Field: D91TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D91TrailerRecord _D91TrailerRecord = new D91TrailerRecord();




// [DEBUG] Field: D92Record, is_external=, is_static_class=False, static_prefix=
private D92Record _D92Record = new D92Record();




// [DEBUG] Field: D93Record, is_external=, is_static_class=False, static_prefix=
private D93Record _D93Record = new D93Record();




// [DEBUG] Field: D93HeaderRecord, is_external=, is_static_class=False, static_prefix=
private D93HeaderRecord _D93HeaderRecord = new D93HeaderRecord();




// [DEBUG] Field: D93TrailerRecord, is_external=, is_static_class=False, static_prefix=
private D93TrailerRecord _D93TrailerRecord = new D93TrailerRecord();




// [DEBUG] Field: D94Record, is_external=, is_static_class=False, static_prefix=
private D94Record _D94Record = new D94Record();




// [DEBUG] Field: D95Record, is_external=, is_static_class=False, static_prefix=
private D95Record _D95Record = new D95Record();




// [DEBUG] Field: D98Record, is_external=, is_static_class=False, static_prefix=
private D98Record _D98Record = new D98Record();




// [DEBUG] Field: D99Record, is_external=, is_static_class=False, static_prefix=
private D99Record _D99Record = new D99Record();




// [DEBUG] Field: D101Record, is_external=, is_static_class=False, static_prefix=
private D101Record _D101Record = new D101Record();




// [DEBUG] Field: D107Record, is_external=, is_static_class=False, static_prefix=
private D107Record _D107Record = new D107Record();




// [DEBUG] Field: D108Record, is_external=, is_static_class=False, static_prefix=
private D108Record _D108Record = new D108Record();




// [DEBUG] Field: D109Record, is_external=, is_static_class=False, static_prefix=
private D109Record _D109Record = new D109Record();




// [DEBUG] Field: D112Record, is_external=, is_static_class=False, static_prefix=
private D112Record _D112Record = new D112Record();




// [DEBUG] Field: D133Record, is_external=, is_static_class=False, static_prefix=
private D133Record _D133Record = new D133Record();




// [DEBUG] Field: D153Record, is_external=, is_static_class=False, static_prefix=
private D153Record _D153Record = new D153Record();




// [DEBUG] Field: D154Record, is_external=, is_static_class=False, static_prefix=
private D154Record _D154Record = new D154Record();




// [DEBUG] Field: D161Record, is_external=, is_static_class=False, static_prefix=
private D161Record _D161Record = new D161Record();




// [DEBUG] Field: D163Record, is_external=, is_static_class=False, static_prefix=
private D163Record _D163Record = new D163Record();




// [DEBUG] Field: D167Record, is_external=, is_static_class=False, static_prefix=
private D167Record _D167Record = new D167Record();




// [DEBUG] Field: D169Record, is_external=, is_static_class=False, static_prefix=
private D169Record _D169Record = new D169Record();




// [DEBUG] Field: D170Record, is_external=, is_static_class=False, static_prefix=
private D170Record _D170Record = new D170Record();




// Getter and Setter methods

// Standard Getter
public D1Record GetD1Record()
{
    return _D1Record;
}

// Standard Setter
public void SetD1Record(D1Record value)
{
    _D1Record = value;
}

// Get<>AsString()
public string GetD1RecordAsString()
{
    return _D1Record != null ? _D1Record.GetD1RecordAsString() : "";
}

// Set<>AsString()
public void SetD1RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D1Record == null)
    {
        _D1Record = new D1Record();
    }
    _D1Record.SetD1RecordAsString(value);
}

// Standard Getter
public D2Record GetD2Record()
{
    return _D2Record;
}

// Standard Setter
public void SetD2Record(D2Record value)
{
    _D2Record = value;
}

// Get<>AsString()
public string GetD2RecordAsString()
{
    return _D2Record != null ? _D2Record.GetD2RecordAsString() : "";
}

// Set<>AsString()
public void SetD2RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D2Record == null)
    {
        _D2Record = new D2Record();
    }
    _D2Record.SetD2RecordAsString(value);
}

// Standard Getter
public D3Record GetD3Record()
{
    return _D3Record;
}

// Standard Setter
public void SetD3Record(D3Record value)
{
    _D3Record = value;
}

// Get<>AsString()
public string GetD3RecordAsString()
{
    return _D3Record != null ? _D3Record.GetD3RecordAsString() : "";
}

// Set<>AsString()
public void SetD3RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D3Record == null)
    {
        _D3Record = new D3Record();
    }
    _D3Record.SetD3RecordAsString(value);
}

// Standard Getter
public D4Record GetD4Record()
{
    return _D4Record;
}

// Standard Setter
public void SetD4Record(D4Record value)
{
    _D4Record = value;
}

// Get<>AsString()
public string GetD4RecordAsString()
{
    return _D4Record != null ? _D4Record.GetD4RecordAsString() : "";
}

// Set<>AsString()
public void SetD4RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D4Record == null)
    {
        _D4Record = new D4Record();
    }
    _D4Record.SetD4RecordAsString(value);
}

// Standard Getter
public D5Record GetD5Record()
{
    return _D5Record;
}

// Standard Setter
public void SetD5Record(D5Record value)
{
    _D5Record = value;
}

// Get<>AsString()
public string GetD5RecordAsString()
{
    return _D5Record != null ? _D5Record.GetD5RecordAsString() : "";
}

// Set<>AsString()
public void SetD5RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D5Record == null)
    {
        _D5Record = new D5Record();
    }
    _D5Record.SetD5RecordAsString(value);
}

// Standard Getter
public D6Record GetD6Record()
{
    return _D6Record;
}

// Standard Setter
public void SetD6Record(D6Record value)
{
    _D6Record = value;
}

// Get<>AsString()
public string GetD6RecordAsString()
{
    return _D6Record != null ? _D6Record.GetD6RecordAsString() : "";
}

// Set<>AsString()
public void SetD6RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D6Record == null)
    {
        _D6Record = new D6Record();
    }
    _D6Record.SetD6RecordAsString(value);
}

// Standard Getter
public D7Record GetD7Record()
{
    return _D7Record;
}

// Standard Setter
public void SetD7Record(D7Record value)
{
    _D7Record = value;
}

// Get<>AsString()
public string GetD7RecordAsString()
{
    return _D7Record != null ? _D7Record.GetD7RecordAsString() : "";
}

// Set<>AsString()
public void SetD7RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D7Record == null)
    {
        _D7Record = new D7Record();
    }
    _D7Record.SetD7RecordAsString(value);
}

// Standard Getter
public D8Record GetD8Record()
{
    return _D8Record;
}

// Standard Setter
public void SetD8Record(D8Record value)
{
    _D8Record = value;
}

// Get<>AsString()
public string GetD8RecordAsString()
{
    return _D8Record != null ? _D8Record.GetD8RecordAsString() : "";
}

// Set<>AsString()
public void SetD8RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D8Record == null)
    {
        _D8Record = new D8Record();
    }
    _D8Record.SetD8RecordAsString(value);
}

// Standard Getter
public D9Record GetD9Record()
{
    return _D9Record;
}

// Standard Setter
public void SetD9Record(D9Record value)
{
    _D9Record = value;
}

// Get<>AsString()
public string GetD9RecordAsString()
{
    return _D9Record != null ? _D9Record.GetD9RecordAsString() : "";
}

// Set<>AsString()
public void SetD9RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D9Record == null)
    {
        _D9Record = new D9Record();
    }
    _D9Record.SetD9RecordAsString(value);
}

// Standard Getter
public D10Record GetD10Record()
{
    return _D10Record;
}

// Standard Setter
public void SetD10Record(D10Record value)
{
    _D10Record = value;
}

// Get<>AsString()
public string GetD10RecordAsString()
{
    return _D10Record != null ? _D10Record.GetD10RecordAsString() : "";
}

// Set<>AsString()
public void SetD10RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D10Record == null)
    {
        _D10Record = new D10Record();
    }
    _D10Record.SetD10RecordAsString(value);
}

// Standard Getter
public D11Record GetD11Record()
{
    return _D11Record;
}

// Standard Setter
public void SetD11Record(D11Record value)
{
    _D11Record = value;
}

// Get<>AsString()
public string GetD11RecordAsString()
{
    return _D11Record != null ? _D11Record.GetD11RecordAsString() : "";
}

// Set<>AsString()
public void SetD11RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D11Record == null)
    {
        _D11Record = new D11Record();
    }
    _D11Record.SetD11RecordAsString(value);
}

// Standard Getter
public D12Record GetD12Record()
{
    return _D12Record;
}

// Standard Setter
public void SetD12Record(D12Record value)
{
    _D12Record = value;
}

// Get<>AsString()
public string GetD12RecordAsString()
{
    return _D12Record != null ? _D12Record.GetD12RecordAsString() : "";
}

// Set<>AsString()
public void SetD12RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D12Record == null)
    {
        _D12Record = new D12Record();
    }
    _D12Record.SetD12RecordAsString(value);
}

// Standard Getter
public D17Record GetD17Record()
{
    return _D17Record;
}

// Standard Setter
public void SetD17Record(D17Record value)
{
    _D17Record = value;
}

// Get<>AsString()
public string GetD17RecordAsString()
{
    return _D17Record != null ? _D17Record.GetD17RecordAsString() : "";
}

// Set<>AsString()
public void SetD17RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D17Record == null)
    {
        _D17Record = new D17Record();
    }
    _D17Record.SetD17RecordAsString(value);
}

// Standard Getter
public D18Record GetD18Record()
{
    return _D18Record;
}

// Standard Setter
public void SetD18Record(D18Record value)
{
    _D18Record = value;
}

// Get<>AsString()
public string GetD18RecordAsString()
{
    return _D18Record != null ? _D18Record.GetD18RecordAsString() : "";
}

// Set<>AsString()
public void SetD18RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D18Record == null)
    {
        _D18Record = new D18Record();
    }
    _D18Record.SetD18RecordAsString(value);
}

// Standard Getter
public D19Record GetD19Record()
{
    return _D19Record;
}

// Standard Setter
public void SetD19Record(D19Record value)
{
    _D19Record = value;
}

// Get<>AsString()
public string GetD19RecordAsString()
{
    return _D19Record != null ? _D19Record.GetD19RecordAsString() : "";
}

// Set<>AsString()
public void SetD19RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D19Record == null)
    {
        _D19Record = new D19Record();
    }
    _D19Record.SetD19RecordAsString(value);
}

// Standard Getter
public D20Record GetD20Record()
{
    return _D20Record;
}

// Standard Setter
public void SetD20Record(D20Record value)
{
    _D20Record = value;
}

// Get<>AsString()
public string GetD20RecordAsString()
{
    return _D20Record != null ? _D20Record.GetD20RecordAsString() : "";
}

// Set<>AsString()
public void SetD20RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D20Record == null)
    {
        _D20Record = new D20Record();
    }
    _D20Record.SetD20RecordAsString(value);
}

// Standard Getter
public D21Record GetD21Record()
{
    return _D21Record;
}

// Standard Setter
public void SetD21Record(D21Record value)
{
    _D21Record = value;
}

// Get<>AsString()
public string GetD21RecordAsString()
{
    return _D21Record != null ? _D21Record.GetD21RecordAsString() : "";
}

// Set<>AsString()
public void SetD21RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D21Record == null)
    {
        _D21Record = new D21Record();
    }
    _D21Record.SetD21RecordAsString(value);
}

// Standard Getter
public D22Record GetD22Record()
{
    return _D22Record;
}

// Standard Setter
public void SetD22Record(D22Record value)
{
    _D22Record = value;
}

// Get<>AsString()
public string GetD22RecordAsString()
{
    return _D22Record != null ? _D22Record.GetD22RecordAsString() : "";
}

// Set<>AsString()
public void SetD22RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D22Record == null)
    {
        _D22Record = new D22Record();
    }
    _D22Record.SetD22RecordAsString(value);
}

// Standard Getter
public D23Record GetD23Record()
{
    return _D23Record;
}

// Standard Setter
public void SetD23Record(D23Record value)
{
    _D23Record = value;
}

// Get<>AsString()
public string GetD23RecordAsString()
{
    return _D23Record != null ? _D23Record.GetD23RecordAsString() : "";
}

// Set<>AsString()
public void SetD23RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D23Record == null)
    {
        _D23Record = new D23Record();
    }
    _D23Record.SetD23RecordAsString(value);
}

// Standard Getter
public D24Record GetD24Record()
{
    return _D24Record;
}

// Standard Setter
public void SetD24Record(D24Record value)
{
    _D24Record = value;
}

// Get<>AsString()
public string GetD24RecordAsString()
{
    return _D24Record != null ? _D24Record.GetD24RecordAsString() : "";
}

// Set<>AsString()
public void SetD24RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D24Record == null)
    {
        _D24Record = new D24Record();
    }
    _D24Record.SetD24RecordAsString(value);
}

// Standard Getter
public D25Record GetD25Record()
{
    return _D25Record;
}

// Standard Setter
public void SetD25Record(D25Record value)
{
    _D25Record = value;
}

// Get<>AsString()
public string GetD25RecordAsString()
{
    return _D25Record != null ? _D25Record.GetD25RecordAsString() : "";
}

// Set<>AsString()
public void SetD25RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D25Record == null)
    {
        _D25Record = new D25Record();
    }
    _D25Record.SetD25RecordAsString(value);
}

// Standard Getter
public D26Record GetD26Record()
{
    return _D26Record;
}

// Standard Setter
public void SetD26Record(D26Record value)
{
    _D26Record = value;
}

// Get<>AsString()
public string GetD26RecordAsString()
{
    return _D26Record != null ? _D26Record.GetD26RecordAsString() : "";
}

// Set<>AsString()
public void SetD26RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D26Record == null)
    {
        _D26Record = new D26Record();
    }
    _D26Record.SetD26RecordAsString(value);
}

// Standard Getter
public D27Record GetD27Record()
{
    return _D27Record;
}

// Standard Setter
public void SetD27Record(D27Record value)
{
    _D27Record = value;
}

// Get<>AsString()
public string GetD27RecordAsString()
{
    return _D27Record != null ? _D27Record.GetD27RecordAsString() : "";
}

// Set<>AsString()
public void SetD27RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D27Record == null)
    {
        _D27Record = new D27Record();
    }
    _D27Record.SetD27RecordAsString(value);
}

// Standard Getter
public D28Record GetD28Record()
{
    return _D28Record;
}

// Standard Setter
public void SetD28Record(D28Record value)
{
    _D28Record = value;
}

// Get<>AsString()
public string GetD28RecordAsString()
{
    return _D28Record != null ? _D28Record.GetD28RecordAsString() : "";
}

// Set<>AsString()
public void SetD28RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D28Record == null)
    {
        _D28Record = new D28Record();
    }
    _D28Record.SetD28RecordAsString(value);
}

// Standard Getter
public D29Record GetD29Record()
{
    return _D29Record;
}

// Standard Setter
public void SetD29Record(D29Record value)
{
    _D29Record = value;
}

// Get<>AsString()
public string GetD29RecordAsString()
{
    return _D29Record != null ? _D29Record.GetD29RecordAsString() : "";
}

// Set<>AsString()
public void SetD29RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D29Record == null)
    {
        _D29Record = new D29Record();
    }
    _D29Record.SetD29RecordAsString(value);
}

// Standard Getter
public D30Record GetD30Record()
{
    return _D30Record;
}

// Standard Setter
public void SetD30Record(D30Record value)
{
    _D30Record = value;
}

// Get<>AsString()
public string GetD30RecordAsString()
{
    return _D30Record != null ? _D30Record.GetD30RecordAsString() : "";
}

// Set<>AsString()
public void SetD30RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D30Record == null)
    {
        _D30Record = new D30Record();
    }
    _D30Record.SetD30RecordAsString(value);
}

// Standard Getter
public D31Record GetD31Record()
{
    return _D31Record;
}

// Standard Setter
public void SetD31Record(D31Record value)
{
    _D31Record = value;
}

// Get<>AsString()
public string GetD31RecordAsString()
{
    return _D31Record != null ? _D31Record.GetD31RecordAsString() : "";
}

// Set<>AsString()
public void SetD31RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D31Record == null)
    {
        _D31Record = new D31Record();
    }
    _D31Record.SetD31RecordAsString(value);
}

// Standard Getter
public D33Record GetD33Record()
{
    return _D33Record;
}

// Standard Setter
public void SetD33Record(D33Record value)
{
    _D33Record = value;
}

// Get<>AsString()
public string GetD33RecordAsString()
{
    return _D33Record != null ? _D33Record.GetD33RecordAsString() : "";
}

// Set<>AsString()
public void SetD33RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D33Record == null)
    {
        _D33Record = new D33Record();
    }
    _D33Record.SetD33RecordAsString(value);
}

// Standard Getter
public D32Record GetD32Record()
{
    return _D32Record;
}

// Standard Setter
public void SetD32Record(D32Record value)
{
    _D32Record = value;
}

// Get<>AsString()
public string GetD32RecordAsString()
{
    return _D32Record != null ? _D32Record.GetD32RecordAsString() : "";
}

// Set<>AsString()
public void SetD32RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D32Record == null)
    {
        _D32Record = new D32Record();
    }
    _D32Record.SetD32RecordAsString(value);
}

// Standard Getter
public D34Record GetD34Record()
{
    return _D34Record;
}

// Standard Setter
public void SetD34Record(D34Record value)
{
    _D34Record = value;
}

// Get<>AsString()
public string GetD34RecordAsString()
{
    return _D34Record != null ? _D34Record.GetD34RecordAsString() : "";
}

// Set<>AsString()
public void SetD34RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D34Record == null)
    {
        _D34Record = new D34Record();
    }
    _D34Record.SetD34RecordAsString(value);
}

// Standard Getter
public D35Record GetD35Record()
{
    return _D35Record;
}

// Standard Setter
public void SetD35Record(D35Record value)
{
    _D35Record = value;
}

// Get<>AsString()
public string GetD35RecordAsString()
{
    return _D35Record != null ? _D35Record.GetD35RecordAsString() : "";
}

// Set<>AsString()
public void SetD35RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D35Record == null)
    {
        _D35Record = new D35Record();
    }
    _D35Record.SetD35RecordAsString(value);
}

// Standard Getter
public D36Record GetD36Record()
{
    return _D36Record;
}

// Standard Setter
public void SetD36Record(D36Record value)
{
    _D36Record = value;
}

// Get<>AsString()
public string GetD36RecordAsString()
{
    return _D36Record != null ? _D36Record.GetD36RecordAsString() : "";
}

// Set<>AsString()
public void SetD36RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D36Record == null)
    {
        _D36Record = new D36Record();
    }
    _D36Record.SetD36RecordAsString(value);
}

// Standard Getter
public D38Record GetD38Record()
{
    return _D38Record;
}

// Standard Setter
public void SetD38Record(D38Record value)
{
    _D38Record = value;
}

// Get<>AsString()
public string GetD38RecordAsString()
{
    return _D38Record != null ? _D38Record.GetD38RecordAsString() : "";
}

// Set<>AsString()
public void SetD38RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D38Record == null)
    {
        _D38Record = new D38Record();
    }
    _D38Record.SetD38RecordAsString(value);
}

// Standard Getter
public D39Record GetD39Record()
{
    return _D39Record;
}

// Standard Setter
public void SetD39Record(D39Record value)
{
    _D39Record = value;
}

// Get<>AsString()
public string GetD39RecordAsString()
{
    return _D39Record != null ? _D39Record.GetD39RecordAsString() : "";
}

// Set<>AsString()
public void SetD39RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D39Record == null)
    {
        _D39Record = new D39Record();
    }
    _D39Record.SetD39RecordAsString(value);
}

// Standard Getter
public D40Record GetD40Record()
{
    return _D40Record;
}

// Standard Setter
public void SetD40Record(D40Record value)
{
    _D40Record = value;
}

// Get<>AsString()
public string GetD40RecordAsString()
{
    return _D40Record != null ? _D40Record.GetD40RecordAsString() : "";
}

// Set<>AsString()
public void SetD40RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D40Record == null)
    {
        _D40Record = new D40Record();
    }
    _D40Record.SetD40RecordAsString(value);
}

// Standard Getter
public D41Record GetD41Record()
{
    return _D41Record;
}

// Standard Setter
public void SetD41Record(D41Record value)
{
    _D41Record = value;
}

// Get<>AsString()
public string GetD41RecordAsString()
{
    return _D41Record != null ? _D41Record.GetD41RecordAsString() : "";
}

// Set<>AsString()
public void SetD41RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D41Record == null)
    {
        _D41Record = new D41Record();
    }
    _D41Record.SetD41RecordAsString(value);
}

// Standard Getter
public D41RecHed GetD41RecHed()
{
    return _D41RecHed;
}

// Standard Setter
public void SetD41RecHed(D41RecHed value)
{
    _D41RecHed = value;
}

// Get<>AsString()
public string GetD41RecHedAsString()
{
    return _D41RecHed != null ? _D41RecHed.GetD41RecHedAsString() : "";
}

// Set<>AsString()
public void SetD41RecHedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D41RecHed == null)
    {
        _D41RecHed = new D41RecHed();
    }
    _D41RecHed.SetD41RecHedAsString(value);
}

// Standard Getter
public D41RecEnd GetD41RecEnd()
{
    return _D41RecEnd;
}

// Standard Setter
public void SetD41RecEnd(D41RecEnd value)
{
    _D41RecEnd = value;
}

// Get<>AsString()
public string GetD41RecEndAsString()
{
    return _D41RecEnd != null ? _D41RecEnd.GetD41RecEndAsString() : "";
}

// Set<>AsString()
public void SetD41RecEndAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D41RecEnd == null)
    {
        _D41RecEnd = new D41RecEnd();
    }
    _D41RecEnd.SetD41RecEndAsString(value);
}

// Standard Getter
public D42Record GetD42Record()
{
    return _D42Record;
}

// Standard Setter
public void SetD42Record(D42Record value)
{
    _D42Record = value;
}

// Get<>AsString()
public string GetD42RecordAsString()
{
    return _D42Record != null ? _D42Record.GetD42RecordAsString() : "";
}

// Set<>AsString()
public void SetD42RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D42Record == null)
    {
        _D42Record = new D42Record();
    }
    _D42Record.SetD42RecordAsString(value);
}

// Standard Getter
public D43Record GetD43Record()
{
    return _D43Record;
}

// Standard Setter
public void SetD43Record(D43Record value)
{
    _D43Record = value;
}

// Get<>AsString()
public string GetD43RecordAsString()
{
    return _D43Record != null ? _D43Record.GetD43RecordAsString() : "";
}

// Set<>AsString()
public void SetD43RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D43Record == null)
    {
        _D43Record = new D43Record();
    }
    _D43Record.SetD43RecordAsString(value);
}

// Standard Getter
public D44Record GetD44Record()
{
    return _D44Record;
}

// Standard Setter
public void SetD44Record(D44Record value)
{
    _D44Record = value;
}

// Get<>AsString()
public string GetD44RecordAsString()
{
    return _D44Record != null ? _D44Record.GetD44RecordAsString() : "";
}

// Set<>AsString()
public void SetD44RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D44Record == null)
    {
        _D44Record = new D44Record();
    }
    _D44Record.SetD44RecordAsString(value);
}

// Standard Getter
public D45Record GetD45Record()
{
    return _D45Record;
}

// Standard Setter
public void SetD45Record(D45Record value)
{
    _D45Record = value;
}

// Get<>AsString()
public string GetD45RecordAsString()
{
    return _D45Record != null ? _D45Record.GetD45RecordAsString() : "";
}

// Set<>AsString()
public void SetD45RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D45Record == null)
    {
        _D45Record = new D45Record();
    }
    _D45Record.SetD45RecordAsString(value);
}

// Standard Getter
public D45BalAcqDispRecord GetD45BalAcqDispRecord()
{
    return _D45BalAcqDispRecord;
}

// Standard Setter
public void SetD45BalAcqDispRecord(D45BalAcqDispRecord value)
{
    _D45BalAcqDispRecord = value;
}

// Get<>AsString()
public string GetD45BalAcqDispRecordAsString()
{
    return _D45BalAcqDispRecord != null ? _D45BalAcqDispRecord.GetD45BalAcqDispRecordAsString() : "";
}

// Set<>AsString()
public void SetD45BalAcqDispRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D45BalAcqDispRecord == null)
    {
        _D45BalAcqDispRecord = new D45BalAcqDispRecord();
    }
    _D45BalAcqDispRecord.SetD45BalAcqDispRecordAsString(value);
}

// Standard Getter
public D46Record GetD46Record()
{
    return _D46Record;
}

// Standard Setter
public void SetD46Record(D46Record value)
{
    _D46Record = value;
}

// Get<>AsString()
public string GetD46RecordAsString()
{
    return _D46Record != null ? _D46Record.GetD46RecordAsString() : "";
}

// Set<>AsString()
public void SetD46RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D46Record == null)
    {
        _D46Record = new D46Record();
    }
    _D46Record.SetD46RecordAsString(value);
}

// Standard Getter
public D46RecordN GetD46RecordN()
{
    return _D46RecordN;
}

// Standard Setter
public void SetD46RecordN(D46RecordN value)
{
    _D46RecordN = value;
}

// Get<>AsString()
public string GetD46RecordNAsString()
{
    return _D46RecordN != null ? _D46RecordN.GetD46RecordNAsString() : "";
}

// Set<>AsString()
public void SetD46RecordNAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D46RecordN == null)
    {
        _D46RecordN = new D46RecordN();
    }
    _D46RecordN.SetD46RecordNAsString(value);
}

// Standard Getter
public D46HeaderRecord GetD46HeaderRecord()
{
    return _D46HeaderRecord;
}

// Standard Setter
public void SetD46HeaderRecord(D46HeaderRecord value)
{
    _D46HeaderRecord = value;
}

// Get<>AsString()
public string GetD46HeaderRecordAsString()
{
    return _D46HeaderRecord != null ? _D46HeaderRecord.GetD46HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD46HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D46HeaderRecord == null)
    {
        _D46HeaderRecord = new D46HeaderRecord();
    }
    _D46HeaderRecord.SetD46HeaderRecordAsString(value);
}

// Standard Getter
public D46TrailerRecord GetD46TrailerRecord()
{
    return _D46TrailerRecord;
}

// Standard Setter
public void SetD46TrailerRecord(D46TrailerRecord value)
{
    _D46TrailerRecord = value;
}

// Get<>AsString()
public string GetD46TrailerRecordAsString()
{
    return _D46TrailerRecord != null ? _D46TrailerRecord.GetD46TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD46TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D46TrailerRecord == null)
    {
        _D46TrailerRecord = new D46TrailerRecord();
    }
    _D46TrailerRecord.SetD46TrailerRecordAsString(value);
}

// Standard Getter
public D49Record GetD49Record()
{
    return _D49Record;
}

// Standard Setter
public void SetD49Record(D49Record value)
{
    _D49Record = value;
}

// Get<>AsString()
public string GetD49RecordAsString()
{
    return _D49Record != null ? _D49Record.GetD49RecordAsString() : "";
}

// Set<>AsString()
public void SetD49RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D49Record == null)
    {
        _D49Record = new D49Record();
    }
    _D49Record.SetD49RecordAsString(value);
}

// Standard Getter
public D50Record GetD50Record()
{
    return _D50Record;
}

// Standard Setter
public void SetD50Record(D50Record value)
{
    _D50Record = value;
}

// Get<>AsString()
public string GetD50RecordAsString()
{
    return _D50Record != null ? _D50Record.GetD50RecordAsString() : "";
}

// Set<>AsString()
public void SetD50RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D50Record == null)
    {
        _D50Record = new D50Record();
    }
    _D50Record.SetD50RecordAsString(value);
}

// Standard Getter
public D51Record GetD51Record()
{
    return _D51Record;
}

// Standard Setter
public void SetD51Record(D51Record value)
{
    _D51Record = value;
}

// Get<>AsString()
public string GetD51RecordAsString()
{
    return _D51Record != null ? _D51Record.GetD51RecordAsString() : "";
}

// Set<>AsString()
public void SetD51RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D51Record == null)
    {
        _D51Record = new D51Record();
    }
    _D51Record.SetD51RecordAsString(value);
}

// Standard Getter
public D51HeaderRecord GetD51HeaderRecord()
{
    return _D51HeaderRecord;
}

// Standard Setter
public void SetD51HeaderRecord(D51HeaderRecord value)
{
    _D51HeaderRecord = value;
}

// Get<>AsString()
public string GetD51HeaderRecordAsString()
{
    return _D51HeaderRecord != null ? _D51HeaderRecord.GetD51HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD51HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D51HeaderRecord == null)
    {
        _D51HeaderRecord = new D51HeaderRecord();
    }
    _D51HeaderRecord.SetD51HeaderRecordAsString(value);
}

// Standard Getter
public D51TrailerRecord GetD51TrailerRecord()
{
    return _D51TrailerRecord;
}

// Standard Setter
public void SetD51TrailerRecord(D51TrailerRecord value)
{
    _D51TrailerRecord = value;
}

// Get<>AsString()
public string GetD51TrailerRecordAsString()
{
    return _D51TrailerRecord != null ? _D51TrailerRecord.GetD51TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD51TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D51TrailerRecord == null)
    {
        _D51TrailerRecord = new D51TrailerRecord();
    }
    _D51TrailerRecord.SetD51TrailerRecordAsString(value);
}

// Standard Getter
public D68Record GetD68Record()
{
    return _D68Record;
}

// Standard Setter
public void SetD68Record(D68Record value)
{
    _D68Record = value;
}

// Get<>AsString()
public string GetD68RecordAsString()
{
    return _D68Record != null ? _D68Record.GetD68RecordAsString() : "";
}

// Set<>AsString()
public void SetD68RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D68Record == null)
    {
        _D68Record = new D68Record();
    }
    _D68Record.SetD68RecordAsString(value);
}

// Standard Getter
public D68HeaderRecord GetD68HeaderRecord()
{
    return _D68HeaderRecord;
}

// Standard Setter
public void SetD68HeaderRecord(D68HeaderRecord value)
{
    _D68HeaderRecord = value;
}

// Get<>AsString()
public string GetD68HeaderRecordAsString()
{
    return _D68HeaderRecord != null ? _D68HeaderRecord.GetD68HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD68HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D68HeaderRecord == null)
    {
        _D68HeaderRecord = new D68HeaderRecord();
    }
    _D68HeaderRecord.SetD68HeaderRecordAsString(value);
}

// Standard Getter
public D68TrailerRecord GetD68TrailerRecord()
{
    return _D68TrailerRecord;
}

// Standard Setter
public void SetD68TrailerRecord(D68TrailerRecord value)
{
    _D68TrailerRecord = value;
}

// Get<>AsString()
public string GetD68TrailerRecordAsString()
{
    return _D68TrailerRecord != null ? _D68TrailerRecord.GetD68TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD68TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D68TrailerRecord == null)
    {
        _D68TrailerRecord = new D68TrailerRecord();
    }
    _D68TrailerRecord.SetD68TrailerRecordAsString(value);
}

// Standard Getter
public D69Record GetD69Record()
{
    return _D69Record;
}

// Standard Setter
public void SetD69Record(D69Record value)
{
    _D69Record = value;
}

// Get<>AsString()
public string GetD69RecordAsString()
{
    return _D69Record != null ? _D69Record.GetD69RecordAsString() : "";
}

// Set<>AsString()
public void SetD69RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D69Record == null)
    {
        _D69Record = new D69Record();
    }
    _D69Record.SetD69RecordAsString(value);
}

// Standard Getter
public D70Record GetD70Record()
{
    return _D70Record;
}

// Standard Setter
public void SetD70Record(D70Record value)
{
    _D70Record = value;
}

// Get<>AsString()
public string GetD70RecordAsString()
{
    return _D70Record != null ? _D70Record.GetD70RecordAsString() : "";
}

// Set<>AsString()
public void SetD70RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D70Record == null)
    {
        _D70Record = new D70Record();
    }
    _D70Record.SetD70RecordAsString(value);
}

// Standard Getter
public D70HeaderRecord GetD70HeaderRecord()
{
    return _D70HeaderRecord;
}

// Standard Setter
public void SetD70HeaderRecord(D70HeaderRecord value)
{
    _D70HeaderRecord = value;
}

// Get<>AsString()
public string GetD70HeaderRecordAsString()
{
    return _D70HeaderRecord != null ? _D70HeaderRecord.GetD70HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD70HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D70HeaderRecord == null)
    {
        _D70HeaderRecord = new D70HeaderRecord();
    }
    _D70HeaderRecord.SetD70HeaderRecordAsString(value);
}

// Standard Getter
public D70TrailerRecord GetD70TrailerRecord()
{
    return _D70TrailerRecord;
}

// Standard Setter
public void SetD70TrailerRecord(D70TrailerRecord value)
{
    _D70TrailerRecord = value;
}

// Get<>AsString()
public string GetD70TrailerRecordAsString()
{
    return _D70TrailerRecord != null ? _D70TrailerRecord.GetD70TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD70TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D70TrailerRecord == null)
    {
        _D70TrailerRecord = new D70TrailerRecord();
    }
    _D70TrailerRecord.SetD70TrailerRecordAsString(value);
}

// Standard Getter
public D71Record GetD71Record()
{
    return _D71Record;
}

// Standard Setter
public void SetD71Record(D71Record value)
{
    _D71Record = value;
}

// Get<>AsString()
public string GetD71RecordAsString()
{
    return _D71Record != null ? _D71Record.GetD71RecordAsString() : "";
}

// Set<>AsString()
public void SetD71RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D71Record == null)
    {
        _D71Record = new D71Record();
    }
    _D71Record.SetD71RecordAsString(value);
}

// Standard Getter
public D72Record GetD72Record()
{
    return _D72Record;
}

// Standard Setter
public void SetD72Record(D72Record value)
{
    _D72Record = value;
}

// Get<>AsString()
public string GetD72RecordAsString()
{
    return _D72Record != null ? _D72Record.GetD72RecordAsString() : "";
}

// Set<>AsString()
public void SetD72RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D72Record == null)
    {
        _D72Record = new D72Record();
    }
    _D72Record.SetD72RecordAsString(value);
}

// Standard Getter
public D73Record GetD73Record()
{
    return _D73Record;
}

// Standard Setter
public void SetD73Record(D73Record value)
{
    _D73Record = value;
}

// Get<>AsString()
public string GetD73RecordAsString()
{
    return _D73Record != null ? _D73Record.GetD73RecordAsString() : "";
}

// Set<>AsString()
public void SetD73RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D73Record == null)
    {
        _D73Record = new D73Record();
    }
    _D73Record.SetD73RecordAsString(value);
}

// Standard Getter
public D74Record GetD74Record()
{
    return _D74Record;
}

// Standard Setter
public void SetD74Record(D74Record value)
{
    _D74Record = value;
}

// Get<>AsString()
public string GetD74RecordAsString()
{
    return _D74Record != null ? _D74Record.GetD74RecordAsString() : "";
}

// Set<>AsString()
public void SetD74RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D74Record == null)
    {
        _D74Record = new D74Record();
    }
    _D74Record.SetD74RecordAsString(value);
}

// Standard Getter
public D75Record GetD75Record()
{
    return _D75Record;
}

// Standard Setter
public void SetD75Record(D75Record value)
{
    _D75Record = value;
}

// Get<>AsString()
public string GetD75RecordAsString()
{
    return _D75Record != null ? _D75Record.GetD75RecordAsString() : "";
}

// Set<>AsString()
public void SetD75RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D75Record == null)
    {
        _D75Record = new D75Record();
    }
    _D75Record.SetD75RecordAsString(value);
}

// Standard Getter
public D76Record GetD76Record()
{
    return _D76Record;
}

// Standard Setter
public void SetD76Record(D76Record value)
{
    _D76Record = value;
}

// Get<>AsString()
public string GetD76RecordAsString()
{
    return _D76Record != null ? _D76Record.GetD76RecordAsString() : "";
}

// Set<>AsString()
public void SetD76RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D76Record == null)
    {
        _D76Record = new D76Record();
    }
    _D76Record.SetD76RecordAsString(value);
}

// Standard Getter
public D77Record GetD77Record()
{
    return _D77Record;
}

// Standard Setter
public void SetD77Record(D77Record value)
{
    _D77Record = value;
}

// Get<>AsString()
public string GetD77RecordAsString()
{
    return _D77Record != null ? _D77Record.GetD77RecordAsString() : "";
}

// Set<>AsString()
public void SetD77RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D77Record == null)
    {
        _D77Record = new D77Record();
    }
    _D77Record.SetD77RecordAsString(value);
}

// Standard Getter
public D78Record GetD78Record()
{
    return _D78Record;
}

// Standard Setter
public void SetD78Record(D78Record value)
{
    _D78Record = value;
}

// Get<>AsString()
public string GetD78RecordAsString()
{
    return _D78Record != null ? _D78Record.GetD78RecordAsString() : "";
}

// Set<>AsString()
public void SetD78RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D78Record == null)
    {
        _D78Record = new D78Record();
    }
    _D78Record.SetD78RecordAsString(value);
}

// Standard Getter
public D79Record GetD79Record()
{
    return _D79Record;
}

// Standard Setter
public void SetD79Record(D79Record value)
{
    _D79Record = value;
}

// Get<>AsString()
public string GetD79RecordAsString()
{
    return _D79Record != null ? _D79Record.GetD79RecordAsString() : "";
}

// Set<>AsString()
public void SetD79RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D79Record == null)
    {
        _D79Record = new D79Record();
    }
    _D79Record.SetD79RecordAsString(value);
}

// Standard Getter
public D80Record GetD80Record()
{
    return _D80Record;
}

// Standard Setter
public void SetD80Record(D80Record value)
{
    _D80Record = value;
}

// Get<>AsString()
public string GetD80RecordAsString()
{
    return _D80Record != null ? _D80Record.GetD80RecordAsString() : "";
}

// Set<>AsString()
public void SetD80RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D80Record == null)
    {
        _D80Record = new D80Record();
    }
    _D80Record.SetD80RecordAsString(value);
}

// Standard Getter
public D81Record GetD81Record()
{
    return _D81Record;
}

// Standard Setter
public void SetD81Record(D81Record value)
{
    _D81Record = value;
}

// Get<>AsString()
public string GetD81RecordAsString()
{
    return _D81Record != null ? _D81Record.GetD81RecordAsString() : "";
}

// Set<>AsString()
public void SetD81RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D81Record == null)
    {
        _D81Record = new D81Record();
    }
    _D81Record.SetD81RecordAsString(value);
}

// Standard Getter
public D82Record GetD82Record()
{
    return _D82Record;
}

// Standard Setter
public void SetD82Record(D82Record value)
{
    _D82Record = value;
}

// Get<>AsString()
public string GetD82RecordAsString()
{
    return _D82Record != null ? _D82Record.GetD82RecordAsString() : "";
}

// Set<>AsString()
public void SetD82RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D82Record == null)
    {
        _D82Record = new D82Record();
    }
    _D82Record.SetD82RecordAsString(value);
}

// Standard Getter
public D83Record GetD83Record()
{
    return _D83Record;
}

// Standard Setter
public void SetD83Record(D83Record value)
{
    _D83Record = value;
}

// Get<>AsString()
public string GetD83RecordAsString()
{
    return _D83Record != null ? _D83Record.GetD83RecordAsString() : "";
}

// Set<>AsString()
public void SetD83RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D83Record == null)
    {
        _D83Record = new D83Record();
    }
    _D83Record.SetD83RecordAsString(value);
}

// Standard Getter
public D83HeaderRecord GetD83HeaderRecord()
{
    return _D83HeaderRecord;
}

// Standard Setter
public void SetD83HeaderRecord(D83HeaderRecord value)
{
    _D83HeaderRecord = value;
}

// Get<>AsString()
public string GetD83HeaderRecordAsString()
{
    return _D83HeaderRecord != null ? _D83HeaderRecord.GetD83HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD83HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D83HeaderRecord == null)
    {
        _D83HeaderRecord = new D83HeaderRecord();
    }
    _D83HeaderRecord.SetD83HeaderRecordAsString(value);
}

// Standard Getter
public D83TrailerRecord GetD83TrailerRecord()
{
    return _D83TrailerRecord;
}

// Standard Setter
public void SetD83TrailerRecord(D83TrailerRecord value)
{
    _D83TrailerRecord = value;
}

// Get<>AsString()
public string GetD83TrailerRecordAsString()
{
    return _D83TrailerRecord != null ? _D83TrailerRecord.GetD83TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD83TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D83TrailerRecord == null)
    {
        _D83TrailerRecord = new D83TrailerRecord();
    }
    _D83TrailerRecord.SetD83TrailerRecordAsString(value);
}

// Standard Getter
public D84Record GetD84Record()
{
    return _D84Record;
}

// Standard Setter
public void SetD84Record(D84Record value)
{
    _D84Record = value;
}

// Get<>AsString()
public string GetD84RecordAsString()
{
    return _D84Record != null ? _D84Record.GetD84RecordAsString() : "";
}

// Set<>AsString()
public void SetD84RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D84Record == null)
    {
        _D84Record = new D84Record();
    }
    _D84Record.SetD84RecordAsString(value);
}

// Standard Getter
public D85Record GetD85Record()
{
    return _D85Record;
}

// Standard Setter
public void SetD85Record(D85Record value)
{
    _D85Record = value;
}

// Get<>AsString()
public string GetD85RecordAsString()
{
    return _D85Record != null ? _D85Record.GetD85RecordAsString() : "";
}

// Set<>AsString()
public void SetD85RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D85Record == null)
    {
        _D85Record = new D85Record();
    }
    _D85Record.SetD85RecordAsString(value);
}

// Standard Getter
public D86Record GetD86Record()
{
    return _D86Record;
}

// Standard Setter
public void SetD86Record(D86Record value)
{
    _D86Record = value;
}

// Get<>AsString()
public string GetD86RecordAsString()
{
    return _D86Record != null ? _D86Record.GetD86RecordAsString() : "";
}

// Set<>AsString()
public void SetD86RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D86Record == null)
    {
        _D86Record = new D86Record();
    }
    _D86Record.SetD86RecordAsString(value);
}

// Standard Getter
public D87Record GetD87Record()
{
    return _D87Record;
}

// Standard Setter
public void SetD87Record(D87Record value)
{
    _D87Record = value;
}

// Get<>AsString()
public string GetD87RecordAsString()
{
    return _D87Record != null ? _D87Record.GetD87RecordAsString() : "";
}

// Set<>AsString()
public void SetD87RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D87Record == null)
    {
        _D87Record = new D87Record();
    }
    _D87Record.SetD87RecordAsString(value);
}

// Standard Getter
public D88Record GetD88Record()
{
    return _D88Record;
}

// Standard Setter
public void SetD88Record(D88Record value)
{
    _D88Record = value;
}

// Get<>AsString()
public string GetD88RecordAsString()
{
    return _D88Record != null ? _D88Record.GetD88RecordAsString() : "";
}

// Set<>AsString()
public void SetD88RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D88Record == null)
    {
        _D88Record = new D88Record();
    }
    _D88Record.SetD88RecordAsString(value);
}

// Standard Getter
public D89Record GetD89Record()
{
    return _D89Record;
}

// Standard Setter
public void SetD89Record(D89Record value)
{
    _D89Record = value;
}

// Get<>AsString()
public string GetD89RecordAsString()
{
    return _D89Record != null ? _D89Record.GetD89RecordAsString() : "";
}

// Set<>AsString()
public void SetD89RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D89Record == null)
    {
        _D89Record = new D89Record();
    }
    _D89Record.SetD89RecordAsString(value);
}

// Standard Getter
public D89HeaderRecord GetD89HeaderRecord()
{
    return _D89HeaderRecord;
}

// Standard Setter
public void SetD89HeaderRecord(D89HeaderRecord value)
{
    _D89HeaderRecord = value;
}

// Get<>AsString()
public string GetD89HeaderRecordAsString()
{
    return _D89HeaderRecord != null ? _D89HeaderRecord.GetD89HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD89HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D89HeaderRecord == null)
    {
        _D89HeaderRecord = new D89HeaderRecord();
    }
    _D89HeaderRecord.SetD89HeaderRecordAsString(value);
}

// Standard Getter
public D89TrailerRecord GetD89TrailerRecord()
{
    return _D89TrailerRecord;
}

// Standard Setter
public void SetD89TrailerRecord(D89TrailerRecord value)
{
    _D89TrailerRecord = value;
}

// Get<>AsString()
public string GetD89TrailerRecordAsString()
{
    return _D89TrailerRecord != null ? _D89TrailerRecord.GetD89TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD89TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D89TrailerRecord == null)
    {
        _D89TrailerRecord = new D89TrailerRecord();
    }
    _D89TrailerRecord.SetD89TrailerRecordAsString(value);
}

// Standard Getter
public D90Record GetD90Record()
{
    return _D90Record;
}

// Standard Setter
public void SetD90Record(D90Record value)
{
    _D90Record = value;
}

// Get<>AsString()
public string GetD90RecordAsString()
{
    return _D90Record != null ? _D90Record.GetD90RecordAsString() : "";
}

// Set<>AsString()
public void SetD90RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D90Record == null)
    {
        _D90Record = new D90Record();
    }
    _D90Record.SetD90RecordAsString(value);
}

// Standard Getter
public D91Record GetD91Record()
{
    return _D91Record;
}

// Standard Setter
public void SetD91Record(D91Record value)
{
    _D91Record = value;
}

// Get<>AsString()
public string GetD91RecordAsString()
{
    return _D91Record != null ? _D91Record.GetD91RecordAsString() : "";
}

// Set<>AsString()
public void SetD91RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D91Record == null)
    {
        _D91Record = new D91Record();
    }
    _D91Record.SetD91RecordAsString(value);
}

// Standard Getter
public D91HeaderRecord GetD91HeaderRecord()
{
    return _D91HeaderRecord;
}

// Standard Setter
public void SetD91HeaderRecord(D91HeaderRecord value)
{
    _D91HeaderRecord = value;
}

// Get<>AsString()
public string GetD91HeaderRecordAsString()
{
    return _D91HeaderRecord != null ? _D91HeaderRecord.GetD91HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD91HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D91HeaderRecord == null)
    {
        _D91HeaderRecord = new D91HeaderRecord();
    }
    _D91HeaderRecord.SetD91HeaderRecordAsString(value);
}

// Standard Getter
public D91TrailerRecord GetD91TrailerRecord()
{
    return _D91TrailerRecord;
}

// Standard Setter
public void SetD91TrailerRecord(D91TrailerRecord value)
{
    _D91TrailerRecord = value;
}

// Get<>AsString()
public string GetD91TrailerRecordAsString()
{
    return _D91TrailerRecord != null ? _D91TrailerRecord.GetD91TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD91TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D91TrailerRecord == null)
    {
        _D91TrailerRecord = new D91TrailerRecord();
    }
    _D91TrailerRecord.SetD91TrailerRecordAsString(value);
}

// Standard Getter
public D92Record GetD92Record()
{
    return _D92Record;
}

// Standard Setter
public void SetD92Record(D92Record value)
{
    _D92Record = value;
}

// Get<>AsString()
public string GetD92RecordAsString()
{
    return _D92Record != null ? _D92Record.GetD92RecordAsString() : "";
}

// Set<>AsString()
public void SetD92RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D92Record == null)
    {
        _D92Record = new D92Record();
    }
    _D92Record.SetD92RecordAsString(value);
}

// Standard Getter
public D93Record GetD93Record()
{
    return _D93Record;
}

// Standard Setter
public void SetD93Record(D93Record value)
{
    _D93Record = value;
}

// Get<>AsString()
public string GetD93RecordAsString()
{
    return _D93Record != null ? _D93Record.GetD93RecordAsString() : "";
}

// Set<>AsString()
public void SetD93RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D93Record == null)
    {
        _D93Record = new D93Record();
    }
    _D93Record.SetD93RecordAsString(value);
}

// Standard Getter
public D93HeaderRecord GetD93HeaderRecord()
{
    return _D93HeaderRecord;
}

// Standard Setter
public void SetD93HeaderRecord(D93HeaderRecord value)
{
    _D93HeaderRecord = value;
}

// Get<>AsString()
public string GetD93HeaderRecordAsString()
{
    return _D93HeaderRecord != null ? _D93HeaderRecord.GetD93HeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD93HeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D93HeaderRecord == null)
    {
        _D93HeaderRecord = new D93HeaderRecord();
    }
    _D93HeaderRecord.SetD93HeaderRecordAsString(value);
}

// Standard Getter
public D93TrailerRecord GetD93TrailerRecord()
{
    return _D93TrailerRecord;
}

// Standard Setter
public void SetD93TrailerRecord(D93TrailerRecord value)
{
    _D93TrailerRecord = value;
}

// Get<>AsString()
public string GetD93TrailerRecordAsString()
{
    return _D93TrailerRecord != null ? _D93TrailerRecord.GetD93TrailerRecordAsString() : "";
}

// Set<>AsString()
public void SetD93TrailerRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D93TrailerRecord == null)
    {
        _D93TrailerRecord = new D93TrailerRecord();
    }
    _D93TrailerRecord.SetD93TrailerRecordAsString(value);
}

// Standard Getter
public D94Record GetD94Record()
{
    return _D94Record;
}

// Standard Setter
public void SetD94Record(D94Record value)
{
    _D94Record = value;
}

// Get<>AsString()
public string GetD94RecordAsString()
{
    return _D94Record != null ? _D94Record.GetD94RecordAsString() : "";
}

// Set<>AsString()
public void SetD94RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D94Record == null)
    {
        _D94Record = new D94Record();
    }
    _D94Record.SetD94RecordAsString(value);
}

// Standard Getter
public D95Record GetD95Record()
{
    return _D95Record;
}

// Standard Setter
public void SetD95Record(D95Record value)
{
    _D95Record = value;
}

// Get<>AsString()
public string GetD95RecordAsString()
{
    return _D95Record != null ? _D95Record.GetD95RecordAsString() : "";
}

// Set<>AsString()
public void SetD95RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D95Record == null)
    {
        _D95Record = new D95Record();
    }
    _D95Record.SetD95RecordAsString(value);
}

// Standard Getter
public D98Record GetD98Record()
{
    return _D98Record;
}

// Standard Setter
public void SetD98Record(D98Record value)
{
    _D98Record = value;
}

// Get<>AsString()
public string GetD98RecordAsString()
{
    return _D98Record != null ? _D98Record.GetD98RecordAsString() : "";
}

// Set<>AsString()
public void SetD98RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D98Record == null)
    {
        _D98Record = new D98Record();
    }
    _D98Record.SetD98RecordAsString(value);
}

// Standard Getter
public D99Record GetD99Record()
{
    return _D99Record;
}

// Standard Setter
public void SetD99Record(D99Record value)
{
    _D99Record = value;
}

// Get<>AsString()
public string GetD99RecordAsString()
{
    return _D99Record != null ? _D99Record.GetD99RecordAsString() : "";
}

// Set<>AsString()
public void SetD99RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D99Record == null)
    {
        _D99Record = new D99Record();
    }
    _D99Record.SetD99RecordAsString(value);
}

// Standard Getter
public D101Record GetD101Record()
{
    return _D101Record;
}

// Standard Setter
public void SetD101Record(D101Record value)
{
    _D101Record = value;
}

// Get<>AsString()
public string GetD101RecordAsString()
{
    return _D101Record != null ? _D101Record.GetD101RecordAsString() : "";
}

// Set<>AsString()
public void SetD101RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D101Record == null)
    {
        _D101Record = new D101Record();
    }
    _D101Record.SetD101RecordAsString(value);
}

// Standard Getter
public D107Record GetD107Record()
{
    return _D107Record;
}

// Standard Setter
public void SetD107Record(D107Record value)
{
    _D107Record = value;
}

// Get<>AsString()
public string GetD107RecordAsString()
{
    return _D107Record != null ? _D107Record.GetD107RecordAsString() : "";
}

// Set<>AsString()
public void SetD107RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D107Record == null)
    {
        _D107Record = new D107Record();
    }
    _D107Record.SetD107RecordAsString(value);
}

// Standard Getter
public D108Record GetD108Record()
{
    return _D108Record;
}

// Standard Setter
public void SetD108Record(D108Record value)
{
    _D108Record = value;
}

// Get<>AsString()
public string GetD108RecordAsString()
{
    return _D108Record != null ? _D108Record.GetD108RecordAsString() : "";
}

// Set<>AsString()
public void SetD108RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D108Record == null)
    {
        _D108Record = new D108Record();
    }
    _D108Record.SetD108RecordAsString(value);
}

// Standard Getter
public D109Record GetD109Record()
{
    return _D109Record;
}

// Standard Setter
public void SetD109Record(D109Record value)
{
    _D109Record = value;
}

// Get<>AsString()
public string GetD109RecordAsString()
{
    return _D109Record != null ? _D109Record.GetD109RecordAsString() : "";
}

// Set<>AsString()
public void SetD109RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D109Record == null)
    {
        _D109Record = new D109Record();
    }
    _D109Record.SetD109RecordAsString(value);
}

// Standard Getter
public D112Record GetD112Record()
{
    return _D112Record;
}

// Standard Setter
public void SetD112Record(D112Record value)
{
    _D112Record = value;
}

// Get<>AsString()
public string GetD112RecordAsString()
{
    return _D112Record != null ? _D112Record.GetD112RecordAsString() : "";
}

// Set<>AsString()
public void SetD112RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D112Record == null)
    {
        _D112Record = new D112Record();
    }
    _D112Record.SetD112RecordAsString(value);
}

// Standard Getter
public D133Record GetD133Record()
{
    return _D133Record;
}

// Standard Setter
public void SetD133Record(D133Record value)
{
    _D133Record = value;
}

// Get<>AsString()
public string GetD133RecordAsString()
{
    return _D133Record != null ? _D133Record.GetD133RecordAsString() : "";
}

// Set<>AsString()
public void SetD133RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D133Record == null)
    {
        _D133Record = new D133Record();
    }
    _D133Record.SetD133RecordAsString(value);
}

// Standard Getter
public D153Record GetD153Record()
{
    return _D153Record;
}

// Standard Setter
public void SetD153Record(D153Record value)
{
    _D153Record = value;
}

// Get<>AsString()
public string GetD153RecordAsString()
{
    return _D153Record != null ? _D153Record.GetD153RecordAsString() : "";
}

// Set<>AsString()
public void SetD153RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D153Record == null)
    {
        _D153Record = new D153Record();
    }
    _D153Record.SetD153RecordAsString(value);
}

// Standard Getter
public D154Record GetD154Record()
{
    return _D154Record;
}

// Standard Setter
public void SetD154Record(D154Record value)
{
    _D154Record = value;
}

// Get<>AsString()
public string GetD154RecordAsString()
{
    return _D154Record != null ? _D154Record.GetD154RecordAsString() : "";
}

// Set<>AsString()
public void SetD154RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D154Record == null)
    {
        _D154Record = new D154Record();
    }
    _D154Record.SetD154RecordAsString(value);
}

// Standard Getter
public D161Record GetD161Record()
{
    return _D161Record;
}

// Standard Setter
public void SetD161Record(D161Record value)
{
    _D161Record = value;
}

// Get<>AsString()
public string GetD161RecordAsString()
{
    return _D161Record != null ? _D161Record.GetD161RecordAsString() : "";
}

// Set<>AsString()
public void SetD161RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D161Record == null)
    {
        _D161Record = new D161Record();
    }
    _D161Record.SetD161RecordAsString(value);
}

// Standard Getter
public D163Record GetD163Record()
{
    return _D163Record;
}

// Standard Setter
public void SetD163Record(D163Record value)
{
    _D163Record = value;
}

// Get<>AsString()
public string GetD163RecordAsString()
{
    return _D163Record != null ? _D163Record.GetD163RecordAsString() : "";
}

// Set<>AsString()
public void SetD163RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D163Record == null)
    {
        _D163Record = new D163Record();
    }
    _D163Record.SetD163RecordAsString(value);
}

// Standard Getter
public D167Record GetD167Record()
{
    return _D167Record;
}

// Standard Setter
public void SetD167Record(D167Record value)
{
    _D167Record = value;
}

// Get<>AsString()
public string GetD167RecordAsString()
{
    return _D167Record != null ? _D167Record.GetD167RecordAsString() : "";
}

// Set<>AsString()
public void SetD167RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D167Record == null)
    {
        _D167Record = new D167Record();
    }
    _D167Record.SetD167RecordAsString(value);
}

// Standard Getter
public D169Record GetD169Record()
{
    return _D169Record;
}

// Standard Setter
public void SetD169Record(D169Record value)
{
    _D169Record = value;
}

// Get<>AsString()
public string GetD169RecordAsString()
{
    return _D169Record != null ? _D169Record.GetD169RecordAsString() : "";
}

// Set<>AsString()
public void SetD169RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D169Record == null)
    {
        _D169Record = new D169Record();
    }
    _D169Record.SetD169RecordAsString(value);
}

// Standard Getter
public D170Record GetD170Record()
{
    return _D170Record;
}

// Standard Setter
public void SetD170Record(D170Record value)
{
    _D170Record = value;
}

// Get<>AsString()
public string GetD170RecordAsString()
{
    return _D170Record != null ? _D170Record.GetD170RecordAsString() : "";
}

// Set<>AsString()
public void SetD170RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D170Record == null)
    {
        _D170Record = new D170Record();
    }
    _D170Record.SetD170RecordAsString(value);
}

public void SyncD46RecordFromD46RecordN()
{
    this.SetD46RecordAsString(this.GetD46RecordNAsString());
}

public void SyncD46RecordNFromD46Record()
{
    this.SetD46RecordNAsString(this.GetD46RecordAsString());
}
public void SyncD46RecordFromD46HeaderRecord()
{
    this.SetD46RecordAsString(this.GetD46HeaderRecordAsString());
}

public void SyncD46HeaderRecordFromD46Record()
{
    this.SetD46HeaderRecordAsString(this.GetD46RecordAsString());
}
public void SyncD46RecordFromD46TrailerRecord()
{
    this.SetD46RecordAsString(this.GetD46TrailerRecordAsString());
}

public void SyncD46TrailerRecordFromD46Record()
{
    this.SetD46TrailerRecordAsString(this.GetD46RecordAsString());
}
public void SyncD51RecordFromD51HeaderRecord()
{
    this.SetD51RecordAsString(this.GetD51HeaderRecordAsString());
}

public void SyncD51HeaderRecordFromD51Record()
{
    this.SetD51HeaderRecordAsString(this.GetD51RecordAsString());
}
public void SyncD51RecordFromD51TrailerRecord()
{
    this.SetD51RecordAsString(this.GetD51TrailerRecordAsString());
}

public void SyncD51TrailerRecordFromD51Record()
{
    this.SetD51TrailerRecordAsString(this.GetD51RecordAsString());
}
public void SyncD68RecordFromD68HeaderRecord()
{
    this.SetD68RecordAsString(this.GetD68HeaderRecordAsString());
}

public void SyncD68HeaderRecordFromD68Record()
{
    this.SetD68HeaderRecordAsString(this.GetD68RecordAsString());
}
public void SyncD68RecordFromD68TrailerRecord()
{
    this.SetD68RecordAsString(this.GetD68TrailerRecordAsString());
}

public void SyncD68TrailerRecordFromD68Record()
{
    this.SetD68TrailerRecordAsString(this.GetD68RecordAsString());
}
public void SyncD70RecordFromD70HeaderRecord()
{
    this.SetD70RecordAsString(this.GetD70HeaderRecordAsString());
}

public void SyncD70HeaderRecordFromD70Record()
{
    this.SetD70HeaderRecordAsString(this.GetD70RecordAsString());
}
public void SyncD70RecordFromD70TrailerRecord()
{
    this.SetD70RecordAsString(this.GetD70TrailerRecordAsString());
}

public void SyncD70TrailerRecordFromD70Record()
{
    this.SetD70TrailerRecordAsString(this.GetD70RecordAsString());
}
public void SyncD83RecordFromD83HeaderRecord()
{
    this.SetD83RecordAsString(this.GetD83HeaderRecordAsString());
}

public void SyncD83HeaderRecordFromD83Record()
{
    this.SetD83HeaderRecordAsString(this.GetD83RecordAsString());
}
public void SyncD83RecordFromD83TrailerRecord()
{
    this.SetD83RecordAsString(this.GetD83TrailerRecordAsString());
}

public void SyncD83TrailerRecordFromD83Record()
{
    this.SetD83TrailerRecordAsString(this.GetD83RecordAsString());
}
public void SyncD89RecordFromD89HeaderRecord()
{
    this.SetD89RecordAsString(this.GetD89HeaderRecordAsString());
}

public void SyncD89HeaderRecordFromD89Record()
{
    this.SetD89HeaderRecordAsString(this.GetD89RecordAsString());
}
public void SyncD89RecordFromD89TrailerRecord()
{
    this.SetD89RecordAsString(this.GetD89TrailerRecordAsString());
}

public void SyncD89TrailerRecordFromD89Record()
{
    this.SetD89TrailerRecordAsString(this.GetD89RecordAsString());
}
public void SyncD91RecordFromD91HeaderRecord()
{
    this.SetD91RecordAsString(this.GetD91HeaderRecordAsString());
}

public void SyncD91HeaderRecordFromD91Record()
{
    this.SetD91HeaderRecordAsString(this.GetD91RecordAsString());
}
public void SyncD91RecordFromD91TrailerRecord()
{
    this.SetD91RecordAsString(this.GetD91TrailerRecordAsString());
}

public void SyncD91TrailerRecordFromD91Record()
{
    this.SetD91TrailerRecordAsString(this.GetD91RecordAsString());
}
public void SyncD93RecordFromD93HeaderRecord()
{
    this.SetD93RecordAsString(this.GetD93HeaderRecordAsString());
}

public void SyncD93HeaderRecordFromD93Record()
{
    this.SetD93HeaderRecordAsString(this.GetD93RecordAsString());
}
public void SyncD93RecordFromD93TrailerRecord()
{
    this.SetD93RecordAsString(this.GetD93TrailerRecordAsString());
}

public void SyncD93TrailerRecordFromD93Record()
{
    this.SetD93TrailerRecordAsString(this.GetD93RecordAsString());
}


}}