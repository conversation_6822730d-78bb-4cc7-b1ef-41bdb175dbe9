using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportFile Data Structure

public class ReportFile
{
    private static int _size = 12;
    // [DEBUG] Class: ReportFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler125, is_external=, is_static_class=False, static_prefix=
    private string _Filler125 ="$";
    
    
    
    
    // [DEBUG] Field: ReportUserNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportUserNo ="9999";
    
    
    
    
    // [DEBUG] Field: Filler126, is_external=, is_static_class=False, static_prefix=
    private string _Filler126 ="BU";
    
    
    
    
    // [DEBUG] Field: ReportGenNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportGenNo ="G";
    
    
    
    
    // [DEBUG] Field: Filler127, is_external=, is_static_class=False, static_prefix=
    private string _Filler127 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetReportFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler125.PadRight(1));
        result.Append(_ReportUserNo.PadRight(4));
        result.Append(_Filler126.PadRight(2));
        result.Append(_ReportGenNo.PadRight(1));
        result.Append(_Filler127.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetReportFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller125(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetReportUserNo(extracted);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller126(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetReportGenNo(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller127(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportFileAsString();
    }
    // Set<>String Override function
    public void SetReportFile(string value)
    {
        SetReportFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller125()
    {
        return _Filler125;
    }
    
    // Standard Setter
    public void SetFiller125(string value)
    {
        _Filler125 = value;
    }
    
    // Get<>AsString()
    public string GetFiller125AsString()
    {
        return _Filler125.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller125AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler125 = value;
    }
    
    // Standard Getter
    public string GetReportUserNo()
    {
        return _ReportUserNo;
    }
    
    // Standard Setter
    public void SetReportUserNo(string value)
    {
        _ReportUserNo = value;
    }
    
    // Get<>AsString()
    public string GetReportUserNoAsString()
    {
        return _ReportUserNo.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetReportUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller126()
    {
        return _Filler126;
    }
    
    // Standard Setter
    public void SetFiller126(string value)
    {
        _Filler126 = value;
    }
    
    // Get<>AsString()
    public string GetFiller126AsString()
    {
        return _Filler126.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller126AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler126 = value;
    }
    
    // Standard Getter
    public string GetReportGenNo()
    {
        return _ReportGenNo;
    }
    
    // Standard Setter
    public void SetReportGenNo(string value)
    {
        _ReportGenNo = value;
    }
    
    // Get<>AsString()
    public string GetReportGenNoAsString()
    {
        return _ReportGenNo.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetReportGenNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportGenNo = value;
    }
    
    // Standard Getter
    public string GetFiller127()
    {
        return _Filler127;
    }
    
    // Standard Setter
    public void SetFiller127(string value)
    {
        _Filler127 = value;
    }
    
    // Get<>AsString()
    public string GetFiller127AsString()
    {
        return _Filler127.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller127AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler127 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}