using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportOpt01 Data Structure

public class ReportOpt01
{
    private static int _size = 0;
    // [DEBUG] Class: ReportOpt01, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler163, is_external=, is_static_class=False, static_prefix=
    private string _Filler163 ="";
    
    
    
    
    // [DEBUG] Field: Filler164, is_external=, is_static_class=False, static_prefix=
    private string _Filler164 ="";
    
    
    
    
    // [DEBUG] Field: Filler165, is_external=, is_static_class=False, static_prefix=
    private string _Filler165 ="";
    
    
    
    
    // [DEBUG] Field: Filler166, is_external=, is_static_class=False, static_prefix=
    private Filler166 _Filler166 = new Filler166();
    
    
    
    
    // [DEBUG] Field: Filler169, is_external=, is_static_class=False, static_prefix=
    private string _Filler169 ="";
    
    
    
    
    // [DEBUG] Field: Filler170, is_external=, is_static_class=False, static_prefix=
    private Filler170 _Filler170 = new Filler170();
    
    
    
    
    // [DEBUG] Field: Filler173, is_external=, is_static_class=False, static_prefix=
    private string _Filler173 ="";
    
    
    
    
    // [DEBUG] Field: Filler174, is_external=, is_static_class=False, static_prefix=
    private string _Filler174 ="";
    
    
    
    
    
    // Serialization methods
    public string GetReportOpt01AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler163.PadRight(0));
        result.Append(_Filler164.PadRight(0));
        result.Append(_Filler165.PadRight(0));
        result.Append(_Filler166.GetFiller166AsString());
        result.Append(_Filler169.PadRight(0));
        result.Append(_Filler170.GetFiller170AsString());
        result.Append(_Filler173.PadRight(0));
        result.Append(_Filler174.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetReportOpt01AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller163(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller164(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller165(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _Filler166.SetFiller166AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler166.SetFiller166AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller169(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _Filler170.SetFiller170AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler170.SetFiller170AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller173(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller174(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportOpt01AsString();
    }
    // Set<>String Override function
    public void SetReportOpt01(string value)
    {
        SetReportOpt01AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller163()
    {
        return _Filler163;
    }
    
    // Standard Setter
    public void SetFiller163(string value)
    {
        _Filler163 = value;
    }
    
    // Get<>AsString()
    public string GetFiller163AsString()
    {
        return _Filler163.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller163AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler163 = value;
    }
    
    // Standard Getter
    public string GetFiller164()
    {
        return _Filler164;
    }
    
    // Standard Setter
    public void SetFiller164(string value)
    {
        _Filler164 = value;
    }
    
    // Get<>AsString()
    public string GetFiller164AsString()
    {
        return _Filler164.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller164AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler164 = value;
    }
    
    // Standard Getter
    public string GetFiller165()
    {
        return _Filler165;
    }
    
    // Standard Setter
    public void SetFiller165(string value)
    {
        _Filler165 = value;
    }
    
    // Get<>AsString()
    public string GetFiller165AsString()
    {
        return _Filler165.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller165AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler165 = value;
    }
    
    // Standard Getter
    public Filler166 GetFiller166()
    {
        return _Filler166;
    }
    
    // Standard Setter
    public void SetFiller166(Filler166 value)
    {
        _Filler166 = value;
    }
    
    // Get<>AsString()
    public string GetFiller166AsString()
    {
        return _Filler166 != null ? _Filler166.GetFiller166AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller166AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler166 == null)
        {
            _Filler166 = new Filler166();
        }
        _Filler166.SetFiller166AsString(value);
    }
    
    // Standard Getter
    public string GetFiller169()
    {
        return _Filler169;
    }
    
    // Standard Setter
    public void SetFiller169(string value)
    {
        _Filler169 = value;
    }
    
    // Get<>AsString()
    public string GetFiller169AsString()
    {
        return _Filler169.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller169AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler169 = value;
    }
    
    // Standard Getter
    public Filler170 GetFiller170()
    {
        return _Filler170;
    }
    
    // Standard Setter
    public void SetFiller170(Filler170 value)
    {
        _Filler170 = value;
    }
    
    // Get<>AsString()
    public string GetFiller170AsString()
    {
        return _Filler170 != null ? _Filler170.GetFiller170AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller170AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler170 == null)
        {
            _Filler170 = new Filler170();
        }
        _Filler170.SetFiller170AsString(value);
    }
    
    // Standard Getter
    public string GetFiller173()
    {
        return _Filler173;
    }
    
    // Standard Setter
    public void SetFiller173(string value)
    {
        _Filler173 = value;
    }
    
    // Get<>AsString()
    public string GetFiller173AsString()
    {
        return _Filler173.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller173AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler173 = value;
    }
    
    // Standard Getter
    public string GetFiller174()
    {
        return _Filler174;
    }
    
    // Standard Setter
    public void SetFiller174(string value)
    {
        _Filler174 = value;
    }
    
    // Get<>AsString()
    public string GetFiller174AsString()
    {
        return _Filler174.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller174AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler174 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller166(string value)
    {
        _Filler166.SetFiller166AsString(value);
    }
    // Nested Class: Filler166
    public class Filler166
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler167, is_external=, is_static_class=False, static_prefix=
        private string _Filler167 ="";
        
        
        
        
        // [DEBUG] Field: RInputFile, is_external=, is_static_class=False, static_prefix=
        private string _RInputFile ="";
        
        
        
        
        // [DEBUG] Field: Filler168, is_external=, is_static_class=False, static_prefix=
        private string _Filler168 ="";
        
        
        
        
    public Filler166() {}
    
    public Filler166(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller167(data.Substring(offset, 0).Trim());
        offset += 0;
        SetRInputFile(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller168(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetFiller166AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler167.PadRight(0));
        result.Append(_RInputFile.PadRight(0));
        result.Append(_Filler168.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetFiller166AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller167(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetRInputFile(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller168(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller167()
    {
        return _Filler167;
    }
    
    // Standard Setter
    public void SetFiller167(string value)
    {
        _Filler167 = value;
    }
    
    // Get<>AsString()
    public string GetFiller167AsString()
    {
        return _Filler167.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller167AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler167 = value;
    }
    
    // Standard Getter
    public string GetRInputFile()
    {
        return _RInputFile;
    }
    
    // Standard Setter
    public void SetRInputFile(string value)
    {
        _RInputFile = value;
    }
    
    // Get<>AsString()
    public string GetRInputFileAsString()
    {
        return _RInputFile.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetRInputFileAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RInputFile = value;
    }
    
    // Standard Getter
    public string GetFiller168()
    {
        return _Filler168;
    }
    
    // Standard Setter
    public void SetFiller168(string value)
    {
        _Filler168 = value;
    }
    
    // Get<>AsString()
    public string GetFiller168AsString()
    {
        return _Filler168.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller168AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler168 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller170(string value)
{
    _Filler170.SetFiller170AsString(value);
}
// Nested Class: Filler170
public class Filler170
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler171, is_external=, is_static_class=False, static_prefix=
    private string _Filler171 ="";
    
    
    
    
    // [DEBUG] Field: RFundSelection, is_external=, is_static_class=False, static_prefix=
    private string _RFundSelection ="";
    
    
    
    
    // [DEBUG] Field: Filler172, is_external=, is_static_class=False, static_prefix=
    private string _Filler172 ="";
    
    
    
    
public Filler170() {}

public Filler170(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller171(data.Substring(offset, 0).Trim());
    offset += 0;
    SetRFundSelection(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller172(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller170AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler171.PadRight(0));
    result.Append(_RFundSelection.PadRight(0));
    result.Append(_Filler172.PadRight(0));
    
    return result.ToString();
}

public void SetFiller170AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller171(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetRFundSelection(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller172(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller171()
{
    return _Filler171;
}

// Standard Setter
public void SetFiller171(string value)
{
    _Filler171 = value;
}

// Get<>AsString()
public string GetFiller171AsString()
{
    return _Filler171.PadRight(0);
}

// Set<>AsString()
public void SetFiller171AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler171 = value;
}

// Standard Getter
public string GetRFundSelection()
{
    return _RFundSelection;
}

// Standard Setter
public void SetRFundSelection(string value)
{
    _RFundSelection = value;
}

// Get<>AsString()
public string GetRFundSelectionAsString()
{
    return _RFundSelection.PadRight(0);
}

// Set<>AsString()
public void SetRFundSelectionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RFundSelection = value;
}

// Standard Getter
public string GetFiller172()
{
    return _Filler172;
}

// Standard Setter
public void SetFiller172(string value)
{
    _Filler172 = value;
}

// Get<>AsString()
public string GetFiller172AsString()
{
    return _Filler172.PadRight(0);
}

// Set<>AsString()
public void SetFiller172AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler172 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}