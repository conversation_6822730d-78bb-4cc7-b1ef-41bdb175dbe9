using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing EBondDetail Data Structure

public class EBondDetail
{
    private static int _size = 181;
    // [DEBUG] Class: EBondDetail, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: EBondControl, is_external=, is_static_class=False, static_prefix=
    private string _EBondControl ="";
    
    
    
    
    // [DEBUG] Field: EBond, is_external=, is_static_class=False, static_prefix=
    private string _EBond ="";
    
    
    
    
    // [DEBUG] Field: EBondText, is_external=, is_static_class=False, static_prefix=
    private string _EBondText ="";
    
    
    
    
    // [DEBUG] Field: Filler87, is_external=, is_static_class=False, static_prefix=
    private string _Filler87 ="";
    
    
    
    
    // [DEBUG] Field: EBondRest, is_external=, is_static_class=False, static_prefix=
    private string _EBondRest ="";
    
    
    
    
    
    // Serialization methods
    public string GetEBondDetailAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EBondControl.PadRight(1));
        result.Append(_EBond.PadRight(1));
        result.Append(_EBondText.PadRight(18));
        result.Append(_Filler87.PadRight(1));
        result.Append(_EBondRest.PadRight(160));
        
        return result.ToString();
    }
    
    public void SetEBondDetailAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetEBondControl(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetEBond(extracted);
        }
        offset += 1;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetEBondText(extracted);
        }
        offset += 18;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller87(extracted);
        }
        offset += 1;
        if (offset + 160 <= data.Length)
        {
            string extracted = data.Substring(offset, 160).Trim();
            SetEBondRest(extracted);
        }
        offset += 160;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetEBondDetailAsString();
    }
    // Set<>String Override function
    public void SetEBondDetail(string value)
    {
        SetEBondDetailAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetEBondControl()
    {
        return _EBondControl;
    }
    
    // Standard Setter
    public void SetEBondControl(string value)
    {
        _EBondControl = value;
    }
    
    // Get<>AsString()
    public string GetEBondControlAsString()
    {
        return _EBondControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetEBondControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EBondControl = value;
    }
    
    // Standard Getter
    public string GetEBond()
    {
        return _EBond;
    }
    
    // Standard Setter
    public void SetEBond(string value)
    {
        _EBond = value;
    }
    
    // Get<>AsString()
    public string GetEBondAsString()
    {
        return _EBond.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetEBondAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EBond = value;
    }
    
    // Standard Getter
    public string GetEBondText()
    {
        return _EBondText;
    }
    
    // Standard Setter
    public void SetEBondText(string value)
    {
        _EBondText = value;
    }
    
    // Get<>AsString()
    public string GetEBondTextAsString()
    {
        return _EBondText.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetEBondTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EBondText = value;
    }
    
    // Standard Getter
    public string GetFiller87()
    {
        return _Filler87;
    }
    
    // Standard Setter
    public void SetFiller87(string value)
    {
        _Filler87 = value;
    }
    
    // Get<>AsString()
    public string GetFiller87AsString()
    {
        return _Filler87.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller87AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler87 = value;
    }
    
    // Standard Getter
    public string GetEBondRest()
    {
        return _EBondRest;
    }
    
    // Standard Setter
    public void SetEBondRest(string value)
    {
        _EBondRest = value;
    }
    
    // Get<>AsString()
    public string GetEBondRestAsString()
    {
        return _EBondRest.PadRight(160);
    }
    
    // Set<>AsString()
    public void SetEBondRestAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EBondRest = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
