using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing LkFundSedol Data Structure

public class LkFundSedol
{
    private static int _size = 0;
    // [DEBUG] Class: LkFundSedol, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LkFund, is_external=, is_static_class=False, static_prefix=
    private string _LkFund =" ";
    
    
    
    
    // [DEBUG] Field: LkSedol, is_external=, is_static_class=False, static_prefix=
    private string _LkSedol =" ";
    
    
    
    
    
    // Serialization methods
    public string GetLkFundSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LkFund.PadRight(0));
        result.Append(_LkSedol.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetLkFundSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLkFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLkSedol(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetLkFundSedolAsString();
    }
    // Set<>String Override function
    public void SetLkFundSedol(string value)
    {
        SetLkFundSedolAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLkFund()
    {
        return _LkFund;
    }
    
    // Standard Setter
    public void SetLkFund(string value)
    {
        _LkFund = value;
    }
    
    // Get<>AsString()
    public string GetLkFundAsString()
    {
        return _LkFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLkFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LkFund = value;
    }
    
    // Standard Getter
    public string GetLkSedol()
    {
        return _LkSedol;
    }
    
    // Standard Setter
    public void SetLkSedol(string value)
    {
        _LkSedol = value;
    }
    
    // Get<>AsString()
    public string GetLkSedolAsString()
    {
        return _LkSedol.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLkSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LkSedol = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}