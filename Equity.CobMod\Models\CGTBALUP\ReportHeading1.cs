using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportHeading1 Data Structure

public class ReportHeading1
{
    private static int _size = 118;
    // [DEBUG] Class: ReportHeading1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ReportRef, is_external=, is_static_class=False, static_prefix=
    private string _ReportRef ="";
    
    
    
    
    // [DEBUG] Field: Filler128, is_external=, is_static_class=False, static_prefix=
    private string _Filler128 ="";
    
    
    
    
    // [DEBUG] Field: Filler129, is_external=, is_static_class=False, static_prefix=
    private string _Filler129 ="RUN DATE";
    
    
    
    
    // [DEBUG] Field: ReportDate, is_external=, is_static_class=False, static_prefix=
    private string _ReportDate ="";
    
    
    
    
    // [DEBUG] Field: Filler130, is_external=, is_static_class=False, static_prefix=
    private string _Filler130 ="PAGE";
    
    
    
    
    // [DEBUG] Field: ReportPageNo, is_external=, is_static_class=False, static_prefix=
    private decimal _ReportPageNo =0;
    
    
    
    
    
    // Serialization methods
    public string GetReportHeading1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ReportRef.PadRight(30));
        result.Append(_Filler128.PadRight(65));
        result.Append(_Filler129.PadRight(0));
        result.Append(_ReportDate.PadRight(18));
        result.Append(_Filler130.PadRight(0));
        result.Append(_ReportPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetReportHeading1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetReportRef(extracted);
        }
        offset += 30;
        if (offset + 65 <= data.Length)
        {
            string extracted = data.Substring(offset, 65).Trim();
            SetFiller128(extracted);
        }
        offset += 65;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller129(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetReportDate(extracted);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller130(extracted);
        }
        offset += 0;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetReportPageNo(parsedDec);
        }
        offset += 5;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportHeading1AsString();
    }
    // Set<>String Override function
    public void SetReportHeading1(string value)
    {
        SetReportHeading1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetReportRef()
    {
        return _ReportRef;
    }
    
    // Standard Setter
    public void SetReportRef(string value)
    {
        _ReportRef = value;
    }
    
    // Get<>AsString()
    public string GetReportRefAsString()
    {
        return _ReportRef.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetReportRefAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportRef = value;
    }
    
    // Standard Getter
    public string GetFiller128()
    {
        return _Filler128;
    }
    
    // Standard Setter
    public void SetFiller128(string value)
    {
        _Filler128 = value;
    }
    
    // Get<>AsString()
    public string GetFiller128AsString()
    {
        return _Filler128.PadRight(65);
    }
    
    // Set<>AsString()
    public void SetFiller128AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler128 = value;
    }
    
    // Standard Getter
    public string GetFiller129()
    {
        return _Filler129;
    }
    
    // Standard Setter
    public void SetFiller129(string value)
    {
        _Filler129 = value;
    }
    
    // Get<>AsString()
    public string GetFiller129AsString()
    {
        return _Filler129.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller129AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler129 = value;
    }
    
    // Standard Getter
    public string GetReportDate()
    {
        return _ReportDate;
    }
    
    // Standard Setter
    public void SetReportDate(string value)
    {
        _ReportDate = value;
    }
    
    // Get<>AsString()
    public string GetReportDateAsString()
    {
        return _ReportDate.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetReportDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportDate = value;
    }
    
    // Standard Getter
    public string GetFiller130()
    {
        return _Filler130;
    }
    
    // Standard Setter
    public void SetFiller130(string value)
    {
        _Filler130 = value;
    }
    
    // Get<>AsString()
    public string GetFiller130AsString()
    {
        return _Filler130.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller130AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler130 = value;
    }
    
    // Standard Getter
    public decimal GetReportPageNo()
    {
        return _ReportPageNo;
    }
    
    // Standard Setter
    public void SetReportPageNo(decimal value)
    {
        _ReportPageNo = value;
    }
    
    // Get<>AsString()
    public string GetReportPageNoAsString()
    {
        return _ReportPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetReportPageNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ReportPageNo = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}