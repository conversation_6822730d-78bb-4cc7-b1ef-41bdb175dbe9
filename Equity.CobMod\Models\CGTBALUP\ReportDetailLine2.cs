using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportDetailLine2 Data Structure

public class ReportDetailLine2
{
    private static int _size = 106;
    // [DEBUG] Class: ReportDetailLine2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ReportFund2, is_external=, is_static_class=False, static_prefix=
    private string _ReportFund2 ="";
    
    
    
    
    // [DEBUG] Field: ReportSedol2, is_external=, is_static_class=False, static_prefix=
    private string _ReportSedol2 ="";
    
    
    
    
    // [DEBUG] Field: ReportBalAdd, is_external=, is_static_class=False, static_prefix=
    private string _ReportBalAdd ="";
    
    
    
    
    // [DEBUG] Field: Filler156, is_external=, is_static_class=False, static_prefix=
    private string _Filler156 ="BALANCES ADDED";
    
    
    
    
    // [DEBUG] Field: ReportBalUpd, is_external=, is_static_class=False, static_prefix=
    private string _ReportBalUpd ="";
    
    
    
    
    // [DEBUG] Field: Filler157, is_external=, is_static_class=False, static_prefix=
    private string _Filler157 ="BALANCES UPDATED";
    
    
    
    
    // [DEBUG] Field: ReportTransAdd, is_external=, is_static_class=False, static_prefix=
    private string _ReportTransAdd ="";
    
    
    
    
    // [DEBUG] Field: Filler158, is_external=, is_static_class=False, static_prefix=
    private string _Filler158 ="TRANSACTIONS ADDED";
    
    
    
    
    // [DEBUG] Field: ReportTransUpd, is_external=, is_static_class=False, static_prefix=
    private string _ReportTransUpd ="";
    
    
    
    
    // [DEBUG] Field: Filler159, is_external=, is_static_class=False, static_prefix=
    private string _Filler159 ="TRANSACTIONS UPDATED";
    
    
    
    
    // [DEBUG] Field: ReportBalDel, is_external=, is_static_class=False, static_prefix=
    private string _ReportBalDel ="";
    
    
    
    
    // [DEBUG] Field: Filler160, is_external=, is_static_class=False, static_prefix=
    private string _Filler160 ="BALANCES DELETED";
    
    
    
    
    
    // Serialization methods
    public string GetReportDetailLine2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ReportFund2.PadRight(6));
        result.Append(_ReportSedol2.PadRight(11));
        result.Append(_ReportBalAdd.PadRight(0));
        result.Append(_Filler156.PadRight(15));
        result.Append(_ReportBalUpd.PadRight(0));
        result.Append(_Filler157.PadRight(17));
        result.Append(_ReportTransAdd.PadRight(0));
        result.Append(_Filler158.PadRight(19));
        result.Append(_ReportTransUpd.PadRight(0));
        result.Append(_Filler159.PadRight(21));
        result.Append(_ReportBalDel.PadRight(0));
        result.Append(_Filler160.PadRight(17));
        
        return result.ToString();
    }
    
    public void SetReportDetailLine2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetReportFund2(extracted);
        }
        offset += 6;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetReportSedol2(extracted);
        }
        offset += 11;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetReportBalAdd(extracted);
        }
        offset += 0;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetFiller156(extracted);
        }
        offset += 15;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetReportBalUpd(extracted);
        }
        offset += 0;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            SetFiller157(extracted);
        }
        offset += 17;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetReportTransAdd(extracted);
        }
        offset += 0;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller158(extracted);
        }
        offset += 19;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetReportTransUpd(extracted);
        }
        offset += 0;
        if (offset + 21 <= data.Length)
        {
            string extracted = data.Substring(offset, 21).Trim();
            SetFiller159(extracted);
        }
        offset += 21;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetReportBalDel(extracted);
        }
        offset += 0;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            SetFiller160(extracted);
        }
        offset += 17;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportDetailLine2AsString();
    }
    // Set<>String Override function
    public void SetReportDetailLine2(string value)
    {
        SetReportDetailLine2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetReportFund2()
    {
        return _ReportFund2;
    }
    
    // Standard Setter
    public void SetReportFund2(string value)
    {
        _ReportFund2 = value;
    }
    
    // Get<>AsString()
    public string GetReportFund2AsString()
    {
        return _ReportFund2.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetReportFund2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportFund2 = value;
    }
    
    // Standard Getter
    public string GetReportSedol2()
    {
        return _ReportSedol2;
    }
    
    // Standard Setter
    public void SetReportSedol2(string value)
    {
        _ReportSedol2 = value;
    }
    
    // Get<>AsString()
    public string GetReportSedol2AsString()
    {
        return _ReportSedol2.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetReportSedol2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportSedol2 = value;
    }
    
    // Standard Getter
    public string GetReportBalAdd()
    {
        return _ReportBalAdd;
    }
    
    // Standard Setter
    public void SetReportBalAdd(string value)
    {
        _ReportBalAdd = value;
    }
    
    // Get<>AsString()
    public string GetReportBalAddAsString()
    {
        return _ReportBalAdd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetReportBalAddAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportBalAdd = value;
    }
    
    // Standard Getter
    public string GetFiller156()
    {
        return _Filler156;
    }
    
    // Standard Setter
    public void SetFiller156(string value)
    {
        _Filler156 = value;
    }
    
    // Get<>AsString()
    public string GetFiller156AsString()
    {
        return _Filler156.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetFiller156AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler156 = value;
    }
    
    // Standard Getter
    public string GetReportBalUpd()
    {
        return _ReportBalUpd;
    }
    
    // Standard Setter
    public void SetReportBalUpd(string value)
    {
        _ReportBalUpd = value;
    }
    
    // Get<>AsString()
    public string GetReportBalUpdAsString()
    {
        return _ReportBalUpd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetReportBalUpdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportBalUpd = value;
    }
    
    // Standard Getter
    public string GetFiller157()
    {
        return _Filler157;
    }
    
    // Standard Setter
    public void SetFiller157(string value)
    {
        _Filler157 = value;
    }
    
    // Get<>AsString()
    public string GetFiller157AsString()
    {
        return _Filler157.PadRight(17);
    }
    
    // Set<>AsString()
    public void SetFiller157AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler157 = value;
    }
    
    // Standard Getter
    public string GetReportTransAdd()
    {
        return _ReportTransAdd;
    }
    
    // Standard Setter
    public void SetReportTransAdd(string value)
    {
        _ReportTransAdd = value;
    }
    
    // Get<>AsString()
    public string GetReportTransAddAsString()
    {
        return _ReportTransAdd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetReportTransAddAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportTransAdd = value;
    }
    
    // Standard Getter
    public string GetFiller158()
    {
        return _Filler158;
    }
    
    // Standard Setter
    public void SetFiller158(string value)
    {
        _Filler158 = value;
    }
    
    // Get<>AsString()
    public string GetFiller158AsString()
    {
        return _Filler158.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller158AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler158 = value;
    }
    
    // Standard Getter
    public string GetReportTransUpd()
    {
        return _ReportTransUpd;
    }
    
    // Standard Setter
    public void SetReportTransUpd(string value)
    {
        _ReportTransUpd = value;
    }
    
    // Get<>AsString()
    public string GetReportTransUpdAsString()
    {
        return _ReportTransUpd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetReportTransUpdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportTransUpd = value;
    }
    
    // Standard Getter
    public string GetFiller159()
    {
        return _Filler159;
    }
    
    // Standard Setter
    public void SetFiller159(string value)
    {
        _Filler159 = value;
    }
    
    // Get<>AsString()
    public string GetFiller159AsString()
    {
        return _Filler159.PadRight(21);
    }
    
    // Set<>AsString()
    public void SetFiller159AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler159 = value;
    }
    
    // Standard Getter
    public string GetReportBalDel()
    {
        return _ReportBalDel;
    }
    
    // Standard Setter
    public void SetReportBalDel(string value)
    {
        _ReportBalDel = value;
    }
    
    // Get<>AsString()
    public string GetReportBalDelAsString()
    {
        return _ReportBalDel.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetReportBalDelAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportBalDel = value;
    }
    
    // Standard Getter
    public string GetFiller160()
    {
        return _Filler160;
    }
    
    // Standard Setter
    public void SetFiller160(string value)
    {
        _Filler160 = value;
    }
    
    // Get<>AsString()
    public string GetFiller160AsString()
    {
        return _Filler160.PadRight(17);
    }
    
    // Set<>AsString()
    public void SetFiller160AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler160 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}