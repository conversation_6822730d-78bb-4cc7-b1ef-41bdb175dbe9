using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdateDTO
{// DTO class representing Ws2MonthTable Data Structure

public class Ws2MonthTable
{
    private static int _size = 26;
    // [DEBUG] Class: Ws2MonthTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Ws21, is_external=, is_static_class=False, static_prefix=
    private string ws21 ="312831303130313130313031";
    
    
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private Filler1 filler1 = new Filler1();
    
    
    
    
    
    // Serialization methods
    public string GetWs2MonthTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(ws21.PadRight(24));
        result.Append(filler1.GetFiller1AsString());
        
        return result.ToString();
    }
    
    public void SetWs2MonthTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetWs21(extracted);
        }
        offset += 24;
        if (offset + 2 <= data.Length)
        {
            filler1.SetFiller1AsString(data.Substring(offset, 2));
        }
        else
        {
            filler1.SetFiller1AsString(data.Substring(offset));
        }
        offset += 2;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWs2MonthTableAsString();
    }
    // Set<>String Override function
    public void SetWs2MonthTable(string value)
    {
        SetWs2MonthTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWs21()
    {
        return ws21;
    }
    
    // Standard Setter
    public void SetWs21(string value)
    {
        ws21 = value;
    }
    
    // Get<>AsString()
    public string GetWs21AsString()
    {
        return ws21.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetWs21AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        ws21 = value;
    }
    
    // Standard Getter
    public Filler1 GetFiller1()
    {
        return filler1;
    }
    
    // Standard Setter
    public void SetFiller1(Filler1 value)
    {
        filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return filler1 != null ? filler1.GetFiller1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (filler1 == null)
        {
            filler1 = new Filler1();
        }
        filler1.SetFiller1AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller1(string value)
    {
        filler1.SetFiller1AsString(value);
    }
    // Nested Class: Filler1
    public class Filler1
    {
        private static int _size = 2;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Ws2DaysInMonth, is_external=, is_static_class=False, static_prefix=
        private int[] ws2DaysInMonth = new int[12];
        
        
        
        
    public Filler1() {}
    
    public Filler1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        for (int i = 0; i < 12; i++)
        {
            string value = data.Substring(offset, 2);
            ws2DaysInMonth[i] = int.Parse(value.Trim());
            offset += 2;
        }
        
    }
    
    // Serialization methods
    public string GetFiller1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 12; i++)
        {
            result.Append(ws2DaysInMonth[i].ToString().PadLeft(2, '0'));
        }
        
        return result.ToString();
    }
    
    public void SetFiller1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 12; i++)
        {
            if (offset + 2 > data.Length) break;
            string val = data.Substring(offset, 2);
            
            int parsedInt;
            if (int.TryParse(val.Trim(), out parsedInt)) ws2DaysInMonth[i] = parsedInt;
            offset += 2;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for Ws2DaysInMonth
    public int GetWs2DaysInMonthAt(int index)
    {
        return ws2DaysInMonth[index];
    }
    
    public void SetWs2DaysInMonthAt(int index, int value)
    {
        ws2DaysInMonth[index] = value;
    }
    
    public string GetWs2DaysInMonthAsStringAt(int index)
    {
        return ws2DaysInMonth[index].ToString().PadLeft(2, '0');
    }
    
    public void SetWs2DaysInMonthAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        int parsed; if (int.TryParse(value.Trim(), out parsed)) ws2DaysInMonth[index] = parsed;
    }
    
    // Flattened accessors (index 0)
    public int GetWs2DaysInMonth()
    {
        return ws2DaysInMonth != null && ws2DaysInMonth.Length > 0
        ? ws2DaysInMonth[0]
        : default(int);
    }
    
    public void SetWs2DaysInMonth(int value)
    {
        if (ws2DaysInMonth == null || ws2DaysInMonth.Length == 0)
        ws2DaysInMonth = new int[1];
        ws2DaysInMonth[0] = value;
    }
    
    public string GetWs2DaysInMonthAsString()
    {
        return ws2DaysInMonth != null && ws2DaysInMonth.Length > 0
        ? ws2DaysInMonth[0].ToString()
        : string.Empty;
    }
    
    public void SetWs2DaysInMonthAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (ws2DaysInMonth == null || ws2DaysInMonth.Length == 0)
        ws2DaysInMonth = new int[1];
        
        int parsed; if (int.TryParse(value.Trim(), out parsed)) ws2DaysInMonth[0] = parsed;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
