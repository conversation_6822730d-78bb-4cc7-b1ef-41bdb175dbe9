using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing WwaWorkareas Data Structure

public class WwaWorkareas
{
    private static int _size = 650;
    // [DEBUG] Class: WwaWorkareas, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler108, is_external=, is_static_class=False, static_prefix=
    private string _Filler108 ="WWA WORKAREAS===";
    
    
    
    
    // [DEBUG] Field: WwaBreak, is_external=, is_static_class=False, static_prefix=
    private int _WwaBreak =0;
    
    
    
    
    // [DEBUG] Field: WwaFund, is_external=, is_static_class=False, static_prefix=
    private WwaFund _WwaFund = new WwaFund();
    
    
    
    
    // [DEBUG] Field: WwaFundOut, is_external=, is_static_class=False, static_prefix=
    private WwaFundOut _WwaFundOut = new WwaFundOut();
    
    
    
    
    // [DEBUG] Field: WwaPeriod, is_external=, is_static_class=False, static_prefix=
    private WwaPeriod _WwaPeriod = new WwaPeriod();
    
    
    
    
    // [DEBUG] Field: WwaPageNumber, is_external=, is_static_class=False, static_prefix=
    private int _WwaPageNumber =0;
    
    
    
    
    // [DEBUG] Field: WwaDate, is_external=, is_static_class=False, static_prefix=
    private WwaDate _WwaDate = new WwaDate();
    
    
    
    
    // [DEBUG] Field: WwaDate2, is_external=, is_static_class=False, static_prefix=
    private WwaDate2 _WwaDate2 = new WwaDate2();
    
    
    
    
    // [DEBUG] Field: WwaDate3, is_external=, is_static_class=False, static_prefix=
    private WwaDate3 _WwaDate3 = new WwaDate3();
    
    
    
    
    // [DEBUG] Field: WwaField, is_external=, is_static_class=False, static_prefix=
    private WwaField _WwaField = new WwaField();
    
    
    
    
    // [DEBUG] Field: WwaFieldOut, is_external=, is_static_class=False, static_prefix=
    private WwaFieldOut _WwaFieldOut = new WwaFieldOut();
    
    
    
    
    // [DEBUG] Field: Filler113, is_external=, is_static_class=False, static_prefix=
    private Filler113 _Filler113 = new Filler113();
    
    
    
    
    // [DEBUG] Field: Filler114, is_external=, is_static_class=False, static_prefix=
    private Filler114 _Filler114 = new Filler114();
    
    
    
    
    // [DEBUG] Field: WwaSedolIn, is_external=, is_static_class=False, static_prefix=
    private WwaSedolIn _WwaSedolIn = new WwaSedolIn();
    
    
    
    
    // [DEBUG] Field: WwaSedolOut, is_external=, is_static_class=False, static_prefix=
    private WwaSedolOut _WwaSedolOut = new WwaSedolOut();
    
    
    
    
    // [DEBUG] Field: WwaSecurityType, is_external=, is_static_class=False, static_prefix=
    private string _WwaSecurityType ="";
    
    
    
    
    // [DEBUG] Field: WwaIssuersName, is_external=, is_static_class=False, static_prefix=
    private WwaIssuersName _WwaIssuersName = new WwaIssuersName();
    
    
    
    
    // [DEBUG] Field: WwaStockDescription, is_external=, is_static_class=False, static_prefix=
    private WwaStockDescription _WwaStockDescription = new WwaStockDescription();
    
    
    
    
    // [DEBUG] Field: WwaLastTrancheContractNo, is_external=, is_static_class=False, static_prefix=
    private string _WwaLastTrancheContractNo ="";
    
    
    
    
    // [DEBUG] Field: WwaText, is_external=, is_static_class=False, static_prefix=
    private string _WwaText =" ";
    
    
    
    
    // [DEBUG] Field: WwaTranchesToBePrinted, is_external=, is_static_class=False, static_prefix=
    private int _WwaTranchesToBePrinted =0;
    
    
    
    
    // [DEBUG] Field: WwaProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WwaProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WwaGroup, is_external=, is_static_class=False, static_prefix=
    private WwaGroup _WwaGroup = new WwaGroup();
    
    
    
    
    // [DEBUG] Field: WwaCountry, is_external=, is_static_class=False, static_prefix=
    private string _WwaCountry ="";
    
    
    
    
    // [DEBUG] Field: WwaCal, is_external=, is_static_class=False, static_prefix=
    private string _WwaCal ="\0";
    
    
    
    
    // [DEBUG] Field: WwaType, is_external=, is_static_class=False, static_prefix=
    private string _WwaType ="";
    
    
    
    
    // [DEBUG] Field: WwaStorePrint, is_external=, is_static_class=False, static_prefix=
    private WwaStorePrint _WwaStorePrint = new WwaStorePrint();
    
    
    
    
    // [DEBUG] Field: WwaTrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _WwaTrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: WwaRecordType, is_external=, is_static_class=False, static_prefix=
    private string _WwaRecordType ="\0";
    
    
    // 88-level condition checks for WwaRecordType
    public bool IsEndOfProcessing()
    {
        if (this._WwaRecordType == "HIGH-VALUES") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WwaLineTotal, is_external=, is_static_class=False, static_prefix=
    private int _WwaLineTotal =99;
    
    
    
    
    // [DEBUG] Field: WwaNoOfLines, is_external=, is_static_class=False, static_prefix=
    private int _WwaNoOfLines =0;
    
    
    
    
    // [DEBUG] Field: WwaLinesLeft, is_external=, is_static_class=False, static_prefix=
    private int _WwaLinesLeft =0;
    
    
    
    
    // [DEBUG] Field: WwaSedolLineCount, is_external=, is_static_class=False, static_prefix=
    private int _WwaSedolLineCount =0;
    
    
    
    
    // [DEBUG] Field: WwaTrancheLineCount, is_external=, is_static_class=False, static_prefix=
    private int _WwaTrancheLineCount =0;
    
    
    
    
    // [DEBUG] Field: WwaShift, is_external=, is_static_class=False, static_prefix=
    private string _WwaShift ="";
    
    
    
    
    // [DEBUG] Field: WwaShift2, is_external=, is_static_class=False, static_prefix=
    private WwaShift2 _WwaShift2 = new WwaShift2();
    
    
    
    
    // [DEBUG] Field: WwaPrintFlag, is_external=, is_static_class=False, static_prefix=
    private string _WwaPrintFlag ="";
    
    
    
    
    // [DEBUG] Field: WwaParmArea, is_external=, is_static_class=False, static_prefix=
    private WwaParmArea _WwaParmArea = new WwaParmArea();
    
    
    
    
    // [DEBUG] Field: WwaParmAreaR, is_external=, is_static_class=False, static_prefix=
    private WwaParmAreaR _WwaParmAreaR = new WwaParmAreaR();
    
    
    
    
    // [DEBUG] Field: WwaHoldingText, is_external=, is_static_class=False, static_prefix=
    private string _WwaHoldingText ="";
    
    
    
    
    
    // Serialization methods
    public string GetWwaWorkareasAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler108.PadRight(16));
        result.Append(_WwaBreak.ToString().PadLeft(1, '0'));
        result.Append(_WwaFund.GetWwaFundAsString());
        result.Append(_WwaFundOut.GetWwaFundOutAsString());
        result.Append(_WwaPeriod.GetWwaPeriodAsString());
        result.Append(_WwaPageNumber.ToString().PadLeft(5, '0'));
        result.Append(_WwaDate.GetWwaDateAsString());
        result.Append(_WwaDate2.GetWwaDate2AsString());
        result.Append(_WwaDate3.GetWwaDate3AsString());
        result.Append(_WwaField.GetWwaFieldAsString());
        result.Append(_WwaFieldOut.GetWwaFieldOutAsString());
        result.Append(_Filler113.GetFiller113AsString());
        result.Append(_Filler114.GetFiller114AsString());
        result.Append(_WwaSedolIn.GetWwaSedolInAsString());
        result.Append(_WwaSedolOut.GetWwaSedolOutAsString());
        result.Append(_WwaSecurityType.PadRight(1));
        result.Append(_WwaIssuersName.GetWwaIssuersNameAsString());
        result.Append(_WwaStockDescription.GetWwaStockDescriptionAsString());
        result.Append(_WwaLastTrancheContractNo.PadRight(10));
        result.Append(_WwaText.PadRight(20));
        result.Append(_WwaTranchesToBePrinted.ToString().PadLeft(3, '0'));
        result.Append(_WwaProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WwaGroup.GetWwaGroupAsString());
        result.Append(_WwaCountry.PadRight(3));
        result.Append(_WwaCal.PadRight(4));
        result.Append(_WwaType.PadRight(1));
        result.Append(_WwaStorePrint.GetWwaStorePrintAsString());
        result.Append(_WwaTrancheContractNumber.PadRight(10));
        result.Append(_WwaRecordType.PadRight(1));
        result.Append(_WwaLineTotal.ToString().PadLeft(3, '0'));
        result.Append(_WwaNoOfLines.ToString().PadLeft(1, '0'));
        result.Append(_WwaLinesLeft.ToString().PadLeft(3, '0'));
        result.Append(_WwaSedolLineCount.ToString().PadLeft(3, '0'));
        result.Append(_WwaTrancheLineCount.ToString().PadLeft(3, '0'));
        result.Append(_WwaShift.PadRight(40));
        result.Append(_WwaShift2.GetWwaShift2AsString());
        result.Append(_WwaPrintFlag.PadRight(1));
        result.Append(_WwaParmArea.GetWwaParmAreaAsString());
        result.Append(_WwaParmAreaR.GetWwaParmAreaRAsString());
        result.Append(_WwaHoldingText.PadRight(18));
        
        return result.ToString();
    }
    
    public void SetWwaWorkareasAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller108(extracted);
        }
        offset += 16;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaBreak(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            _WwaFund.SetWwaFundAsString(data.Substring(offset, 4));
        }
        else
        {
            _WwaFund.SetWwaFundAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            _WwaFundOut.SetWwaFundOutAsString(data.Substring(offset, 6));
        }
        else
        {
            _WwaFundOut.SetWwaFundOutAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 17 <= data.Length)
        {
            _WwaPeriod.SetWwaPeriodAsString(data.Substring(offset, 17));
        }
        else
        {
            _WwaPeriod.SetWwaPeriodAsString(data.Substring(offset));
        }
        offset += 17;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaPageNumber(parsedInt);
        }
        offset += 5;
        if (offset + 6 <= data.Length)
        {
            _WwaDate.SetWwaDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WwaDate.SetWwaDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 7 <= data.Length)
        {
            _WwaDate2.SetWwaDate2AsString(data.Substring(offset, 7));
        }
        else
        {
            _WwaDate2.SetWwaDate2AsString(data.Substring(offset));
        }
        offset += 7;
        if (offset + 5 <= data.Length)
        {
            _WwaDate3.SetWwaDate3AsString(data.Substring(offset, 5));
        }
        else
        {
            _WwaDate3.SetWwaDate3AsString(data.Substring(offset));
        }
        offset += 5;
        if (offset + 15 <= data.Length)
        {
            _WwaField.SetWwaFieldAsString(data.Substring(offset, 15));
        }
        else
        {
            _WwaField.SetWwaFieldAsString(data.Substring(offset));
        }
        offset += 15;
        if (offset + 16 <= data.Length)
        {
            _WwaFieldOut.SetWwaFieldOutAsString(data.Substring(offset, 16));
        }
        else
        {
            _WwaFieldOut.SetWwaFieldOutAsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 1 <= data.Length)
        {
            _Filler113.SetFiller113AsString(data.Substring(offset, 1));
        }
        else
        {
            _Filler113.SetFiller113AsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 19 <= data.Length)
        {
            _Filler114.SetFiller114AsString(data.Substring(offset, 19));
        }
        else
        {
            _Filler114.SetFiller114AsString(data.Substring(offset));
        }
        offset += 19;
        if (offset + 7 <= data.Length)
        {
            _WwaSedolIn.SetWwaSedolInAsString(data.Substring(offset, 7));
        }
        else
        {
            _WwaSedolIn.SetWwaSedolInAsString(data.Substring(offset));
        }
        offset += 7;
        if (offset + 9 <= data.Length)
        {
            _WwaSedolOut.SetWwaSedolOutAsString(data.Substring(offset, 9));
        }
        else
        {
            _WwaSedolOut.SetWwaSedolOutAsString(data.Substring(offset));
        }
        offset += 9;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWwaSecurityType(extracted);
        }
        offset += 1;
        if (offset + 35 <= data.Length)
        {
            _WwaIssuersName.SetWwaIssuersNameAsString(data.Substring(offset, 35));
        }
        else
        {
            _WwaIssuersName.SetWwaIssuersNameAsString(data.Substring(offset));
        }
        offset += 35;
        if (offset + 40 <= data.Length)
        {
            _WwaStockDescription.SetWwaStockDescriptionAsString(data.Substring(offset, 40));
        }
        else
        {
            _WwaStockDescription.SetWwaStockDescriptionAsString(data.Substring(offset));
        }
        offset += 40;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetWwaLastTrancheContractNo(extracted);
        }
        offset += 10;
        if (offset + 20 <= data.Length)
        {
            string extracted = data.Substring(offset, 20).Trim();
            SetWwaText(extracted);
        }
        offset += 20;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaTranchesToBePrinted(parsedInt);
        }
        offset += 3;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWwaProfitLoss(parsedDec);
        }
        offset += 11;
        if (offset + 3 <= data.Length)
        {
            _WwaGroup.SetWwaGroupAsString(data.Substring(offset, 3));
        }
        else
        {
            _WwaGroup.SetWwaGroupAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWwaCountry(extracted);
        }
        offset += 3;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWwaCal(extracted);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWwaType(extracted);
        }
        offset += 1;
        if (offset + 180 <= data.Length)
        {
            _WwaStorePrint.SetWwaStorePrintAsString(data.Substring(offset, 180));
        }
        else
        {
            _WwaStorePrint.SetWwaStorePrintAsString(data.Substring(offset));
        }
        offset += 180;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetWwaTrancheContractNumber(extracted);
        }
        offset += 10;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWwaRecordType(extracted);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaLineTotal(parsedInt);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaNoOfLines(parsedInt);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaLinesLeft(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaSedolLineCount(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWwaTrancheLineCount(parsedInt);
        }
        offset += 3;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWwaShift(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            _WwaShift2.SetWwaShift2AsString(data.Substring(offset, 40));
        }
        else
        {
            _WwaShift2.SetWwaShift2AsString(data.Substring(offset));
        }
        offset += 40;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWwaPrintFlag(extracted);
        }
        offset += 1;
        if (offset + 81 <= data.Length)
        {
            _WwaParmArea.SetWwaParmAreaAsString(data.Substring(offset, 81));
        }
        else
        {
            _WwaParmArea.SetWwaParmAreaAsString(data.Substring(offset));
        }
        offset += 81;
        if (offset + 1 <= data.Length)
        {
            _WwaParmAreaR.SetWwaParmAreaRAsString(data.Substring(offset, 1));
        }
        else
        {
            _WwaParmAreaR.SetWwaParmAreaRAsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetWwaHoldingText(extracted);
        }
        offset += 18;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWwaWorkareasAsString();
    }
    // Set<>String Override function
    public void SetWwaWorkareas(string value)
    {
        SetWwaWorkareasAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller108()
    {
        return _Filler108;
    }
    
    // Standard Setter
    public void SetFiller108(string value)
    {
        _Filler108 = value;
    }
    
    // Get<>AsString()
    public string GetFiller108AsString()
    {
        return _Filler108.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller108AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler108 = value;
    }
    
    // Standard Getter
    public int GetWwaBreak()
    {
        return _WwaBreak;
    }
    
    // Standard Setter
    public void SetWwaBreak(int value)
    {
        _WwaBreak = value;
    }
    
    // Get<>AsString()
    public string GetWwaBreakAsString()
    {
        return _WwaBreak.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWwaBreakAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaBreak = parsed;
    }
    
    // Standard Getter
    public WwaFund GetWwaFund()
    {
        return _WwaFund;
    }
    
    // Standard Setter
    public void SetWwaFund(WwaFund value)
    {
        _WwaFund = value;
    }
    
    // Get<>AsString()
    public string GetWwaFundAsString()
    {
        return _WwaFund != null ? _WwaFund.GetWwaFundAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaFund == null)
        {
            _WwaFund = new WwaFund();
        }
        _WwaFund.SetWwaFundAsString(value);
    }
    
    // Standard Getter
    public WwaFundOut GetWwaFundOut()
    {
        return _WwaFundOut;
    }
    
    // Standard Setter
    public void SetWwaFundOut(WwaFundOut value)
    {
        _WwaFundOut = value;
    }
    
    // Get<>AsString()
    public string GetWwaFundOutAsString()
    {
        return _WwaFundOut != null ? _WwaFundOut.GetWwaFundOutAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaFundOutAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaFundOut == null)
        {
            _WwaFundOut = new WwaFundOut();
        }
        _WwaFundOut.SetWwaFundOutAsString(value);
    }
    
    // Standard Getter
    public WwaPeriod GetWwaPeriod()
    {
        return _WwaPeriod;
    }
    
    // Standard Setter
    public void SetWwaPeriod(WwaPeriod value)
    {
        _WwaPeriod = value;
    }
    
    // Get<>AsString()
    public string GetWwaPeriodAsString()
    {
        return _WwaPeriod != null ? _WwaPeriod.GetWwaPeriodAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaPeriodAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaPeriod == null)
        {
            _WwaPeriod = new WwaPeriod();
        }
        _WwaPeriod.SetWwaPeriodAsString(value);
    }
    
    // Standard Getter
    public int GetWwaPageNumber()
    {
        return _WwaPageNumber;
    }
    
    // Standard Setter
    public void SetWwaPageNumber(int value)
    {
        _WwaPageNumber = value;
    }
    
    // Get<>AsString()
    public string GetWwaPageNumberAsString()
    {
        return _WwaPageNumber.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWwaPageNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaPageNumber = parsed;
    }
    
    // Standard Getter
    public WwaDate GetWwaDate()
    {
        return _WwaDate;
    }
    
    // Standard Setter
    public void SetWwaDate(WwaDate value)
    {
        _WwaDate = value;
    }
    
    // Get<>AsString()
    public string GetWwaDateAsString()
    {
        return _WwaDate != null ? _WwaDate.GetWwaDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaDate == null)
        {
            _WwaDate = new WwaDate();
        }
        _WwaDate.SetWwaDateAsString(value);
    }
    
    // Standard Getter
    public WwaDate2 GetWwaDate2()
    {
        return _WwaDate2;
    }
    
    // Standard Setter
    public void SetWwaDate2(WwaDate2 value)
    {
        _WwaDate2 = value;
    }
    
    // Get<>AsString()
    public string GetWwaDate2AsString()
    {
        return _WwaDate2 != null ? _WwaDate2.GetWwaDate2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaDate2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaDate2 == null)
        {
            _WwaDate2 = new WwaDate2();
        }
        _WwaDate2.SetWwaDate2AsString(value);
    }
    
    // Standard Getter
    public WwaDate3 GetWwaDate3()
    {
        return _WwaDate3;
    }
    
    // Standard Setter
    public void SetWwaDate3(WwaDate3 value)
    {
        _WwaDate3 = value;
    }
    
    // Get<>AsString()
    public string GetWwaDate3AsString()
    {
        return _WwaDate3 != null ? _WwaDate3.GetWwaDate3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaDate3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaDate3 == null)
        {
            _WwaDate3 = new WwaDate3();
        }
        _WwaDate3.SetWwaDate3AsString(value);
    }
    
    // Standard Getter
    public WwaField GetWwaField()
    {
        return _WwaField;
    }
    
    // Standard Setter
    public void SetWwaField(WwaField value)
    {
        _WwaField = value;
    }
    
    // Get<>AsString()
    public string GetWwaFieldAsString()
    {
        return _WwaField != null ? _WwaField.GetWwaFieldAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaFieldAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaField == null)
        {
            _WwaField = new WwaField();
        }
        _WwaField.SetWwaFieldAsString(value);
    }
    
    // Standard Getter
    public WwaFieldOut GetWwaFieldOut()
    {
        return _WwaFieldOut;
    }
    
    // Standard Setter
    public void SetWwaFieldOut(WwaFieldOut value)
    {
        _WwaFieldOut = value;
    }
    
    // Get<>AsString()
    public string GetWwaFieldOutAsString()
    {
        return _WwaFieldOut != null ? _WwaFieldOut.GetWwaFieldOutAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaFieldOutAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaFieldOut == null)
        {
            _WwaFieldOut = new WwaFieldOut();
        }
        _WwaFieldOut.SetWwaFieldOutAsString(value);
    }
    
    // Standard Getter
    public Filler113 GetFiller113()
    {
        return _Filler113;
    }
    
    // Standard Setter
    public void SetFiller113(Filler113 value)
    {
        _Filler113 = value;
    }
    
    // Get<>AsString()
    public string GetFiller113AsString()
    {
        return _Filler113 != null ? _Filler113.GetFiller113AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller113AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler113 == null)
        {
            _Filler113 = new Filler113();
        }
        _Filler113.SetFiller113AsString(value);
    }
    
    // Standard Getter
    public Filler114 GetFiller114()
    {
        return _Filler114;
    }
    
    // Standard Setter
    public void SetFiller114(Filler114 value)
    {
        _Filler114 = value;
    }
    
    // Get<>AsString()
    public string GetFiller114AsString()
    {
        return _Filler114 != null ? _Filler114.GetFiller114AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller114AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler114 == null)
        {
            _Filler114 = new Filler114();
        }
        _Filler114.SetFiller114AsString(value);
    }
    
    // Standard Getter
    public WwaSedolIn GetWwaSedolIn()
    {
        return _WwaSedolIn;
    }
    
    // Standard Setter
    public void SetWwaSedolIn(WwaSedolIn value)
    {
        _WwaSedolIn = value;
    }
    
    // Get<>AsString()
    public string GetWwaSedolInAsString()
    {
        return _WwaSedolIn != null ? _WwaSedolIn.GetWwaSedolInAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaSedolInAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaSedolIn == null)
        {
            _WwaSedolIn = new WwaSedolIn();
        }
        _WwaSedolIn.SetWwaSedolInAsString(value);
    }
    
    // Standard Getter
    public WwaSedolOut GetWwaSedolOut()
    {
        return _WwaSedolOut;
    }
    
    // Standard Setter
    public void SetWwaSedolOut(WwaSedolOut value)
    {
        _WwaSedolOut = value;
    }
    
    // Get<>AsString()
    public string GetWwaSedolOutAsString()
    {
        return _WwaSedolOut != null ? _WwaSedolOut.GetWwaSedolOutAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaSedolOutAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaSedolOut == null)
        {
            _WwaSedolOut = new WwaSedolOut();
        }
        _WwaSedolOut.SetWwaSedolOutAsString(value);
    }
    
    // Standard Getter
    public string GetWwaSecurityType()
    {
        return _WwaSecurityType;
    }
    
    // Standard Setter
    public void SetWwaSecurityType(string value)
    {
        _WwaSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetWwaSecurityTypeAsString()
    {
        return _WwaSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWwaSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaSecurityType = value;
    }
    
    // Standard Getter
    public WwaIssuersName GetWwaIssuersName()
    {
        return _WwaIssuersName;
    }
    
    // Standard Setter
    public void SetWwaIssuersName(WwaIssuersName value)
    {
        _WwaIssuersName = value;
    }
    
    // Get<>AsString()
    public string GetWwaIssuersNameAsString()
    {
        return _WwaIssuersName != null ? _WwaIssuersName.GetWwaIssuersNameAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaIssuersNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaIssuersName == null)
        {
            _WwaIssuersName = new WwaIssuersName();
        }
        _WwaIssuersName.SetWwaIssuersNameAsString(value);
    }
    
    // Standard Getter
    public WwaStockDescription GetWwaStockDescription()
    {
        return _WwaStockDescription;
    }
    
    // Standard Setter
    public void SetWwaStockDescription(WwaStockDescription value)
    {
        _WwaStockDescription = value;
    }
    
    // Get<>AsString()
    public string GetWwaStockDescriptionAsString()
    {
        return _WwaStockDescription != null ? _WwaStockDescription.GetWwaStockDescriptionAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaStockDescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaStockDescription == null)
        {
            _WwaStockDescription = new WwaStockDescription();
        }
        _WwaStockDescription.SetWwaStockDescriptionAsString(value);
    }
    
    // Standard Getter
    public string GetWwaLastTrancheContractNo()
    {
        return _WwaLastTrancheContractNo;
    }
    
    // Standard Setter
    public void SetWwaLastTrancheContractNo(string value)
    {
        _WwaLastTrancheContractNo = value;
    }
    
    // Get<>AsString()
    public string GetWwaLastTrancheContractNoAsString()
    {
        return _WwaLastTrancheContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetWwaLastTrancheContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaLastTrancheContractNo = value;
    }
    
    // Standard Getter
    public string GetWwaText()
    {
        return _WwaText;
    }
    
    // Standard Setter
    public void SetWwaText(string value)
    {
        _WwaText = value;
    }
    
    // Get<>AsString()
    public string GetWwaTextAsString()
    {
        return _WwaText.PadRight(20);
    }
    
    // Set<>AsString()
    public void SetWwaTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaText = value;
    }
    
    // Standard Getter
    public int GetWwaTranchesToBePrinted()
    {
        return _WwaTranchesToBePrinted;
    }
    
    // Standard Setter
    public void SetWwaTranchesToBePrinted(int value)
    {
        _WwaTranchesToBePrinted = value;
    }
    
    // Get<>AsString()
    public string GetWwaTranchesToBePrintedAsString()
    {
        return _WwaTranchesToBePrinted.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWwaTranchesToBePrintedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaTranchesToBePrinted = parsed;
    }
    
    // Standard Getter
    public decimal GetWwaProfitLoss()
    {
        return _WwaProfitLoss;
    }
    
    // Standard Setter
    public void SetWwaProfitLoss(decimal value)
    {
        _WwaProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetWwaProfitLossAsString()
    {
        return _WwaProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWwaProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WwaProfitLoss = parsed;
    }
    
    // Standard Getter
    public WwaGroup GetWwaGroup()
    {
        return _WwaGroup;
    }
    
    // Standard Setter
    public void SetWwaGroup(WwaGroup value)
    {
        _WwaGroup = value;
    }
    
    // Get<>AsString()
    public string GetWwaGroupAsString()
    {
        return _WwaGroup != null ? _WwaGroup.GetWwaGroupAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaGroupAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaGroup == null)
        {
            _WwaGroup = new WwaGroup();
        }
        _WwaGroup.SetWwaGroupAsString(value);
    }
    
    // Standard Getter
    public string GetWwaCountry()
    {
        return _WwaCountry;
    }
    
    // Standard Setter
    public void SetWwaCountry(string value)
    {
        _WwaCountry = value;
    }
    
    // Get<>AsString()
    public string GetWwaCountryAsString()
    {
        return _WwaCountry.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWwaCountryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaCountry = value;
    }
    
    // Standard Getter
    public string GetWwaCal()
    {
        return _WwaCal;
    }
    
    // Standard Setter
    public void SetWwaCal(string value)
    {
        _WwaCal = value;
    }
    
    // Get<>AsString()
    public string GetWwaCalAsString()
    {
        return _WwaCal.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWwaCalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaCal = value;
    }
    
    // Standard Getter
    public string GetWwaType()
    {
        return _WwaType;
    }
    
    // Standard Setter
    public void SetWwaType(string value)
    {
        _WwaType = value;
    }
    
    // Get<>AsString()
    public string GetWwaTypeAsString()
    {
        return _WwaType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWwaTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaType = value;
    }
    
    // Standard Getter
    public WwaStorePrint GetWwaStorePrint()
    {
        return _WwaStorePrint;
    }
    
    // Standard Setter
    public void SetWwaStorePrint(WwaStorePrint value)
    {
        _WwaStorePrint = value;
    }
    
    // Get<>AsString()
    public string GetWwaStorePrintAsString()
    {
        return _WwaStorePrint != null ? _WwaStorePrint.GetWwaStorePrintAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaStorePrintAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaStorePrint == null)
        {
            _WwaStorePrint = new WwaStorePrint();
        }
        _WwaStorePrint.SetWwaStorePrintAsString(value);
    }
    
    // Standard Getter
    public string GetWwaTrancheContractNumber()
    {
        return _WwaTrancheContractNumber;
    }
    
    // Standard Setter
    public void SetWwaTrancheContractNumber(string value)
    {
        _WwaTrancheContractNumber = value;
    }
    
    // Get<>AsString()
    public string GetWwaTrancheContractNumberAsString()
    {
        return _WwaTrancheContractNumber.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetWwaTrancheContractNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaTrancheContractNumber = value;
    }
    
    // Standard Getter
    public string GetWwaRecordType()
    {
        return _WwaRecordType;
    }
    
    // Standard Setter
    public void SetWwaRecordType(string value)
    {
        _WwaRecordType = value;
    }
    
    // Get<>AsString()
    public string GetWwaRecordTypeAsString()
    {
        return _WwaRecordType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWwaRecordTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaRecordType = value;
    }
    
    // Standard Getter
    public int GetWwaLineTotal()
    {
        return _WwaLineTotal;
    }
    
    // Standard Setter
    public void SetWwaLineTotal(int value)
    {
        _WwaLineTotal = value;
    }
    
    // Get<>AsString()
    public string GetWwaLineTotalAsString()
    {
        return _WwaLineTotal.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWwaLineTotalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaLineTotal = parsed;
    }
    
    // Standard Getter
    public int GetWwaNoOfLines()
    {
        return _WwaNoOfLines;
    }
    
    // Standard Setter
    public void SetWwaNoOfLines(int value)
    {
        _WwaNoOfLines = value;
    }
    
    // Get<>AsString()
    public string GetWwaNoOfLinesAsString()
    {
        return _WwaNoOfLines.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWwaNoOfLinesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaNoOfLines = parsed;
    }
    
    // Standard Getter
    public int GetWwaLinesLeft()
    {
        return _WwaLinesLeft;
    }
    
    // Standard Setter
    public void SetWwaLinesLeft(int value)
    {
        _WwaLinesLeft = value;
    }
    
    // Get<>AsString()
    public string GetWwaLinesLeftAsString()
    {
        return _WwaLinesLeft.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWwaLinesLeftAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaLinesLeft = parsed;
    }
    
    // Standard Getter
    public int GetWwaSedolLineCount()
    {
        return _WwaSedolLineCount;
    }
    
    // Standard Setter
    public void SetWwaSedolLineCount(int value)
    {
        _WwaSedolLineCount = value;
    }
    
    // Get<>AsString()
    public string GetWwaSedolLineCountAsString()
    {
        return _WwaSedolLineCount.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWwaSedolLineCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaSedolLineCount = parsed;
    }
    
    // Standard Getter
    public int GetWwaTrancheLineCount()
    {
        return _WwaTrancheLineCount;
    }
    
    // Standard Setter
    public void SetWwaTrancheLineCount(int value)
    {
        _WwaTrancheLineCount = value;
    }
    
    // Get<>AsString()
    public string GetWwaTrancheLineCountAsString()
    {
        return _WwaTrancheLineCount.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWwaTrancheLineCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WwaTrancheLineCount = parsed;
    }
    
    // Standard Getter
    public string GetWwaShift()
    {
        return _WwaShift;
    }
    
    // Standard Setter
    public void SetWwaShift(string value)
    {
        _WwaShift = value;
    }
    
    // Get<>AsString()
    public string GetWwaShiftAsString()
    {
        return _WwaShift.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWwaShiftAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaShift = value;
    }
    
    // Standard Getter
    public WwaShift2 GetWwaShift2()
    {
        return _WwaShift2;
    }
    
    // Standard Setter
    public void SetWwaShift2(WwaShift2 value)
    {
        _WwaShift2 = value;
    }
    
    // Get<>AsString()
    public string GetWwaShift2AsString()
    {
        return _WwaShift2 != null ? _WwaShift2.GetWwaShift2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaShift2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaShift2 == null)
        {
            _WwaShift2 = new WwaShift2();
        }
        _WwaShift2.SetWwaShift2AsString(value);
    }
    
    // Standard Getter
    public string GetWwaPrintFlag()
    {
        return _WwaPrintFlag;
    }
    
    // Standard Setter
    public void SetWwaPrintFlag(string value)
    {
        _WwaPrintFlag = value;
    }
    
    // Get<>AsString()
    public string GetWwaPrintFlagAsString()
    {
        return _WwaPrintFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWwaPrintFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaPrintFlag = value;
    }
    
    // Standard Getter
    public WwaParmArea GetWwaParmArea()
    {
        return _WwaParmArea;
    }
    
    // Standard Setter
    public void SetWwaParmArea(WwaParmArea value)
    {
        _WwaParmArea = value;
    }
    
    // Get<>AsString()
    public string GetWwaParmAreaAsString()
    {
        return _WwaParmArea != null ? _WwaParmArea.GetWwaParmAreaAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaParmAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaParmArea == null)
        {
            _WwaParmArea = new WwaParmArea();
        }
        _WwaParmArea.SetWwaParmAreaAsString(value);
    }
    
    // Standard Getter
    public WwaParmAreaR GetWwaParmAreaR()
    {
        return _WwaParmAreaR;
    }
    
    // Standard Setter
    public void SetWwaParmAreaR(WwaParmAreaR value)
    {
        _WwaParmAreaR = value;
    }
    
    // Get<>AsString()
    public string GetWwaParmAreaRAsString()
    {
        return _WwaParmAreaR != null ? _WwaParmAreaR.GetWwaParmAreaRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWwaParmAreaRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WwaParmAreaR == null)
        {
            _WwaParmAreaR = new WwaParmAreaR();
        }
        _WwaParmAreaR.SetWwaParmAreaRAsString(value);
    }
    
    // Standard Getter
    public string GetWwaHoldingText()
    {
        return _WwaHoldingText;
    }
    
    // Standard Setter
    public void SetWwaHoldingText(string value)
    {
        _WwaHoldingText = value;
    }
    
    // Get<>AsString()
    public string GetWwaHoldingTextAsString()
    {
        return _WwaHoldingText.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetWwaHoldingTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaHoldingText = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWwaFund(string value)
    {
        _WwaFund.SetWwaFundAsString(value);
    }
    // Nested Class: WwaFund
    public class WwaFund
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WwaFundCo, is_external=, is_static_class=False, static_prefix=
        private string _WwaFundCo ="";
        
        
        
        
        // [DEBUG] Field: WwaFundAc, is_external=, is_static_class=False, static_prefix=
        private string _WwaFundAc ="";
        
        
        
        
        // [DEBUG] Field: WwaFundLk, is_external=, is_static_class=False, static_prefix=
        private string _WwaFundLk ="";
        
        
        
        
    public WwaFund() {}
    
    public WwaFund(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWwaFundCo(data.Substring(offset, 2).Trim());
        offset += 2;
        SetWwaFundAc(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWwaFundLk(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetWwaFundAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WwaFundCo.PadRight(2));
        result.Append(_WwaFundAc.PadRight(1));
        result.Append(_WwaFundLk.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWwaFundAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWwaFundCo(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWwaFundAc(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWwaFundLk(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWwaFundCo()
    {
        return _WwaFundCo;
    }
    
    // Standard Setter
    public void SetWwaFundCo(string value)
    {
        _WwaFundCo = value;
    }
    
    // Get<>AsString()
    public string GetWwaFundCoAsString()
    {
        return _WwaFundCo.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWwaFundCoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaFundCo = value;
    }
    
    // Standard Getter
    public string GetWwaFundAc()
    {
        return _WwaFundAc;
    }
    
    // Standard Setter
    public void SetWwaFundAc(string value)
    {
        _WwaFundAc = value;
    }
    
    // Get<>AsString()
    public string GetWwaFundAcAsString()
    {
        return _WwaFundAc.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWwaFundAcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaFundAc = value;
    }
    
    // Standard Getter
    public string GetWwaFundLk()
    {
        return _WwaFundLk;
    }
    
    // Standard Setter
    public void SetWwaFundLk(string value)
    {
        _WwaFundLk = value;
    }
    
    // Get<>AsString()
    public string GetWwaFundLkAsString()
    {
        return _WwaFundLk.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWwaFundLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WwaFundLk = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWwaFundOut(string value)
{
    _WwaFundOut.SetWwaFundOutAsString(value);
}
// Nested Class: WwaFundOut
public class WwaFundOut
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaFundOutCo, is_external=, is_static_class=False, static_prefix=
    private string _WwaFundOutCo ="";
    
    
    
    
    // [DEBUG] Field: Filler109, is_external=, is_static_class=False, static_prefix=
    private string _Filler109 ="/";
    
    
    
    
    // [DEBUG] Field: WwaFundOutAc, is_external=, is_static_class=False, static_prefix=
    private string _WwaFundOutAc ="";
    
    
    
    
    // [DEBUG] Field: Filler110, is_external=, is_static_class=False, static_prefix=
    private string _Filler110 ="/";
    
    
    
    
    // [DEBUG] Field: WwaFundOutLk, is_external=, is_static_class=False, static_prefix=
    private string _WwaFundOutLk ="";
    
    
    
    
public WwaFundOut() {}

public WwaFundOut(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaFundOutCo(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller109(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaFundOutAc(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller110(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaFundOutLk(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetWwaFundOutAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaFundOutCo.PadRight(2));
    result.Append(_Filler109.PadRight(1));
    result.Append(_WwaFundOutAc.PadRight(1));
    result.Append(_Filler110.PadRight(1));
    result.Append(_WwaFundOutLk.PadRight(1));
    
    return result.ToString();
}

public void SetWwaFundOutAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWwaFundOutCo(extracted);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller109(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaFundOutAc(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller110(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaFundOutLk(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaFundOutCo()
{
    return _WwaFundOutCo;
}

// Standard Setter
public void SetWwaFundOutCo(string value)
{
    _WwaFundOutCo = value;
}

// Get<>AsString()
public string GetWwaFundOutCoAsString()
{
    return _WwaFundOutCo.PadRight(2);
}

// Set<>AsString()
public void SetWwaFundOutCoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaFundOutCo = value;
}

// Standard Getter
public string GetFiller109()
{
    return _Filler109;
}

// Standard Setter
public void SetFiller109(string value)
{
    _Filler109 = value;
}

// Get<>AsString()
public string GetFiller109AsString()
{
    return _Filler109.PadRight(1);
}

// Set<>AsString()
public void SetFiller109AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler109 = value;
}

// Standard Getter
public string GetWwaFundOutAc()
{
    return _WwaFundOutAc;
}

// Standard Setter
public void SetWwaFundOutAc(string value)
{
    _WwaFundOutAc = value;
}

// Get<>AsString()
public string GetWwaFundOutAcAsString()
{
    return _WwaFundOutAc.PadRight(1);
}

// Set<>AsString()
public void SetWwaFundOutAcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaFundOutAc = value;
}

// Standard Getter
public string GetFiller110()
{
    return _Filler110;
}

// Standard Setter
public void SetFiller110(string value)
{
    _Filler110 = value;
}

// Get<>AsString()
public string GetFiller110AsString()
{
    return _Filler110.PadRight(1);
}

// Set<>AsString()
public void SetFiller110AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler110 = value;
}

// Standard Getter
public string GetWwaFundOutLk()
{
    return _WwaFundOutLk;
}

// Standard Setter
public void SetWwaFundOutLk(string value)
{
    _WwaFundOutLk = value;
}

// Get<>AsString()
public string GetWwaFundOutLkAsString()
{
    return _WwaFundOutLk.PadRight(1);
}

// Set<>AsString()
public void SetWwaFundOutLkAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaFundOutLk = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaPeriod(string value)
{
    _WwaPeriod.SetWwaPeriodAsString(value);
}
// Nested Class: WwaPeriod
public class WwaPeriod
{
    private static int _size = 17;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaPeriod1, is_external=, is_static_class=False, static_prefix=
    private WwaPeriod.WwaPeriod1 _WwaPeriod1 = new WwaPeriod.WwaPeriod1();
    
    
    
    
    // [DEBUG] Field: Filler111, is_external=, is_static_class=False, static_prefix=
    private string _Filler111 =" - ";
    
    
    
    
    // [DEBUG] Field: WwaPeriod2, is_external=, is_static_class=False, static_prefix=
    private WwaPeriod.WwaPeriod2 _WwaPeriod2 = new WwaPeriod.WwaPeriod2();
    
    
    
    
public WwaPeriod() {}

public WwaPeriod(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WwaPeriod1.SetWwaPeriod1AsString(data.Substring(offset, WwaPeriod1.GetSize()));
    offset += 7;
    SetFiller111(data.Substring(offset, 3).Trim());
    offset += 3;
    _WwaPeriod2.SetWwaPeriod2AsString(data.Substring(offset, WwaPeriod2.GetSize()));
    offset += 7;
    
}

// Serialization methods
public string GetWwaPeriodAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaPeriod1.GetWwaPeriod1AsString());
    result.Append(_Filler111.PadRight(3));
    result.Append(_WwaPeriod2.GetWwaPeriod2AsString());
    
    return result.ToString();
}

public void SetWwaPeriodAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        _WwaPeriod1.SetWwaPeriod1AsString(data.Substring(offset, 7));
    }
    else
    {
        _WwaPeriod1.SetWwaPeriod1AsString(data.Substring(offset));
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller111(extracted);
    }
    offset += 3;
    if (offset + 7 <= data.Length)
    {
        _WwaPeriod2.SetWwaPeriod2AsString(data.Substring(offset, 7));
    }
    else
    {
        _WwaPeriod2.SetWwaPeriod2AsString(data.Substring(offset));
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public WwaPeriod1 GetWwaPeriod1()
{
    return _WwaPeriod1;
}

// Standard Setter
public void SetWwaPeriod1(WwaPeriod1 value)
{
    _WwaPeriod1 = value;
}

// Get<>AsString()
public string GetWwaPeriod1AsString()
{
    return _WwaPeriod1 != null ? _WwaPeriod1.GetWwaPeriod1AsString() : "";
}

// Set<>AsString()
public void SetWwaPeriod1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WwaPeriod1 == null)
    {
        _WwaPeriod1 = new WwaPeriod1();
    }
    _WwaPeriod1.SetWwaPeriod1AsString(value);
}

// Standard Getter
public string GetFiller111()
{
    return _Filler111;
}

// Standard Setter
public void SetFiller111(string value)
{
    _Filler111 = value;
}

// Get<>AsString()
public string GetFiller111AsString()
{
    return _Filler111.PadRight(3);
}

// Set<>AsString()
public void SetFiller111AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler111 = value;
}

// Standard Getter
public WwaPeriod2 GetWwaPeriod2()
{
    return _WwaPeriod2;
}

// Standard Setter
public void SetWwaPeriod2(WwaPeriod2 value)
{
    _WwaPeriod2 = value;
}

// Get<>AsString()
public string GetWwaPeriod2AsString()
{
    return _WwaPeriod2 != null ? _WwaPeriod2.GetWwaPeriod2AsString() : "";
}

// Set<>AsString()
public void SetWwaPeriod2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WwaPeriod2 == null)
    {
        _WwaPeriod2 = new WwaPeriod2();
    }
    _WwaPeriod2.SetWwaPeriod2AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WwaPeriod1
public class WwaPeriod1
{
    private static int _size = 7;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaPeriod1Dd, is_external=, is_static_class=False, static_prefix=
    private string _WwaPeriod1Dd ="";
    
    
    
    
    // [DEBUG] Field: WwaPeriod1Mmm, is_external=, is_static_class=False, static_prefix=
    private string _WwaPeriod1Mmm ="";
    
    
    
    
    // [DEBUG] Field: WwaPeriod1Yy, is_external=, is_static_class=False, static_prefix=
    private string _WwaPeriod1Yy ="";
    
    
    
    
public WwaPeriod1() {}

public WwaPeriod1(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaPeriod1Dd(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWwaPeriod1Mmm(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWwaPeriod1Yy(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWwaPeriod1AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaPeriod1Dd.PadRight(2));
    result.Append(_WwaPeriod1Mmm.PadRight(3));
    result.Append(_WwaPeriod1Yy.PadRight(2));
    
    return result.ToString();
}

public void SetWwaPeriod1AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWwaPeriod1Dd(extracted);
    }
    offset += 2;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaPeriod1Mmm(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWwaPeriod1Yy(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaPeriod1Dd()
{
    return _WwaPeriod1Dd;
}

// Standard Setter
public void SetWwaPeriod1Dd(string value)
{
    _WwaPeriod1Dd = value;
}

// Get<>AsString()
public string GetWwaPeriod1DdAsString()
{
    return _WwaPeriod1Dd.PadRight(2);
}

// Set<>AsString()
public void SetWwaPeriod1DdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaPeriod1Dd = value;
}

// Standard Getter
public string GetWwaPeriod1Mmm()
{
    return _WwaPeriod1Mmm;
}

// Standard Setter
public void SetWwaPeriod1Mmm(string value)
{
    _WwaPeriod1Mmm = value;
}

// Get<>AsString()
public string GetWwaPeriod1MmmAsString()
{
    return _WwaPeriod1Mmm.PadRight(3);
}

// Set<>AsString()
public void SetWwaPeriod1MmmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaPeriod1Mmm = value;
}

// Standard Getter
public string GetWwaPeriod1Yy()
{
    return _WwaPeriod1Yy;
}

// Standard Setter
public void SetWwaPeriod1Yy(string value)
{
    _WwaPeriod1Yy = value;
}

// Get<>AsString()
public string GetWwaPeriod1YyAsString()
{
    return _WwaPeriod1Yy.PadRight(2);
}

// Set<>AsString()
public void SetWwaPeriod1YyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaPeriod1Yy = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WwaPeriod2
public class WwaPeriod2
{
    private static int _size = 7;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaPeriod2Dd, is_external=, is_static_class=False, static_prefix=
    private string _WwaPeriod2Dd ="";
    
    
    
    
    // [DEBUG] Field: WwaPeriod2Mmm, is_external=, is_static_class=False, static_prefix=
    private string _WwaPeriod2Mmm ="";
    
    
    
    
    // [DEBUG] Field: WwaPeriod2Yy, is_external=, is_static_class=False, static_prefix=
    private string _WwaPeriod2Yy ="";
    
    
    
    
public WwaPeriod2() {}

public WwaPeriod2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaPeriod2Dd(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWwaPeriod2Mmm(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWwaPeriod2Yy(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWwaPeriod2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaPeriod2Dd.PadRight(2));
    result.Append(_WwaPeriod2Mmm.PadRight(3));
    result.Append(_WwaPeriod2Yy.PadRight(2));
    
    return result.ToString();
}

public void SetWwaPeriod2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWwaPeriod2Dd(extracted);
    }
    offset += 2;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaPeriod2Mmm(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWwaPeriod2Yy(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaPeriod2Dd()
{
    return _WwaPeriod2Dd;
}

// Standard Setter
public void SetWwaPeriod2Dd(string value)
{
    _WwaPeriod2Dd = value;
}

// Get<>AsString()
public string GetWwaPeriod2DdAsString()
{
    return _WwaPeriod2Dd.PadRight(2);
}

// Set<>AsString()
public void SetWwaPeriod2DdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaPeriod2Dd = value;
}

// Standard Getter
public string GetWwaPeriod2Mmm()
{
    return _WwaPeriod2Mmm;
}

// Standard Setter
public void SetWwaPeriod2Mmm(string value)
{
    _WwaPeriod2Mmm = value;
}

// Get<>AsString()
public string GetWwaPeriod2MmmAsString()
{
    return _WwaPeriod2Mmm.PadRight(3);
}

// Set<>AsString()
public void SetWwaPeriod2MmmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaPeriod2Mmm = value;
}

// Standard Getter
public string GetWwaPeriod2Yy()
{
    return _WwaPeriod2Yy;
}

// Standard Setter
public void SetWwaPeriod2Yy(string value)
{
    _WwaPeriod2Yy = value;
}

// Get<>AsString()
public string GetWwaPeriod2YyAsString()
{
    return _WwaPeriod2Yy.PadRight(2);
}

// Set<>AsString()
public void SetWwaPeriod2YyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaPeriod2Yy = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWwaDate(string value)
{
    _WwaDate.SetWwaDateAsString(value);
}
// Nested Class: WwaDate
public class WwaDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WwaDateYy =0;
    
    
    
    
    // [DEBUG] Field: WwaDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WwaDateMm =0;
    
    
    
    
    // [DEBUG] Field: WwaDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WwaDateDd =0;
    
    
    
    
public WwaDate() {}

public WwaDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWwaDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWwaDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWwaDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WwaDateMm.ToString().PadLeft(2, '0'));
    result.Append(_WwaDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWwaDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWwaDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWwaDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWwaDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWwaDateYy()
{
    return _WwaDateYy;
}

// Standard Setter
public void SetWwaDateYy(int value)
{
    _WwaDateYy = value;
}

// Get<>AsString()
public string GetWwaDateYyAsString()
{
    return _WwaDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWwaDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WwaDateYy = parsed;
}

// Standard Getter
public int GetWwaDateMm()
{
    return _WwaDateMm;
}

// Standard Setter
public void SetWwaDateMm(int value)
{
    _WwaDateMm = value;
}

// Get<>AsString()
public string GetWwaDateMmAsString()
{
    return _WwaDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWwaDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WwaDateMm = parsed;
}

// Standard Getter
public int GetWwaDateDd()
{
    return _WwaDateDd;
}

// Standard Setter
public void SetWwaDateDd(int value)
{
    _WwaDateDd = value;
}

// Get<>AsString()
public string GetWwaDateDdAsString()
{
    return _WwaDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWwaDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WwaDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaDate2(string value)
{
    _WwaDate2.SetWwaDate2AsString(value);
}
// Nested Class: WwaDate2
public class WwaDate2
{
    private static int _size = 7;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaDate2Dd, is_external=, is_static_class=False, static_prefix=
    private int _WwaDate2Dd =0;
    
    
    
    
    // [DEBUG] Field: WwaDate2Mmm, is_external=, is_static_class=False, static_prefix=
    private string _WwaDate2Mmm ="";
    
    
    
    
    // [DEBUG] Field: WwaDate2Yy, is_external=, is_static_class=False, static_prefix=
    private int _WwaDate2Yy =0;
    
    
    
    
public WwaDate2() {}

public WwaDate2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaDate2Dd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWwaDate2Mmm(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWwaDate2Yy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWwaDate2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaDate2Dd.ToString().PadLeft(2, '0'));
    result.Append(_WwaDate2Mmm.PadRight(3));
    result.Append(_WwaDate2Yy.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWwaDate2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWwaDate2Dd(parsedInt);
    }
    offset += 2;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaDate2Mmm(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWwaDate2Yy(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWwaDate2Dd()
{
    return _WwaDate2Dd;
}

// Standard Setter
public void SetWwaDate2Dd(int value)
{
    _WwaDate2Dd = value;
}

// Get<>AsString()
public string GetWwaDate2DdAsString()
{
    return _WwaDate2Dd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWwaDate2DdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WwaDate2Dd = parsed;
}

// Standard Getter
public string GetWwaDate2Mmm()
{
    return _WwaDate2Mmm;
}

// Standard Setter
public void SetWwaDate2Mmm(string value)
{
    _WwaDate2Mmm = value;
}

// Get<>AsString()
public string GetWwaDate2MmmAsString()
{
    return _WwaDate2Mmm.PadRight(3);
}

// Set<>AsString()
public void SetWwaDate2MmmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaDate2Mmm = value;
}

// Standard Getter
public int GetWwaDate2Yy()
{
    return _WwaDate2Yy;
}

// Standard Setter
public void SetWwaDate2Yy(int value)
{
    _WwaDate2Yy = value;
}

// Get<>AsString()
public string GetWwaDate2YyAsString()
{
    return _WwaDate2Yy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWwaDate2YyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WwaDate2Yy = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaDate3(string value)
{
    _WwaDate3.SetWwaDate3AsString(value);
}
// Nested Class: WwaDate3
public class WwaDate3
{
    private static int _size = 5;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaDate3Mmm, is_external=, is_static_class=False, static_prefix=
    private string _WwaDate3Mmm ="";
    
    
    
    
    // [DEBUG] Field: WwaDate3Yy, is_external=, is_static_class=False, static_prefix=
    private int _WwaDate3Yy =0;
    
    
    
    
public WwaDate3() {}

public WwaDate3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaDate3Mmm(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWwaDate3Yy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWwaDate3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaDate3Mmm.PadRight(3));
    result.Append(_WwaDate3Yy.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWwaDate3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaDate3Mmm(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWwaDate3Yy(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaDate3Mmm()
{
    return _WwaDate3Mmm;
}

// Standard Setter
public void SetWwaDate3Mmm(string value)
{
    _WwaDate3Mmm = value;
}

// Get<>AsString()
public string GetWwaDate3MmmAsString()
{
    return _WwaDate3Mmm.PadRight(3);
}

// Set<>AsString()
public void SetWwaDate3MmmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaDate3Mmm = value;
}

// Standard Getter
public int GetWwaDate3Yy()
{
    return _WwaDate3Yy;
}

// Standard Setter
public void SetWwaDate3Yy(int value)
{
    _WwaDate3Yy = value;
}

// Get<>AsString()
public string GetWwaDate3YyAsString()
{
    return _WwaDate3Yy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWwaDate3YyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WwaDate3Yy = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaField(string value)
{
    _WwaField.SetWwaFieldAsString(value);
}
// Nested Class: WwaField
public class WwaField
{
    private static int _size = 15;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaField9, is_external=, is_static_class=False, static_prefix=
    private decimal _WwaField9 =0;
    
    
    
    
public WwaField() {}

public WwaField(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaField9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    
}

// Serialization methods
public string GetWwaFieldAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaField9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWwaFieldAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWwaField9(parsedDec);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWwaField9()
{
    return _WwaField9;
}

// Standard Setter
public void SetWwaField9(decimal value)
{
    _WwaField9 = value;
}

// Get<>AsString()
public string GetWwaField9AsString()
{
    return _WwaField9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWwaField9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WwaField9 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaFieldOut(string value)
{
    _WwaFieldOut.SetWwaFieldOutAsString(value);
}
// Nested Class: WwaFieldOut
public class WwaFieldOut
{
    private static int _size = 16;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaFieldOut9, is_external=, is_static_class=False, static_prefix=
    private decimal _WwaFieldOut9 =0;
    
    
    
    
    // [DEBUG] Field: Filler112, is_external=, is_static_class=False, static_prefix=
    private string _Filler112 ="";
    
    
    
    
public WwaFieldOut() {}

public WwaFieldOut(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaFieldOut9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetFiller112(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetWwaFieldOutAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaFieldOut9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler112.PadRight(1));
    
    return result.ToString();
}

public void SetWwaFieldOutAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWwaFieldOut9(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller112(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWwaFieldOut9()
{
    return _WwaFieldOut9;
}

// Standard Setter
public void SetWwaFieldOut9(decimal value)
{
    _WwaFieldOut9 = value;
}

// Get<>AsString()
public string GetWwaFieldOut9AsString()
{
    return _WwaFieldOut9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWwaFieldOut9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WwaFieldOut9 = parsed;
}

// Standard Getter
public string GetFiller112()
{
    return _Filler112;
}

// Standard Setter
public void SetFiller112(string value)
{
    _Filler112 = value;
}

// Get<>AsString()
public string GetFiller112AsString()
{
    return _Filler112.PadRight(1);
}

// Set<>AsString()
public void SetFiller112AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler112 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller113(string value)
{
    _Filler113.SetFiller113AsString(value);
}
// Nested Class: Filler113
public class Filler113
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaByteOut, is_external=, is_static_class=False, static_prefix=
    private string[] _WwaByteOut = new string[19];
    
    
    
    
public Filler113() {}

public Filler113(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 19; i++)
    {
        string value = data.Substring(offset, 1);
        _WwaByteOut[i] = value.Trim();
        offset += 1;
    }
    
}

// Serialization methods
public string GetFiller113AsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 19; i++)
    {
        result.Append(_WwaByteOut[i].PadRight(1));
    }
    
    return result.ToString();
}

public void SetFiller113AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 19; i++)
    {
        if (offset + 1 > data.Length) break;
        string val = data.Substring(offset, 1);
        
        _WwaByteOut[i] = val.Trim();
        offset += 1;
    }
}

// Getter and Setter methods

// Array Accessors for WwaByteOut
public string GetWwaByteOutAt(int index)
{
    return _WwaByteOut[index];
}

public void SetWwaByteOutAt(int index, string value)
{
    _WwaByteOut[index] = value;
}

public string GetWwaByteOutAsStringAt(int index)
{
    return _WwaByteOut[index].PadRight(1);
}

public void SetWwaByteOutAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _WwaByteOut[index] = value;
}

// Flattened accessors (index 0)
public string GetWwaByteOut()
{
    return _WwaByteOut != null && _WwaByteOut.Length > 0
    ? _WwaByteOut[0]
    : default(string);
}

public void SetWwaByteOut(string value)
{
    if (_WwaByteOut == null || _WwaByteOut.Length == 0)
    _WwaByteOut = new string[1];
    _WwaByteOut[0] = value;
}

public string GetWwaByteOutAsString()
{
    return _WwaByteOut != null && _WwaByteOut.Length > 0
    ? _WwaByteOut[0].ToString()
    : string.Empty;
}

public void SetWwaByteOutAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_WwaByteOut == null || _WwaByteOut.Length == 0)
    _WwaByteOut = new string[1];
    
    _WwaByteOut[0] = value;
}




public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller114(string value)
{
    _Filler114.SetFiller114AsString(value);
}
// Nested Class: Filler114
public class Filler114
{
    private static int _size = 19;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler115, is_external=, is_static_class=False, static_prefix=
    private string _Filler115 ="";
    
    
    
    
    // [DEBUG] Field: WwaFieldOutBracket1, is_external=, is_static_class=False, static_prefix=
    private string _WwaFieldOutBracket1 ="";
    
    
    
    
    // [DEBUG] Field: WwaFieldOutBracket2, is_external=, is_static_class=False, static_prefix=
    private string _WwaFieldOutBracket2 ="";
    
    
    
    
public Filler114() {}

public Filler114(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller115(data.Substring(offset, 17).Trim());
    offset += 17;
    SetWwaFieldOutBracket1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaFieldOutBracket2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller114AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler115.PadRight(17));
    result.Append(_WwaFieldOutBracket1.PadRight(1));
    result.Append(_WwaFieldOutBracket2.PadRight(1));
    
    return result.ToString();
}

public void SetFiller114AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        SetFiller115(extracted);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaFieldOutBracket1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaFieldOutBracket2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller115()
{
    return _Filler115;
}

// Standard Setter
public void SetFiller115(string value)
{
    _Filler115 = value;
}

// Get<>AsString()
public string GetFiller115AsString()
{
    return _Filler115.PadRight(17);
}

// Set<>AsString()
public void SetFiller115AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler115 = value;
}

// Standard Getter
public string GetWwaFieldOutBracket1()
{
    return _WwaFieldOutBracket1;
}

// Standard Setter
public void SetWwaFieldOutBracket1(string value)
{
    _WwaFieldOutBracket1 = value;
}

// Get<>AsString()
public string GetWwaFieldOutBracket1AsString()
{
    return _WwaFieldOutBracket1.PadRight(1);
}

// Set<>AsString()
public void SetWwaFieldOutBracket1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaFieldOutBracket1 = value;
}

// Standard Getter
public string GetWwaFieldOutBracket2()
{
    return _WwaFieldOutBracket2;
}

// Standard Setter
public void SetWwaFieldOutBracket2(string value)
{
    _WwaFieldOutBracket2 = value;
}

// Get<>AsString()
public string GetWwaFieldOutBracket2AsString()
{
    return _WwaFieldOutBracket2.PadRight(1);
}

// Set<>AsString()
public void SetWwaFieldOutBracket2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaFieldOutBracket2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaSedolIn(string value)
{
    _WwaSedolIn.SetWwaSedolInAsString(value);
}
// Nested Class: WwaSedolIn
public class WwaSedolIn
{
    private static int _size = 7;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaSedolIn1, is_external=, is_static_class=False, static_prefix=
    private string _WwaSedolIn1 ="";
    
    
    
    
    // [DEBUG] Field: WwaSedolIn24, is_external=, is_static_class=False, static_prefix=
    private string _WwaSedolIn24 ="";
    
    
    
    
    // [DEBUG] Field: WwaSedolIn57, is_external=, is_static_class=False, static_prefix=
    private string _WwaSedolIn57 ="";
    
    
    
    
public WwaSedolIn() {}

public WwaSedolIn(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaSedolIn1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaSedolIn24(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWwaSedolIn57(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWwaSedolInAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaSedolIn1.PadRight(1));
    result.Append(_WwaSedolIn24.PadRight(3));
    result.Append(_WwaSedolIn57.PadRight(3));
    
    return result.ToString();
}

public void SetWwaSedolInAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaSedolIn1(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaSedolIn24(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaSedolIn57(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaSedolIn1()
{
    return _WwaSedolIn1;
}

// Standard Setter
public void SetWwaSedolIn1(string value)
{
    _WwaSedolIn1 = value;
}

// Get<>AsString()
public string GetWwaSedolIn1AsString()
{
    return _WwaSedolIn1.PadRight(1);
}

// Set<>AsString()
public void SetWwaSedolIn1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaSedolIn1 = value;
}

// Standard Getter
public string GetWwaSedolIn24()
{
    return _WwaSedolIn24;
}

// Standard Setter
public void SetWwaSedolIn24(string value)
{
    _WwaSedolIn24 = value;
}

// Get<>AsString()
public string GetWwaSedolIn24AsString()
{
    return _WwaSedolIn24.PadRight(3);
}

// Set<>AsString()
public void SetWwaSedolIn24AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaSedolIn24 = value;
}

// Standard Getter
public string GetWwaSedolIn57()
{
    return _WwaSedolIn57;
}

// Standard Setter
public void SetWwaSedolIn57(string value)
{
    _WwaSedolIn57 = value;
}

// Get<>AsString()
public string GetWwaSedolIn57AsString()
{
    return _WwaSedolIn57.PadRight(3);
}

// Set<>AsString()
public void SetWwaSedolIn57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaSedolIn57 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaSedolOut(string value)
{
    _WwaSedolOut.SetWwaSedolOutAsString(value);
}
// Nested Class: WwaSedolOut
public class WwaSedolOut
{
    private static int _size = 9;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaSedolOut1, is_external=, is_static_class=False, static_prefix=
    private string _WwaSedolOut1 ="";
    
    
    
    
    // [DEBUG] Field: Filler116, is_external=, is_static_class=False, static_prefix=
    private string _Filler116 ="-";
    
    
    
    
    // [DEBUG] Field: WwaSedolOut24, is_external=, is_static_class=False, static_prefix=
    private string _WwaSedolOut24 ="";
    
    
    
    
    // [DEBUG] Field: Filler117, is_external=, is_static_class=False, static_prefix=
    private string _Filler117 ="-";
    
    
    
    
    // [DEBUG] Field: WwaSedolOut57, is_external=, is_static_class=False, static_prefix=
    private string _WwaSedolOut57 ="";
    
    
    
    
public WwaSedolOut() {}

public WwaSedolOut(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaSedolOut1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller116(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaSedolOut24(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller117(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaSedolOut57(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWwaSedolOutAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaSedolOut1.PadRight(1));
    result.Append(_Filler116.PadRight(1));
    result.Append(_WwaSedolOut24.PadRight(3));
    result.Append(_Filler117.PadRight(1));
    result.Append(_WwaSedolOut57.PadRight(3));
    
    return result.ToString();
}

public void SetWwaSedolOutAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaSedolOut1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller116(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaSedolOut24(extracted);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller117(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWwaSedolOut57(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaSedolOut1()
{
    return _WwaSedolOut1;
}

// Standard Setter
public void SetWwaSedolOut1(string value)
{
    _WwaSedolOut1 = value;
}

// Get<>AsString()
public string GetWwaSedolOut1AsString()
{
    return _WwaSedolOut1.PadRight(1);
}

// Set<>AsString()
public void SetWwaSedolOut1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaSedolOut1 = value;
}

// Standard Getter
public string GetFiller116()
{
    return _Filler116;
}

// Standard Setter
public void SetFiller116(string value)
{
    _Filler116 = value;
}

// Get<>AsString()
public string GetFiller116AsString()
{
    return _Filler116.PadRight(1);
}

// Set<>AsString()
public void SetFiller116AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler116 = value;
}

// Standard Getter
public string GetWwaSedolOut24()
{
    return _WwaSedolOut24;
}

// Standard Setter
public void SetWwaSedolOut24(string value)
{
    _WwaSedolOut24 = value;
}

// Get<>AsString()
public string GetWwaSedolOut24AsString()
{
    return _WwaSedolOut24.PadRight(3);
}

// Set<>AsString()
public void SetWwaSedolOut24AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaSedolOut24 = value;
}

// Standard Getter
public string GetFiller117()
{
    return _Filler117;
}

// Standard Setter
public void SetFiller117(string value)
{
    _Filler117 = value;
}

// Get<>AsString()
public string GetFiller117AsString()
{
    return _Filler117.PadRight(1);
}

// Set<>AsString()
public void SetFiller117AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler117 = value;
}

// Standard Getter
public string GetWwaSedolOut57()
{
    return _WwaSedolOut57;
}

// Standard Setter
public void SetWwaSedolOut57(string value)
{
    _WwaSedolOut57 = value;
}

// Get<>AsString()
public string GetWwaSedolOut57AsString()
{
    return _WwaSedolOut57.PadRight(3);
}

// Set<>AsString()
public void SetWwaSedolOut57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaSedolOut57 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaIssuersName(string value)
{
    _WwaIssuersName.SetWwaIssuersNameAsString(value);
}
// Nested Class: WwaIssuersName
public class WwaIssuersName
{
    private static int _size = 35;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaIssuersName1, is_external=, is_static_class=False, static_prefix=
    private string _WwaIssuersName1 ="";
    
    
    
    
    // [DEBUG] Field: WwaIssuersName2, is_external=, is_static_class=False, static_prefix=
    private string _WwaIssuersName2 ="";
    
    
    
    
public WwaIssuersName() {}

public WwaIssuersName(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaIssuersName1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetWwaIssuersName2(data.Substring(offset, 15).Trim());
    offset += 15;
    
}

// Serialization methods
public string GetWwaIssuersNameAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaIssuersName1.PadRight(20));
    result.Append(_WwaIssuersName2.PadRight(15));
    
    return result.ToString();
}

public void SetWwaIssuersNameAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetWwaIssuersName1(extracted);
    }
    offset += 20;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetWwaIssuersName2(extracted);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaIssuersName1()
{
    return _WwaIssuersName1;
}

// Standard Setter
public void SetWwaIssuersName1(string value)
{
    _WwaIssuersName1 = value;
}

// Get<>AsString()
public string GetWwaIssuersName1AsString()
{
    return _WwaIssuersName1.PadRight(20);
}

// Set<>AsString()
public void SetWwaIssuersName1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaIssuersName1 = value;
}

// Standard Getter
public string GetWwaIssuersName2()
{
    return _WwaIssuersName2;
}

// Standard Setter
public void SetWwaIssuersName2(string value)
{
    _WwaIssuersName2 = value;
}

// Get<>AsString()
public string GetWwaIssuersName2AsString()
{
    return _WwaIssuersName2.PadRight(15);
}

// Set<>AsString()
public void SetWwaIssuersName2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaIssuersName2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaStockDescription(string value)
{
    _WwaStockDescription.SetWwaStockDescriptionAsString(value);
}
// Nested Class: WwaStockDescription
public class WwaStockDescription
{
    private static int _size = 40;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaStockDescription1, is_external=, is_static_class=False, static_prefix=
    private string _WwaStockDescription1 ="";
    
    
    
    
    // [DEBUG] Field: WwaStockDescription2, is_external=, is_static_class=False, static_prefix=
    private string _WwaStockDescription2 ="";
    
    
    
    
public WwaStockDescription() {}

public WwaStockDescription(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaStockDescription1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetWwaStockDescription2(data.Substring(offset, 20).Trim());
    offset += 20;
    
}

// Serialization methods
public string GetWwaStockDescriptionAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaStockDescription1.PadRight(20));
    result.Append(_WwaStockDescription2.PadRight(20));
    
    return result.ToString();
}

public void SetWwaStockDescriptionAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetWwaStockDescription1(extracted);
    }
    offset += 20;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetWwaStockDescription2(extracted);
    }
    offset += 20;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaStockDescription1()
{
    return _WwaStockDescription1;
}

// Standard Setter
public void SetWwaStockDescription1(string value)
{
    _WwaStockDescription1 = value;
}

// Get<>AsString()
public string GetWwaStockDescription1AsString()
{
    return _WwaStockDescription1.PadRight(20);
}

// Set<>AsString()
public void SetWwaStockDescription1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaStockDescription1 = value;
}

// Standard Getter
public string GetWwaStockDescription2()
{
    return _WwaStockDescription2;
}

// Standard Setter
public void SetWwaStockDescription2(string value)
{
    _WwaStockDescription2 = value;
}

// Get<>AsString()
public string GetWwaStockDescription2AsString()
{
    return _WwaStockDescription2.PadRight(20);
}

// Set<>AsString()
public void SetWwaStockDescription2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaStockDescription2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaGroup(string value)
{
    _WwaGroup.SetWwaGroupAsString(value);
}
// Nested Class: WwaGroup
public class WwaGroup
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler118, is_external=, is_static_class=False, static_prefix=
    private string _Filler118 ="";
    
    
    
    
    // [DEBUG] Field: WwaGroupLast, is_external=, is_static_class=False, static_prefix=
    private string _WwaGroupLast ="";
    
    
    
    
public WwaGroup() {}

public WwaGroup(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller118(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaGroupLast(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWwaGroupAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler118.PadRight(1));
    result.Append(_WwaGroupLast.PadRight(2));
    
    return result.ToString();
}

public void SetWwaGroupAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller118(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWwaGroupLast(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller118()
{
    return _Filler118;
}

// Standard Setter
public void SetFiller118(string value)
{
    _Filler118 = value;
}

// Get<>AsString()
public string GetFiller118AsString()
{
    return _Filler118.PadRight(1);
}

// Set<>AsString()
public void SetFiller118AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler118 = value;
}

// Standard Getter
public string GetWwaGroupLast()
{
    return _WwaGroupLast;
}

// Standard Setter
public void SetWwaGroupLast(string value)
{
    _WwaGroupLast = value;
}

// Get<>AsString()
public string GetWwaGroupLastAsString()
{
    return _WwaGroupLast.PadRight(2);
}

// Set<>AsString()
public void SetWwaGroupLastAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaGroupLast = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWwaStorePrint(string value)
{
    _WwaStorePrint.SetWwaStorePrintAsString(value);
}
// Nested Class: WwaStorePrint
public class WwaStorePrint
{
    private static int _size = 180;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler119, is_external=, is_static_class=False, static_prefix=
    private string _Filler119 ="";
    
    
    
    
    // [DEBUG] Field: WwaStoreText, is_external=, is_static_class=False, static_prefix=
    private WwaStorePrint.WwaStoreText _WwaStoreText = new WwaStorePrint.WwaStoreText();
    
    
    
    
    // [DEBUG] Field: Filler121, is_external=, is_static_class=False, static_prefix=
    private string _Filler121 ="";
    
    
    
    
public WwaStorePrint() {}

public WwaStorePrint(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller119(data.Substring(offset, 2).Trim());
    offset += 2;
    _WwaStoreText.SetWwaStoreTextAsString(data.Substring(offset, WwaStoreText.GetSize()));
    offset += 20;
    SetFiller121(data.Substring(offset, 158).Trim());
    offset += 158;
    
}

// Serialization methods
public string GetWwaStorePrintAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler119.PadRight(2));
    result.Append(_WwaStoreText.GetWwaStoreTextAsString());
    result.Append(_Filler121.PadRight(158));
    
    return result.ToString();
}

public void SetWwaStorePrintAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller119(extracted);
    }
    offset += 2;
    if (offset + 20 <= data.Length)
    {
        _WwaStoreText.SetWwaStoreTextAsString(data.Substring(offset, 20));
    }
    else
    {
        _WwaStoreText.SetWwaStoreTextAsString(data.Substring(offset));
    }
    offset += 20;
    if (offset + 158 <= data.Length)
    {
        string extracted = data.Substring(offset, 158).Trim();
        SetFiller121(extracted);
    }
    offset += 158;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller119()
{
    return _Filler119;
}

// Standard Setter
public void SetFiller119(string value)
{
    _Filler119 = value;
}

// Get<>AsString()
public string GetFiller119AsString()
{
    return _Filler119.PadRight(2);
}

// Set<>AsString()
public void SetFiller119AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler119 = value;
}

// Standard Getter
public WwaStoreText GetWwaStoreText()
{
    return _WwaStoreText;
}

// Standard Setter
public void SetWwaStoreText(WwaStoreText value)
{
    _WwaStoreText = value;
}

// Get<>AsString()
public string GetWwaStoreTextAsString()
{
    return _WwaStoreText != null ? _WwaStoreText.GetWwaStoreTextAsString() : "";
}

// Set<>AsString()
public void SetWwaStoreTextAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WwaStoreText == null)
    {
        _WwaStoreText = new WwaStoreText();
    }
    _WwaStoreText.SetWwaStoreTextAsString(value);
}

// Standard Getter
public string GetFiller121()
{
    return _Filler121;
}

// Standard Setter
public void SetFiller121(string value)
{
    _Filler121 = value;
}

// Get<>AsString()
public string GetFiller121AsString()
{
    return _Filler121.PadRight(158);
}

// Set<>AsString()
public void SetFiller121AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler121 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WwaStoreText
public class WwaStoreText
{
    private static int _size = 20;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler120, is_external=, is_static_class=False, static_prefix=
    private string _Filler120 ="";
    
    
    
    
    // [DEBUG] Field: WwaStoreSectype, is_external=, is_static_class=False, static_prefix=
    private string _WwaStoreSectype ="";
    
    
    
    
public WwaStoreText() {}

public WwaStoreText(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller120(data.Substring(offset, 19).Trim());
    offset += 19;
    SetWwaStoreSectype(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetWwaStoreTextAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler120.PadRight(19));
    result.Append(_WwaStoreSectype.PadRight(1));
    
    return result.ToString();
}

public void SetWwaStoreTextAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller120(extracted);
    }
    offset += 19;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaStoreSectype(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller120()
{
    return _Filler120;
}

// Standard Setter
public void SetFiller120(string value)
{
    _Filler120 = value;
}

// Get<>AsString()
public string GetFiller120AsString()
{
    return _Filler120.PadRight(19);
}

// Set<>AsString()
public void SetFiller120AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler120 = value;
}

// Standard Getter
public string GetWwaStoreSectype()
{
    return _WwaStoreSectype;
}

// Standard Setter
public void SetWwaStoreSectype(string value)
{
    _WwaStoreSectype = value;
}

// Get<>AsString()
public string GetWwaStoreSectypeAsString()
{
    return _WwaStoreSectype.PadRight(1);
}

// Set<>AsString()
public void SetWwaStoreSectypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaStoreSectype = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWwaShift2(string value)
{
    _WwaShift2.SetWwaShift2AsString(value);
}
// Nested Class: WwaShift2
public class WwaShift2
{
    private static int _size = 40;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaShiftSpace, is_external=, is_static_class=False, static_prefix=
    private string _WwaShiftSpace ="";
    
    
    
    
    // [DEBUG] Field: WwaShiftBy1Byte, is_external=, is_static_class=False, static_prefix=
    private WwaShift2.WwaShiftBy1Byte _WwaShiftBy1Byte = new WwaShift2.WwaShiftBy1Byte();
    
    
    
    
public WwaShift2() {}

public WwaShift2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaShiftSpace(data.Substring(offset, 1).Trim());
    offset += 1;
    _WwaShiftBy1Byte.SetWwaShiftBy1ByteAsString(data.Substring(offset, WwaShiftBy1Byte.GetSize()));
    offset += 39;
    
}

// Serialization methods
public string GetWwaShift2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaShiftSpace.PadRight(1));
    result.Append(_WwaShiftBy1Byte.GetWwaShiftBy1ByteAsString());
    
    return result.ToString();
}

public void SetWwaShift2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaShiftSpace(extracted);
    }
    offset += 1;
    if (offset + 39 <= data.Length)
    {
        _WwaShiftBy1Byte.SetWwaShiftBy1ByteAsString(data.Substring(offset, 39));
    }
    else
    {
        _WwaShiftBy1Byte.SetWwaShiftBy1ByteAsString(data.Substring(offset));
    }
    offset += 39;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaShiftSpace()
{
    return _WwaShiftSpace;
}

// Standard Setter
public void SetWwaShiftSpace(string value)
{
    _WwaShiftSpace = value;
}

// Get<>AsString()
public string GetWwaShiftSpaceAsString()
{
    return _WwaShiftSpace.PadRight(1);
}

// Set<>AsString()
public void SetWwaShiftSpaceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaShiftSpace = value;
}

// Standard Getter
public WwaShiftBy1Byte GetWwaShiftBy1Byte()
{
    return _WwaShiftBy1Byte;
}

// Standard Setter
public void SetWwaShiftBy1Byte(WwaShiftBy1Byte value)
{
    _WwaShiftBy1Byte = value;
}

// Get<>AsString()
public string GetWwaShiftBy1ByteAsString()
{
    return _WwaShiftBy1Byte != null ? _WwaShiftBy1Byte.GetWwaShiftBy1ByteAsString() : "";
}

// Set<>AsString()
public void SetWwaShiftBy1ByteAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WwaShiftBy1Byte == null)
    {
        _WwaShiftBy1Byte = new WwaShiftBy1Byte();
    }
    _WwaShiftBy1Byte.SetWwaShiftBy1ByteAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WwaShiftBy1Byte
public class WwaShiftBy1Byte
{
    private static int _size = 39;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler122, is_external=, is_static_class=False, static_prefix=
    private string _Filler122 ="";
    
    
    
    
    // [DEBUG] Field: WwaShiftLast, is_external=, is_static_class=False, static_prefix=
    private string _WwaShiftLast ="";
    
    
    
    
public WwaShiftBy1Byte() {}

public WwaShiftBy1Byte(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller122(data.Substring(offset, 38).Trim());
    offset += 38;
    SetWwaShiftLast(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetWwaShiftBy1ByteAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler122.PadRight(38));
    result.Append(_WwaShiftLast.PadRight(1));
    
    return result.ToString();
}

public void SetWwaShiftBy1ByteAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 38 <= data.Length)
    {
        string extracted = data.Substring(offset, 38).Trim();
        SetFiller122(extracted);
    }
    offset += 38;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaShiftLast(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller122()
{
    return _Filler122;
}

// Standard Setter
public void SetFiller122(string value)
{
    _Filler122 = value;
}

// Get<>AsString()
public string GetFiller122AsString()
{
    return _Filler122.PadRight(38);
}

// Set<>AsString()
public void SetFiller122AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler122 = value;
}

// Standard Getter
public string GetWwaShiftLast()
{
    return _WwaShiftLast;
}

// Standard Setter
public void SetWwaShiftLast(string value)
{
    _WwaShiftLast = value;
}

// Get<>AsString()
public string GetWwaShiftLastAsString()
{
    return _WwaShiftLast.PadRight(1);
}

// Set<>AsString()
public void SetWwaShiftLastAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaShiftLast = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWwaParmArea(string value)
{
    _WwaParmArea.SetWwaParmAreaAsString(value);
}
// Nested Class: WwaParmArea
public class WwaParmArea
{
    private static int _size = 81;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaParmChar1, is_external=, is_static_class=False, static_prefix=
    private string _WwaParmChar1 =" ";
    
    
    // 88-level condition checks for WwaParmChar1
    public bool IsSuppressIfNotFlagged()
    {
        if (this._WwaParmChar1 == "'S'") return true;
        return false;
    }
    public bool IsPrintBookCost()
    {
        if (this._WwaParmChar1 == "'B'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WwaParmChar2, is_external=, is_static_class=False, static_prefix=
    private string _WwaParmChar2 ="?";
    
    
    
    
    // [DEBUG] Field: Filler123, is_external=, is_static_class=False, static_prefix=
    private string _Filler123 ="";
    
    
    
    
    // [DEBUG] Field: WwaParmChar6, is_external=, is_static_class=False, static_prefix=
    private string _WwaParmChar6 =" ";
    
    
    
    
    // [DEBUG] Field: Filler124, is_external=, is_static_class=False, static_prefix=
    private string _Filler124 ="";
    
    
    
    
    // [DEBUG] Field: WwaParmFn, is_external=, is_static_class=False, static_prefix=
    private WwaParmArea.WwaParmFn _WwaParmFn = new WwaParmArea.WwaParmFn();
    
    
    
    
    // [DEBUG] Field: Filler125, is_external=, is_static_class=False, static_prefix=
    private string _Filler125 ="";
    
    
    
    
public WwaParmArea() {}

public WwaParmArea(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaParmChar1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaParmChar2(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller123(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWwaParmChar6(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller124(data.Substring(offset, 3).Trim());
    offset += 3;
    _WwaParmFn.SetWwaParmFnAsString(data.Substring(offset, WwaParmFn.GetSize()));
    offset += 8;
    SetFiller125(data.Substring(offset, 64).Trim());
    offset += 64;
    
}

// Serialization methods
public string GetWwaParmAreaAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaParmChar1.PadRight(1));
    result.Append(_WwaParmChar2.PadRight(1));
    result.Append(_Filler123.PadRight(3));
    result.Append(_WwaParmChar6.PadRight(1));
    result.Append(_Filler124.PadRight(3));
    result.Append(_WwaParmFn.GetWwaParmFnAsString());
    result.Append(_Filler125.PadRight(64));
    
    return result.ToString();
}

public void SetWwaParmAreaAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaParmChar1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaParmChar2(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller123(extracted);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaParmChar6(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller124(extracted);
    }
    offset += 3;
    if (offset + 8 <= data.Length)
    {
        _WwaParmFn.SetWwaParmFnAsString(data.Substring(offset, 8));
    }
    else
    {
        _WwaParmFn.SetWwaParmFnAsString(data.Substring(offset));
    }
    offset += 8;
    if (offset + 64 <= data.Length)
    {
        string extracted = data.Substring(offset, 64).Trim();
        SetFiller125(extracted);
    }
    offset += 64;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaParmChar1()
{
    return _WwaParmChar1;
}

// Standard Setter
public void SetWwaParmChar1(string value)
{
    _WwaParmChar1 = value;
}

// Get<>AsString()
public string GetWwaParmChar1AsString()
{
    return _WwaParmChar1.PadRight(1);
}

// Set<>AsString()
public void SetWwaParmChar1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaParmChar1 = value;
}

// Standard Getter
public string GetWwaParmChar2()
{
    return _WwaParmChar2;
}

// Standard Setter
public void SetWwaParmChar2(string value)
{
    _WwaParmChar2 = value;
}

// Get<>AsString()
public string GetWwaParmChar2AsString()
{
    return _WwaParmChar2.PadRight(1);
}

// Set<>AsString()
public void SetWwaParmChar2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaParmChar2 = value;
}

// Standard Getter
public string GetFiller123()
{
    return _Filler123;
}

// Standard Setter
public void SetFiller123(string value)
{
    _Filler123 = value;
}

// Get<>AsString()
public string GetFiller123AsString()
{
    return _Filler123.PadRight(3);
}

// Set<>AsString()
public void SetFiller123AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler123 = value;
}

// Standard Getter
public string GetWwaParmChar6()
{
    return _WwaParmChar6;
}

// Standard Setter
public void SetWwaParmChar6(string value)
{
    _WwaParmChar6 = value;
}

// Get<>AsString()
public string GetWwaParmChar6AsString()
{
    return _WwaParmChar6.PadRight(1);
}

// Set<>AsString()
public void SetWwaParmChar6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaParmChar6 = value;
}

// Standard Getter
public string GetFiller124()
{
    return _Filler124;
}

// Standard Setter
public void SetFiller124(string value)
{
    _Filler124 = value;
}

// Get<>AsString()
public string GetFiller124AsString()
{
    return _Filler124.PadRight(3);
}

// Set<>AsString()
public void SetFiller124AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler124 = value;
}

// Standard Getter
public WwaParmFn GetWwaParmFn()
{
    return _WwaParmFn;
}

// Standard Setter
public void SetWwaParmFn(WwaParmFn value)
{
    _WwaParmFn = value;
}

// Get<>AsString()
public string GetWwaParmFnAsString()
{
    return _WwaParmFn != null ? _WwaParmFn.GetWwaParmFnAsString() : "";
}

// Set<>AsString()
public void SetWwaParmFnAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WwaParmFn == null)
    {
        _WwaParmFn = new WwaParmFn();
    }
    _WwaParmFn.SetWwaParmFnAsString(value);
}

// Standard Getter
public string GetFiller125()
{
    return _Filler125;
}

// Standard Setter
public void SetFiller125(string value)
{
    _Filler125 = value;
}

// Get<>AsString()
public string GetFiller125AsString()
{
    return _Filler125.PadRight(64);
}

// Set<>AsString()
public void SetFiller125AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler125 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WwaParmFn
public class WwaParmFn
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaParmFn1To5, is_external=, is_static_class=False, static_prefix=
    private string _WwaParmFn1To5 ="[USER";
    
    
    
    
    // [DEBUG] Field: WwaParmFnrest, is_external=, is_static_class=False, static_prefix=
    private string _WwaParmFnrest ="N";
    
    
    
    
    // [DEBUG] Field: WwaParmFnYy, is_external=, is_static_class=False, static_prefix=
    private string _WwaParmFnYy ="YY";
    
    
    
    
public WwaParmFn() {}

public WwaParmFn(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWwaParmFn1To5(data.Substring(offset, 5).Trim());
    offset += 5;
    SetWwaParmFnrest(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWwaParmFnYy(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWwaParmFnAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WwaParmFn1To5.PadRight(5));
    result.Append(_WwaParmFnrest.PadRight(1));
    result.Append(_WwaParmFnYy.PadRight(2));
    
    return result.ToString();
}

public void SetWwaParmFnAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        SetWwaParmFn1To5(extracted);
    }
    offset += 5;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWwaParmFnrest(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWwaParmFnYy(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWwaParmFn1To5()
{
    return _WwaParmFn1To5;
}

// Standard Setter
public void SetWwaParmFn1To5(string value)
{
    _WwaParmFn1To5 = value;
}

// Get<>AsString()
public string GetWwaParmFn1To5AsString()
{
    return _WwaParmFn1To5.PadRight(5);
}

// Set<>AsString()
public void SetWwaParmFn1To5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaParmFn1To5 = value;
}

// Standard Getter
public string GetWwaParmFnrest()
{
    return _WwaParmFnrest;
}

// Standard Setter
public void SetWwaParmFnrest(string value)
{
    _WwaParmFnrest = value;
}

// Get<>AsString()
public string GetWwaParmFnrestAsString()
{
    return _WwaParmFnrest.PadRight(1);
}

// Set<>AsString()
public void SetWwaParmFnrestAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaParmFnrest = value;
}

// Standard Getter
public string GetWwaParmFnYy()
{
    return _WwaParmFnYy;
}

// Standard Setter
public void SetWwaParmFnYy(string value)
{
    _WwaParmFnYy = value;
}

// Get<>AsString()
public string GetWwaParmFnYyAsString()
{
    return _WwaParmFnYy.PadRight(2);
}

// Set<>AsString()
public void SetWwaParmFnYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WwaParmFnYy = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWwaParmAreaR(string value)
{
    _WwaParmAreaR.SetWwaParmAreaRAsString(value);
}
// Nested Class: WwaParmAreaR
public class WwaParmAreaR
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WwaParmChar, is_external=, is_static_class=False, static_prefix=
    private string[] _WwaParmChar = new string[81];
    
    
    
    
public WwaParmAreaR() {}

public WwaParmAreaR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 81; i++)
    {
        string value = data.Substring(offset, 1);
        _WwaParmChar[i] = value.Trim();
        offset += 1;
    }
    
}

// Serialization methods
public string GetWwaParmAreaRAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 81; i++)
    {
        result.Append(_WwaParmChar[i].PadRight(1));
    }
    
    return result.ToString();
}

public void SetWwaParmAreaRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 81; i++)
    {
        if (offset + 1 > data.Length) break;
        string val = data.Substring(offset, 1);
        
        _WwaParmChar[i] = val.Trim();
        offset += 1;
    }
}

// Getter and Setter methods

// Array Accessors for WwaParmChar
public string GetWwaParmCharAt(int index)
{
    return _WwaParmChar[index];
}

public void SetWwaParmCharAt(int index, string value)
{
    _WwaParmChar[index] = value;
}

public string GetWwaParmCharAsStringAt(int index)
{
    return _WwaParmChar[index].PadRight(1);
}

public void SetWwaParmCharAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _WwaParmChar[index] = value;
}

// Flattened accessors (index 0)
public string GetWwaParmChar()
{
    return _WwaParmChar != null && _WwaParmChar.Length > 0
    ? _WwaParmChar[0]
    : default(string);
}

public void SetWwaParmChar(string value)
{
    if (_WwaParmChar == null || _WwaParmChar.Length == 0)
    _WwaParmChar = new string[1];
    _WwaParmChar[0] = value;
}

public string GetWwaParmCharAsString()
{
    return _WwaParmChar != null && _WwaParmChar.Length > 0
    ? _WwaParmChar[0].ToString()
    : string.Empty;
}

public void SetWwaParmCharAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_WwaParmChar == null || _WwaParmChar.Length == 0)
    _WwaParmChar = new string[1];
    
    _WwaParmChar[0] = value;
}




public static int GetSize()
{
    return _size;
}

}

}}
