using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// <Section> Class for Gvar
public class Gvar
{
public Gvar() {}

// Fields in the class


// [DEBUG] Field: WsMessageNo, is_external=, is_static_class=False, static_prefix=
private int _WsMessageNo =0;




// [DEBUG] Field: WsProcessFlag, is_external=, is_static_class=False, static_prefix=
private string _WsProcessFlag ="";


// 88-level condition checks for WsProcessFlag
public bool IsQuitProcess()
{
    if (this._WsProcessFlag == "'Q'") return true;
    return false;
}


// [DEBUG] Field: WsProgramName, is_external=, is_static_class=False, static_prefix=
private string _WsProgramName ="CGTFILES";




// [DEBUG] Field: LKey, is_external=, is_static_class=False, static_prefix=
private string _LKey ="";




// [DEBUG] Field: RecLength, is_external=, is_static_class=False, static_prefix=
private int _RecLength =0;




// [DEBUG] Field: WSub, is_external=, is_static_class=False, static_prefix=
private int _WSub =0;




// [DEBUG] Field: WScheduleCounters, is_external=, is_static_class=False, static_prefix=
private WScheduleCounters _WScheduleCounters = new WScheduleCounters();




// [DEBUG] Field: FileStatus, is_external=, is_static_class=False, static_prefix=
private FileStatus _FileStatus = new FileStatus();




// [DEBUG] Field: WBinaryField, is_external=, is_static_class=False, static_prefix=
private WBinaryField _WBinaryField = new WBinaryField();




// [DEBUG] Field: WBinaryStatus2, is_external=, is_static_class=False, static_prefix=
private int _WBinaryStatus2 =0;




// [DEBUG] Field: WPrintRecord, is_external=, is_static_class=False, static_prefix=
private WPrintRecord _WPrintRecord = new WPrintRecord();




// [DEBUG] Field: WPrintCharSub, is_external=, is_static_class=False, static_prefix=
private int _WPrintCharSub =0;




// [DEBUG] Field: Wd161Record, is_external=, is_static_class=False, static_prefix=
private Wd161Record _Wd161Record = new Wd161Record();




// [DEBUG] Field: WD161CharSub, is_external=, is_static_class=False, static_prefix=
private int _WD161CharSub =0;




// [DEBUG] Field: D1FileName, is_external=, is_static_class=False, static_prefix=
private string _D1FileName ="";




// [DEBUG] Field: D2FileName, is_external=, is_static_class=False, static_prefix=
private string _D2FileName ="";




// [DEBUG] Field: D3FileName, is_external=, is_static_class=False, static_prefix=
private string _D3FileName ="";




// [DEBUG] Field: D4FileName, is_external=, is_static_class=False, static_prefix=
private string _D4FileName ="";




// [DEBUG] Field: D5FileName, is_external=, is_static_class=False, static_prefix=
private string _D5FileName ="";




// [DEBUG] Field: D6FileName, is_external=, is_static_class=False, static_prefix=
private string _D6FileName ="";




// [DEBUG] Field: D7FileName, is_external=, is_static_class=False, static_prefix=
private string _D7FileName ="";




// [DEBUG] Field: D8FileName, is_external=, is_static_class=False, static_prefix=
private string _D8FileName ="";




// [DEBUG] Field: D9FileName, is_external=, is_static_class=False, static_prefix=
private string _D9FileName ="";




// [DEBUG] Field: D10FileName, is_external=, is_static_class=False, static_prefix=
private string _D10FileName ="";




// [DEBUG] Field: D11FileName, is_external=, is_static_class=False, static_prefix=
private string _D11FileName ="";




// [DEBUG] Field: D12FileName, is_external=, is_static_class=False, static_prefix=
private string _D12FileName ="";




// [DEBUG] Field: D17FileName, is_external=, is_static_class=False, static_prefix=
private string _D17FileName ="";




// [DEBUG] Field: D18FileName, is_external=, is_static_class=False, static_prefix=
private string _D18FileName ="";




// [DEBUG] Field: D19FileName, is_external=, is_static_class=False, static_prefix=
private string _D19FileName ="";




// [DEBUG] Field: D20FileName, is_external=, is_static_class=False, static_prefix=
private string _D20FileName ="";




// [DEBUG] Field: D21FileName, is_external=, is_static_class=False, static_prefix=
private string _D21FileName ="";




// [DEBUG] Field: D22FileName, is_external=, is_static_class=False, static_prefix=
private string _D22FileName ="";




// [DEBUG] Field: D23FileName, is_external=, is_static_class=False, static_prefix=
private string _D23FileName ="";




// [DEBUG] Field: D24FileName, is_external=, is_static_class=False, static_prefix=
private string _D24FileName ="";




// [DEBUG] Field: D25FileName, is_external=, is_static_class=False, static_prefix=
private string _D25FileName ="";




// [DEBUG] Field: D26FileName, is_external=, is_static_class=False, static_prefix=
private string _D26FileName ="";




// [DEBUG] Field: D27FileName, is_external=, is_static_class=False, static_prefix=
private string _D27FileName ="";




// [DEBUG] Field: D28FileName, is_external=, is_static_class=False, static_prefix=
private string _D28FileName ="";




// [DEBUG] Field: D29FileName, is_external=, is_static_class=False, static_prefix=
private string _D29FileName ="";




// [DEBUG] Field: D30FileName, is_external=, is_static_class=False, static_prefix=
private string _D30FileName ="";




// [DEBUG] Field: D31FileName, is_external=, is_static_class=False, static_prefix=
private string _D31FileName ="";




// [DEBUG] Field: D32FileName, is_external=, is_static_class=False, static_prefix=
private string _D32FileName ="";




// [DEBUG] Field: D33FileName, is_external=, is_static_class=False, static_prefix=
private string _D33FileName ="";




// [DEBUG] Field: D34FileName, is_external=, is_static_class=False, static_prefix=
private string _D34FileName ="";




// [DEBUG] Field: D35FileName, is_external=, is_static_class=False, static_prefix=
private string _D35FileName ="";




// [DEBUG] Field: D36FileName, is_external=, is_static_class=False, static_prefix=
private string _D36FileName ="";




// [DEBUG] Field: D38FileName, is_external=, is_static_class=False, static_prefix=
private string _D38FileName ="";




// [DEBUG] Field: D39FileName, is_external=, is_static_class=False, static_prefix=
private string _D39FileName ="";




// [DEBUG] Field: D40FileName, is_external=, is_static_class=False, static_prefix=
private string _D40FileName ="";




// [DEBUG] Field: D41FileName, is_external=, is_static_class=False, static_prefix=
private string _D41FileName ="";




// [DEBUG] Field: D42FileName, is_external=, is_static_class=False, static_prefix=
private string _D42FileName ="";




// [DEBUG] Field: D43FileName, is_external=, is_static_class=False, static_prefix=
private string _D43FileName ="";




// [DEBUG] Field: D44FileName, is_external=, is_static_class=False, static_prefix=
private string _D44FileName ="";




// [DEBUG] Field: D45FileName, is_external=, is_static_class=False, static_prefix=
private string _D45FileName ="";




// [DEBUG] Field: D46FileName, is_external=, is_static_class=False, static_prefix=
private string _D46FileName ="";




// [DEBUG] Field: D49FileName, is_external=, is_static_class=False, static_prefix=
private string _D49FileName ="";




// [DEBUG] Field: D50FileName, is_external=, is_static_class=False, static_prefix=
private string _D50FileName ="";




// [DEBUG] Field: D51FileName, is_external=, is_static_class=False, static_prefix=
private string _D51FileName ="";




// [DEBUG] Field: D68FileName, is_external=, is_static_class=False, static_prefix=
private string _D68FileName ="";




// [DEBUG] Field: D69FileName, is_external=, is_static_class=False, static_prefix=
private string _D69FileName ="";




// [DEBUG] Field: D70FileName, is_external=, is_static_class=False, static_prefix=
private string _D70FileName ="";




// [DEBUG] Field: D71FileName, is_external=, is_static_class=False, static_prefix=
private string _D71FileName ="";




// [DEBUG] Field: D72FileName, is_external=, is_static_class=False, static_prefix=
private string _D72FileName ="";




// [DEBUG] Field: D73FileName, is_external=, is_static_class=False, static_prefix=
private string _D73FileName ="";




// [DEBUG] Field: D74FileName, is_external=, is_static_class=False, static_prefix=
private string _D74FileName ="";




// [DEBUG] Field: D75FileName, is_external=, is_static_class=False, static_prefix=
private string _D75FileName ="";




// [DEBUG] Field: D76FileName, is_external=, is_static_class=False, static_prefix=
private string _D76FileName ="";




// [DEBUG] Field: D77FileName, is_external=, is_static_class=False, static_prefix=
private string _D77FileName ="";




// [DEBUG] Field: D78FileName, is_external=, is_static_class=False, static_prefix=
private string _D78FileName ="";




// [DEBUG] Field: D79FileName, is_external=, is_static_class=False, static_prefix=
private string _D79FileName ="";




// [DEBUG] Field: D80FileName, is_external=, is_static_class=False, static_prefix=
private string _D80FileName ="";




// [DEBUG] Field: D81FileName, is_external=, is_static_class=False, static_prefix=
private string _D81FileName ="";




// [DEBUG] Field: D82FileName, is_external=, is_static_class=False, static_prefix=
private string _D82FileName ="";




// [DEBUG] Field: D83FileName, is_external=, is_static_class=False, static_prefix=
private string _D83FileName ="";




// [DEBUG] Field: D84FileName, is_external=, is_static_class=False, static_prefix=
private string _D84FileName ="";




// [DEBUG] Field: D85FileName, is_external=, is_static_class=False, static_prefix=
private string _D85FileName ="";




// [DEBUG] Field: D86FileName, is_external=, is_static_class=False, static_prefix=
private string _D86FileName ="";




// [DEBUG] Field: D87FileName, is_external=, is_static_class=False, static_prefix=
private string _D87FileName ="";




// [DEBUG] Field: D88FileName, is_external=, is_static_class=False, static_prefix=
private string _D88FileName ="";




// [DEBUG] Field: D89FileName, is_external=, is_static_class=False, static_prefix=
private string _D89FileName ="";




// [DEBUG] Field: D90FileName, is_external=, is_static_class=False, static_prefix=
private string _D90FileName ="";




// [DEBUG] Field: D91FileName, is_external=, is_static_class=False, static_prefix=
private string _D91FileName ="";




// [DEBUG] Field: D92FileName, is_external=, is_static_class=False, static_prefix=
private string _D92FileName ="";




// [DEBUG] Field: D93FileName, is_external=, is_static_class=False, static_prefix=
private string _D93FileName ="";




// [DEBUG] Field: D94FileName, is_external=, is_static_class=False, static_prefix=
private string _D94FileName ="";




// [DEBUG] Field: D95FileName, is_external=, is_static_class=False, static_prefix=
private string _D95FileName ="";




// [DEBUG] Field: D96FileName, is_external=, is_static_class=False, static_prefix=
private string _D96FileName ="";




// [DEBUG] Field: D97FileName, is_external=, is_static_class=False, static_prefix=
private string _D97FileName ="";




// [DEBUG] Field: D98FileName, is_external=, is_static_class=False, static_prefix=
private string _D98FileName ="";




// [DEBUG] Field: D100FileName, is_external=, is_static_class=False, static_prefix=
private string _D100FileName ="";




// [DEBUG] Field: D101FileName, is_external=, is_static_class=False, static_prefix=
private string _D101FileName ="";




// [DEBUG] Field: D102FileName, is_external=, is_static_class=False, static_prefix=
private string _D102FileName ="";




// [DEBUG] Field: D103FileName, is_external=, is_static_class=False, static_prefix=
private string _D103FileName ="";




// [DEBUG] Field: D104FileName, is_external=, is_static_class=False, static_prefix=
private string _D104FileName ="";




// [DEBUG] Field: D105FileName, is_external=, is_static_class=False, static_prefix=
private string _D105FileName ="";




// [DEBUG] Field: Filler158, is_external=, is_static_class=False, static_prefix=
private string _Filler158 ="";




// [DEBUG] Field: D107FileName, is_external=, is_static_class=False, static_prefix=
private string _D107FileName ="";




// [DEBUG] Field: D108FileName, is_external=, is_static_class=False, static_prefix=
private string _D108FileName ="";




// [DEBUG] Field: D109FileName, is_external=, is_static_class=False, static_prefix=
private string _D109FileName ="";




// [DEBUG] Field: D110FileName, is_external=, is_static_class=False, static_prefix=
private string _D110FileName ="";




// [DEBUG] Field: D111FileName, is_external=, is_static_class=False, static_prefix=
private string _D111FileName ="";




// [DEBUG] Field: D112FileName, is_external=, is_static_class=False, static_prefix=
private string _D112FileName ="";




// [DEBUG] Field: D133FileName, is_external=, is_static_class=False, static_prefix=
private string _D133FileName ="";




// [DEBUG] Field: D153FileName, is_external=, is_static_class=False, static_prefix=
private string _D153FileName ="";




// [DEBUG] Field: D154FileName, is_external=, is_static_class=False, static_prefix=
private string _D154FileName ="";




// [DEBUG] Field: D159FileName, is_external=, is_static_class=False, static_prefix=
private string _D159FileName ="";




// [DEBUG] Field: D160FileName, is_external=, is_static_class=False, static_prefix=
private string _D160FileName ="";




// [DEBUG] Field: D161FileName, is_external=, is_static_class=False, static_prefix=
private string _D161FileName ="";




// [DEBUG] Field: D163FileName, is_external=, is_static_class=False, static_prefix=
private string _D163FileName ="";




// [DEBUG] Field: D164FileName, is_external=, is_static_class=False, static_prefix=
private string _D164FileName ="";




// [DEBUG] Field: D165FileName, is_external=, is_static_class=False, static_prefix=
private string _D165FileName ="";




// [DEBUG] Field: D167FileName, is_external=, is_static_class=False, static_prefix=
private string _D167FileName ="";




// [DEBUG] Field: D169FileName, is_external=, is_static_class=False, static_prefix=
private string _D169FileName ="";




// [DEBUG] Field: D170FileName, is_external=, is_static_class=False, static_prefix=
private string _D170FileName ="";




// [DEBUG] Field: D171FileName, is_external=, is_static_class=False, static_prefix=
private string _D171FileName ="";




// [DEBUG] Field: D1File, is_external=, is_static_class=False, static_prefix=
private string _D1File ="COUNTRY.DAT";




// [DEBUG] Field: D2File, is_external=, is_static_class=False, static_prefix=
private string _D2File ="GROUP.DAT";




// [DEBUG] Field: D3File, is_external=, is_static_class=False, static_prefix=
private string _D3File ="STOCK.DAT";




// [DEBUG] Field: D4File, is_external=, is_static_class=False, static_prefix=
private string _D4File ="FUND.DAT";




// [DEBUG] Field: D7File, is_external=, is_static_class=False, static_prefix=
private string _D7File ="RPI.DAT";




// [DEBUG] Field: D8File, is_external=, is_static_class=False, static_prefix=
private string _D8File ="PARAM.DAT";




// [DEBUG] Field: D9File, is_external=, is_static_class=False, static_prefix=
private string _D9File ="USERID.DAT";




// [DEBUG] Field: D17File, is_external=, is_static_class=False, static_prefix=
private string _D17File ="LOG.DAT";




// [DEBUG] Field: D31File, is_external=, is_static_class=False, static_prefix=
private string _D31File ="PRINTER.DAT";




// [DEBUG] Field: D32File, is_external=, is_static_class=False, static_prefix=
private string _D32File ="S-TYPE.DAT";




// [DEBUG] Field: D34File, is_external=, is_static_class=False, static_prefix=
private string _D34File ="TR-CODE.DAT";




// [DEBUG] Field: D35File, is_external=, is_static_class=False, static_prefix=
private string _D35File ="OUT-LOG.DAT";




// [DEBUG] Field: D36File, is_external=, is_static_class=False, static_prefix=
private string _D36File ="MESSAGE.DAT";




// [DEBUG] Field: D38File, is_external=, is_static_class=False, static_prefix=
private string _D38File ="HELP.DAT";




// [DEBUG] Field: D39File, is_external=, is_static_class=False, static_prefix=
private string _D39File ="DF-AXESS.DAT";




// [DEBUG] Field: D40File, is_external=, is_static_class=False, static_prefix=
private string _D40File ="ACCESS.DAT";




// [DEBUG] Field: D41File, is_external=, is_static_class=False, static_prefix=
private string _D41File ="EX-PRICE.DAT";




// [DEBUG] Field: D42File, is_external=, is_static_class=False, static_prefix=
private string _D42File ="EX-CURR.DAT";




// [DEBUG] Field: D43File, is_external=, is_static_class=False, static_prefix=
private string _D43File ="ST-HIST.DAT";




// [DEBUG] Field: D44File, is_external=, is_static_class=False, static_prefix=
private string _D44File ="EX-TRANS.DAT";




// [DEBUG] Field: D86File, is_external=, is_static_class=False, static_prefix=
private string _D86File ="CGTRCF.DAT";




// [DEBUG] Field: D112File, is_external=, is_static_class=False, static_prefix=
private string _D112File ="TAPERATE.DAT";




// [DEBUG] Field: D133File, is_external=, is_static_class=False, static_prefix=
private string _D133File ="CALENDAR.DAT";




// [DEBUG] Field: D153File, is_external=, is_static_class=False, static_prefix=
private string _D153File ="PENDCAL.DAT";




// [DEBUG] Field: D154File, is_external=, is_static_class=False, static_prefix=
private string _D154File ="PENDCALD.DAT";




// [DEBUG] Field: D163File, is_external=, is_static_class=False, static_prefix=
private string _D163File ="ICFUNDS.DAT";




// [DEBUG] Field: D167File, is_external=, is_static_class=False, static_prefix=
private string _D167File ="PriceTyp.DAT";




// [DEBUG] Field: D169File, is_external=, is_static_class=False, static_prefix=
private string _D169File ="PendLog.DAT";




// [DEBUG] Field: D170File, is_external=, is_static_class=False, static_prefix=
private string _D170File ="PendItem.DAT";




// [DEBUG] Field: D5File, is_external=, is_static_class=False, static_prefix=
private D5File _D5File = new D5File();




// [DEBUG] Field: D6File, is_external=, is_static_class=False, static_prefix=
private D6File _D6File = new D6File();




// [DEBUG] Field: D10File, is_external=, is_static_class=False, static_prefix=
private D10File _D10File = new D10File();




// [DEBUG] Field: D11File, is_external=, is_static_class=False, static_prefix=
private D11File _D11File = new D11File();




// [DEBUG] Field: D12File, is_external=, is_static_class=False, static_prefix=
private D12File _D12File = new D12File();




// [DEBUG] Field: D18File, is_external=, is_static_class=False, static_prefix=
private D18File _D18File = new D18File();




// [DEBUG] Field: D19File, is_external=, is_static_class=False, static_prefix=
private D19File _D19File = new D19File();




// [DEBUG] Field: D20File, is_external=, is_static_class=False, static_prefix=
private D20File _D20File = new D20File();




// [DEBUG] Field: D21File, is_external=, is_static_class=False, static_prefix=
private D21File _D21File = new D21File();




// [DEBUG] Field: D22File, is_external=, is_static_class=False, static_prefix=
private D22File _D22File = new D22File();




// [DEBUG] Field: D23File, is_external=, is_static_class=False, static_prefix=
private D23File _D23File = new D23File();




// [DEBUG] Field: D24File, is_external=, is_static_class=False, static_prefix=
private D24File _D24File = new D24File();




// [DEBUG] Field: D25File, is_external=, is_static_class=False, static_prefix=
private D25File _D25File = new D25File();




// [DEBUG] Field: D26File, is_external=, is_static_class=False, static_prefix=
private D26File _D26File = new D26File();




// [DEBUG] Field: D27File, is_external=, is_static_class=False, static_prefix=
private D27File _D27File = new D27File();




// [DEBUG] Field: D28File, is_external=, is_static_class=False, static_prefix=
private D28File _D28File = new D28File();




// [DEBUG] Field: D29File, is_external=, is_static_class=False, static_prefix=
private D29File _D29File = new D29File();




// [DEBUG] Field: D30File, is_external=, is_static_class=False, static_prefix=
private D30File _D30File = new D30File();




// [DEBUG] Field: D33File, is_external=, is_static_class=False, static_prefix=
private D33File _D33File = new D33File();




// [DEBUG] Field: D45File, is_external=, is_static_class=False, static_prefix=
private D45File _D45File = new D45File();




// [DEBUG] Field: D46File, is_external=, is_static_class=False, static_prefix=
private D46File _D46File = new D46File();




// [DEBUG] Field: D49File, is_external=, is_static_class=False, static_prefix=
private D49File _D49File = new D49File();




// [DEBUG] Field: D50File, is_external=, is_static_class=False, static_prefix=
private D50File _D50File = new D50File();




// [DEBUG] Field: D51File, is_external=, is_static_class=False, static_prefix=
private D51File _D51File = new D51File();




// [DEBUG] Field: D68File, is_external=, is_static_class=False, static_prefix=
private D68File _D68File = new D68File();




// [DEBUG] Field: D69File, is_external=, is_static_class=False, static_prefix=
private D69File _D69File = new D69File();




// [DEBUG] Field: D70File, is_external=, is_static_class=False, static_prefix=
private D70File _D70File = new D70File();




// [DEBUG] Field: D71File, is_external=, is_static_class=False, static_prefix=
private D71File _D71File = new D71File();




// [DEBUG] Field: D72File, is_external=, is_static_class=False, static_prefix=
private D72File _D72File = new D72File();




// [DEBUG] Field: D73File, is_external=, is_static_class=False, static_prefix=
private D73File _D73File = new D73File();




// [DEBUG] Field: D74File, is_external=, is_static_class=False, static_prefix=
private D74File _D74File = new D74File();




// [DEBUG] Field: D75File, is_external=, is_static_class=False, static_prefix=
private D75File _D75File = new D75File();




// [DEBUG] Field: D76File, is_external=, is_static_class=False, static_prefix=
private D76File _D76File = new D76File();




// [DEBUG] Field: D77File, is_external=, is_static_class=False, static_prefix=
private D77File _D77File = new D77File();




// [DEBUG] Field: D78File, is_external=, is_static_class=False, static_prefix=
private D78File _D78File = new D78File();




// [DEBUG] Field: D79File, is_external=, is_static_class=False, static_prefix=
private D79File _D79File = new D79File();




// [DEBUG] Field: D80File, is_external=, is_static_class=False, static_prefix=
private D80File _D80File = new D80File();




// [DEBUG] Field: D81File, is_external=, is_static_class=False, static_prefix=
private D81File _D81File = new D81File();




// [DEBUG] Field: D82File, is_external=, is_static_class=False, static_prefix=
private D82File _D82File = new D82File();




// [DEBUG] Field: D83File, is_external=, is_static_class=False, static_prefix=
private D83File _D83File = new D83File();




// [DEBUG] Field: D84File, is_external=, is_static_class=False, static_prefix=
private D84File _D84File = new D84File();




// [DEBUG] Field: D85File, is_external=, is_static_class=False, static_prefix=
private D85File _D85File = new D85File();




// [DEBUG] Field: D87File, is_external=, is_static_class=False, static_prefix=
private D87File _D87File = new D87File();




// [DEBUG] Field: D88File, is_external=, is_static_class=False, static_prefix=
private D88File _D88File = new D88File();




// [DEBUG] Field: D89File, is_external=, is_static_class=False, static_prefix=
private D89File _D89File = new D89File();




// [DEBUG] Field: D90File, is_external=, is_static_class=False, static_prefix=
private D90File _D90File = new D90File();




// [DEBUG] Field: D91File, is_external=, is_static_class=False, static_prefix=
private D91File _D91File = new D91File();




// [DEBUG] Field: D92File, is_external=, is_static_class=False, static_prefix=
private D92File _D92File = new D92File();




// [DEBUG] Field: D93File, is_external=, is_static_class=False, static_prefix=
private D93File _D93File = new D93File();




// [DEBUG] Field: D94File, is_external=, is_static_class=False, static_prefix=
private D94File _D94File = new D94File();




// [DEBUG] Field: D95File, is_external=, is_static_class=False, static_prefix=
private D95File _D95File = new D95File();




// [DEBUG] Field: D96File, is_external=, is_static_class=False, static_prefix=
private D96File _D96File = new D96File();




// [DEBUG] Field: D97File, is_external=, is_static_class=False, static_prefix=
private D97File _D97File = new D97File();




// [DEBUG] Field: D98File, is_external=, is_static_class=False, static_prefix=
private D98File _D98File = new D98File();




// [DEBUG] Field: D100File, is_external=, is_static_class=False, static_prefix=
private D100File _D100File = new D100File();




// [DEBUG] Field: D101File, is_external=, is_static_class=False, static_prefix=
private D101File _D101File = new D101File();




// [DEBUG] Field: D102File, is_external=, is_static_class=False, static_prefix=
private D102File _D102File = new D102File();




// [DEBUG] Field: D103File, is_external=, is_static_class=False, static_prefix=
private D103File _D103File = new D103File();




// [DEBUG] Field: D104File, is_external=, is_static_class=False, static_prefix=
private D104File _D104File = new D104File();




// [DEBUG] Field: D105File, is_external=, is_static_class=False, static_prefix=
private D105File _D105File = new D105File();




// [DEBUG] Field: D107File, is_external=, is_static_class=False, static_prefix=
private D107File _D107File = new D107File();




// [DEBUG] Field: D108File, is_external=, is_static_class=False, static_prefix=
private D108File _D108File = new D108File();




// [DEBUG] Field: D109File, is_external=, is_static_class=False, static_prefix=
private D109File _D109File = new D109File();




// [DEBUG] Field: D110File, is_external=, is_static_class=False, static_prefix=
private D110File _D110File = new D110File();




// [DEBUG] Field: D111File, is_external=, is_static_class=False, static_prefix=
private D111File _D111File = new D111File();




// [DEBUG] Field: D159File, is_external=, is_static_class=False, static_prefix=
private D159File _D159File = new D159File();




// [DEBUG] Field: D160File, is_external=, is_static_class=False, static_prefix=
private D160File _D160File = new D160File();




// [DEBUG] Field: D161File, is_external=, is_static_class=False, static_prefix=
private D161File _D161File = new D161File();




// [DEBUG] Field: D164File, is_external=, is_static_class=False, static_prefix=
private D164File _D164File = new D164File();




// [DEBUG] Field: D165File, is_external=, is_static_class=False, static_prefix=
private D165File _D165File = new D165File();




// [DEBUG] Field: D171File, is_external=, is_static_class=False, static_prefix=
private D171File _D171File = new D171File();




// [DEBUG] Field: WStoreAction, is_external=, is_static_class=False, static_prefix=
private string _WStoreAction =" ";




// [DEBUG] Field: WSchedSub, is_external=, is_static_class=False, static_prefix=
private int _WSchedSub =0;


// 88-level condition checks for WSchedSub
public bool IsSubRealised()
{
    if (this._WSchedSub == 1) return true;
    return false;
}
public bool IsSubUnrealised()
{
    if (this._WSchedSub == 2) return true;
    return false;
}
public bool IsSubDeemedDisp()
{
    if (this._WSchedSub == 3) return true;
    return false;
}
public bool IsSubRealisedTax()
{
    if (this._WSchedSub == 4) return true;
    return false;
}
public bool IsSubUnrealisedTax()
{
    if (this._WSchedSub == 5) return true;
    return false;
}


// [DEBUG] Field: SUB_CURRENT_RECORD, is_external=, is_static_class=False, static_prefix=
public const int SUB_CURRENT_RECORD = 6;




// [DEBUG] Field: WSchedRecTable, is_external=, is_static_class=False, static_prefix=
private WSchedRecTable _WSchedRecTable = new WSchedRecTable();




// [DEBUG] Field: Filler334, is_external=, is_static_class=False, static_prefix=
private Filler334 _Filler334 = new Filler334();




// [DEBUG] Field: WRecordRetrieved, is_external=, is_static_class=False, static_prefix=
private int _WRecordRetrieved =0;


// 88-level condition checks for WRecordRetrieved
public bool IsRecordNotRetrieved()
{
    if (this._WRecordRetrieved == 0) return true;
    return false;
}
public bool IsRecordRetrieved()
{
    if (this._WRecordRetrieved == 1) return true;
    return false;
}


// [DEBUG] Field: WReport, is_external=, is_static_class=False, static_prefix=
private string _WReport ="";




// [DEBUG] Field: W80ByteField, is_external=, is_static_class=False, static_prefix=
private string _W80ByteField ="";




// [DEBUG] Field: Cgtdate2LinkageDate1, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate1 _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();




// [DEBUG] Field: Cgtdate2LinkageDate2, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate2 _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();




// [DEBUG] Field: Cgtdate2LinkageDate3, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate3 _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();




// [DEBUG] Field: Cgtdate2LinkageDate4, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate4 _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();




// [DEBUG] Field: Cgtdate2LinkageDate5, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate5 _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();




// [DEBUG] Field: Cgtdate2LinkageDate6, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate6 _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();




// [DEBUG] Field: Cgtdate2LinkageDate7, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate7 _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();




// [DEBUG] Field: Cgtdate2LinkageDate8, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate8 _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();




// [DEBUG] Field: Cgtdate2LinkageDate9, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate9 _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();




// [DEBUG] Field: Cgtdate2LinkageDate10, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate10 _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();




// [DEBUG] Field: EqtpathLinkage, is_external=, is_static_class=False, static_prefix=
private EqtpathLinkage _EqtpathLinkage = new EqtpathLinkage();




// [DEBUG] Field: ADMIN_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string ADMIN_DATA_PATH = "EQADMIN";




// [DEBUG] Field: USER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string USER_DATA_PATH = "EQUSER";




// [DEBUG] Field: MASTER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string MASTER_DATA_PATH = "EQMASTER";




// [DEBUG] Field: EqtdebugLinkage, is_external=, is_static_class=False, static_prefix=
private EqtdebugLinkage _EqtdebugLinkage = new EqtdebugLinkage();




// [DEBUG] Field: TimingLinkage, is_external=, is_static_class=False, static_prefix=
private TimingLinkage _TimingLinkage = new TimingLinkage();




// [DEBUG] Field: CgtlogLinkageArea1, is_external=, is_static_class=False, static_prefix=
private CgtlogLinkageArea1 _CgtlogLinkageArea1 = new CgtlogLinkageArea1();




// [DEBUG] Field: CLEAR_RUN_LOG, is_external=, is_static_class=False, static_prefix=
public const string CLEAR_RUN_LOG = "CRL";




// [DEBUG] Field: CgtlogLinkageArea2, is_external=, is_static_class=False, static_prefix=
private CgtlogLinkageArea2 _CgtlogLinkageArea2 = new CgtlogLinkageArea2();




// [DEBUG] Field: LMessage, is_external=, is_static_class=False, static_prefix=
private string _LMessage ="";


// [DEBUG] Field: IO, is_external=, is_static_class=False, static_prefix=
private string _IO ="";


// [DEBUG] Field: Zero, is_external=, is_static_class=False, static_prefix=
private int _Zero =0;


// [DEBUG] Field: Advancing, is_external=, is_static_class=False, static_prefix=
private int _Advancing =0;


// [DEBUG] Field: D19SplitKey, is_external=, is_static_class=False, static_prefix=
private string _D19SplitKey ="";


// [DEBUG] Field: D20SplitKey, is_external=, is_static_class=False, static_prefix=
private string _D20SplitKey ="";


// [DEBUG] Field: D81SplitKey, is_external=, is_static_class=False, static_prefix=
private string _D81SplitKey ="";


// [DEBUG] Field: D98SplitKey, is_external=, is_static_class=False, static_prefix=
private string _D98SplitKey ="";


// [DEBUG] Field: D101SplitKey, is_external=, is_static_class=False, static_prefix=
private string _D101SplitKey ="";




// Getter and Setter methods

// Standard Getter
public int GetWsMessageNo()
{
    return _WsMessageNo;
}

// Standard Setter
public void SetWsMessageNo(int value)
{
    _WsMessageNo = value;
}

// Get<>AsString()
public string GetWsMessageNoAsString()
{
    return _WsMessageNo.ToString().PadLeft(15, '0');
}

// Set<>AsString()
public void SetWsMessageNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsMessageNo = parsed;
}

// Standard Getter
public string GetWsProcessFlag()
{
    return _WsProcessFlag;
}

// Standard Setter
public void SetWsProcessFlag(string value)
{
    _WsProcessFlag = value;
}

// Get<>AsString()
public string GetWsProcessFlagAsString()
{
    return _WsProcessFlag.PadRight(0);
}

// Set<>AsString()
public void SetWsProcessFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsProcessFlag = value;
}

// Standard Getter
public string GetWsProgramName()
{
    return _WsProgramName;
}

// Standard Setter
public void SetWsProgramName(string value)
{
    _WsProgramName = value;
}

// Get<>AsString()
public string GetWsProgramNameAsString()
{
    return _WsProgramName.PadRight(8);
}

// Set<>AsString()
public void SetWsProgramNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsProgramName = value;
}

// Standard Getter
public string GetLKey()
{
    return _LKey;
}

// Standard Setter
public void SetLKey(string value)
{
    _LKey = value;
}

// Get<>AsString()
public string GetLKeyAsString()
{
    return _LKey.PadRight(100);
}

// Set<>AsString()
public void SetLKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LKey = value;
}

// Standard Getter
public int GetRecLength()
{
    return _RecLength;
}

// Standard Setter
public void SetRecLength(int value)
{
    _RecLength = value;
}

// Get<>AsString()
public string GetRecLengthAsString()
{
    return _RecLength.ToString().PadLeft(5, '0');
}

// Set<>AsString()
public void SetRecLengthAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _RecLength = parsed;
}

// Standard Getter
public int GetWSub()
{
    return _WSub;
}

// Standard Setter
public void SetWSub(int value)
{
    _WSub = value;
}

// Get<>AsString()
public string GetWSubAsString()
{
    return _WSub.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWSubAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WSub = parsed;
}

// Standard Getter
public WScheduleCounters GetWScheduleCounters()
{
    return _WScheduleCounters;
}

// Standard Setter
public void SetWScheduleCounters(WScheduleCounters value)
{
    _WScheduleCounters = value;
}

// Get<>AsString()
public string GetWScheduleCountersAsString()
{
    return _WScheduleCounters != null ? _WScheduleCounters.GetWScheduleCountersAsString() : "";
}

// Set<>AsString()
public void SetWScheduleCountersAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WScheduleCounters == null)
    {
        _WScheduleCounters = new WScheduleCounters();
    }
    _WScheduleCounters.SetWScheduleCountersAsString(value);
}

// Standard Getter
public FileStatus GetFileStatus()
{
    return _FileStatus;
}

// Standard Setter
public void SetFileStatus(FileStatus value)
{
    _FileStatus = value;
}

// Get<>AsString()
public string GetFileStatusAsString()
{
    return _FileStatus != null ? _FileStatus.GetFileStatusAsString() : "";
}

// Set<>AsString()
public void SetFileStatusAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_FileStatus == null)
    {
        _FileStatus = new FileStatus();
    }
    _FileStatus.SetFileStatusAsString(value);
}

// Standard Getter
public WBinaryField GetWBinaryField()
{
    return _WBinaryField;
}

// Standard Setter
public void SetWBinaryField(WBinaryField value)
{
    _WBinaryField = value;
}

// Get<>AsString()
public string GetWBinaryFieldAsString()
{
    return _WBinaryField != null ? _WBinaryField.GetWBinaryFieldAsString() : "";
}

// Set<>AsString()
public void SetWBinaryFieldAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WBinaryField == null)
    {
        _WBinaryField = new WBinaryField();
    }
    _WBinaryField.SetWBinaryFieldAsString(value);
}

// Standard Getter
public int GetWBinaryStatus2()
{
    return _WBinaryStatus2;
}

// Standard Setter
public void SetWBinaryStatus2(int value)
{
    _WBinaryStatus2 = value;
}

// Get<>AsString()
public string GetWBinaryStatus2AsString()
{
    return _WBinaryStatus2.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWBinaryStatus2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WBinaryStatus2 = parsed;
}

// Standard Getter
public WPrintRecord GetWPrintRecord()
{
    return _WPrintRecord;
}

// Standard Setter
public void SetWPrintRecord(WPrintRecord value)
{
    _WPrintRecord = value;
}

// Get<>AsString()
public string GetWPrintRecordAsString()
{
    return _WPrintRecord != null ? _WPrintRecord.GetWPrintRecordAsString() : "";
}

// Set<>AsString()
public void SetWPrintRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WPrintRecord == null)
    {
        _WPrintRecord = new WPrintRecord();
    }
    _WPrintRecord.SetWPrintRecordAsString(value);
}

// Standard Getter
public int GetWPrintCharSub()
{
    return _WPrintCharSub;
}

// Standard Setter
public void SetWPrintCharSub(int value)
{
    _WPrintCharSub = value;
}

// Get<>AsString()
public string GetWPrintCharSubAsString()
{
    return _WPrintCharSub.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWPrintCharSubAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WPrintCharSub = parsed;
}

// Standard Getter
public Wd161Record GetWd161Record()
{
    return _Wd161Record;
}

// Standard Setter
public void SetWd161Record(Wd161Record value)
{
    _Wd161Record = value;
}

// Get<>AsString()
public string GetWd161RecordAsString()
{
    return _Wd161Record != null ? _Wd161Record.GetWd161RecordAsString() : "";
}

// Set<>AsString()
public void SetWd161RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Wd161Record == null)
    {
        _Wd161Record = new Wd161Record();
    }
    _Wd161Record.SetWd161RecordAsString(value);
}

// Standard Getter
public int GetWd161CharSub()
{
    return _WD161CharSub;
}

// Standard Setter
public void SetWd161CharSub(int value)
{
    _WD161CharSub = value;
}

// Get<>AsString()
public string GetWd161CharSubAsString()
{
    return _WD161CharSub.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWd161CharSubAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WD161CharSub = parsed;
}

// Standard Getter
public string GetD1FileName()
{
    return _D1FileName;
}

// Standard Setter
public void SetD1FileName(string value)
{
    _D1FileName = value;
}

// Get<>AsString()
public string GetD1FileNameAsString()
{
    return _D1FileName.PadRight(256);
}

// Set<>AsString()
public void SetD1FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D1FileName = value;
}

// Standard Getter
public string GetD2FileName()
{
    return _D2FileName;
}

// Standard Setter
public void SetD2FileName(string value)
{
    _D2FileName = value;
}

// Get<>AsString()
public string GetD2FileNameAsString()
{
    return _D2FileName.PadRight(256);
}

// Set<>AsString()
public void SetD2FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D2FileName = value;
}

// Standard Getter
public string GetD3FileName()
{
    return _D3FileName;
}

// Standard Setter
public void SetD3FileName(string value)
{
    _D3FileName = value;
}

// Get<>AsString()
public string GetD3FileNameAsString()
{
    return _D3FileName.PadRight(256);
}

// Set<>AsString()
public void SetD3FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D3FileName = value;
}

// Standard Getter
public string GetD4FileName()
{
    return _D4FileName;
}

// Standard Setter
public void SetD4FileName(string value)
{
    _D4FileName = value;
}

// Get<>AsString()
public string GetD4FileNameAsString()
{
    return _D4FileName.PadRight(256);
}

// Set<>AsString()
public void SetD4FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4FileName = value;
}

// Standard Getter
public string GetD5FileName()
{
    return _D5FileName;
}

// Standard Setter
public void SetD5FileName(string value)
{
    _D5FileName = value;
}

// Get<>AsString()
public string GetD5FileNameAsString()
{
    return _D5FileName.PadRight(256);
}

// Set<>AsString()
public void SetD5FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D5FileName = value;
}

// Standard Getter
public string GetD6FileName()
{
    return _D6FileName;
}

// Standard Setter
public void SetD6FileName(string value)
{
    _D6FileName = value;
}

// Get<>AsString()
public string GetD6FileNameAsString()
{
    return _D6FileName.PadRight(256);
}

// Set<>AsString()
public void SetD6FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D6FileName = value;
}

// Standard Getter
public string GetD7FileName()
{
    return _D7FileName;
}

// Standard Setter
public void SetD7FileName(string value)
{
    _D7FileName = value;
}

// Get<>AsString()
public string GetD7FileNameAsString()
{
    return _D7FileName.PadRight(256);
}

// Set<>AsString()
public void SetD7FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D7FileName = value;
}

// Standard Getter
public string GetD8FileName()
{
    return _D8FileName;
}

// Standard Setter
public void SetD8FileName(string value)
{
    _D8FileName = value;
}

// Get<>AsString()
public string GetD8FileNameAsString()
{
    return _D8FileName.PadRight(256);
}

// Set<>AsString()
public void SetD8FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8FileName = value;
}

// Standard Getter
public string GetD9FileName()
{
    return _D9FileName;
}

// Standard Setter
public void SetD9FileName(string value)
{
    _D9FileName = value;
}

// Get<>AsString()
public string GetD9FileNameAsString()
{
    return _D9FileName.PadRight(256);
}

// Set<>AsString()
public void SetD9FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D9FileName = value;
}

// Standard Getter
public string GetD10FileName()
{
    return _D10FileName;
}

// Standard Setter
public void SetD10FileName(string value)
{
    _D10FileName = value;
}

// Get<>AsString()
public string GetD10FileNameAsString()
{
    return _D10FileName.PadRight(256);
}

// Set<>AsString()
public void SetD10FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D10FileName = value;
}

// Standard Getter
public string GetD11FileName()
{
    return _D11FileName;
}

// Standard Setter
public void SetD11FileName(string value)
{
    _D11FileName = value;
}

// Get<>AsString()
public string GetD11FileNameAsString()
{
    return _D11FileName.PadRight(256);
}

// Set<>AsString()
public void SetD11FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D11FileName = value;
}

// Standard Getter
public string GetD12FileName()
{
    return _D12FileName;
}

// Standard Setter
public void SetD12FileName(string value)
{
    _D12FileName = value;
}

// Get<>AsString()
public string GetD12FileNameAsString()
{
    return _D12FileName.PadRight(256);
}

// Set<>AsString()
public void SetD12FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D12FileName = value;
}

// Standard Getter
public string GetD17FileName()
{
    return _D17FileName;
}

// Standard Setter
public void SetD17FileName(string value)
{
    _D17FileName = value;
}

// Get<>AsString()
public string GetD17FileNameAsString()
{
    return _D17FileName.PadRight(256);
}

// Set<>AsString()
public void SetD17FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D17FileName = value;
}

// Standard Getter
public string GetD18FileName()
{
    return _D18FileName;
}

// Standard Setter
public void SetD18FileName(string value)
{
    _D18FileName = value;
}

// Get<>AsString()
public string GetD18FileNameAsString()
{
    return _D18FileName.PadRight(256);
}

// Set<>AsString()
public void SetD18FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D18FileName = value;
}

// Standard Getter
public string GetD19FileName()
{
    return _D19FileName;
}

// Standard Setter
public void SetD19FileName(string value)
{
    _D19FileName = value;
}

// Get<>AsString()
public string GetD19FileNameAsString()
{
    return _D19FileName.PadRight(256);
}

// Set<>AsString()
public void SetD19FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D19FileName = value;
}

// Standard Getter
public string GetD20FileName()
{
    return _D20FileName;
}

// Standard Setter
public void SetD20FileName(string value)
{
    _D20FileName = value;
}

// Get<>AsString()
public string GetD20FileNameAsString()
{
    return _D20FileName.PadRight(256);
}

// Set<>AsString()
public void SetD20FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D20FileName = value;
}

// Standard Getter
public string GetD21FileName()
{
    return _D21FileName;
}

// Standard Setter
public void SetD21FileName(string value)
{
    _D21FileName = value;
}

// Get<>AsString()
public string GetD21FileNameAsString()
{
    return _D21FileName.PadRight(256);
}

// Set<>AsString()
public void SetD21FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D21FileName = value;
}

// Standard Getter
public string GetD22FileName()
{
    return _D22FileName;
}

// Standard Setter
public void SetD22FileName(string value)
{
    _D22FileName = value;
}

// Get<>AsString()
public string GetD22FileNameAsString()
{
    return _D22FileName.PadRight(256);
}

// Set<>AsString()
public void SetD22FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D22FileName = value;
}

// Standard Getter
public string GetD23FileName()
{
    return _D23FileName;
}

// Standard Setter
public void SetD23FileName(string value)
{
    _D23FileName = value;
}

// Get<>AsString()
public string GetD23FileNameAsString()
{
    return _D23FileName.PadRight(256);
}

// Set<>AsString()
public void SetD23FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D23FileName = value;
}

// Standard Getter
public string GetD24FileName()
{
    return _D24FileName;
}

// Standard Setter
public void SetD24FileName(string value)
{
    _D24FileName = value;
}

// Get<>AsString()
public string GetD24FileNameAsString()
{
    return _D24FileName.PadRight(256);
}

// Set<>AsString()
public void SetD24FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D24FileName = value;
}

// Standard Getter
public string GetD25FileName()
{
    return _D25FileName;
}

// Standard Setter
public void SetD25FileName(string value)
{
    _D25FileName = value;
}

// Get<>AsString()
public string GetD25FileNameAsString()
{
    return _D25FileName.PadRight(256);
}

// Set<>AsString()
public void SetD25FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D25FileName = value;
}

// Standard Getter
public string GetD26FileName()
{
    return _D26FileName;
}

// Standard Setter
public void SetD26FileName(string value)
{
    _D26FileName = value;
}

// Get<>AsString()
public string GetD26FileNameAsString()
{
    return _D26FileName.PadRight(256);
}

// Set<>AsString()
public void SetD26FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D26FileName = value;
}

// Standard Getter
public string GetD27FileName()
{
    return _D27FileName;
}

// Standard Setter
public void SetD27FileName(string value)
{
    _D27FileName = value;
}

// Get<>AsString()
public string GetD27FileNameAsString()
{
    return _D27FileName.PadRight(256);
}

// Set<>AsString()
public void SetD27FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D27FileName = value;
}

// Standard Getter
public string GetD28FileName()
{
    return _D28FileName;
}

// Standard Setter
public void SetD28FileName(string value)
{
    _D28FileName = value;
}

// Get<>AsString()
public string GetD28FileNameAsString()
{
    return _D28FileName.PadRight(256);
}

// Set<>AsString()
public void SetD28FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D28FileName = value;
}

// Standard Getter
public string GetD29FileName()
{
    return _D29FileName;
}

// Standard Setter
public void SetD29FileName(string value)
{
    _D29FileName = value;
}

// Get<>AsString()
public string GetD29FileNameAsString()
{
    return _D29FileName.PadRight(256);
}

// Set<>AsString()
public void SetD29FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D29FileName = value;
}

// Standard Getter
public string GetD30FileName()
{
    return _D30FileName;
}

// Standard Setter
public void SetD30FileName(string value)
{
    _D30FileName = value;
}

// Get<>AsString()
public string GetD30FileNameAsString()
{
    return _D30FileName.PadRight(256);
}

// Set<>AsString()
public void SetD30FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D30FileName = value;
}

// Standard Getter
public string GetD31FileName()
{
    return _D31FileName;
}

// Standard Setter
public void SetD31FileName(string value)
{
    _D31FileName = value;
}

// Get<>AsString()
public string GetD31FileNameAsString()
{
    return _D31FileName.PadRight(256);
}

// Set<>AsString()
public void SetD31FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31FileName = value;
}

// Standard Getter
public string GetD32FileName()
{
    return _D32FileName;
}

// Standard Setter
public void SetD32FileName(string value)
{
    _D32FileName = value;
}

// Get<>AsString()
public string GetD32FileNameAsString()
{
    return _D32FileName.PadRight(256);
}

// Set<>AsString()
public void SetD32FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D32FileName = value;
}

// Standard Getter
public string GetD33FileName()
{
    return _D33FileName;
}

// Standard Setter
public void SetD33FileName(string value)
{
    _D33FileName = value;
}

// Get<>AsString()
public string GetD33FileNameAsString()
{
    return _D33FileName.PadRight(256);
}

// Set<>AsString()
public void SetD33FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D33FileName = value;
}

// Standard Getter
public string GetD34FileName()
{
    return _D34FileName;
}

// Standard Setter
public void SetD34FileName(string value)
{
    _D34FileName = value;
}

// Get<>AsString()
public string GetD34FileNameAsString()
{
    return _D34FileName.PadRight(256);
}

// Set<>AsString()
public void SetD34FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D34FileName = value;
}

// Standard Getter
public string GetD35FileName()
{
    return _D35FileName;
}

// Standard Setter
public void SetD35FileName(string value)
{
    _D35FileName = value;
}

// Get<>AsString()
public string GetD35FileNameAsString()
{
    return _D35FileName.PadRight(256);
}

// Set<>AsString()
public void SetD35FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D35FileName = value;
}

// Standard Getter
public string GetD36FileName()
{
    return _D36FileName;
}

// Standard Setter
public void SetD36FileName(string value)
{
    _D36FileName = value;
}

// Get<>AsString()
public string GetD36FileNameAsString()
{
    return _D36FileName.PadRight(256);
}

// Set<>AsString()
public void SetD36FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D36FileName = value;
}

// Standard Getter
public string GetD38FileName()
{
    return _D38FileName;
}

// Standard Setter
public void SetD38FileName(string value)
{
    _D38FileName = value;
}

// Get<>AsString()
public string GetD38FileNameAsString()
{
    return _D38FileName.PadRight(256);
}

// Set<>AsString()
public void SetD38FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D38FileName = value;
}

// Standard Getter
public string GetD39FileName()
{
    return _D39FileName;
}

// Standard Setter
public void SetD39FileName(string value)
{
    _D39FileName = value;
}

// Get<>AsString()
public string GetD39FileNameAsString()
{
    return _D39FileName.PadRight(256);
}

// Set<>AsString()
public void SetD39FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D39FileName = value;
}

// Standard Getter
public string GetD40FileName()
{
    return _D40FileName;
}

// Standard Setter
public void SetD40FileName(string value)
{
    _D40FileName = value;
}

// Get<>AsString()
public string GetD40FileNameAsString()
{
    return _D40FileName.PadRight(256);
}

// Set<>AsString()
public void SetD40FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D40FileName = value;
}

// Standard Getter
public string GetD41FileName()
{
    return _D41FileName;
}

// Standard Setter
public void SetD41FileName(string value)
{
    _D41FileName = value;
}

// Get<>AsString()
public string GetD41FileNameAsString()
{
    return _D41FileName.PadRight(256);
}

// Set<>AsString()
public void SetD41FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D41FileName = value;
}

// Standard Getter
public string GetD42FileName()
{
    return _D42FileName;
}

// Standard Setter
public void SetD42FileName(string value)
{
    _D42FileName = value;
}

// Get<>AsString()
public string GetD42FileNameAsString()
{
    return _D42FileName.PadRight(256);
}

// Set<>AsString()
public void SetD42FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D42FileName = value;
}

// Standard Getter
public string GetD43FileName()
{
    return _D43FileName;
}

// Standard Setter
public void SetD43FileName(string value)
{
    _D43FileName = value;
}

// Get<>AsString()
public string GetD43FileNameAsString()
{
    return _D43FileName.PadRight(256);
}

// Set<>AsString()
public void SetD43FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D43FileName = value;
}

// Standard Getter
public string GetD44FileName()
{
    return _D44FileName;
}

// Standard Setter
public void SetD44FileName(string value)
{
    _D44FileName = value;
}

// Get<>AsString()
public string GetD44FileNameAsString()
{
    return _D44FileName.PadRight(256);
}

// Set<>AsString()
public void SetD44FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D44FileName = value;
}

// Standard Getter
public string GetD45FileName()
{
    return _D45FileName;
}

// Standard Setter
public void SetD45FileName(string value)
{
    _D45FileName = value;
}

// Get<>AsString()
public string GetD45FileNameAsString()
{
    return _D45FileName.PadRight(256);
}

// Set<>AsString()
public void SetD45FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D45FileName = value;
}

// Standard Getter
public string GetD46FileName()
{
    return _D46FileName;
}

// Standard Setter
public void SetD46FileName(string value)
{
    _D46FileName = value;
}

// Get<>AsString()
public string GetD46FileNameAsString()
{
    return _D46FileName.PadRight(256);
}

// Set<>AsString()
public void SetD46FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D46FileName = value;
}

// Standard Getter
public string GetD49FileName()
{
    return _D49FileName;
}

// Standard Setter
public void SetD49FileName(string value)
{
    _D49FileName = value;
}

// Get<>AsString()
public string GetD49FileNameAsString()
{
    return _D49FileName.PadRight(256);
}

// Set<>AsString()
public void SetD49FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D49FileName = value;
}

// Standard Getter
public string GetD50FileName()
{
    return _D50FileName;
}

// Standard Setter
public void SetD50FileName(string value)
{
    _D50FileName = value;
}

// Get<>AsString()
public string GetD50FileNameAsString()
{
    return _D50FileName.PadRight(256);
}

// Set<>AsString()
public void SetD50FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D50FileName = value;
}

// Standard Getter
public string GetD51FileName()
{
    return _D51FileName;
}

// Standard Setter
public void SetD51FileName(string value)
{
    _D51FileName = value;
}

// Get<>AsString()
public string GetD51FileNameAsString()
{
    return _D51FileName.PadRight(256);
}

// Set<>AsString()
public void SetD51FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D51FileName = value;
}

// Standard Getter
public string GetD68FileName()
{
    return _D68FileName;
}

// Standard Setter
public void SetD68FileName(string value)
{
    _D68FileName = value;
}

// Get<>AsString()
public string GetD68FileNameAsString()
{
    return _D68FileName.PadRight(256);
}

// Set<>AsString()
public void SetD68FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D68FileName = value;
}

// Standard Getter
public string GetD69FileName()
{
    return _D69FileName;
}

// Standard Setter
public void SetD69FileName(string value)
{
    _D69FileName = value;
}

// Get<>AsString()
public string GetD69FileNameAsString()
{
    return _D69FileName.PadRight(256);
}

// Set<>AsString()
public void SetD69FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D69FileName = value;
}

// Standard Getter
public string GetD70FileName()
{
    return _D70FileName;
}

// Standard Setter
public void SetD70FileName(string value)
{
    _D70FileName = value;
}

// Get<>AsString()
public string GetD70FileNameAsString()
{
    return _D70FileName.PadRight(256);
}

// Set<>AsString()
public void SetD70FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D70FileName = value;
}

// Standard Getter
public string GetD71FileName()
{
    return _D71FileName;
}

// Standard Setter
public void SetD71FileName(string value)
{
    _D71FileName = value;
}

// Get<>AsString()
public string GetD71FileNameAsString()
{
    return _D71FileName.PadRight(256);
}

// Set<>AsString()
public void SetD71FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D71FileName = value;
}

// Standard Getter
public string GetD72FileName()
{
    return _D72FileName;
}

// Standard Setter
public void SetD72FileName(string value)
{
    _D72FileName = value;
}

// Get<>AsString()
public string GetD72FileNameAsString()
{
    return _D72FileName.PadRight(256);
}

// Set<>AsString()
public void SetD72FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D72FileName = value;
}

// Standard Getter
public string GetD73FileName()
{
    return _D73FileName;
}

// Standard Setter
public void SetD73FileName(string value)
{
    _D73FileName = value;
}

// Get<>AsString()
public string GetD73FileNameAsString()
{
    return _D73FileName.PadRight(256);
}

// Set<>AsString()
public void SetD73FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D73FileName = value;
}

// Standard Getter
public string GetD74FileName()
{
    return _D74FileName;
}

// Standard Setter
public void SetD74FileName(string value)
{
    _D74FileName = value;
}

// Get<>AsString()
public string GetD74FileNameAsString()
{
    return _D74FileName.PadRight(256);
}

// Set<>AsString()
public void SetD74FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D74FileName = value;
}

// Standard Getter
public string GetD75FileName()
{
    return _D75FileName;
}

// Standard Setter
public void SetD75FileName(string value)
{
    _D75FileName = value;
}

// Get<>AsString()
public string GetD75FileNameAsString()
{
    return _D75FileName.PadRight(256);
}

// Set<>AsString()
public void SetD75FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D75FileName = value;
}

// Standard Getter
public string GetD76FileName()
{
    return _D76FileName;
}

// Standard Setter
public void SetD76FileName(string value)
{
    _D76FileName = value;
}

// Get<>AsString()
public string GetD76FileNameAsString()
{
    return _D76FileName.PadRight(256);
}

// Set<>AsString()
public void SetD76FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D76FileName = value;
}

// Standard Getter
public string GetD77FileName()
{
    return _D77FileName;
}

// Standard Setter
public void SetD77FileName(string value)
{
    _D77FileName = value;
}

// Get<>AsString()
public string GetD77FileNameAsString()
{
    return _D77FileName.PadRight(256);
}

// Set<>AsString()
public void SetD77FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D77FileName = value;
}

// Standard Getter
public string GetD78FileName()
{
    return _D78FileName;
}

// Standard Setter
public void SetD78FileName(string value)
{
    _D78FileName = value;
}

// Get<>AsString()
public string GetD78FileNameAsString()
{
    return _D78FileName.PadRight(256);
}

// Set<>AsString()
public void SetD78FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D78FileName = value;
}

// Standard Getter
public string GetD79FileName()
{
    return _D79FileName;
}

// Standard Setter
public void SetD79FileName(string value)
{
    _D79FileName = value;
}

// Get<>AsString()
public string GetD79FileNameAsString()
{
    return _D79FileName.PadRight(256);
}

// Set<>AsString()
public void SetD79FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D79FileName = value;
}

// Standard Getter
public string GetD80FileName()
{
    return _D80FileName;
}

// Standard Setter
public void SetD80FileName(string value)
{
    _D80FileName = value;
}

// Get<>AsString()
public string GetD80FileNameAsString()
{
    return _D80FileName.PadRight(256);
}

// Set<>AsString()
public void SetD80FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D80FileName = value;
}

// Standard Getter
public string GetD81FileName()
{
    return _D81FileName;
}

// Standard Setter
public void SetD81FileName(string value)
{
    _D81FileName = value;
}

// Get<>AsString()
public string GetD81FileNameAsString()
{
    return _D81FileName.PadRight(256);
}

// Set<>AsString()
public void SetD81FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81FileName = value;
}

// Standard Getter
public string GetD82FileName()
{
    return _D82FileName;
}

// Standard Setter
public void SetD82FileName(string value)
{
    _D82FileName = value;
}

// Get<>AsString()
public string GetD82FileNameAsString()
{
    return _D82FileName.PadRight(256);
}

// Set<>AsString()
public void SetD82FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D82FileName = value;
}

// Standard Getter
public string GetD83FileName()
{
    return _D83FileName;
}

// Standard Setter
public void SetD83FileName(string value)
{
    _D83FileName = value;
}

// Get<>AsString()
public string GetD83FileNameAsString()
{
    return _D83FileName.PadRight(256);
}

// Set<>AsString()
public void SetD83FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D83FileName = value;
}

// Standard Getter
public string GetD84FileName()
{
    return _D84FileName;
}

// Standard Setter
public void SetD84FileName(string value)
{
    _D84FileName = value;
}

// Get<>AsString()
public string GetD84FileNameAsString()
{
    return _D84FileName.PadRight(256);
}

// Set<>AsString()
public void SetD84FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D84FileName = value;
}

// Standard Getter
public string GetD85FileName()
{
    return _D85FileName;
}

// Standard Setter
public void SetD85FileName(string value)
{
    _D85FileName = value;
}

// Get<>AsString()
public string GetD85FileNameAsString()
{
    return _D85FileName.PadRight(256);
}

// Set<>AsString()
public void SetD85FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D85FileName = value;
}

// Standard Getter
public string GetD86FileName()
{
    return _D86FileName;
}

// Standard Setter
public void SetD86FileName(string value)
{
    _D86FileName = value;
}

// Get<>AsString()
public string GetD86FileNameAsString()
{
    return _D86FileName.PadRight(256);
}

// Set<>AsString()
public void SetD86FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D86FileName = value;
}

// Standard Getter
public string GetD87FileName()
{
    return _D87FileName;
}

// Standard Setter
public void SetD87FileName(string value)
{
    _D87FileName = value;
}

// Get<>AsString()
public string GetD87FileNameAsString()
{
    return _D87FileName.PadRight(256);
}

// Set<>AsString()
public void SetD87FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D87FileName = value;
}

// Standard Getter
public string GetD88FileName()
{
    return _D88FileName;
}

// Standard Setter
public void SetD88FileName(string value)
{
    _D88FileName = value;
}

// Get<>AsString()
public string GetD88FileNameAsString()
{
    return _D88FileName.PadRight(256);
}

// Set<>AsString()
public void SetD88FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D88FileName = value;
}

// Standard Getter
public string GetD89FileName()
{
    return _D89FileName;
}

// Standard Setter
public void SetD89FileName(string value)
{
    _D89FileName = value;
}

// Get<>AsString()
public string GetD89FileNameAsString()
{
    return _D89FileName.PadRight(256);
}

// Set<>AsString()
public void SetD89FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D89FileName = value;
}

// Standard Getter
public string GetD90FileName()
{
    return _D90FileName;
}

// Standard Setter
public void SetD90FileName(string value)
{
    _D90FileName = value;
}

// Get<>AsString()
public string GetD90FileNameAsString()
{
    return _D90FileName.PadRight(256);
}

// Set<>AsString()
public void SetD90FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D90FileName = value;
}

// Standard Getter
public string GetD91FileName()
{
    return _D91FileName;
}

// Standard Setter
public void SetD91FileName(string value)
{
    _D91FileName = value;
}

// Get<>AsString()
public string GetD91FileNameAsString()
{
    return _D91FileName.PadRight(256);
}

// Set<>AsString()
public void SetD91FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D91FileName = value;
}

// Standard Getter
public string GetD92FileName()
{
    return _D92FileName;
}

// Standard Setter
public void SetD92FileName(string value)
{
    _D92FileName = value;
}

// Get<>AsString()
public string GetD92FileNameAsString()
{
    return _D92FileName.PadRight(256);
}

// Set<>AsString()
public void SetD92FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D92FileName = value;
}

// Standard Getter
public string GetD93FileName()
{
    return _D93FileName;
}

// Standard Setter
public void SetD93FileName(string value)
{
    _D93FileName = value;
}

// Get<>AsString()
public string GetD93FileNameAsString()
{
    return _D93FileName.PadRight(256);
}

// Set<>AsString()
public void SetD93FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D93FileName = value;
}

// Standard Getter
public string GetD94FileName()
{
    return _D94FileName;
}

// Standard Setter
public void SetD94FileName(string value)
{
    _D94FileName = value;
}

// Get<>AsString()
public string GetD94FileNameAsString()
{
    return _D94FileName.PadRight(256);
}

// Set<>AsString()
public void SetD94FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D94FileName = value;
}

// Standard Getter
public string GetD95FileName()
{
    return _D95FileName;
}

// Standard Setter
public void SetD95FileName(string value)
{
    _D95FileName = value;
}

// Get<>AsString()
public string GetD95FileNameAsString()
{
    return _D95FileName.PadRight(256);
}

// Set<>AsString()
public void SetD95FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D95FileName = value;
}

// Standard Getter
public string GetD96FileName()
{
    return _D96FileName;
}

// Standard Setter
public void SetD96FileName(string value)
{
    _D96FileName = value;
}

// Get<>AsString()
public string GetD96FileNameAsString()
{
    return _D96FileName.PadRight(256);
}

// Set<>AsString()
public void SetD96FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D96FileName = value;
}

// Standard Getter
public string GetD97FileName()
{
    return _D97FileName;
}

// Standard Setter
public void SetD97FileName(string value)
{
    _D97FileName = value;
}

// Get<>AsString()
public string GetD97FileNameAsString()
{
    return _D97FileName.PadRight(256);
}

// Set<>AsString()
public void SetD97FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D97FileName = value;
}

// Standard Getter
public string GetD98FileName()
{
    return _D98FileName;
}

// Standard Setter
public void SetD98FileName(string value)
{
    _D98FileName = value;
}

// Get<>AsString()
public string GetD98FileNameAsString()
{
    return _D98FileName.PadRight(256);
}

// Set<>AsString()
public void SetD98FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98FileName = value;
}

// Standard Getter
public string GetD100FileName()
{
    return _D100FileName;
}

// Standard Setter
public void SetD100FileName(string value)
{
    _D100FileName = value;
}

// Get<>AsString()
public string GetD100FileNameAsString()
{
    return _D100FileName.PadRight(256);
}

// Set<>AsString()
public void SetD100FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D100FileName = value;
}

// Standard Getter
public string GetD101FileName()
{
    return _D101FileName;
}

// Standard Setter
public void SetD101FileName(string value)
{
    _D101FileName = value;
}

// Get<>AsString()
public string GetD101FileNameAsString()
{
    return _D101FileName.PadRight(256);
}

// Set<>AsString()
public void SetD101FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D101FileName = value;
}

// Standard Getter
public string GetD102FileName()
{
    return _D102FileName;
}

// Standard Setter
public void SetD102FileName(string value)
{
    _D102FileName = value;
}

// Get<>AsString()
public string GetD102FileNameAsString()
{
    return _D102FileName.PadRight(256);
}

// Set<>AsString()
public void SetD102FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D102FileName = value;
}

// Standard Getter
public string GetD103FileName()
{
    return _D103FileName;
}

// Standard Setter
public void SetD103FileName(string value)
{
    _D103FileName = value;
}

// Get<>AsString()
public string GetD103FileNameAsString()
{
    return _D103FileName.PadRight(256);
}

// Set<>AsString()
public void SetD103FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D103FileName = value;
}

// Standard Getter
public string GetD104FileName()
{
    return _D104FileName;
}

// Standard Setter
public void SetD104FileName(string value)
{
    _D104FileName = value;
}

// Get<>AsString()
public string GetD104FileNameAsString()
{
    return _D104FileName.PadRight(256);
}

// Set<>AsString()
public void SetD104FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D104FileName = value;
}

// Standard Getter
public string GetD105FileName()
{
    return _D105FileName;
}

// Standard Setter
public void SetD105FileName(string value)
{
    _D105FileName = value;
}

// Get<>AsString()
public string GetD105FileNameAsString()
{
    return _D105FileName.PadRight(256);
}

// Set<>AsString()
public void SetD105FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D105FileName = value;
}

// Standard Getter
public string GetFiller158()
{
    return _Filler158;
}

// Standard Setter
public void SetFiller158(string value)
{
    _Filler158 = value;
}

// Get<>AsString()
public string GetFiller158AsString()
{
    return _Filler158.PadRight(256);
}

// Set<>AsString()
public void SetFiller158AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler158 = value;
}

// Standard Getter
public string GetD107FileName()
{
    return _D107FileName;
}

// Standard Setter
public void SetD107FileName(string value)
{
    _D107FileName = value;
}

// Get<>AsString()
public string GetD107FileNameAsString()
{
    return _D107FileName.PadRight(256);
}

// Set<>AsString()
public void SetD107FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D107FileName = value;
}

// Standard Getter
public string GetD108FileName()
{
    return _D108FileName;
}

// Standard Setter
public void SetD108FileName(string value)
{
    _D108FileName = value;
}

// Get<>AsString()
public string GetD108FileNameAsString()
{
    return _D108FileName.PadRight(256);
}

// Set<>AsString()
public void SetD108FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D108FileName = value;
}

// Standard Getter
public string GetD109FileName()
{
    return _D109FileName;
}

// Standard Setter
public void SetD109FileName(string value)
{
    _D109FileName = value;
}

// Get<>AsString()
public string GetD109FileNameAsString()
{
    return _D109FileName.PadRight(256);
}

// Set<>AsString()
public void SetD109FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D109FileName = value;
}

// Standard Getter
public string GetD110FileName()
{
    return _D110FileName;
}

// Standard Setter
public void SetD110FileName(string value)
{
    _D110FileName = value;
}

// Get<>AsString()
public string GetD110FileNameAsString()
{
    return _D110FileName.PadRight(256);
}

// Set<>AsString()
public void SetD110FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D110FileName = value;
}

// Standard Getter
public string GetD111FileName()
{
    return _D111FileName;
}

// Standard Setter
public void SetD111FileName(string value)
{
    _D111FileName = value;
}

// Get<>AsString()
public string GetD111FileNameAsString()
{
    return _D111FileName.PadRight(256);
}

// Set<>AsString()
public void SetD111FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D111FileName = value;
}

// Standard Getter
public string GetD112FileName()
{
    return _D112FileName;
}

// Standard Setter
public void SetD112FileName(string value)
{
    _D112FileName = value;
}

// Get<>AsString()
public string GetD112FileNameAsString()
{
    return _D112FileName.PadRight(256);
}

// Set<>AsString()
public void SetD112FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D112FileName = value;
}

// Standard Getter
public string GetD133FileName()
{
    return _D133FileName;
}

// Standard Setter
public void SetD133FileName(string value)
{
    _D133FileName = value;
}

// Get<>AsString()
public string GetD133FileNameAsString()
{
    return _D133FileName.PadRight(256);
}

// Set<>AsString()
public void SetD133FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D133FileName = value;
}

// Standard Getter
public string GetD153FileName()
{
    return _D153FileName;
}

// Standard Setter
public void SetD153FileName(string value)
{
    _D153FileName = value;
}

// Get<>AsString()
public string GetD153FileNameAsString()
{
    return _D153FileName.PadRight(256);
}

// Set<>AsString()
public void SetD153FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D153FileName = value;
}

// Standard Getter
public string GetD154FileName()
{
    return _D154FileName;
}

// Standard Setter
public void SetD154FileName(string value)
{
    _D154FileName = value;
}

// Get<>AsString()
public string GetD154FileNameAsString()
{
    return _D154FileName.PadRight(256);
}

// Set<>AsString()
public void SetD154FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D154FileName = value;
}

// Standard Getter
public string GetD159FileName()
{
    return _D159FileName;
}

// Standard Setter
public void SetD159FileName(string value)
{
    _D159FileName = value;
}

// Get<>AsString()
public string GetD159FileNameAsString()
{
    return _D159FileName.PadRight(256);
}

// Set<>AsString()
public void SetD159FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D159FileName = value;
}

// Standard Getter
public string GetD160FileName()
{
    return _D160FileName;
}

// Standard Setter
public void SetD160FileName(string value)
{
    _D160FileName = value;
}

// Get<>AsString()
public string GetD160FileNameAsString()
{
    return _D160FileName.PadRight(256);
}

// Set<>AsString()
public void SetD160FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D160FileName = value;
}

// Standard Getter
public string GetD161FileName()
{
    return _D161FileName;
}

// Standard Setter
public void SetD161FileName(string value)
{
    _D161FileName = value;
}

// Get<>AsString()
public string GetD161FileNameAsString()
{
    return _D161FileName.PadRight(256);
}

// Set<>AsString()
public void SetD161FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D161FileName = value;
}

// Standard Getter
public string GetD163FileName()
{
    return _D163FileName;
}

// Standard Setter
public void SetD163FileName(string value)
{
    _D163FileName = value;
}

// Get<>AsString()
public string GetD163FileNameAsString()
{
    return _D163FileName.PadRight(256);
}

// Set<>AsString()
public void SetD163FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D163FileName = value;
}

// Standard Getter
public string GetD164FileName()
{
    return _D164FileName;
}

// Standard Setter
public void SetD164FileName(string value)
{
    _D164FileName = value;
}

// Get<>AsString()
public string GetD164FileNameAsString()
{
    return _D164FileName.PadRight(256);
}

// Set<>AsString()
public void SetD164FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D164FileName = value;
}

// Standard Getter
public string GetD165FileName()
{
    return _D165FileName;
}

// Standard Setter
public void SetD165FileName(string value)
{
    _D165FileName = value;
}

// Get<>AsString()
public string GetD165FileNameAsString()
{
    return _D165FileName.PadRight(256);
}

// Set<>AsString()
public void SetD165FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D165FileName = value;
}

// Standard Getter
public string GetD167FileName()
{
    return _D167FileName;
}

// Standard Setter
public void SetD167FileName(string value)
{
    _D167FileName = value;
}

// Get<>AsString()
public string GetD167FileNameAsString()
{
    return _D167FileName.PadRight(256);
}

// Set<>AsString()
public void SetD167FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D167FileName = value;
}

// Standard Getter
public string GetD169FileName()
{
    return _D169FileName;
}

// Standard Setter
public void SetD169FileName(string value)
{
    _D169FileName = value;
}

// Get<>AsString()
public string GetD169FileNameAsString()
{
    return _D169FileName.PadRight(256);
}

// Set<>AsString()
public void SetD169FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D169FileName = value;
}

// Standard Getter
public string GetD170FileName()
{
    return _D170FileName;
}

// Standard Setter
public void SetD170FileName(string value)
{
    _D170FileName = value;
}

// Get<>AsString()
public string GetD170FileNameAsString()
{
    return _D170FileName.PadRight(256);
}

// Set<>AsString()
public void SetD170FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D170FileName = value;
}

// Standard Getter
public string GetD171FileName()
{
    return _D171FileName;
}

// Standard Setter
public void SetD171FileName(string value)
{
    _D171FileName = value;
}

// Get<>AsString()
public string GetD171FileNameAsString()
{
    return _D171FileName.PadRight(256);
}

// Set<>AsString()
public void SetD171FileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D171FileName = value;
}

// Standard Getter
public string GetD1File()
{
    return _D1File;
}

// Standard Setter
public void SetD1File(string value)
{
    _D1File = value;
}

// Get<>AsString()
public string GetD1FileAsString()
{
    return _D1File.PadRight(12);
}

// Set<>AsString()
public void SetD1FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D1File = value;
}

// Standard Getter
public string GetD2File()
{
    return _D2File;
}

// Standard Setter
public void SetD2File(string value)
{
    _D2File = value;
}

// Get<>AsString()
public string GetD2FileAsString()
{
    return _D2File.PadRight(12);
}

// Set<>AsString()
public void SetD2FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D2File = value;
}

// Standard Getter
public string GetD3File()
{
    return _D3File;
}

// Standard Setter
public void SetD3File(string value)
{
    _D3File = value;
}

// Get<>AsString()
public string GetD3FileAsString()
{
    return _D3File.PadRight(12);
}

// Set<>AsString()
public void SetD3FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D3File = value;
}

// Standard Getter
public string GetD4File()
{
    return _D4File;
}

// Standard Setter
public void SetD4File(string value)
{
    _D4File = value;
}

// Get<>AsString()
public string GetD4FileAsString()
{
    return _D4File.PadRight(12);
}

// Set<>AsString()
public void SetD4FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4File = value;
}

// Standard Getter
public string GetD7File()
{
    return _D7File;
}

// Standard Setter
public void SetD7File(string value)
{
    _D7File = value;
}

// Get<>AsString()
public string GetD7FileAsString()
{
    return _D7File.PadRight(12);
}

// Set<>AsString()
public void SetD7FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D7File = value;
}

// Standard Getter
public string GetD8File()
{
    return _D8File;
}

// Standard Setter
public void SetD8File(string value)
{
    _D8File = value;
}

// Get<>AsString()
public string GetD8FileAsString()
{
    return _D8File.PadRight(12);
}

// Set<>AsString()
public void SetD8FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8File = value;
}

// Standard Getter
public string GetD9File()
{
    return _D9File;
}

// Standard Setter
public void SetD9File(string value)
{
    _D9File = value;
}

// Get<>AsString()
public string GetD9FileAsString()
{
    return _D9File.PadRight(12);
}

// Set<>AsString()
public void SetD9FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D9File = value;
}

// Standard Getter
public string GetD17File()
{
    return _D17File;
}

// Standard Setter
public void SetD17File(string value)
{
    _D17File = value;
}

// Get<>AsString()
public string GetD17FileAsString()
{
    return _D17File.PadRight(12);
}

// Set<>AsString()
public void SetD17FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D17File = value;
}

// Standard Getter
public string GetD31File()
{
    return _D31File;
}

// Standard Setter
public void SetD31File(string value)
{
    _D31File = value;
}

// Get<>AsString()
public string GetD31FileAsString()
{
    return _D31File.PadRight(12);
}

// Set<>AsString()
public void SetD31FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D31File = value;
}

// Standard Getter
public string GetD32File()
{
    return _D32File;
}

// Standard Setter
public void SetD32File(string value)
{
    _D32File = value;
}

// Get<>AsString()
public string GetD32FileAsString()
{
    return _D32File.PadRight(12);
}

// Set<>AsString()
public void SetD32FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D32File = value;
}

// Standard Getter
public string GetD34File()
{
    return _D34File;
}

// Standard Setter
public void SetD34File(string value)
{
    _D34File = value;
}

// Get<>AsString()
public string GetD34FileAsString()
{
    return _D34File.PadRight(12);
}

// Set<>AsString()
public void SetD34FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D34File = value;
}

// Standard Getter
public string GetD35File()
{
    return _D35File;
}

// Standard Setter
public void SetD35File(string value)
{
    _D35File = value;
}

// Get<>AsString()
public string GetD35FileAsString()
{
    return _D35File.PadRight(12);
}

// Set<>AsString()
public void SetD35FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D35File = value;
}

// Standard Getter
public string GetD36File()
{
    return _D36File;
}

// Standard Setter
public void SetD36File(string value)
{
    _D36File = value;
}

// Get<>AsString()
public string GetD36FileAsString()
{
    return _D36File.PadRight(12);
}

// Set<>AsString()
public void SetD36FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D36File = value;
}

// Standard Getter
public string GetD38File()
{
    return _D38File;
}

// Standard Setter
public void SetD38File(string value)
{
    _D38File = value;
}

// Get<>AsString()
public string GetD38FileAsString()
{
    return _D38File.PadRight(12);
}

// Set<>AsString()
public void SetD38FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D38File = value;
}

// Standard Getter
public string GetD39File()
{
    return _D39File;
}

// Standard Setter
public void SetD39File(string value)
{
    _D39File = value;
}

// Get<>AsString()
public string GetD39FileAsString()
{
    return _D39File.PadRight(12);
}

// Set<>AsString()
public void SetD39FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D39File = value;
}

// Standard Getter
public string GetD40File()
{
    return _D40File;
}

// Standard Setter
public void SetD40File(string value)
{
    _D40File = value;
}

// Get<>AsString()
public string GetD40FileAsString()
{
    return _D40File.PadRight(12);
}

// Set<>AsString()
public void SetD40FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D40File = value;
}

// Standard Getter
public string GetD41File()
{
    return _D41File;
}

// Standard Setter
public void SetD41File(string value)
{
    _D41File = value;
}

// Get<>AsString()
public string GetD41FileAsString()
{
    return _D41File.PadRight(12);
}

// Set<>AsString()
public void SetD41FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D41File = value;
}

// Standard Getter
public string GetD42File()
{
    return _D42File;
}

// Standard Setter
public void SetD42File(string value)
{
    _D42File = value;
}

// Get<>AsString()
public string GetD42FileAsString()
{
    return _D42File.PadRight(12);
}

// Set<>AsString()
public void SetD42FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D42File = value;
}

// Standard Getter
public string GetD43File()
{
    return _D43File;
}

// Standard Setter
public void SetD43File(string value)
{
    _D43File = value;
}

// Get<>AsString()
public string GetD43FileAsString()
{
    return _D43File.PadRight(12);
}

// Set<>AsString()
public void SetD43FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D43File = value;
}

// Standard Getter
public string GetD44File()
{
    return _D44File;
}

// Standard Setter
public void SetD44File(string value)
{
    _D44File = value;
}

// Get<>AsString()
public string GetD44FileAsString()
{
    return _D44File.PadRight(12);
}

// Set<>AsString()
public void SetD44FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D44File = value;
}

// Standard Getter
public string GetD86File()
{
    return _D86File;
}

// Standard Setter
public void SetD86File(string value)
{
    _D86File = value;
}

// Get<>AsString()
public string GetD86FileAsString()
{
    return _D86File.PadRight(12);
}

// Set<>AsString()
public void SetD86FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D86File = value;
}

// Standard Getter
public string GetD112File()
{
    return _D112File;
}

// Standard Setter
public void SetD112File(string value)
{
    _D112File = value;
}

// Get<>AsString()
public string GetD112FileAsString()
{
    return _D112File.PadRight(12);
}

// Set<>AsString()
public void SetD112FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D112File = value;
}

// Standard Getter
public string GetD133File()
{
    return _D133File;
}

// Standard Setter
public void SetD133File(string value)
{
    _D133File = value;
}

// Get<>AsString()
public string GetD133FileAsString()
{
    return _D133File.PadRight(12);
}

// Set<>AsString()
public void SetD133FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D133File = value;
}

// Standard Getter
public string GetD153File()
{
    return _D153File;
}

// Standard Setter
public void SetD153File(string value)
{
    _D153File = value;
}

// Get<>AsString()
public string GetD153FileAsString()
{
    return _D153File.PadRight(12);
}

// Set<>AsString()
public void SetD153FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D153File = value;
}

// Standard Getter
public string GetD154File()
{
    return _D154File;
}

// Standard Setter
public void SetD154File(string value)
{
    _D154File = value;
}

// Get<>AsString()
public string GetD154FileAsString()
{
    return _D154File.PadRight(12);
}

// Set<>AsString()
public void SetD154FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D154File = value;
}

// Standard Getter
public string GetD163File()
{
    return _D163File;
}

// Standard Setter
public void SetD163File(string value)
{
    _D163File = value;
}

// Get<>AsString()
public string GetD163FileAsString()
{
    return _D163File.PadRight(12);
}

// Set<>AsString()
public void SetD163FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D163File = value;
}

// Standard Getter
public string GetD167File()
{
    return _D167File;
}

// Standard Setter
public void SetD167File(string value)
{
    _D167File = value;
}

// Get<>AsString()
public string GetD167FileAsString()
{
    return _D167File.PadRight(12);
}

// Set<>AsString()
public void SetD167FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D167File = value;
}

// Standard Getter
public string GetD169File()
{
    return _D169File;
}

// Standard Setter
public void SetD169File(string value)
{
    _D169File = value;
}

// Get<>AsString()
public string GetD169FileAsString()
{
    return _D169File.PadRight(12);
}

// Set<>AsString()
public void SetD169FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D169File = value;
}

// Standard Getter
public string GetD170File()
{
    return _D170File;
}

// Standard Setter
public void SetD170File(string value)
{
    _D170File = value;
}

// Get<>AsString()
public string GetD170FileAsString()
{
    return _D170File.PadRight(12);
}

// Set<>AsString()
public void SetD170FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D170File = value;
}

// Standard Getter
public D5File GetD5File()
{
    return _D5File;
}

// Standard Setter
public void SetD5File(D5File value)
{
    _D5File = value;
}

// Get<>AsString()
public string GetD5FileAsString()
{
    return _D5File != null ? _D5File.GetD5FileAsString() : "";
}

// Set<>AsString()
public void SetD5FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D5File == null)
    {
        _D5File = new D5File();
    }
    _D5File.SetD5FileAsString(value);
}

// Standard Getter
public D6File GetD6File()
{
    return _D6File;
}

// Standard Setter
public void SetD6File(D6File value)
{
    _D6File = value;
}

// Get<>AsString()
public string GetD6FileAsString()
{
    return _D6File != null ? _D6File.GetD6FileAsString() : "";
}

// Set<>AsString()
public void SetD6FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D6File == null)
    {
        _D6File = new D6File();
    }
    _D6File.SetD6FileAsString(value);
}

// Standard Getter
public D10File GetD10File()
{
    return _D10File;
}

// Standard Setter
public void SetD10File(D10File value)
{
    _D10File = value;
}

// Get<>AsString()
public string GetD10FileAsString()
{
    return _D10File != null ? _D10File.GetD10FileAsString() : "";
}

// Set<>AsString()
public void SetD10FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D10File == null)
    {
        _D10File = new D10File();
    }
    _D10File.SetD10FileAsString(value);
}

// Standard Getter
public D11File GetD11File()
{
    return _D11File;
}

// Standard Setter
public void SetD11File(D11File value)
{
    _D11File = value;
}

// Get<>AsString()
public string GetD11FileAsString()
{
    return _D11File != null ? _D11File.GetD11FileAsString() : "";
}

// Set<>AsString()
public void SetD11FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D11File == null)
    {
        _D11File = new D11File();
    }
    _D11File.SetD11FileAsString(value);
}

// Standard Getter
public D12File GetD12File()
{
    return _D12File;
}

// Standard Setter
public void SetD12File(D12File value)
{
    _D12File = value;
}

// Get<>AsString()
public string GetD12FileAsString()
{
    return _D12File != null ? _D12File.GetD12FileAsString() : "";
}

// Set<>AsString()
public void SetD12FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D12File == null)
    {
        _D12File = new D12File();
    }
    _D12File.SetD12FileAsString(value);
}

// Standard Getter
public D18File GetD18File()
{
    return _D18File;
}

// Standard Setter
public void SetD18File(D18File value)
{
    _D18File = value;
}

// Get<>AsString()
public string GetD18FileAsString()
{
    return _D18File != null ? _D18File.GetD18FileAsString() : "";
}

// Set<>AsString()
public void SetD18FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D18File == null)
    {
        _D18File = new D18File();
    }
    _D18File.SetD18FileAsString(value);
}

// Standard Getter
public D19File GetD19File()
{
    return _D19File;
}

// Standard Setter
public void SetD19File(D19File value)
{
    _D19File = value;
}

// Get<>AsString()
public string GetD19FileAsString()
{
    return _D19File != null ? _D19File.GetD19FileAsString() : "";
}

// Set<>AsString()
public void SetD19FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D19File == null)
    {
        _D19File = new D19File();
    }
    _D19File.SetD19FileAsString(value);
}

// Standard Getter
public D20File GetD20File()
{
    return _D20File;
}

// Standard Setter
public void SetD20File(D20File value)
{
    _D20File = value;
}

// Get<>AsString()
public string GetD20FileAsString()
{
    return _D20File != null ? _D20File.GetD20FileAsString() : "";
}

// Set<>AsString()
public void SetD20FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D20File == null)
    {
        _D20File = new D20File();
    }
    _D20File.SetD20FileAsString(value);
}

// Standard Getter
public D21File GetD21File()
{
    return _D21File;
}

// Standard Setter
public void SetD21File(D21File value)
{
    _D21File = value;
}

// Get<>AsString()
public string GetD21FileAsString()
{
    return _D21File != null ? _D21File.GetD21FileAsString() : "";
}

// Set<>AsString()
public void SetD21FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D21File == null)
    {
        _D21File = new D21File();
    }
    _D21File.SetD21FileAsString(value);
}

// Standard Getter
public D22File GetD22File()
{
    return _D22File;
}

// Standard Setter
public void SetD22File(D22File value)
{
    _D22File = value;
}

// Get<>AsString()
public string GetD22FileAsString()
{
    return _D22File != null ? _D22File.GetD22FileAsString() : "";
}

// Set<>AsString()
public void SetD22FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D22File == null)
    {
        _D22File = new D22File();
    }
    _D22File.SetD22FileAsString(value);
}

// Standard Getter
public D23File GetD23File()
{
    return _D23File;
}

// Standard Setter
public void SetD23File(D23File value)
{
    _D23File = value;
}

// Get<>AsString()
public string GetD23FileAsString()
{
    return _D23File != null ? _D23File.GetD23FileAsString() : "";
}

// Set<>AsString()
public void SetD23FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D23File == null)
    {
        _D23File = new D23File();
    }
    _D23File.SetD23FileAsString(value);
}

// Standard Getter
public D24File GetD24File()
{
    return _D24File;
}

// Standard Setter
public void SetD24File(D24File value)
{
    _D24File = value;
}

// Get<>AsString()
public string GetD24FileAsString()
{
    return _D24File != null ? _D24File.GetD24FileAsString() : "";
}

// Set<>AsString()
public void SetD24FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D24File == null)
    {
        _D24File = new D24File();
    }
    _D24File.SetD24FileAsString(value);
}

// Standard Getter
public D25File GetD25File()
{
    return _D25File;
}

// Standard Setter
public void SetD25File(D25File value)
{
    _D25File = value;
}

// Get<>AsString()
public string GetD25FileAsString()
{
    return _D25File != null ? _D25File.GetD25FileAsString() : "";
}

// Set<>AsString()
public void SetD25FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D25File == null)
    {
        _D25File = new D25File();
    }
    _D25File.SetD25FileAsString(value);
}

// Standard Getter
public D26File GetD26File()
{
    return _D26File;
}

// Standard Setter
public void SetD26File(D26File value)
{
    _D26File = value;
}

// Get<>AsString()
public string GetD26FileAsString()
{
    return _D26File != null ? _D26File.GetD26FileAsString() : "";
}

// Set<>AsString()
public void SetD26FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D26File == null)
    {
        _D26File = new D26File();
    }
    _D26File.SetD26FileAsString(value);
}

// Standard Getter
public D27File GetD27File()
{
    return _D27File;
}

// Standard Setter
public void SetD27File(D27File value)
{
    _D27File = value;
}

// Get<>AsString()
public string GetD27FileAsString()
{
    return _D27File != null ? _D27File.GetD27FileAsString() : "";
}

// Set<>AsString()
public void SetD27FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D27File == null)
    {
        _D27File = new D27File();
    }
    _D27File.SetD27FileAsString(value);
}

// Standard Getter
public D28File GetD28File()
{
    return _D28File;
}

// Standard Setter
public void SetD28File(D28File value)
{
    _D28File = value;
}

// Get<>AsString()
public string GetD28FileAsString()
{
    return _D28File != null ? _D28File.GetD28FileAsString() : "";
}

// Set<>AsString()
public void SetD28FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D28File == null)
    {
        _D28File = new D28File();
    }
    _D28File.SetD28FileAsString(value);
}

// Standard Getter
public D29File GetD29File()
{
    return _D29File;
}

// Standard Setter
public void SetD29File(D29File value)
{
    _D29File = value;
}

// Get<>AsString()
public string GetD29FileAsString()
{
    return _D29File != null ? _D29File.GetD29FileAsString() : "";
}

// Set<>AsString()
public void SetD29FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D29File == null)
    {
        _D29File = new D29File();
    }
    _D29File.SetD29FileAsString(value);
}

// Standard Getter
public D30File GetD30File()
{
    return _D30File;
}

// Standard Setter
public void SetD30File(D30File value)
{
    _D30File = value;
}

// Get<>AsString()
public string GetD30FileAsString()
{
    return _D30File != null ? _D30File.GetD30FileAsString() : "";
}

// Set<>AsString()
public void SetD30FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D30File == null)
    {
        _D30File = new D30File();
    }
    _D30File.SetD30FileAsString(value);
}

// Standard Getter
public D33File GetD33File()
{
    return _D33File;
}

// Standard Setter
public void SetD33File(D33File value)
{
    _D33File = value;
}

// Get<>AsString()
public string GetD33FileAsString()
{
    return _D33File != null ? _D33File.GetD33FileAsString() : "";
}

// Set<>AsString()
public void SetD33FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D33File == null)
    {
        _D33File = new D33File();
    }
    _D33File.SetD33FileAsString(value);
}

// Standard Getter
public D45File GetD45File()
{
    return _D45File;
}

// Standard Setter
public void SetD45File(D45File value)
{
    _D45File = value;
}

// Get<>AsString()
public string GetD45FileAsString()
{
    return _D45File != null ? _D45File.GetD45FileAsString() : "";
}

// Set<>AsString()
public void SetD45FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D45File == null)
    {
        _D45File = new D45File();
    }
    _D45File.SetD45FileAsString(value);
}

// Standard Getter
public D46File GetD46File()
{
    return _D46File;
}

// Standard Setter
public void SetD46File(D46File value)
{
    _D46File = value;
}

// Get<>AsString()
public string GetD46FileAsString()
{
    return _D46File != null ? _D46File.GetD46FileAsString() : "";
}

// Set<>AsString()
public void SetD46FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D46File == null)
    {
        _D46File = new D46File();
    }
    _D46File.SetD46FileAsString(value);
}

// Standard Getter
public D49File GetD49File()
{
    return _D49File;
}

// Standard Setter
public void SetD49File(D49File value)
{
    _D49File = value;
}

// Get<>AsString()
public string GetD49FileAsString()
{
    return _D49File != null ? _D49File.GetD49FileAsString() : "";
}

// Set<>AsString()
public void SetD49FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D49File == null)
    {
        _D49File = new D49File();
    }
    _D49File.SetD49FileAsString(value);
}

// Standard Getter
public D50File GetD50File()
{
    return _D50File;
}

// Standard Setter
public void SetD50File(D50File value)
{
    _D50File = value;
}

// Get<>AsString()
public string GetD50FileAsString()
{
    return _D50File != null ? _D50File.GetD50FileAsString() : "";
}

// Set<>AsString()
public void SetD50FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D50File == null)
    {
        _D50File = new D50File();
    }
    _D50File.SetD50FileAsString(value);
}

// Standard Getter
public D51File GetD51File()
{
    return _D51File;
}

// Standard Setter
public void SetD51File(D51File value)
{
    _D51File = value;
}

// Get<>AsString()
public string GetD51FileAsString()
{
    return _D51File != null ? _D51File.GetD51FileAsString() : "";
}

// Set<>AsString()
public void SetD51FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D51File == null)
    {
        _D51File = new D51File();
    }
    _D51File.SetD51FileAsString(value);
}

// Standard Getter
public D68File GetD68File()
{
    return _D68File;
}

// Standard Setter
public void SetD68File(D68File value)
{
    _D68File = value;
}

// Get<>AsString()
public string GetD68FileAsString()
{
    return _D68File != null ? _D68File.GetD68FileAsString() : "";
}

// Set<>AsString()
public void SetD68FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D68File == null)
    {
        _D68File = new D68File();
    }
    _D68File.SetD68FileAsString(value);
}

// Standard Getter
public D69File GetD69File()
{
    return _D69File;
}

// Standard Setter
public void SetD69File(D69File value)
{
    _D69File = value;
}

// Get<>AsString()
public string GetD69FileAsString()
{
    return _D69File != null ? _D69File.GetD69FileAsString() : "";
}

// Set<>AsString()
public void SetD69FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D69File == null)
    {
        _D69File = new D69File();
    }
    _D69File.SetD69FileAsString(value);
}

// Standard Getter
public D70File GetD70File()
{
    return _D70File;
}

// Standard Setter
public void SetD70File(D70File value)
{
    _D70File = value;
}

// Get<>AsString()
public string GetD70FileAsString()
{
    return _D70File != null ? _D70File.GetD70FileAsString() : "";
}

// Set<>AsString()
public void SetD70FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D70File == null)
    {
        _D70File = new D70File();
    }
    _D70File.SetD70FileAsString(value);
}

// Standard Getter
public D71File GetD71File()
{
    return _D71File;
}

// Standard Setter
public void SetD71File(D71File value)
{
    _D71File = value;
}

// Get<>AsString()
public string GetD71FileAsString()
{
    return _D71File != null ? _D71File.GetD71FileAsString() : "";
}

// Set<>AsString()
public void SetD71FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D71File == null)
    {
        _D71File = new D71File();
    }
    _D71File.SetD71FileAsString(value);
}

// Standard Getter
public D72File GetD72File()
{
    return _D72File;
}

// Standard Setter
public void SetD72File(D72File value)
{
    _D72File = value;
}

// Get<>AsString()
public string GetD72FileAsString()
{
    return _D72File != null ? _D72File.GetD72FileAsString() : "";
}

// Set<>AsString()
public void SetD72FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D72File == null)
    {
        _D72File = new D72File();
    }
    _D72File.SetD72FileAsString(value);
}

// Standard Getter
public D73File GetD73File()
{
    return _D73File;
}

// Standard Setter
public void SetD73File(D73File value)
{
    _D73File = value;
}

// Get<>AsString()
public string GetD73FileAsString()
{
    return _D73File != null ? _D73File.GetD73FileAsString() : "";
}

// Set<>AsString()
public void SetD73FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D73File == null)
    {
        _D73File = new D73File();
    }
    _D73File.SetD73FileAsString(value);
}

// Standard Getter
public D74File GetD74File()
{
    return _D74File;
}

// Standard Setter
public void SetD74File(D74File value)
{
    _D74File = value;
}

// Get<>AsString()
public string GetD74FileAsString()
{
    return _D74File != null ? _D74File.GetD74FileAsString() : "";
}

// Set<>AsString()
public void SetD74FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D74File == null)
    {
        _D74File = new D74File();
    }
    _D74File.SetD74FileAsString(value);
}

// Standard Getter
public D75File GetD75File()
{
    return _D75File;
}

// Standard Setter
public void SetD75File(D75File value)
{
    _D75File = value;
}

// Get<>AsString()
public string GetD75FileAsString()
{
    return _D75File != null ? _D75File.GetD75FileAsString() : "";
}

// Set<>AsString()
public void SetD75FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D75File == null)
    {
        _D75File = new D75File();
    }
    _D75File.SetD75FileAsString(value);
}

// Standard Getter
public D76File GetD76File()
{
    return _D76File;
}

// Standard Setter
public void SetD76File(D76File value)
{
    _D76File = value;
}

// Get<>AsString()
public string GetD76FileAsString()
{
    return _D76File != null ? _D76File.GetD76FileAsString() : "";
}

// Set<>AsString()
public void SetD76FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D76File == null)
    {
        _D76File = new D76File();
    }
    _D76File.SetD76FileAsString(value);
}

// Standard Getter
public D77File GetD77File()
{
    return _D77File;
}

// Standard Setter
public void SetD77File(D77File value)
{
    _D77File = value;
}

// Get<>AsString()
public string GetD77FileAsString()
{
    return _D77File != null ? _D77File.GetD77FileAsString() : "";
}

// Set<>AsString()
public void SetD77FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D77File == null)
    {
        _D77File = new D77File();
    }
    _D77File.SetD77FileAsString(value);
}

// Standard Getter
public D78File GetD78File()
{
    return _D78File;
}

// Standard Setter
public void SetD78File(D78File value)
{
    _D78File = value;
}

// Get<>AsString()
public string GetD78FileAsString()
{
    return _D78File != null ? _D78File.GetD78FileAsString() : "";
}

// Set<>AsString()
public void SetD78FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D78File == null)
    {
        _D78File = new D78File();
    }
    _D78File.SetD78FileAsString(value);
}

// Standard Getter
public D79File GetD79File()
{
    return _D79File;
}

// Standard Setter
public void SetD79File(D79File value)
{
    _D79File = value;
}

// Get<>AsString()
public string GetD79FileAsString()
{
    return _D79File != null ? _D79File.GetD79FileAsString() : "";
}

// Set<>AsString()
public void SetD79FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D79File == null)
    {
        _D79File = new D79File();
    }
    _D79File.SetD79FileAsString(value);
}

// Standard Getter
public D80File GetD80File()
{
    return _D80File;
}

// Standard Setter
public void SetD80File(D80File value)
{
    _D80File = value;
}

// Get<>AsString()
public string GetD80FileAsString()
{
    return _D80File != null ? _D80File.GetD80FileAsString() : "";
}

// Set<>AsString()
public void SetD80FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D80File == null)
    {
        _D80File = new D80File();
    }
    _D80File.SetD80FileAsString(value);
}

// Standard Getter
public D81File GetD81File()
{
    return _D81File;
}

// Standard Setter
public void SetD81File(D81File value)
{
    _D81File = value;
}

// Get<>AsString()
public string GetD81FileAsString()
{
    return _D81File != null ? _D81File.GetD81FileAsString() : "";
}

// Set<>AsString()
public void SetD81FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D81File == null)
    {
        _D81File = new D81File();
    }
    _D81File.SetD81FileAsString(value);
}

// Standard Getter
public D82File GetD82File()
{
    return _D82File;
}

// Standard Setter
public void SetD82File(D82File value)
{
    _D82File = value;
}

// Get<>AsString()
public string GetD82FileAsString()
{
    return _D82File != null ? _D82File.GetD82FileAsString() : "";
}

// Set<>AsString()
public void SetD82FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D82File == null)
    {
        _D82File = new D82File();
    }
    _D82File.SetD82FileAsString(value);
}

// Standard Getter
public D83File GetD83File()
{
    return _D83File;
}

// Standard Setter
public void SetD83File(D83File value)
{
    _D83File = value;
}

// Get<>AsString()
public string GetD83FileAsString()
{
    return _D83File != null ? _D83File.GetD83FileAsString() : "";
}

// Set<>AsString()
public void SetD83FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D83File == null)
    {
        _D83File = new D83File();
    }
    _D83File.SetD83FileAsString(value);
}

// Standard Getter
public D84File GetD84File()
{
    return _D84File;
}

// Standard Setter
public void SetD84File(D84File value)
{
    _D84File = value;
}

// Get<>AsString()
public string GetD84FileAsString()
{
    return _D84File != null ? _D84File.GetD84FileAsString() : "";
}

// Set<>AsString()
public void SetD84FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D84File == null)
    {
        _D84File = new D84File();
    }
    _D84File.SetD84FileAsString(value);
}

// Standard Getter
public D85File GetD85File()
{
    return _D85File;
}

// Standard Setter
public void SetD85File(D85File value)
{
    _D85File = value;
}

// Get<>AsString()
public string GetD85FileAsString()
{
    return _D85File != null ? _D85File.GetD85FileAsString() : "";
}

// Set<>AsString()
public void SetD85FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D85File == null)
    {
        _D85File = new D85File();
    }
    _D85File.SetD85FileAsString(value);
}

// Standard Getter
public D87File GetD87File()
{
    return _D87File;
}

// Standard Setter
public void SetD87File(D87File value)
{
    _D87File = value;
}

// Get<>AsString()
public string GetD87FileAsString()
{
    return _D87File != null ? _D87File.GetD87FileAsString() : "";
}

// Set<>AsString()
public void SetD87FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D87File == null)
    {
        _D87File = new D87File();
    }
    _D87File.SetD87FileAsString(value);
}

// Standard Getter
public D88File GetD88File()
{
    return _D88File;
}

// Standard Setter
public void SetD88File(D88File value)
{
    _D88File = value;
}

// Get<>AsString()
public string GetD88FileAsString()
{
    return _D88File != null ? _D88File.GetD88FileAsString() : "";
}

// Set<>AsString()
public void SetD88FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D88File == null)
    {
        _D88File = new D88File();
    }
    _D88File.SetD88FileAsString(value);
}

// Standard Getter
public D89File GetD89File()
{
    return _D89File;
}

// Standard Setter
public void SetD89File(D89File value)
{
    _D89File = value;
}

// Get<>AsString()
public string GetD89FileAsString()
{
    return _D89File != null ? _D89File.GetD89FileAsString() : "";
}

// Set<>AsString()
public void SetD89FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D89File == null)
    {
        _D89File = new D89File();
    }
    _D89File.SetD89FileAsString(value);
}

// Standard Getter
public D90File GetD90File()
{
    return _D90File;
}

// Standard Setter
public void SetD90File(D90File value)
{
    _D90File = value;
}

// Get<>AsString()
public string GetD90FileAsString()
{
    return _D90File != null ? _D90File.GetD90FileAsString() : "";
}

// Set<>AsString()
public void SetD90FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D90File == null)
    {
        _D90File = new D90File();
    }
    _D90File.SetD90FileAsString(value);
}

// Standard Getter
public D91File GetD91File()
{
    return _D91File;
}

// Standard Setter
public void SetD91File(D91File value)
{
    _D91File = value;
}

// Get<>AsString()
public string GetD91FileAsString()
{
    return _D91File != null ? _D91File.GetD91FileAsString() : "";
}

// Set<>AsString()
public void SetD91FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D91File == null)
    {
        _D91File = new D91File();
    }
    _D91File.SetD91FileAsString(value);
}

// Standard Getter
public D92File GetD92File()
{
    return _D92File;
}

// Standard Setter
public void SetD92File(D92File value)
{
    _D92File = value;
}

// Get<>AsString()
public string GetD92FileAsString()
{
    return _D92File != null ? _D92File.GetD92FileAsString() : "";
}

// Set<>AsString()
public void SetD92FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D92File == null)
    {
        _D92File = new D92File();
    }
    _D92File.SetD92FileAsString(value);
}

// Standard Getter
public D93File GetD93File()
{
    return _D93File;
}

// Standard Setter
public void SetD93File(D93File value)
{
    _D93File = value;
}

// Get<>AsString()
public string GetD93FileAsString()
{
    return _D93File != null ? _D93File.GetD93FileAsString() : "";
}

// Set<>AsString()
public void SetD93FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D93File == null)
    {
        _D93File = new D93File();
    }
    _D93File.SetD93FileAsString(value);
}

// Standard Getter
public D94File GetD94File()
{
    return _D94File;
}

// Standard Setter
public void SetD94File(D94File value)
{
    _D94File = value;
}

// Get<>AsString()
public string GetD94FileAsString()
{
    return _D94File != null ? _D94File.GetD94FileAsString() : "";
}

// Set<>AsString()
public void SetD94FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D94File == null)
    {
        _D94File = new D94File();
    }
    _D94File.SetD94FileAsString(value);
}

// Standard Getter
public D95File GetD95File()
{
    return _D95File;
}

// Standard Setter
public void SetD95File(D95File value)
{
    _D95File = value;
}

// Get<>AsString()
public string GetD95FileAsString()
{
    return _D95File != null ? _D95File.GetD95FileAsString() : "";
}

// Set<>AsString()
public void SetD95FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D95File == null)
    {
        _D95File = new D95File();
    }
    _D95File.SetD95FileAsString(value);
}

// Standard Getter
public D96File GetD96File()
{
    return _D96File;
}

// Standard Setter
public void SetD96File(D96File value)
{
    _D96File = value;
}

// Get<>AsString()
public string GetD96FileAsString()
{
    return _D96File != null ? _D96File.GetD96FileAsString() : "";
}

// Set<>AsString()
public void SetD96FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D96File == null)
    {
        _D96File = new D96File();
    }
    _D96File.SetD96FileAsString(value);
}

// Standard Getter
public D97File GetD97File()
{
    return _D97File;
}

// Standard Setter
public void SetD97File(D97File value)
{
    _D97File = value;
}

// Get<>AsString()
public string GetD97FileAsString()
{
    return _D97File != null ? _D97File.GetD97FileAsString() : "";
}

// Set<>AsString()
public void SetD97FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D97File == null)
    {
        _D97File = new D97File();
    }
    _D97File.SetD97FileAsString(value);
}

// Standard Getter
public D98File GetD98File()
{
    return _D98File;
}

// Standard Setter
public void SetD98File(D98File value)
{
    _D98File = value;
}

// Get<>AsString()
public string GetD98FileAsString()
{
    return _D98File != null ? _D98File.GetD98FileAsString() : "";
}

// Set<>AsString()
public void SetD98FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D98File == null)
    {
        _D98File = new D98File();
    }
    _D98File.SetD98FileAsString(value);
}

// Standard Getter
public D100File GetD100File()
{
    return _D100File;
}

// Standard Setter
public void SetD100File(D100File value)
{
    _D100File = value;
}

// Get<>AsString()
public string GetD100FileAsString()
{
    return _D100File != null ? _D100File.GetD100FileAsString() : "";
}

// Set<>AsString()
public void SetD100FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D100File == null)
    {
        _D100File = new D100File();
    }
    _D100File.SetD100FileAsString(value);
}

// Standard Getter
public D101File GetD101File()
{
    return _D101File;
}

// Standard Setter
public void SetD101File(D101File value)
{
    _D101File = value;
}

// Get<>AsString()
public string GetD101FileAsString()
{
    return _D101File != null ? _D101File.GetD101FileAsString() : "";
}

// Set<>AsString()
public void SetD101FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D101File == null)
    {
        _D101File = new D101File();
    }
    _D101File.SetD101FileAsString(value);
}

// Standard Getter
public D102File GetD102File()
{
    return _D102File;
}

// Standard Setter
public void SetD102File(D102File value)
{
    _D102File = value;
}

// Get<>AsString()
public string GetD102FileAsString()
{
    return _D102File != null ? _D102File.GetD102FileAsString() : "";
}

// Set<>AsString()
public void SetD102FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D102File == null)
    {
        _D102File = new D102File();
    }
    _D102File.SetD102FileAsString(value);
}

// Standard Getter
public D103File GetD103File()
{
    return _D103File;
}

// Standard Setter
public void SetD103File(D103File value)
{
    _D103File = value;
}

// Get<>AsString()
public string GetD103FileAsString()
{
    return _D103File != null ? _D103File.GetD103FileAsString() : "";
}

// Set<>AsString()
public void SetD103FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D103File == null)
    {
        _D103File = new D103File();
    }
    _D103File.SetD103FileAsString(value);
}

// Standard Getter
public D104File GetD104File()
{
    return _D104File;
}

// Standard Setter
public void SetD104File(D104File value)
{
    _D104File = value;
}

// Get<>AsString()
public string GetD104FileAsString()
{
    return _D104File != null ? _D104File.GetD104FileAsString() : "";
}

// Set<>AsString()
public void SetD104FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D104File == null)
    {
        _D104File = new D104File();
    }
    _D104File.SetD104FileAsString(value);
}

// Standard Getter
public D105File GetD105File()
{
    return _D105File;
}

// Standard Setter
public void SetD105File(D105File value)
{
    _D105File = value;
}

// Get<>AsString()
public string GetD105FileAsString()
{
    return _D105File != null ? _D105File.GetD105FileAsString() : "";
}

// Set<>AsString()
public void SetD105FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D105File == null)
    {
        _D105File = new D105File();
    }
    _D105File.SetD105FileAsString(value);
}

// Standard Getter
public D107File GetD107File()
{
    return _D107File;
}

// Standard Setter
public void SetD107File(D107File value)
{
    _D107File = value;
}

// Get<>AsString()
public string GetD107FileAsString()
{
    return _D107File != null ? _D107File.GetD107FileAsString() : "";
}

// Set<>AsString()
public void SetD107FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D107File == null)
    {
        _D107File = new D107File();
    }
    _D107File.SetD107FileAsString(value);
}

// Standard Getter
public D108File GetD108File()
{
    return _D108File;
}

// Standard Setter
public void SetD108File(D108File value)
{
    _D108File = value;
}

// Get<>AsString()
public string GetD108FileAsString()
{
    return _D108File != null ? _D108File.GetD108FileAsString() : "";
}

// Set<>AsString()
public void SetD108FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D108File == null)
    {
        _D108File = new D108File();
    }
    _D108File.SetD108FileAsString(value);
}

// Standard Getter
public D109File GetD109File()
{
    return _D109File;
}

// Standard Setter
public void SetD109File(D109File value)
{
    _D109File = value;
}

// Get<>AsString()
public string GetD109FileAsString()
{
    return _D109File != null ? _D109File.GetD109FileAsString() : "";
}

// Set<>AsString()
public void SetD109FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D109File == null)
    {
        _D109File = new D109File();
    }
    _D109File.SetD109FileAsString(value);
}

// Standard Getter
public D110File GetD110File()
{
    return _D110File;
}

// Standard Setter
public void SetD110File(D110File value)
{
    _D110File = value;
}

// Get<>AsString()
public string GetD110FileAsString()
{
    return _D110File != null ? _D110File.GetD110FileAsString() : "";
}

// Set<>AsString()
public void SetD110FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D110File == null)
    {
        _D110File = new D110File();
    }
    _D110File.SetD110FileAsString(value);
}

// Standard Getter
public D111File GetD111File()
{
    return _D111File;
}

// Standard Setter
public void SetD111File(D111File value)
{
    _D111File = value;
}

// Get<>AsString()
public string GetD111FileAsString()
{
    return _D111File != null ? _D111File.GetD111FileAsString() : "";
}

// Set<>AsString()
public void SetD111FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D111File == null)
    {
        _D111File = new D111File();
    }
    _D111File.SetD111FileAsString(value);
}

// Standard Getter
public D159File GetD159File()
{
    return _D159File;
}

// Standard Setter
public void SetD159File(D159File value)
{
    _D159File = value;
}

// Get<>AsString()
public string GetD159FileAsString()
{
    return _D159File != null ? _D159File.GetD159FileAsString() : "";
}

// Set<>AsString()
public void SetD159FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D159File == null)
    {
        _D159File = new D159File();
    }
    _D159File.SetD159FileAsString(value);
}

// Standard Getter
public D160File GetD160File()
{
    return _D160File;
}

// Standard Setter
public void SetD160File(D160File value)
{
    _D160File = value;
}

// Get<>AsString()
public string GetD160FileAsString()
{
    return _D160File != null ? _D160File.GetD160FileAsString() : "";
}

// Set<>AsString()
public void SetD160FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D160File == null)
    {
        _D160File = new D160File();
    }
    _D160File.SetD160FileAsString(value);
}

// Standard Getter
public D161File GetD161File()
{
    return _D161File;
}

// Standard Setter
public void SetD161File(D161File value)
{
    _D161File = value;
}

// Get<>AsString()
public string GetD161FileAsString()
{
    return _D161File != null ? _D161File.GetD161FileAsString() : "";
}

// Set<>AsString()
public void SetD161FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D161File == null)
    {
        _D161File = new D161File();
    }
    _D161File.SetD161FileAsString(value);
}

// Standard Getter
public D164File GetD164File()
{
    return _D164File;
}

// Standard Setter
public void SetD164File(D164File value)
{
    _D164File = value;
}

// Get<>AsString()
public string GetD164FileAsString()
{
    return _D164File != null ? _D164File.GetD164FileAsString() : "";
}

// Set<>AsString()
public void SetD164FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D164File == null)
    {
        _D164File = new D164File();
    }
    _D164File.SetD164FileAsString(value);
}

// Standard Getter
public D165File GetD165File()
{
    return _D165File;
}

// Standard Setter
public void SetD165File(D165File value)
{
    _D165File = value;
}

// Get<>AsString()
public string GetD165FileAsString()
{
    return _D165File != null ? _D165File.GetD165FileAsString() : "";
}

// Set<>AsString()
public void SetD165FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D165File == null)
    {
        _D165File = new D165File();
    }
    _D165File.SetD165FileAsString(value);
}

// Standard Getter
public D171File GetD171File()
{
    return _D171File;
}

// Standard Setter
public void SetD171File(D171File value)
{
    _D171File = value;
}

// Get<>AsString()
public string GetD171FileAsString()
{
    return _D171File != null ? _D171File.GetD171FileAsString() : "";
}

// Set<>AsString()
public void SetD171FileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D171File == null)
    {
        _D171File = new D171File();
    }
    _D171File.SetD171FileAsString(value);
}

// Standard Getter
public string GetWStoreAction()
{
    return _WStoreAction;
}

// Standard Setter
public void SetWStoreAction(string value)
{
    _WStoreAction = value;
}

// Get<>AsString()
public string GetWStoreActionAsString()
{
    return _WStoreAction.PadRight(3);
}

// Set<>AsString()
public void SetWStoreActionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WStoreAction = value;
}

// Standard Getter
public int GetWSchedSub()
{
    return _WSchedSub;
}

// Standard Setter
public void SetWSchedSub(int value)
{
    _WSchedSub = value;
}

// Get<>AsString()
public string GetWSchedSubAsString()
{
    return _WSchedSub.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWSchedSubAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WSchedSub = parsed;
}

// Standard Getter
public WSchedRecTable GetWSchedRecTable()
{
    return _WSchedRecTable;
}

// Standard Setter
public void SetWSchedRecTable(WSchedRecTable value)
{
    _WSchedRecTable = value;
}

// Get<>AsString()
public string GetWSchedRecTableAsString()
{
    return _WSchedRecTable != null ? _WSchedRecTable.GetWSchedRecTableAsString() : "";
}

// Set<>AsString()
public void SetWSchedRecTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WSchedRecTable == null)
    {
        _WSchedRecTable = new WSchedRecTable();
    }
    _WSchedRecTable.SetWSchedRecTableAsString(value);
}

// Standard Getter
public Filler334 GetFiller334()
{
    return _Filler334;
}

// Standard Setter
public void SetFiller334(Filler334 value)
{
    _Filler334 = value;
}

// Get<>AsString()
public string GetFiller334AsString()
{
    return _Filler334 != null ? _Filler334.GetFiller334AsString() : "";
}

// Set<>AsString()
public void SetFiller334AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler334 == null)
    {
        _Filler334 = new Filler334();
    }
    _Filler334.SetFiller334AsString(value);
}

// Standard Getter
public int GetWRecordRetrieved()
{
    return _WRecordRetrieved;
}

// Standard Setter
public void SetWRecordRetrieved(int value)
{
    _WRecordRetrieved = value;
}

// Get<>AsString()
public string GetWRecordRetrievedAsString()
{
    return _WRecordRetrieved.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWRecordRetrievedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WRecordRetrieved = parsed;
}

// Standard Getter
public string GetWReport()
{
    return _WReport;
}

// Standard Setter
public void SetWReport(string value)
{
    _WReport = value;
}

// Get<>AsString()
public string GetWReportAsString()
{
    return _WReport.PadRight(12);
}

// Set<>AsString()
public void SetWReportAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WReport = value;
}

// Standard Getter
public string GetW80ByteField()
{
    return _W80ByteField;
}

// Standard Setter
public void SetW80ByteField(string value)
{
    _W80ByteField = value;
}

// Get<>AsString()
public string GetW80ByteFieldAsString()
{
    return _W80ByteField.PadRight(80);
}

// Set<>AsString()
public void SetW80ByteFieldAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _W80ByteField = value;
}

// Standard Getter
public Cgtdate2LinkageDate1 GetCgtdate2LinkageDate1()
{
    return _Cgtdate2LinkageDate1;
}

// Standard Setter
public void SetCgtdate2LinkageDate1(Cgtdate2LinkageDate1 value)
{
    _Cgtdate2LinkageDate1 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate1AsString()
{
    return _Cgtdate2LinkageDate1 != null ? _Cgtdate2LinkageDate1.GetCgtdate2LinkageDate1AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate1 == null)
    {
        _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();
    }
    _Cgtdate2LinkageDate1.SetCgtdate2LinkageDate1AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate2 GetCgtdate2LinkageDate2()
{
    return _Cgtdate2LinkageDate2;
}

// Standard Setter
public void SetCgtdate2LinkageDate2(Cgtdate2LinkageDate2 value)
{
    _Cgtdate2LinkageDate2 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate2AsString()
{
    return _Cgtdate2LinkageDate2 != null ? _Cgtdate2LinkageDate2.GetCgtdate2LinkageDate2AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate2 == null)
    {
        _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();
    }
    _Cgtdate2LinkageDate2.SetCgtdate2LinkageDate2AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate3 GetCgtdate2LinkageDate3()
{
    return _Cgtdate2LinkageDate3;
}

// Standard Setter
public void SetCgtdate2LinkageDate3(Cgtdate2LinkageDate3 value)
{
    _Cgtdate2LinkageDate3 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate3AsString()
{
    return _Cgtdate2LinkageDate3 != null ? _Cgtdate2LinkageDate3.GetCgtdate2LinkageDate3AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate3 == null)
    {
        _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();
    }
    _Cgtdate2LinkageDate3.SetCgtdate2LinkageDate3AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate4 GetCgtdate2LinkageDate4()
{
    return _Cgtdate2LinkageDate4;
}

// Standard Setter
public void SetCgtdate2LinkageDate4(Cgtdate2LinkageDate4 value)
{
    _Cgtdate2LinkageDate4 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate4AsString()
{
    return _Cgtdate2LinkageDate4 != null ? _Cgtdate2LinkageDate4.GetCgtdate2LinkageDate4AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate4 == null)
    {
        _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();
    }
    _Cgtdate2LinkageDate4.SetCgtdate2LinkageDate4AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate5 GetCgtdate2LinkageDate5()
{
    return _Cgtdate2LinkageDate5;
}

// Standard Setter
public void SetCgtdate2LinkageDate5(Cgtdate2LinkageDate5 value)
{
    _Cgtdate2LinkageDate5 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate5AsString()
{
    return _Cgtdate2LinkageDate5 != null ? _Cgtdate2LinkageDate5.GetCgtdate2LinkageDate5AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate5 == null)
    {
        _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();
    }
    _Cgtdate2LinkageDate5.SetCgtdate2LinkageDate5AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate6 GetCgtdate2LinkageDate6()
{
    return _Cgtdate2LinkageDate6;
}

// Standard Setter
public void SetCgtdate2LinkageDate6(Cgtdate2LinkageDate6 value)
{
    _Cgtdate2LinkageDate6 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate6AsString()
{
    return _Cgtdate2LinkageDate6 != null ? _Cgtdate2LinkageDate6.GetCgtdate2LinkageDate6AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate6 == null)
    {
        _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();
    }
    _Cgtdate2LinkageDate6.SetCgtdate2LinkageDate6AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate7 GetCgtdate2LinkageDate7()
{
    return _Cgtdate2LinkageDate7;
}

// Standard Setter
public void SetCgtdate2LinkageDate7(Cgtdate2LinkageDate7 value)
{
    _Cgtdate2LinkageDate7 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate7AsString()
{
    return _Cgtdate2LinkageDate7 != null ? _Cgtdate2LinkageDate7.GetCgtdate2LinkageDate7AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate7 == null)
    {
        _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();
    }
    _Cgtdate2LinkageDate7.SetCgtdate2LinkageDate7AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate8 GetCgtdate2LinkageDate8()
{
    return _Cgtdate2LinkageDate8;
}

// Standard Setter
public void SetCgtdate2LinkageDate8(Cgtdate2LinkageDate8 value)
{
    _Cgtdate2LinkageDate8 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate8AsString()
{
    return _Cgtdate2LinkageDate8 != null ? _Cgtdate2LinkageDate8.GetCgtdate2LinkageDate8AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate8 == null)
    {
        _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();
    }
    _Cgtdate2LinkageDate8.SetCgtdate2LinkageDate8AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate9 GetCgtdate2LinkageDate9()
{
    return _Cgtdate2LinkageDate9;
}

// Standard Setter
public void SetCgtdate2LinkageDate9(Cgtdate2LinkageDate9 value)
{
    _Cgtdate2LinkageDate9 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate9AsString()
{
    return _Cgtdate2LinkageDate9 != null ? _Cgtdate2LinkageDate9.GetCgtdate2LinkageDate9AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate9 == null)
    {
        _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();
    }
    _Cgtdate2LinkageDate9.SetCgtdate2LinkageDate9AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate10 GetCgtdate2LinkageDate10()
{
    return _Cgtdate2LinkageDate10;
}

// Standard Setter
public void SetCgtdate2LinkageDate10(Cgtdate2LinkageDate10 value)
{
    _Cgtdate2LinkageDate10 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate10AsString()
{
    return _Cgtdate2LinkageDate10 != null ? _Cgtdate2LinkageDate10.GetCgtdate2LinkageDate10AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate10 == null)
    {
        _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();
    }
    _Cgtdate2LinkageDate10.SetCgtdate2LinkageDate10AsString(value);
}

// Standard Getter
public EqtpathLinkage GetEqtpathLinkage()
{
    return _EqtpathLinkage;
}

// Standard Setter
public void SetEqtpathLinkage(EqtpathLinkage value)
{
    _EqtpathLinkage = value;
}

// Get<>AsString()
public string GetEqtpathLinkageAsString()
{
    return _EqtpathLinkage != null ? _EqtpathLinkage.GetEqtpathLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqtpathLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqtpathLinkage == null)
    {
        _EqtpathLinkage = new EqtpathLinkage();
    }
    _EqtpathLinkage.SetEqtpathLinkageAsString(value);
}

// Standard Getter
public EqtdebugLinkage GetEqtdebugLinkage()
{
    return _EqtdebugLinkage;
}

// Standard Setter
public void SetEqtdebugLinkage(EqtdebugLinkage value)
{
    _EqtdebugLinkage = value;
}

// Get<>AsString()
public string GetEqtdebugLinkageAsString()
{
    return _EqtdebugLinkage != null ? _EqtdebugLinkage.GetEqtdebugLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqtdebugLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqtdebugLinkage == null)
    {
        _EqtdebugLinkage = new EqtdebugLinkage();
    }
    _EqtdebugLinkage.SetEqtdebugLinkageAsString(value);
}

// Standard Getter
public TimingLinkage GetTimingLinkage()
{
    return _TimingLinkage;
}

// Standard Setter
public void SetTimingLinkage(TimingLinkage value)
{
    _TimingLinkage = value;
}

// Get<>AsString()
public string GetTimingLinkageAsString()
{
    return _TimingLinkage != null ? _TimingLinkage.GetTimingLinkageAsString() : "";
}

// Set<>AsString()
public void SetTimingLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_TimingLinkage == null)
    {
        _TimingLinkage = new TimingLinkage();
    }
    _TimingLinkage.SetTimingLinkageAsString(value);
}

// Standard Getter
public CgtlogLinkageArea1 GetCgtlogLinkageArea1()
{
    return _CgtlogLinkageArea1;
}

// Standard Setter
public void SetCgtlogLinkageArea1(CgtlogLinkageArea1 value)
{
    _CgtlogLinkageArea1 = value;
}

// Get<>AsString()
public string GetCgtlogLinkageArea1AsString()
{
    return _CgtlogLinkageArea1 != null ? _CgtlogLinkageArea1.GetCgtlogLinkageArea1AsString() : "";
}

// Set<>AsString()
public void SetCgtlogLinkageArea1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtlogLinkageArea1 == null)
    {
        _CgtlogLinkageArea1 = new CgtlogLinkageArea1();
    }
    _CgtlogLinkageArea1.SetCgtlogLinkageArea1AsString(value);
}

// Standard Getter
public CgtlogLinkageArea2 GetCgtlogLinkageArea2()
{
    return _CgtlogLinkageArea2;
}

// Standard Setter
public void SetCgtlogLinkageArea2(CgtlogLinkageArea2 value)
{
    _CgtlogLinkageArea2 = value;
}

// Get<>AsString()
public string GetCgtlogLinkageArea2AsString()
{
    return _CgtlogLinkageArea2 != null ? _CgtlogLinkageArea2.GetCgtlogLinkageArea2AsString() : "";
}

// Set<>AsString()
public void SetCgtlogLinkageArea2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtlogLinkageArea2 == null)
    {
        _CgtlogLinkageArea2 = new CgtlogLinkageArea2();
    }
    _CgtlogLinkageArea2.SetCgtlogLinkageArea2AsString(value);
}

// Standard Getter
public string GetLMessage()
{
    return _LMessage;
}

// Standard Setter
public void SetLMessage(string value)
{
    _LMessage = value;
}

// Get<>AsString()
public string GetLMessageAsString()
{
    return _LMessage.PadRight(200);
}

// Set<>AsString()
public void SetLMessageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LMessage = value;
}

public void SyncWBinaryFieldFromWBinaryStatus2()
{
    this.SetWBinaryFieldAsString(this.GetWBinaryStatus2AsString());
}

public void SyncWBinaryStatus2FromWBinaryField()
{
    this.SetWBinaryStatus2AsString(this.GetWBinaryFieldAsString());
}
public void SyncWSchedRecTableFromFiller334()
{
    this.SetWSchedRecTableAsString(this.GetFiller334AsString());
}

public void SyncFiller334FromWSchedRecTable()
{
    this.SetFiller334AsString(this.GetWSchedRecTableAsString());
}

// Standard Getter
public string GetIO()
{
    return _IO;
}

// Standard Setter
public void SetIO(string value)
{
    _IO = value;
}

// Get<>AsString()
public string GetIOAsString()
{
    return _IO.PadRight(0);
}

// Set<>AsString()
public void SetIOAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    _IO = value;
}

// Standard Getter
public int GetZero()
{
    return _Zero;
}

// Standard Setter
public void SetZero(int value)
{
    _Zero = value;
}

// Get<>AsString()
public string GetZeroAsString()
{
    return _Zero.ToString().PadLeft(15, '0');
}

// Set<>AsString()
public void SetZeroAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Zero = parsed;
}

// Standard Getter
public int GetAdvancing()
{
    return _Advancing;
}

// Standard Setter
public void SetAdvancing(int value)
{
    _Advancing = value;
}

// Get<>AsString()
public string GetAdvancingAsString()
{
    return _Advancing.ToString().PadLeft(15, '0');
}

// Set<>AsString()
public void SetAdvancingAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Advancing = parsed;
}

// Standard Getter
public string GetD19SplitKey()
{
    return _D19SplitKey;
}

// Standard Setter
public void SetD19SplitKey(string value)
{
    _D19SplitKey = value;
}

// Get<>AsString()
public string GetD19SplitKeyAsString()
{
    return _D19SplitKey.PadRight(0);
}

// Set<>AsString()
public void SetD19SplitKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    _D19SplitKey = value;
}

// Standard Getter
public string GetD20SplitKey()
{
    return _D20SplitKey;
}

// Standard Setter
public void SetD20SplitKey(string value)
{
    _D20SplitKey = value;
}

// Get<>AsString()
public string GetD20SplitKeyAsString()
{
    return _D20SplitKey.PadRight(0);
}

// Set<>AsString()
public void SetD20SplitKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    _D20SplitKey = value;
}

// Standard Getter
public string GetD81SplitKey()
{
    return _D81SplitKey;
}

// Standard Setter
public void SetD81SplitKey(string value)
{
    _D81SplitKey = value;
}

// Get<>AsString()
public string GetD81SplitKeyAsString()
{
    return _D81SplitKey.PadRight(0);
}

// Set<>AsString()
public void SetD81SplitKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    _D81SplitKey = value;
}

// Standard Getter
public string GetD98SplitKey()
{
    return _D98SplitKey;
}

// Standard Setter
public void SetD98SplitKey(string value)
{
    _D98SplitKey = value;
}

// Get<>AsString()
public string GetD98SplitKeyAsString()
{
    return _D98SplitKey.PadRight(0);
}

// Set<>AsString()
public void SetD98SplitKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    _D98SplitKey = value;
}

// Standard Getter
public string GetD101SplitKey()
{
    return _D101SplitKey;
}

// Standard Setter
public void SetD101SplitKey(string value)
{
    _D101SplitKey = value;
}

// Get<>AsString()
public string GetD101SplitKeyAsString()
{
    return _D101SplitKey.PadRight(0);
}

// Set<>AsString()
public void SetD101SplitKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;

    _D101SplitKey = value;
}


}}