using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing F200Footing Data Structure

public class F200Footing
{
    private static int _size = 172;
    // [DEBUG] Class: F200Footing, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: F201Control, is_external=, is_static_class=False, static_prefix=
    private string _F201Control ="4";
    
    
    
    
    // [DEBUG] Field: F202, is_external=, is_static_class=False, static_prefix=
    private string _F202 ="An asterisk (*) next to the acquisition date indicates that the tranche has been flagged";
    
    
    
    
    // [DEBUG] Field: F203, is_external=, is_static_class=False, static_prefix=
    private string _F203 =" ";
    
    
    
    
    
    // Serialization methods
    public string GetF200FootingAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_F201Control.PadRight(1));
        result.Append(_F202.PadRight(170));
        result.Append(_F203.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetF200FootingAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetF201Control(extracted);
        }
        offset += 1;
        if (offset + 170 <= data.Length)
        {
            string extracted = data.Substring(offset, 170).Trim();
            SetF202(extracted);
        }
        offset += 170;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetF203(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetF200FootingAsString();
    }
    // Set<>String Override function
    public void SetF200Footing(string value)
    {
        SetF200FootingAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetF201Control()
    {
        return _F201Control;
    }
    
    // Standard Setter
    public void SetF201Control(string value)
    {
        _F201Control = value;
    }
    
    // Get<>AsString()
    public string GetF201ControlAsString()
    {
        return _F201Control.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetF201ControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _F201Control = value;
    }
    
    // Standard Getter
    public string GetF202()
    {
        return _F202;
    }
    
    // Standard Setter
    public void SetF202(string value)
    {
        _F202 = value;
    }
    
    // Get<>AsString()
    public string GetF202AsString()
    {
        return _F202.PadRight(170);
    }
    
    // Set<>AsString()
    public void SetF202AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _F202 = value;
    }
    
    // Standard Getter
    public string GetF203()
    {
        return _F203;
    }
    
    // Standard Setter
    public void SetF203(string value)
    {
        _F203 = value;
    }
    
    // Get<>AsString()
    public string GetF203AsString()
    {
        return _F203.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetF203AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _F203 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
