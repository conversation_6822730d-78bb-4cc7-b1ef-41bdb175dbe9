using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportRecord Data Structure

public class ReportRecord
{
    private static int _size = 133;
    // [DEBUG] Class: ReportRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _PrintControl ="";
    
    
    
    
    // [DEBUG] Field: ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetReportRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_PrintControl.PadRight(1));
        result.Append(_ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetReportRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetPrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportRecordAsString();
    }
    // Set<>String Override function
    public void SetReportRecord(string value)
    {
        SetReportRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetPrintControl()
    {
        return _PrintControl;
    }
    
    // Standard Setter
    public void SetPrintControl(string value)
    {
        _PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetPrintControlAsString()
    {
        return _PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetPrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _PrintControl = value;
    }
    
    // Standard Getter
    public string GetReportLine()
    {
        return _ReportLine;
    }
    
    // Standard Setter
    public void SetReportLine(string value)
    {
        _ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetReportLineAsString()
    {
        return _ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}