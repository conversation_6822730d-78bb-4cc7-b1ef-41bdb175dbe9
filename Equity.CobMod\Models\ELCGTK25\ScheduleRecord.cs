using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing ScheduleRecord Data Structure

public class ScheduleRecord
{
    private static int _size = 1064;
    // [DEBUG] Class: ScheduleRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: SSort, is_external=, is_static_class=False, static_prefix=
    private SSort _SSort = new SSort();
    
    
    
    
    // [DEBUG] Field: SRecordType, is_external=, is_static_class=False, static_prefix=
    private string _SRecordType ="";
    
    
    // 88-level condition checks for SRecordType
    public bool IsSSedolRecord()
    {
        if (this._SRecordType == "'1'") return true;
        return false;
    }
    public bool IsSDetailRecord()
    {
        if (this._SRecordType == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SData, is_external=, is_static_class=False, static_prefix=
    private SData _SData = new SData();
    
    
    
    
    // [DEBUG] Field: SSedolData, is_external=, is_static_class=False, static_prefix=
    private SSedolData _SSedolData = new SSedolData();
    
    
    
    
    // [DEBUG] Field: STrancheDetailData, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData _STrancheDetailData = new STrancheDetailData();
    
    
    
    
    
    // Serialization methods
    public string GetScheduleRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_SSort.GetSSortAsString());
        result.Append(_SRecordType.PadRight(1));
        result.Append(_SData.GetSDataAsString());
        result.Append(_SSedolData.GetSSedolDataAsString());
        result.Append(_STrancheDetailData.GetSTrancheDetailDataAsString());
        
        return result.ToString();
    }
    
    public void SetScheduleRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 34 <= data.Length)
        {
            _SSort.SetSSortAsString(data.Substring(offset, 34));
        }
        else
        {
            _SSort.SetSSortAsString(data.Substring(offset));
        }
        offset += 34;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetSRecordType(extracted);
        }
        offset += 1;
        if (offset + 130 <= data.Length)
        {
            _SData.SetSDataAsString(data.Substring(offset, 130));
        }
        else
        {
            _SData.SetSDataAsString(data.Substring(offset));
        }
        offset += 130;
        if (offset + 154 <= data.Length)
        {
            _SSedolData.SetSSedolDataAsString(data.Substring(offset, 154));
        }
        else
        {
            _SSedolData.SetSSedolDataAsString(data.Substring(offset));
        }
        offset += 154;
        if (offset + 745 <= data.Length)
        {
            _STrancheDetailData.SetSTrancheDetailDataAsString(data.Substring(offset, 745));
        }
        else
        {
            _STrancheDetailData.SetSTrancheDetailDataAsString(data.Substring(offset));
        }
        offset += 745;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetScheduleRecordAsString();
    }
    // Set<>String Override function
    public void SetScheduleRecord(string value)
    {
        SetScheduleRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public SSort GetSSort()
    {
        return _SSort;
    }
    
    // Standard Setter
    public void SetSSort(SSort value)
    {
        _SSort = value;
    }
    
    // Get<>AsString()
    public string GetSSortAsString()
    {
        return _SSort != null ? _SSort.GetSSortAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SSort == null)
        {
            _SSort = new SSort();
        }
        _SSort.SetSSortAsString(value);
    }
    
    // Standard Getter
    public string GetSRecordType()
    {
        return _SRecordType;
    }
    
    // Standard Setter
    public void SetSRecordType(string value)
    {
        _SRecordType = value;
    }
    
    // Get<>AsString()
    public string GetSRecordTypeAsString()
    {
        return _SRecordType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetSRecordTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SRecordType = value;
    }
    
    // Standard Getter
    public SData GetSData()
    {
        return _SData;
    }
    
    // Standard Setter
    public void SetSData(SData value)
    {
        _SData = value;
    }
    
    // Get<>AsString()
    public string GetSDataAsString()
    {
        return _SData != null ? _SData.GetSDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SData == null)
        {
            _SData = new SData();
        }
        _SData.SetSDataAsString(value);
    }
    
    // Standard Getter
    public SSedolData GetSSedolData()
    {
        return _SSedolData;
    }
    
    // Standard Setter
    public void SetSSedolData(SSedolData value)
    {
        _SSedolData = value;
    }
    
    // Get<>AsString()
    public string GetSSedolDataAsString()
    {
        return _SSedolData != null ? _SSedolData.GetSSedolDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSSedolDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SSedolData == null)
        {
            _SSedolData = new SSedolData();
        }
        _SSedolData.SetSSedolDataAsString(value);
    }
    
    // Standard Getter
    public STrancheDetailData GetSTrancheDetailData()
    {
        return _STrancheDetailData;
    }
    
    // Standard Setter
    public void SetSTrancheDetailData(STrancheDetailData value)
    {
        _STrancheDetailData = value;
    }
    
    // Get<>AsString()
    public string GetSTrancheDetailDataAsString()
    {
        return _STrancheDetailData != null ? _STrancheDetailData.GetSTrancheDetailDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSTrancheDetailDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_STrancheDetailData == null)
        {
            _STrancheDetailData = new STrancheDetailData();
        }
        _STrancheDetailData.SetSTrancheDetailDataAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetSSort(string value)
    {
        _SSort.SetSSortAsString(value);
    }
    // Nested Class: SSort
    public class SSort
    {
        private static int _size = 34;
        
        // Fields in the class
        
        
        // [DEBUG] Field: SCurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _SCurrencySort =0;
        
        
        // 88-level condition checks for SCurrencySort
        public bool IsSCurrencySterling()
        {
            if (this._SCurrencySort == 0) return true;
            return false;
        }
        public bool IsSCurrencyEuro()
        {
            if (this._SCurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: SCoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _SCoAcLk ="";
        
        
        
        
        // [DEBUG] Field: SCountryCode, is_external=, is_static_class=False, static_prefix=
        private string _SCountryCode ="";
        
        
        
        
        // [DEBUG] Field: SMainGroup, is_external=, is_static_class=False, static_prefix=
        private string _SMainGroup ="";
        
        
        
        
        // [DEBUG] Field: SSecurityType, is_external=, is_static_class=False, static_prefix=
        private string _SSecurityType ="";
        
        
        // 88-level condition checks for SSecurityType
        public bool IsSShortFuture()
        {
            if (this._SSecurityType == "'G'") return true;
            return false;
        }
        public bool IsSLongFuture()
        {
            if (this._SSecurityType == "'H'") return true;
            return false;
        }
        public bool IsSWrittenCall()
        {
            if (this._SSecurityType == "'K'") return true;
            return false;
        }
        public bool IsSWrittenPut()
        {
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSShortWrittenDerivative()
        {
            if (this._SSecurityType == "'G'") return true;
            if (this._SSecurityType == "'K'") return true;
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSFutureWrittenDerivative()
        {
            if (this._SSecurityType == "'G'") return true;
            if (this._SSecurityType == "'H'") return true;
            if (this._SSecurityType == "'K'") return true;
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSPurchasedOption()
        {
            if (this._SSecurityType == "'I'") return true;
            if (this._SSecurityType == "'J'") return true;
            return false;
        }
        public bool IsSWrittenOption()
        {
            if (this._SSecurityType == "'K'") return true;
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSDerivative()
        {
            if (!string.IsNullOrEmpty(this.GetSSecurityType()) && this.GetSSecurityType().Length == 1)
            {
                var ch = this.GetSSecurityType()[0];
                if (ch >= 'G' && ch <= 'L') return true;
            }
            return false;
        }
        
        
        // [DEBUG] Field: SSecuritySortCode, is_external=, is_static_class=False, static_prefix=
        private string _SSecuritySortCode ="";
        
        
        
        
        // [DEBUG] Field: SSedolSort, is_external=, is_static_class=False, static_prefix=
        private string _SSedolSort ="";
        
        
        
        
    public SSort() {}
    
    public SSort(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetSCurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetSCoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetSCountryCode(data.Substring(offset, 3).Trim());
        offset += 3;
        SetSMainGroup(data.Substring(offset, 3).Trim());
        offset += 3;
        SetSSecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetSSecuritySortCode(data.Substring(offset, 15).Trim());
        offset += 15;
        SetSSedolSort(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetSSortAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_SCurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_SCoAcLk.PadRight(4));
        result.Append(_SCountryCode.PadRight(3));
        result.Append(_SMainGroup.PadRight(3));
        result.Append(_SSecurityType.PadRight(1));
        result.Append(_SSecuritySortCode.PadRight(15));
        result.Append(_SSedolSort.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetSSortAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetSCurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetSCoAcLk(extracted);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetSCountryCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetSMainGroup(extracted);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetSSecurityType(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetSSecuritySortCode(extracted);
        }
        offset += 15;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetSSedolSort(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetSCurrencySort()
    {
        return _SCurrencySort;
    }
    
    // Standard Setter
    public void SetSCurrencySort(int value)
    {
        _SCurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetSCurrencySortAsString()
    {
        return _SCurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetSCurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _SCurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetSCoAcLk()
    {
        return _SCoAcLk;
    }
    
    // Standard Setter
    public void SetSCoAcLk(string value)
    {
        _SCoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetSCoAcLkAsString()
    {
        return _SCoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetSCoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SCoAcLk = value;
    }
    
    // Standard Getter
    public string GetSCountryCode()
    {
        return _SCountryCode;
    }
    
    // Standard Setter
    public void SetSCountryCode(string value)
    {
        _SCountryCode = value;
    }
    
    // Get<>AsString()
    public string GetSCountryCodeAsString()
    {
        return _SCountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetSCountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SCountryCode = value;
    }
    
    // Standard Getter
    public string GetSMainGroup()
    {
        return _SMainGroup;
    }
    
    // Standard Setter
    public void SetSMainGroup(string value)
    {
        _SMainGroup = value;
    }
    
    // Get<>AsString()
    public string GetSMainGroupAsString()
    {
        return _SMainGroup.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetSMainGroupAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SMainGroup = value;
    }
    
    // Standard Getter
    public string GetSSecurityType()
    {
        return _SSecurityType;
    }
    
    // Standard Setter
    public void SetSSecurityType(string value)
    {
        _SSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetSSecurityTypeAsString()
    {
        return _SSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetSSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSecurityType = value;
    }
    
    // Standard Getter
    public string GetSSecuritySortCode()
    {
        return _SSecuritySortCode;
    }
    
    // Standard Setter
    public void SetSSecuritySortCode(string value)
    {
        _SSecuritySortCode = value;
    }
    
    // Get<>AsString()
    public string GetSSecuritySortCodeAsString()
    {
        return _SSecuritySortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetSSecuritySortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSecuritySortCode = value;
    }
    
    // Standard Getter
    public string GetSSedolSort()
    {
        return _SSedolSort;
    }
    
    // Standard Setter
    public void SetSSedolSort(string value)
    {
        _SSedolSort = value;
    }
    
    // Get<>AsString()
    public string GetSSedolSortAsString()
    {
        return _SSedolSort.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetSSedolSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSedolSort = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetSData(string value)
{
    _SData.SetSDataAsString(value);
}
// Nested Class: SData
public class SData
{
    private static int _size = 130;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SCompanyName, is_external=, is_static_class=False, static_prefix=
    private string _SCompanyName ="";
    
    
    
    
    // [DEBUG] Field: STitle, is_external=, is_static_class=False, static_prefix=
    private string _STitle ="";
    
    
    
    
    // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
    private string _Filler28 ="";
    
    
    
    
    // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
    private string _Filler29 ="";
    
    
    
    
    // [DEBUG] Field: Filler30, is_external=, is_static_class=False, static_prefix=
    private string _Filler30 ="";
    
    
    
    
    // [DEBUG] Field: Filler31, is_external=, is_static_class=False, static_prefix=
    private string _Filler31 ="";
    
    
    
    
    // [DEBUG] Field: Filler32, is_external=, is_static_class=False, static_prefix=
    private string _Filler32 ="";
    
    
    
    
    // [DEBUG] Field: Filler33, is_external=, is_static_class=False, static_prefix=
    private string _Filler33 ="";
    
    
    
    
    // [DEBUG] Field: Filler34, is_external=, is_static_class=False, static_prefix=
    private string _Filler34 ="";
    
    
    
    
    // [DEBUG] Field: Filler35, is_external=, is_static_class=False, static_prefix=
    private string _Filler35 ="";
    
    
    
    
    // [DEBUG] Field: Filler36, is_external=, is_static_class=False, static_prefix=
    private string _Filler36 ="";
    
    
    
    
    // [DEBUG] Field: Filler37, is_external=, is_static_class=False, static_prefix=
    private string _Filler37 ="";
    
    
    
    
    // [DEBUG] Field: Filler38, is_external=, is_static_class=False, static_prefix=
    private string _Filler38 ="";
    
    
    
    
    // [DEBUG] Field: Filler39, is_external=, is_static_class=False, static_prefix=
    private string _Filler39 ="";
    
    
    
    
    // [DEBUG] Field: Filler40, is_external=, is_static_class=False, static_prefix=
    private string _Filler40 ="";
    
    
    
    
    // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
    private string _Filler41 ="";
    
    
    
    
public SData() {}

public SData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSCompanyName(data.Substring(offset, 51).Trim());
    offset += 51;
    SetSTitle(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller28(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller29(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller30(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller31(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller32(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller33(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller34(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller35(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller36(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller37(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller38(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller39(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller40(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller41(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SCompanyName.PadRight(51));
    result.Append(_STitle.PadRight(60));
    result.Append(_Filler28.PadRight(19));
    result.Append(_Filler29.PadRight(0));
    result.Append(_Filler30.PadRight(0));
    result.Append(_Filler31.PadRight(0));
    result.Append(_Filler32.PadRight(0));
    result.Append(_Filler33.PadRight(0));
    result.Append(_Filler34.PadRight(0));
    result.Append(_Filler35.PadRight(0));
    result.Append(_Filler36.PadRight(0));
    result.Append(_Filler37.PadRight(0));
    result.Append(_Filler38.PadRight(0));
    result.Append(_Filler39.PadRight(0));
    result.Append(_Filler40.PadRight(0));
    result.Append(_Filler41.PadRight(0));
    
    return result.ToString();
}

public void SetSDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 51 <= data.Length)
    {
        string extracted = data.Substring(offset, 51).Trim();
        SetSCompanyName(extracted);
    }
    offset += 51;
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetSTitle(extracted);
    }
    offset += 60;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller28(extracted);
    }
    offset += 19;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller29(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller30(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller31(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller32(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller33(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller34(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller35(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller36(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller37(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller38(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller39(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller40(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller41(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetSCompanyName()
{
    return _SCompanyName;
}

// Standard Setter
public void SetSCompanyName(string value)
{
    _SCompanyName = value;
}

// Get<>AsString()
public string GetSCompanyNameAsString()
{
    return _SCompanyName.PadRight(51);
}

// Set<>AsString()
public void SetSCompanyNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SCompanyName = value;
}

// Standard Getter
public string GetSTitle()
{
    return _STitle;
}

// Standard Setter
public void SetSTitle(string value)
{
    _STitle = value;
}

// Get<>AsString()
public string GetSTitleAsString()
{
    return _STitle.PadRight(60);
}

// Set<>AsString()
public void SetSTitleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STitle = value;
}

// Standard Getter
public string GetFiller28()
{
    return _Filler28;
}

// Standard Setter
public void SetFiller28(string value)
{
    _Filler28 = value;
}

// Get<>AsString()
public string GetFiller28AsString()
{
    return _Filler28.PadRight(19);
}

// Set<>AsString()
public void SetFiller28AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler28 = value;
}

// Standard Getter
public string GetFiller29()
{
    return _Filler29;
}

// Standard Setter
public void SetFiller29(string value)
{
    _Filler29 = value;
}

// Get<>AsString()
public string GetFiller29AsString()
{
    return _Filler29.PadRight(0);
}

// Set<>AsString()
public void SetFiller29AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler29 = value;
}

// Standard Getter
public string GetFiller30()
{
    return _Filler30;
}

// Standard Setter
public void SetFiller30(string value)
{
    _Filler30 = value;
}

// Get<>AsString()
public string GetFiller30AsString()
{
    return _Filler30.PadRight(0);
}

// Set<>AsString()
public void SetFiller30AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler30 = value;
}

// Standard Getter
public string GetFiller31()
{
    return _Filler31;
}

// Standard Setter
public void SetFiller31(string value)
{
    _Filler31 = value;
}

// Get<>AsString()
public string GetFiller31AsString()
{
    return _Filler31.PadRight(0);
}

// Set<>AsString()
public void SetFiller31AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler31 = value;
}

// Standard Getter
public string GetFiller32()
{
    return _Filler32;
}

// Standard Setter
public void SetFiller32(string value)
{
    _Filler32 = value;
}

// Get<>AsString()
public string GetFiller32AsString()
{
    return _Filler32.PadRight(0);
}

// Set<>AsString()
public void SetFiller32AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler32 = value;
}

// Standard Getter
public string GetFiller33()
{
    return _Filler33;
}

// Standard Setter
public void SetFiller33(string value)
{
    _Filler33 = value;
}

// Get<>AsString()
public string GetFiller33AsString()
{
    return _Filler33.PadRight(0);
}

// Set<>AsString()
public void SetFiller33AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler33 = value;
}

// Standard Getter
public string GetFiller34()
{
    return _Filler34;
}

// Standard Setter
public void SetFiller34(string value)
{
    _Filler34 = value;
}

// Get<>AsString()
public string GetFiller34AsString()
{
    return _Filler34.PadRight(0);
}

// Set<>AsString()
public void SetFiller34AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler34 = value;
}

// Standard Getter
public string GetFiller35()
{
    return _Filler35;
}

// Standard Setter
public void SetFiller35(string value)
{
    _Filler35 = value;
}

// Get<>AsString()
public string GetFiller35AsString()
{
    return _Filler35.PadRight(0);
}

// Set<>AsString()
public void SetFiller35AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler35 = value;
}

// Standard Getter
public string GetFiller36()
{
    return _Filler36;
}

// Standard Setter
public void SetFiller36(string value)
{
    _Filler36 = value;
}

// Get<>AsString()
public string GetFiller36AsString()
{
    return _Filler36.PadRight(0);
}

// Set<>AsString()
public void SetFiller36AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler36 = value;
}

// Standard Getter
public string GetFiller37()
{
    return _Filler37;
}

// Standard Setter
public void SetFiller37(string value)
{
    _Filler37 = value;
}

// Get<>AsString()
public string GetFiller37AsString()
{
    return _Filler37.PadRight(0);
}

// Set<>AsString()
public void SetFiller37AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler37 = value;
}

// Standard Getter
public string GetFiller38()
{
    return _Filler38;
}

// Standard Setter
public void SetFiller38(string value)
{
    _Filler38 = value;
}

// Get<>AsString()
public string GetFiller38AsString()
{
    return _Filler38.PadRight(0);
}

// Set<>AsString()
public void SetFiller38AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler38 = value;
}

// Standard Getter
public string GetFiller39()
{
    return _Filler39;
}

// Standard Setter
public void SetFiller39(string value)
{
    _Filler39 = value;
}

// Get<>AsString()
public string GetFiller39AsString()
{
    return _Filler39.PadRight(0);
}

// Set<>AsString()
public void SetFiller39AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler39 = value;
}

// Standard Getter
public string GetFiller40()
{
    return _Filler40;
}

// Standard Setter
public void SetFiller40(string value)
{
    _Filler40 = value;
}

// Get<>AsString()
public string GetFiller40AsString()
{
    return _Filler40.PadRight(0);
}

// Set<>AsString()
public void SetFiller40AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler40 = value;
}

// Standard Getter
public string GetFiller41()
{
    return _Filler41;
}

// Standard Setter
public void SetFiller41(string value)
{
    _Filler41 = value;
}

// Get<>AsString()
public string GetFiller41AsString()
{
    return _Filler41.PadRight(0);
}

// Set<>AsString()
public void SetFiller41AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler41 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetSSedolData(string value)
{
    _SSedolData.SetSSedolDataAsString(value);
}
// Nested Class: SSedolData
public class SSedolData
{
    private static int _size = 154;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
    private string _Filler42 ="";
    
    
    
    
    // [DEBUG] Field: SSedolNumber, is_external=, is_static_class=False, static_prefix=
    private string _SSedolNumber ="";
    
    
    
    
    // [DEBUG] Field: SIssuersName, is_external=, is_static_class=False, static_prefix=
    private SSedolData.SIssuersName _SIssuersName = new SSedolData.SIssuersName();
    
    
    
    
    // [DEBUG] Field: SStockDescription, is_external=, is_static_class=False, static_prefix=
    private SSedolData.SStockDescription _SStockDescription = new SSedolData.SStockDescription();
    
    
    
    
    // [DEBUG] Field: SLastTrancheContractNo, is_external=, is_static_class=False, static_prefix=
    private string _SLastTrancheContractNo ="";
    
    
    
    
    // [DEBUG] Field: SProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _SProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: SSedolRecordCount, is_external=, is_static_class=False, static_prefix=
    private int _SSedolRecordCount =0;
    
    
    
    
    // [DEBUG] Field: STrancheCount, is_external=, is_static_class=False, static_prefix=
    private int _STrancheCount =0;
    
    
    
    
    // [DEBUG] Field: SPrintFlag, is_external=, is_static_class=False, static_prefix=
    private string _SPrintFlag ="";
    
    
    
    
    // [DEBUG] Field: SHoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _SHoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler43, is_external=, is_static_class=False, static_prefix=
    private string _Filler43 ="";
    
    
    
    
    // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
    private string _Filler44 ="";
    
    
    
    
    // [DEBUG] Field: SReitSecurityType, is_external=, is_static_class=False, static_prefix=
    private string _SReitSecurityType ="";
    
    
    
    
    // [DEBUG] Field: SQuotedIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SQuotedIndicator ="";
    
    
    
    
    // [DEBUG] Field: Filler45, is_external=, is_static_class=False, static_prefix=
    private string _Filler45 ="";
    
    
    
    
    // [DEBUG] Field: SLostIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _SLostIndexation =0;
    
    
    
    
    // [DEBUG] Field: SOffshoreIncomeGain, is_external=, is_static_class=False, static_prefix=
    private decimal _SOffshoreIncomeGain =0;
    
    
    
    
    // [DEBUG] Field: Filler46, is_external=, is_static_class=False, static_prefix=
    private string _Filler46 ="";
    
    
    
    
    // [DEBUG] Field: Filler47, is_external=, is_static_class=False, static_prefix=
    private string _Filler47 ="";
    
    
    
    
    // [DEBUG] Field: Filler48, is_external=, is_static_class=False, static_prefix=
    private string _Filler48 ="";
    
    
    
    
    // [DEBUG] Field: Filler49, is_external=, is_static_class=False, static_prefix=
    private string _Filler49 ="";
    
    
    
    
    // [DEBUG] Field: Filler50, is_external=, is_static_class=False, static_prefix=
    private string _Filler50 ="";
    
    
    
    
    // [DEBUG] Field: Filler51, is_external=, is_static_class=False, static_prefix=
    private string _Filler51 ="";
    
    
    
    
    // [DEBUG] Field: Filler52, is_external=, is_static_class=False, static_prefix=
    private string _Filler52 ="";
    
    
    
    
public SSedolData() {}

public SSedolData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller42(data.Substring(offset, 19).Trim());
    offset += 19;
    SetSSedolNumber(data.Substring(offset, 7).Trim());
    offset += 7;
    _SIssuersName.SetSIssuersNameAsString(data.Substring(offset, SIssuersName.GetSize()));
    offset += 35;
    _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset, SStockDescription.GetSize()));
    offset += 40;
    SetSLastTrancheContractNo(data.Substring(offset, 10).Trim());
    offset += 10;
    SetSProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetSSedolRecordCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSTrancheCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSPrintFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSHoldingFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller43(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller44(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSReitSecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSQuotedIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller45(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSLostIndexation(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSOffshoreIncomeGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetFiller46(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller47(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller48(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller49(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller50(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller51(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller52(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSSedolDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler42.PadRight(19));
    result.Append(_SSedolNumber.PadRight(7));
    result.Append(_SIssuersName.GetSIssuersNameAsString());
    result.Append(_SStockDescription.GetSStockDescriptionAsString());
    result.Append(_SLastTrancheContractNo.PadRight(10));
    result.Append(_SProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SSedolRecordCount.ToString().PadLeft(3, '0'));
    result.Append(_STrancheCount.ToString().PadLeft(3, '0'));
    result.Append(_SPrintFlag.PadRight(1));
    result.Append(_SHoldingFlag.PadRight(1));
    result.Append(_Filler43.PadRight(0));
    result.Append(_Filler44.PadRight(0));
    result.Append(_SReitSecurityType.PadRight(0));
    result.Append(_SQuotedIndicator.PadRight(0));
    result.Append(_Filler45.PadRight(0));
    result.Append(_SLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SOffshoreIncomeGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler46.PadRight(0));
    result.Append(_Filler47.PadRight(0));
    result.Append(_Filler48.PadRight(0));
    result.Append(_Filler49.PadRight(0));
    result.Append(_Filler50.PadRight(0));
    result.Append(_Filler51.PadRight(0));
    result.Append(_Filler52.PadRight(0));
    
    return result.ToString();
}

public void SetSSedolDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller42(extracted);
    }
    offset += 19;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetSSedolNumber(extracted);
    }
    offset += 7;
    if (offset + 35 <= data.Length)
    {
        _SIssuersName.SetSIssuersNameAsString(data.Substring(offset, 35));
    }
    else
    {
        _SIssuersName.SetSIssuersNameAsString(data.Substring(offset));
    }
    offset += 35;
    if (offset + 40 <= data.Length)
    {
        _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset, 40));
    }
    else
    {
        _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset));
    }
    offset += 40;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetSLastTrancheContractNo(extracted);
    }
    offset += 10;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSProfitLoss(parsedDec);
    }
    offset += 11;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSSedolRecordCount(parsedInt);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSTrancheCount(parsedInt);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSPrintFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSHoldingFlag(extracted);
    }
    offset += 1;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller43(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller44(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSReitSecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSQuotedIndicator(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller45(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSLostIndexation(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSOffshoreIncomeGain(parsedDec);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller46(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller47(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller48(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller49(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller50(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller51(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller52(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller42()
{
    return _Filler42;
}

// Standard Setter
public void SetFiller42(string value)
{
    _Filler42 = value;
}

// Get<>AsString()
public string GetFiller42AsString()
{
    return _Filler42.PadRight(19);
}

// Set<>AsString()
public void SetFiller42AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler42 = value;
}

// Standard Getter
public string GetSSedolNumber()
{
    return _SSedolNumber;
}

// Standard Setter
public void SetSSedolNumber(string value)
{
    _SSedolNumber = value;
}

// Get<>AsString()
public string GetSSedolNumberAsString()
{
    return _SSedolNumber.PadRight(7);
}

// Set<>AsString()
public void SetSSedolNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SSedolNumber = value;
}

// Standard Getter
public SIssuersName GetSIssuersName()
{
    return _SIssuersName;
}

// Standard Setter
public void SetSIssuersName(SIssuersName value)
{
    _SIssuersName = value;
}

// Get<>AsString()
public string GetSIssuersNameAsString()
{
    return _SIssuersName != null ? _SIssuersName.GetSIssuersNameAsString() : "";
}

// Set<>AsString()
public void SetSIssuersNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SIssuersName == null)
    {
        _SIssuersName = new SIssuersName();
    }
    _SIssuersName.SetSIssuersNameAsString(value);
}

// Standard Getter
public SStockDescription GetSStockDescription()
{
    return _SStockDescription;
}

// Standard Setter
public void SetSStockDescription(SStockDescription value)
{
    _SStockDescription = value;
}

// Get<>AsString()
public string GetSStockDescriptionAsString()
{
    return _SStockDescription != null ? _SStockDescription.GetSStockDescriptionAsString() : "";
}

// Set<>AsString()
public void SetSStockDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SStockDescription == null)
    {
        _SStockDescription = new SStockDescription();
    }
    _SStockDescription.SetSStockDescriptionAsString(value);
}

// Standard Getter
public string GetSLastTrancheContractNo()
{
    return _SLastTrancheContractNo;
}

// Standard Setter
public void SetSLastTrancheContractNo(string value)
{
    _SLastTrancheContractNo = value;
}

// Get<>AsString()
public string GetSLastTrancheContractNoAsString()
{
    return _SLastTrancheContractNo.PadRight(10);
}

// Set<>AsString()
public void SetSLastTrancheContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SLastTrancheContractNo = value;
}

// Standard Getter
public decimal GetSProfitLoss()
{
    return _SProfitLoss;
}

// Standard Setter
public void SetSProfitLoss(decimal value)
{
    _SProfitLoss = value;
}

// Get<>AsString()
public string GetSProfitLossAsString()
{
    return _SProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SProfitLoss = parsed;
}

// Standard Getter
public int GetSSedolRecordCount()
{
    return _SSedolRecordCount;
}

// Standard Setter
public void SetSSedolRecordCount(int value)
{
    _SSedolRecordCount = value;
}

// Get<>AsString()
public string GetSSedolRecordCountAsString()
{
    return _SSedolRecordCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSSedolRecordCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SSedolRecordCount = parsed;
}

// Standard Getter
public int GetSTrancheCount()
{
    return _STrancheCount;
}

// Standard Setter
public void SetSTrancheCount(int value)
{
    _STrancheCount = value;
}

// Get<>AsString()
public string GetSTrancheCountAsString()
{
    return _STrancheCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSTrancheCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _STrancheCount = parsed;
}

// Standard Getter
public string GetSPrintFlag()
{
    return _SPrintFlag;
}

// Standard Setter
public void SetSPrintFlag(string value)
{
    _SPrintFlag = value;
}

// Get<>AsString()
public string GetSPrintFlagAsString()
{
    return _SPrintFlag.PadRight(1);
}

// Set<>AsString()
public void SetSPrintFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SPrintFlag = value;
}

// Standard Getter
public string GetSHoldingFlag()
{
    return _SHoldingFlag;
}

// Standard Setter
public void SetSHoldingFlag(string value)
{
    _SHoldingFlag = value;
}

// Get<>AsString()
public string GetSHoldingFlagAsString()
{
    return _SHoldingFlag.PadRight(1);
}

// Set<>AsString()
public void SetSHoldingFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHoldingFlag = value;
}

// Standard Getter
public string GetFiller43()
{
    return _Filler43;
}

// Standard Setter
public void SetFiller43(string value)
{
    _Filler43 = value;
}

// Get<>AsString()
public string GetFiller43AsString()
{
    return _Filler43.PadRight(0);
}

// Set<>AsString()
public void SetFiller43AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler43 = value;
}

// Standard Getter
public string GetFiller44()
{
    return _Filler44;
}

// Standard Setter
public void SetFiller44(string value)
{
    _Filler44 = value;
}

// Get<>AsString()
public string GetFiller44AsString()
{
    return _Filler44.PadRight(0);
}

// Set<>AsString()
public void SetFiller44AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler44 = value;
}

// Standard Getter
public string GetSReitSecurityType()
{
    return _SReitSecurityType;
}

// Standard Setter
public void SetSReitSecurityType(string value)
{
    _SReitSecurityType = value;
}

// Get<>AsString()
public string GetSReitSecurityTypeAsString()
{
    return _SReitSecurityType.PadRight(0);
}

// Set<>AsString()
public void SetSReitSecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SReitSecurityType = value;
}

// Standard Getter
public string GetSQuotedIndicator()
{
    return _SQuotedIndicator;
}

// Standard Setter
public void SetSQuotedIndicator(string value)
{
    _SQuotedIndicator = value;
}

// Get<>AsString()
public string GetSQuotedIndicatorAsString()
{
    return _SQuotedIndicator.PadRight(0);
}

// Set<>AsString()
public void SetSQuotedIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SQuotedIndicator = value;
}

// Standard Getter
public string GetFiller45()
{
    return _Filler45;
}

// Standard Setter
public void SetFiller45(string value)
{
    _Filler45 = value;
}

// Get<>AsString()
public string GetFiller45AsString()
{
    return _Filler45.PadRight(0);
}

// Set<>AsString()
public void SetFiller45AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler45 = value;
}

// Standard Getter
public decimal GetSLostIndexation()
{
    return _SLostIndexation;
}

// Standard Setter
public void SetSLostIndexation(decimal value)
{
    _SLostIndexation = value;
}

// Get<>AsString()
public string GetSLostIndexationAsString()
{
    return _SLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSLostIndexationAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SLostIndexation = parsed;
}

// Standard Getter
public decimal GetSOffshoreIncomeGain()
{
    return _SOffshoreIncomeGain;
}

// Standard Setter
public void SetSOffshoreIncomeGain(decimal value)
{
    _SOffshoreIncomeGain = value;
}

// Get<>AsString()
public string GetSOffshoreIncomeGainAsString()
{
    return _SOffshoreIncomeGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSOffshoreIncomeGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SOffshoreIncomeGain = parsed;
}

// Standard Getter
public string GetFiller46()
{
    return _Filler46;
}

// Standard Setter
public void SetFiller46(string value)
{
    _Filler46 = value;
}

// Get<>AsString()
public string GetFiller46AsString()
{
    return _Filler46.PadRight(0);
}

// Set<>AsString()
public void SetFiller46AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler46 = value;
}

// Standard Getter
public string GetFiller47()
{
    return _Filler47;
}

// Standard Setter
public void SetFiller47(string value)
{
    _Filler47 = value;
}

// Get<>AsString()
public string GetFiller47AsString()
{
    return _Filler47.PadRight(0);
}

// Set<>AsString()
public void SetFiller47AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler47 = value;
}

// Standard Getter
public string GetFiller48()
{
    return _Filler48;
}

// Standard Setter
public void SetFiller48(string value)
{
    _Filler48 = value;
}

// Get<>AsString()
public string GetFiller48AsString()
{
    return _Filler48.PadRight(0);
}

// Set<>AsString()
public void SetFiller48AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler48 = value;
}

// Standard Getter
public string GetFiller49()
{
    return _Filler49;
}

// Standard Setter
public void SetFiller49(string value)
{
    _Filler49 = value;
}

// Get<>AsString()
public string GetFiller49AsString()
{
    return _Filler49.PadRight(0);
}

// Set<>AsString()
public void SetFiller49AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler49 = value;
}

// Standard Getter
public string GetFiller50()
{
    return _Filler50;
}

// Standard Setter
public void SetFiller50(string value)
{
    _Filler50 = value;
}

// Get<>AsString()
public string GetFiller50AsString()
{
    return _Filler50.PadRight(0);
}

// Set<>AsString()
public void SetFiller50AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler50 = value;
}

// Standard Getter
public string GetFiller51()
{
    return _Filler51;
}

// Standard Setter
public void SetFiller51(string value)
{
    _Filler51 = value;
}

// Get<>AsString()
public string GetFiller51AsString()
{
    return _Filler51.PadRight(0);
}

// Set<>AsString()
public void SetFiller51AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler51 = value;
}

// Standard Getter
public string GetFiller52()
{
    return _Filler52;
}

// Standard Setter
public void SetFiller52(string value)
{
    _Filler52 = value;
}

// Get<>AsString()
public string GetFiller52AsString()
{
    return _Filler52.PadRight(0);
}

// Set<>AsString()
public void SetFiller52AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler52 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: SIssuersName
public class SIssuersName
{
    private static int _size = 35;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SIssuersName1, is_external=, is_static_class=False, static_prefix=
    private string _SIssuersName1 ="";
    
    
    
    
    // [DEBUG] Field: SIssuersName2, is_external=, is_static_class=False, static_prefix=
    private string _SIssuersName2 ="";
    
    
    
    
public SIssuersName() {}

public SIssuersName(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSIssuersName1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetSIssuersName2(data.Substring(offset, 15).Trim());
    offset += 15;
    
}

// Serialization methods
public string GetSIssuersNameAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SIssuersName1.PadRight(20));
    result.Append(_SIssuersName2.PadRight(15));
    
    return result.ToString();
}

public void SetSIssuersNameAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSIssuersName1(extracted);
    }
    offset += 20;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetSIssuersName2(extracted);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public string GetSIssuersName1()
{
    return _SIssuersName1;
}

// Standard Setter
public void SetSIssuersName1(string value)
{
    _SIssuersName1 = value;
}

// Get<>AsString()
public string GetSIssuersName1AsString()
{
    return _SIssuersName1.PadRight(20);
}

// Set<>AsString()
public void SetSIssuersName1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIssuersName1 = value;
}

// Standard Getter
public string GetSIssuersName2()
{
    return _SIssuersName2;
}

// Standard Setter
public void SetSIssuersName2(string value)
{
    _SIssuersName2 = value;
}

// Get<>AsString()
public string GetSIssuersName2AsString()
{
    return _SIssuersName2.PadRight(15);
}

// Set<>AsString()
public void SetSIssuersName2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIssuersName2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: SStockDescription
public class SStockDescription
{
    private static int _size = 40;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SStockDescription1, is_external=, is_static_class=False, static_prefix=
    private string _SStockDescription1 ="";
    
    
    
    
    // [DEBUG] Field: SStockDescription2, is_external=, is_static_class=False, static_prefix=
    private string _SStockDescription2 ="";
    
    
    
    
public SStockDescription() {}

public SStockDescription(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSStockDescription1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetSStockDescription2(data.Substring(offset, 20).Trim());
    offset += 20;
    
}

// Serialization methods
public string GetSStockDescriptionAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SStockDescription1.PadRight(20));
    result.Append(_SStockDescription2.PadRight(20));
    
    return result.ToString();
}

public void SetSStockDescriptionAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSStockDescription1(extracted);
    }
    offset += 20;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSStockDescription2(extracted);
    }
    offset += 20;
}

// Getter and Setter methods

// Standard Getter
public string GetSStockDescription1()
{
    return _SStockDescription1;
}

// Standard Setter
public void SetSStockDescription1(string value)
{
    _SStockDescription1 = value;
}

// Get<>AsString()
public string GetSStockDescription1AsString()
{
    return _SStockDescription1.PadRight(20);
}

// Set<>AsString()
public void SetSStockDescription1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SStockDescription1 = value;
}

// Standard Getter
public string GetSStockDescription2()
{
    return _SStockDescription2;
}

// Standard Setter
public void SetSStockDescription2(string value)
{
    _SStockDescription2 = value;
}

// Get<>AsString()
public string GetSStockDescription2AsString()
{
    return _SStockDescription2.PadRight(20);
}

// Set<>AsString()
public void SetSStockDescription2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SStockDescription2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetSTrancheDetailData(string value)
{
    _STrancheDetailData.SetSTrancheDetailDataAsString(value);
}
// Nested Class: STrancheDetailData
public class STrancheDetailData
{
    private static int _size = 745;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SAcquisitionDateX, is_external=, is_static_class=False, static_prefix=
    private string _SAcquisitionDateX ="";
    
    
    
    
    // [DEBUG] Field: SAcquisitionDate9, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData.SAcquisitionDate9 _SAcquisitionDate9 = new STrancheDetailData.SAcquisitionDate9();
    
    
    
    
    // [DEBUG] Field: STrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _STrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: SLineNumber, is_external=, is_static_class=False, static_prefix=
    private int _SLineNumber =0;
    
    
    // 88-level condition checks for SLineNumber
    public bool IsSTrancheHeader()
    {
        if (this._SLineNumber == 00000) return true;
        return false;
    }
    
    
    // [DEBUG] Field: STrancheData, is_external=, is_static_class=False, static_prefix=
    private string _STrancheData ="";
    
    
    
    
    // [DEBUG] Field: STrancheHeaderData, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData.STrancheHeaderData _STrancheHeaderData = new STrancheDetailData.STrancheHeaderData();
    
    
    
    
    // [DEBUG] Field: STrancheLine, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData.STrancheLine _STrancheLine = new STrancheDetailData.STrancheLine();
    
    
    
    
public STrancheDetailData() {}

public STrancheDetailData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSAcquisitionDateX(data.Substring(offset, 6).Trim());
    offset += 6;
    _SAcquisitionDate9.SetSAcquisitionDate9AsString(data.Substring(offset, SAcquisitionDate9.GetSize()));
    offset += 6;
    SetSTrancheContractNumber(data.Substring(offset, 10).Trim());
    offset += 10;
    SetSLineNumber(int.Parse(data.Substring(offset, 5).Trim()));
    offset += 5;
    SetSTrancheData(data.Substring(offset, 233).Trim());
    offset += 233;
    _STrancheHeaderData.SetSTrancheHeaderDataAsString(data.Substring(offset, STrancheHeaderData.GetSize()));
    offset += 110;
    _STrancheLine.SetSTrancheLineAsString(data.Substring(offset, STrancheLine.GetSize()));
    offset += 375;
    
}

// Serialization methods
public string GetSTrancheDetailDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SAcquisitionDateX.PadRight(6));
    result.Append(_SAcquisitionDate9.GetSAcquisitionDate9AsString());
    result.Append(_STrancheContractNumber.PadRight(10));
    result.Append(_SLineNumber.ToString().PadLeft(5, '0'));
    result.Append(_STrancheData.PadRight(233));
    result.Append(_STrancheHeaderData.GetSTrancheHeaderDataAsString());
    result.Append(_STrancheLine.GetSTrancheLineAsString());
    
    return result.ToString();
}

public void SetSTrancheDetailDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetSAcquisitionDateX(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _SAcquisitionDate9.SetSAcquisitionDate9AsString(data.Substring(offset, 6));
    }
    else
    {
        _SAcquisitionDate9.SetSAcquisitionDate9AsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetSTrancheContractNumber(extracted);
    }
    offset += 10;
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSLineNumber(parsedInt);
    }
    offset += 5;
    if (offset + 233 <= data.Length)
    {
        string extracted = data.Substring(offset, 233).Trim();
        SetSTrancheData(extracted);
    }
    offset += 233;
    if (offset + 110 <= data.Length)
    {
        _STrancheHeaderData.SetSTrancheHeaderDataAsString(data.Substring(offset, 110));
    }
    else
    {
        _STrancheHeaderData.SetSTrancheHeaderDataAsString(data.Substring(offset));
    }
    offset += 110;
    if (offset + 375 <= data.Length)
    {
        _STrancheLine.SetSTrancheLineAsString(data.Substring(offset, 375));
    }
    else
    {
        _STrancheLine.SetSTrancheLineAsString(data.Substring(offset));
    }
    offset += 375;
}

// Getter and Setter methods

// Standard Getter
public string GetSAcquisitionDateX()
{
    return _SAcquisitionDateX;
}

// Standard Setter
public void SetSAcquisitionDateX(string value)
{
    _SAcquisitionDateX = value;
}

// Get<>AsString()
public string GetSAcquisitionDateXAsString()
{
    return _SAcquisitionDateX.PadRight(6);
}

// Set<>AsString()
public void SetSAcquisitionDateXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SAcquisitionDateX = value;
}

// Standard Getter
public SAcquisitionDate9 GetSAcquisitionDate9()
{
    return _SAcquisitionDate9;
}

// Standard Setter
public void SetSAcquisitionDate9(SAcquisitionDate9 value)
{
    _SAcquisitionDate9 = value;
}

// Get<>AsString()
public string GetSAcquisitionDate9AsString()
{
    return _SAcquisitionDate9 != null ? _SAcquisitionDate9.GetSAcquisitionDate9AsString() : "";
}

// Set<>AsString()
public void SetSAcquisitionDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SAcquisitionDate9 == null)
    {
        _SAcquisitionDate9 = new SAcquisitionDate9();
    }
    _SAcquisitionDate9.SetSAcquisitionDate9AsString(value);
}

// Standard Getter
public string GetSTrancheContractNumber()
{
    return _STrancheContractNumber;
}

// Standard Setter
public void SetSTrancheContractNumber(string value)
{
    _STrancheContractNumber = value;
}

// Get<>AsString()
public string GetSTrancheContractNumberAsString()
{
    return _STrancheContractNumber.PadRight(10);
}

// Set<>AsString()
public void SetSTrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheContractNumber = value;
}

// Standard Getter
public int GetSLineNumber()
{
    return _SLineNumber;
}

// Standard Setter
public void SetSLineNumber(int value)
{
    _SLineNumber = value;
}

// Get<>AsString()
public string GetSLineNumberAsString()
{
    return _SLineNumber.ToString().PadLeft(5, '0');
}

// Set<>AsString()
public void SetSLineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SLineNumber = parsed;
}

// Standard Getter
public string GetSTrancheData()
{
    return _STrancheData;
}

// Standard Setter
public void SetSTrancheData(string value)
{
    _STrancheData = value;
}

// Get<>AsString()
public string GetSTrancheDataAsString()
{
    return _STrancheData.PadRight(233);
}

// Set<>AsString()
public void SetSTrancheDataAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheData = value;
}

// Standard Getter
public STrancheHeaderData GetSTrancheHeaderData()
{
    return _STrancheHeaderData;
}

// Standard Setter
public void SetSTrancheHeaderData(STrancheHeaderData value)
{
    _STrancheHeaderData = value;
}

// Get<>AsString()
public string GetSTrancheHeaderDataAsString()
{
    return _STrancheHeaderData != null ? _STrancheHeaderData.GetSTrancheHeaderDataAsString() : "";
}

// Set<>AsString()
public void SetSTrancheHeaderDataAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_STrancheHeaderData == null)
    {
        _STrancheHeaderData = new STrancheHeaderData();
    }
    _STrancheHeaderData.SetSTrancheHeaderDataAsString(value);
}

// Standard Getter
public STrancheLine GetSTrancheLine()
{
    return _STrancheLine;
}

// Standard Setter
public void SetSTrancheLine(STrancheLine value)
{
    _STrancheLine = value;
}

// Get<>AsString()
public string GetSTrancheLineAsString()
{
    return _STrancheLine != null ? _STrancheLine.GetSTrancheLineAsString() : "";
}

// Set<>AsString()
public void SetSTrancheLineAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_STrancheLine == null)
    {
        _STrancheLine = new STrancheLine();
    }
    _STrancheLine.SetSTrancheLineAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: SAcquisitionDate9
public class SAcquisitionDate9
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SAcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private int _SAcquisitionDateYy =0;
    
    
    
    
    // [DEBUG] Field: SAcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private int _SAcquisitionDateMm =0;
    
    
    
    
    // [DEBUG] Field: SAcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private int _SAcquisitionDateDd =0;
    
    
    
    
public SAcquisitionDate9() {}

public SAcquisitionDate9(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSAcquisitionDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSAcquisitionDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSAcquisitionDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetSAcquisitionDate9AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SAcquisitionDateYy.ToString().PadLeft(2, '0'));
    result.Append(_SAcquisitionDateMm.ToString().PadLeft(2, '0'));
    result.Append(_SAcquisitionDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetSAcquisitionDate9AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSAcquisitionDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSAcquisitionDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSAcquisitionDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetSAcquisitionDateYy()
{
    return _SAcquisitionDateYy;
}

// Standard Setter
public void SetSAcquisitionDateYy(int value)
{
    _SAcquisitionDateYy = value;
}

// Get<>AsString()
public string GetSAcquisitionDateYyAsString()
{
    return _SAcquisitionDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSAcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SAcquisitionDateYy = parsed;
}

// Standard Getter
public int GetSAcquisitionDateMm()
{
    return _SAcquisitionDateMm;
}

// Standard Setter
public void SetSAcquisitionDateMm(int value)
{
    _SAcquisitionDateMm = value;
}

// Get<>AsString()
public string GetSAcquisitionDateMmAsString()
{
    return _SAcquisitionDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSAcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SAcquisitionDateMm = parsed;
}

// Standard Getter
public int GetSAcquisitionDateDd()
{
    return _SAcquisitionDateDd;
}

// Standard Setter
public void SetSAcquisitionDateDd(int value)
{
    _SAcquisitionDateDd = value;
}

// Get<>AsString()
public string GetSAcquisitionDateDdAsString()
{
    return _SAcquisitionDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSAcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SAcquisitionDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: STrancheHeaderData
public class STrancheHeaderData
{
    private static int _size = 110;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler53, is_external=, is_static_class=False, static_prefix=
    private string _Filler53 ="";
    
    
    
    
    // [DEBUG] Field: STrancheRecordCount, is_external=, is_static_class=False, static_prefix=
    private int _STrancheRecordCount =0;
    
    
    
    
    // [DEBUG] Field: Filler54, is_external=, is_static_class=False, static_prefix=
    private string _Filler54 ="";
    
    
    
    
    // [DEBUG] Field: Filler55, is_external=, is_static_class=False, static_prefix=
    private string _Filler55 ="";
    
    
    
    
    // [DEBUG] Field: Filler56, is_external=, is_static_class=False, static_prefix=
    private string _Filler56 ="";
    
    
    
    
    // [DEBUG] Field: Filler57, is_external=, is_static_class=False, static_prefix=
    private string _Filler57 ="";
    
    
    
    
    // [DEBUG] Field: Filler58, is_external=, is_static_class=False, static_prefix=
    private string _Filler58 ="";
    
    
    
    
    // [DEBUG] Field: Filler59, is_external=, is_static_class=False, static_prefix=
    private string _Filler59 ="";
    
    
    
    
    // [DEBUG] Field: Filler60, is_external=, is_static_class=False, static_prefix=
    private string _Filler60 ="";
    
    
    
    
    // [DEBUG] Field: Filler61, is_external=, is_static_class=False, static_prefix=
    private string _Filler61 ="";
    
    
    
    
    // [DEBUG] Field: Filler62, is_external=, is_static_class=False, static_prefix=
    private string _Filler62 ="";
    
    
    
    
    // [DEBUG] Field: Filler63, is_external=, is_static_class=False, static_prefix=
    private string _Filler63 ="";
    
    
    
    
    // [DEBUG] Field: Filler64, is_external=, is_static_class=False, static_prefix=
    private string _Filler64 ="";
    
    
    
    
    // [DEBUG] Field: Filler65, is_external=, is_static_class=False, static_prefix=
    private string _Filler65 ="";
    
    
    
    
    // [DEBUG] Field: Filler66, is_external=, is_static_class=False, static_prefix=
    private string _Filler66 ="";
    
    
    
    
    // [DEBUG] Field: Filler67, is_external=, is_static_class=False, static_prefix=
    private string _Filler67 ="";
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private string _Filler68 ="";
    
    
    
    
public STrancheHeaderData() {}

public STrancheHeaderData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller53(data.Substring(offset, 2).Trim());
    offset += 2;
    SetSTrancheRecordCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetFiller54(data.Substring(offset, 105).Trim());
    offset += 105;
    SetFiller55(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller56(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller57(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller58(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller59(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller60(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller61(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller62(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller63(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller64(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller65(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller66(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller67(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller68(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSTrancheHeaderDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler53.PadRight(2));
    result.Append(_STrancheRecordCount.ToString().PadLeft(3, '0'));
    result.Append(_Filler54.PadRight(105));
    result.Append(_Filler55.PadRight(0));
    result.Append(_Filler56.PadRight(0));
    result.Append(_Filler57.PadRight(0));
    result.Append(_Filler58.PadRight(0));
    result.Append(_Filler59.PadRight(0));
    result.Append(_Filler60.PadRight(0));
    result.Append(_Filler61.PadRight(0));
    result.Append(_Filler62.PadRight(0));
    result.Append(_Filler63.PadRight(0));
    result.Append(_Filler64.PadRight(0));
    result.Append(_Filler65.PadRight(0));
    result.Append(_Filler66.PadRight(0));
    result.Append(_Filler67.PadRight(0));
    result.Append(_Filler68.PadRight(0));
    
    return result.ToString();
}

public void SetSTrancheHeaderDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller53(extracted);
    }
    offset += 2;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSTrancheRecordCount(parsedInt);
    }
    offset += 3;
    if (offset + 105 <= data.Length)
    {
        string extracted = data.Substring(offset, 105).Trim();
        SetFiller54(extracted);
    }
    offset += 105;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller55(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller56(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller57(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller58(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller59(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller60(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller61(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller62(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller63(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller64(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller65(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller66(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller67(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller68(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller53()
{
    return _Filler53;
}

// Standard Setter
public void SetFiller53(string value)
{
    _Filler53 = value;
}

// Get<>AsString()
public string GetFiller53AsString()
{
    return _Filler53.PadRight(2);
}

// Set<>AsString()
public void SetFiller53AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler53 = value;
}

// Standard Getter
public int GetSTrancheRecordCount()
{
    return _STrancheRecordCount;
}

// Standard Setter
public void SetSTrancheRecordCount(int value)
{
    _STrancheRecordCount = value;
}

// Get<>AsString()
public string GetSTrancheRecordCountAsString()
{
    return _STrancheRecordCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSTrancheRecordCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _STrancheRecordCount = parsed;
}

// Standard Getter
public string GetFiller54()
{
    return _Filler54;
}

// Standard Setter
public void SetFiller54(string value)
{
    _Filler54 = value;
}

// Get<>AsString()
public string GetFiller54AsString()
{
    return _Filler54.PadRight(105);
}

// Set<>AsString()
public void SetFiller54AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler54 = value;
}

// Standard Getter
public string GetFiller55()
{
    return _Filler55;
}

// Standard Setter
public void SetFiller55(string value)
{
    _Filler55 = value;
}

// Get<>AsString()
public string GetFiller55AsString()
{
    return _Filler55.PadRight(0);
}

// Set<>AsString()
public void SetFiller55AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler55 = value;
}

// Standard Getter
public string GetFiller56()
{
    return _Filler56;
}

// Standard Setter
public void SetFiller56(string value)
{
    _Filler56 = value;
}

// Get<>AsString()
public string GetFiller56AsString()
{
    return _Filler56.PadRight(0);
}

// Set<>AsString()
public void SetFiller56AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler56 = value;
}

// Standard Getter
public string GetFiller57()
{
    return _Filler57;
}

// Standard Setter
public void SetFiller57(string value)
{
    _Filler57 = value;
}

// Get<>AsString()
public string GetFiller57AsString()
{
    return _Filler57.PadRight(0);
}

// Set<>AsString()
public void SetFiller57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler57 = value;
}

// Standard Getter
public string GetFiller58()
{
    return _Filler58;
}

// Standard Setter
public void SetFiller58(string value)
{
    _Filler58 = value;
}

// Get<>AsString()
public string GetFiller58AsString()
{
    return _Filler58.PadRight(0);
}

// Set<>AsString()
public void SetFiller58AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler58 = value;
}

// Standard Getter
public string GetFiller59()
{
    return _Filler59;
}

// Standard Setter
public void SetFiller59(string value)
{
    _Filler59 = value;
}

// Get<>AsString()
public string GetFiller59AsString()
{
    return _Filler59.PadRight(0);
}

// Set<>AsString()
public void SetFiller59AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler59 = value;
}

// Standard Getter
public string GetFiller60()
{
    return _Filler60;
}

// Standard Setter
public void SetFiller60(string value)
{
    _Filler60 = value;
}

// Get<>AsString()
public string GetFiller60AsString()
{
    return _Filler60.PadRight(0);
}

// Set<>AsString()
public void SetFiller60AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler60 = value;
}

// Standard Getter
public string GetFiller61()
{
    return _Filler61;
}

// Standard Setter
public void SetFiller61(string value)
{
    _Filler61 = value;
}

// Get<>AsString()
public string GetFiller61AsString()
{
    return _Filler61.PadRight(0);
}

// Set<>AsString()
public void SetFiller61AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler61 = value;
}

// Standard Getter
public string GetFiller62()
{
    return _Filler62;
}

// Standard Setter
public void SetFiller62(string value)
{
    _Filler62 = value;
}

// Get<>AsString()
public string GetFiller62AsString()
{
    return _Filler62.PadRight(0);
}

// Set<>AsString()
public void SetFiller62AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler62 = value;
}

// Standard Getter
public string GetFiller63()
{
    return _Filler63;
}

// Standard Setter
public void SetFiller63(string value)
{
    _Filler63 = value;
}

// Get<>AsString()
public string GetFiller63AsString()
{
    return _Filler63.PadRight(0);
}

// Set<>AsString()
public void SetFiller63AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler63 = value;
}

// Standard Getter
public string GetFiller64()
{
    return _Filler64;
}

// Standard Setter
public void SetFiller64(string value)
{
    _Filler64 = value;
}

// Get<>AsString()
public string GetFiller64AsString()
{
    return _Filler64.PadRight(0);
}

// Set<>AsString()
public void SetFiller64AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler64 = value;
}

// Standard Getter
public string GetFiller65()
{
    return _Filler65;
}

// Standard Setter
public void SetFiller65(string value)
{
    _Filler65 = value;
}

// Get<>AsString()
public string GetFiller65AsString()
{
    return _Filler65.PadRight(0);
}

// Set<>AsString()
public void SetFiller65AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler65 = value;
}

// Standard Getter
public string GetFiller66()
{
    return _Filler66;
}

// Standard Setter
public void SetFiller66(string value)
{
    _Filler66 = value;
}

// Get<>AsString()
public string GetFiller66AsString()
{
    return _Filler66.PadRight(0);
}

// Set<>AsString()
public void SetFiller66AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler66 = value;
}

// Standard Getter
public string GetFiller67()
{
    return _Filler67;
}

// Standard Setter
public void SetFiller67(string value)
{
    _Filler67 = value;
}

// Get<>AsString()
public string GetFiller67AsString()
{
    return _Filler67.PadRight(0);
}

// Set<>AsString()
public void SetFiller67AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler67 = value;
}

// Standard Getter
public string GetFiller68()
{
    return _Filler68;
}

// Standard Setter
public void SetFiller68(string value)
{
    _Filler68 = value;
}

// Get<>AsString()
public string GetFiller68AsString()
{
    return _Filler68.PadRight(0);
}

// Set<>AsString()
public void SetFiller68AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler68 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: STrancheLine
public class STrancheLine
{
    private static int _size = 375;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SBdvLineIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SBdvLineIndicator ="";
    
    
    
    
    // [DEBUG] Field: STrancheType, is_external=, is_static_class=False, static_prefix=
    private string _STrancheType ="";
    
    
    // 88-level condition checks for STrancheType
    public bool IsSPoolTranche()
    {
        if (this._STrancheType == "'A'") return true;
        return false;
    }
    public bool IsSParallelPoolTranche()
    {
        if (this._STrancheType == "'B'") return true;
        return false;
    }
    public bool IsSIndexPoolTranche()
    {
        if (this._STrancheType == "'C'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SMovementDateX, is_external=, is_static_class=False, static_prefix=
    private string _SMovementDateX ="";
    
    
    
    
    // [DEBUG] Field: SMovementDate9, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.SMovementDate9 _SMovementDate9 = new STrancheLine.SMovementDate9();
    
    
    
    
    // [DEBUG] Field: SHatchIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SHatchIndicator ="";
    
    
    
    
    // [DEBUG] Field: SMovementDescription, is_external=, is_static_class=False, static_prefix=
    private string _SMovementDescription ="";
    
    
    // 88-level condition checks for SMovementDescription
    public bool IsSale()
    {
        if (this._SMovementDescription == "'DISPOSAL'") return true;
        if (this._SMovementDescription == "'STOCK SALE'") return true;
        if (this._SMovementDescription == "'RIGHTS SALE'") return true;
        if (this._SMovementDescription == "'RIGHTS LAPSE'") return true;
        if (this._SMovementDescription == "'REDEMPTION'") return true;
        if (this._SMovementDescription == "'LIQUIDATION'") return true;
        if (this._SMovementDescription == "'DEEMED DISPOSAL'") return true;
        if (this._SMovementDescription == "'RIGHTS FRACTION'") return true;
        if (this._SMovementDescription == "'EQUALISATION'") return true;
        return false;
    }
    public bool IsCdSale()
    {
        if (this._SMovementDescription == "'CAPITAL DISTBTN'") return true;
        return false;
    }
    public bool IsRevaluationSale()
    {
        if (this._SMovementDescription == "'REVALUATION'") return true;
        return false;
    }
    public bool IsAcquisition()
    {
        if (this._SMovementDescription == "'SHARE EXCHANGE'") return true;
        if (this._SMovementDescription == "'ACCUMULATION'") return true;
        if (this._SMovementDescription == "'PRIVATISATION'") return true;
        if (this._SMovementDescription == "'NEW ISSUE SALE'") return true;
        if (this._SMovementDescription == "'NEW ISSUE PURCH'") return true;
        if (this._SMovementDescription == "'ACQUISITION'") return true;
        if (this._SMovementDescription == "'STOCK PURCHASE'") return true;
        if (this._SMovementDescription == "'BONUS'") return true;
        if (this._SMovementDescription == "'NEW ISSUE'") return true;
        if (this._SMovementDescription == "'RIGHTS'") return true;
        if (this._SMovementDescription == "'RIGHTS PURCHASE'") return true;
        if (this._SMovementDescription == "'U/W PURCHASE'") return true;
        return false;
    }
    public bool IsBalanceBf()
    {
        if (this._SMovementDescription == "'BALANCE B/F'") return true;
        return false;
    }
    public bool IsBalance()
    {
        if (this._SMovementDescription == "'        BALANCE'") return true;
        return false;
    }
    public bool IsSchedDescIrishNdlCost()
    {
        if (this._SMovementDescription == "'NDL'") return true;
        return false;
    }
    public bool IsSchedDescIrishDdHistCost()
    {
        if (this._SMovementDescription == "'HISTORIC COST'") return true;
        return false;
    }
    public bool IsSchedDescDeferredLoss()
    {
        if (this._SMovementDescription == "'DEFERRED LOSS'") return true;
        return false;
    }
    public bool IsOptionLapse()
    {
        if (this._SMovementDescription == "'OPTION LAPSE'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler69, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler69 _Filler69 = new STrancheLine.Filler69();
    
    
    
    
    // [DEBUG] Field: Filler72, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler72 _Filler72 = new STrancheLine.Filler72();
    
    
    
    
    // [DEBUG] Field: Filler76, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler76 _Filler76 = new STrancheLine.Filler76();
    
    
    
    
    // [DEBUG] Field: Filler79, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler79 _Filler79 = new STrancheLine.Filler79();
    
    
    
    
    // [DEBUG] Field: SHoldingUnitsX, is_external=, is_static_class=False, static_prefix=
    private string _SHoldingUnitsX ="";
    
    
    
    
    // [DEBUG] Field: SHoldingUnits9, is_external=, is_static_class=False, static_prefix=
    private decimal _SHoldingUnits9 =0;
    
    
    
    
    // [DEBUG] Field: SHoldingCostX, is_external=, is_static_class=False, static_prefix=
    private string _SHoldingCostX ="";
    
    
    
    
    // [DEBUG] Field: SHoldingCost9, is_external=, is_static_class=False, static_prefix=
    private decimal _SHoldingCost9 =0;
    
    
    
    
    // [DEBUG] Field: SHoldingCostUnsigned, is_external=, is_static_class=False, static_prefix=
    private decimal _SHoldingCostUnsigned =0;
    
    
    
    
    // [DEBUG] Field: SIndexDateX, is_external=, is_static_class=False, static_prefix=
    private string _SIndexDateX ="";
    
    
    
    
    // [DEBUG] Field: SIndexDate9, is_external=, is_static_class=False, static_prefix=
    private int _SIndexDate9 =0;
    
    
    
    
    // [DEBUG] Field: SIndexFactorX, is_external=, is_static_class=False, static_prefix=
    private string _SIndexFactorX ="";
    
    
    
    
    // [DEBUG] Field: SIndexFactor9, is_external=, is_static_class=False, static_prefix=
    private int _SIndexFactor9 =0;
    
    
    
    
    // [DEBUG] Field: SIndexationLimit, is_external=, is_static_class=False, static_prefix=
    private string _SIndexationLimit ="";
    
    
    // 88-level condition checks for SIndexationLimit
    public bool IsSchedSaleGivesRiseToNdl()
    {
        if (this._SIndexationLimit == "'F'") return true;
        return false;
    }
    public bool IsSchedLossRestrToOrigCost()
    {
        if (this._SIndexationLimit == "'G'") return true;
        return false;
    }
    public bool IsSchedDdlossRestrOrigCost()
    {
        if (this._SIndexationLimit == "'H'") return true;
        return false;
    }
    public bool IsSchedGainRelievedByNdl()
    {
        if (this._SIndexationLimit == "'J'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SIndexationCostX, is_external=, is_static_class=False, static_prefix=
    private string _SIndexationCostX ="";
    
    
    
    
    // [DEBUG] Field: SIndexationCost9, is_external=, is_static_class=False, static_prefix=
    private decimal _SIndexationCost9 =0;
    
    
    
    
    // [DEBUG] Field: SIndexationCostUnsigned, is_external=, is_static_class=False, static_prefix=
    private decimal _SIndexationCostUnsigned =0;
    
    
    
    
    // [DEBUG] Field: SDisposalProceedsX, is_external=, is_static_class=False, static_prefix=
    private string _SDisposalProceedsX ="";
    
    
    
    
    // [DEBUG] Field: SDisposalProceeds9, is_external=, is_static_class=False, static_prefix=
    private decimal _SDisposalProceeds9 =0;
    
    
    
    
    // [DEBUG] Field: SDisposalProceedsUnsigned, is_external=, is_static_class=False, static_prefix=
    private decimal _SDisposalProceedsUnsigned =0;
    
    
    
    
    // [DEBUG] Field: SLongTermIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SLongTermIndicator ="";
    
    
    
    
    // [DEBUG] Field: SCapitalGainOrLossX, is_external=, is_static_class=False, static_prefix=
    private string _SCapitalGainOrLossX ="";
    
    
    
    
    // [DEBUG] Field: SCapitalGainOrLoss9, is_external=, is_static_class=False, static_prefix=
    private decimal _SCapitalGainOrLoss9 =0;
    
    
    
    
    // [DEBUG] Field: SBookCostX, is_external=, is_static_class=False, static_prefix=
    private string _SBookCostX ="";
    
    
    
    
    // [DEBUG] Field: SBookCost9, is_external=, is_static_class=False, static_prefix=
    private decimal _SBookCost9 =0;
    
    
    
    
    // [DEBUG] Field: Filler81, is_external=, is_static_class=False, static_prefix=
    private string _Filler81 ="";
    
    
    
    
    // [DEBUG] Field: SSequenceNoX, is_external=, is_static_class=False, static_prefix=
    private string _SSequenceNoX ="";
    
    
    
    
    // [DEBUG] Field: SSequenceNo9, is_external=, is_static_class=False, static_prefix=
    private int _SSequenceNo9 =0;
    
    
    
    
    // [DEBUG] Field: SYear, is_external=, is_static_class=False, static_prefix=
    private string _SYear ="";
    
    
    
    
    // [DEBUG] Field: SPercentBusinessUseX, is_external=, is_static_class=False, static_prefix=
    private string _SPercentBusinessUseX ="";
    
    
    
    
    // [DEBUG] Field: SPercentBusinessUse9, is_external=, is_static_class=False, static_prefix=
    private int _SPercentBusinessUse9 =0;
    
    
    
    
    // [DEBUG] Field: STrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _STrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: SLostIndexationX, is_external=, is_static_class=False, static_prefix=
    private string _SLostIndexationX ="";
    
    
    
    
    // [DEBUG] Field: SLostIndexation9, is_external=, is_static_class=False, static_prefix=
    private decimal _SLostIndexation9 =0;
    
    
    
    
    // [DEBUG] Field: SOffshoreIncomeGainX, is_external=, is_static_class=False, static_prefix=
    private string _SOffshoreIncomeGainX ="";
    
    
    
    
    // [DEBUG] Field: SOffshoreIncomeGain9, is_external=, is_static_class=False, static_prefix=
    private decimal _SOffshoreIncomeGain9 =0;
    
    
    
    
    // [DEBUG] Field: SDisposalContractNo, is_external=, is_static_class=False, static_prefix=
    private string _SDisposalContractNo ="";
    
    
    
    
    // [DEBUG] Field: SFullTaperDate, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.SFullTaperDate _SFullTaperDate = new STrancheLine.SFullTaperDate();
    
    
    
    
    // [DEBUG] Field: STaperUnitsX, is_external=, is_static_class=False, static_prefix=
    private string _STaperUnitsX ="";
    
    
    
    
    // [DEBUG] Field: STaperUnits9, is_external=, is_static_class=False, static_prefix=
    private decimal _STaperUnits9 =0;
    
    
    
    
    // [DEBUG] Field: STaperGainX, is_external=, is_static_class=False, static_prefix=
    private string _STaperGainX ="";
    
    
    
    
    // [DEBUG] Field: STaperGain9, is_external=, is_static_class=False, static_prefix=
    private decimal _STaperGain9 =0;
    
    
    
    
    // [DEBUG] Field: STaperProcX, is_external=, is_static_class=False, static_prefix=
    private string _STaperProcX ="";
    
    
    
    
    // [DEBUG] Field: STaperProc9, is_external=, is_static_class=False, static_prefix=
    private decimal _STaperProc9 =0;
    
    
    
    
    // [DEBUG] Field: SSourceCategory, is_external=, is_static_class=False, static_prefix=
    private string _SSourceCategory ="";
    
    
    // 88-level condition checks for SSourceCategory
    public bool IsSStockPurchase()
    {
        if (this._SSourceCategory == "'SP'") return true;
        return false;
    }
    public bool IsSStockSale()
    {
        if (this._SSourceCategory == "'SS'") return true;
        return false;
    }
    public bool IsSEpWcExercise()
    {
        if (this._SSourceCategory == "'EP'") return true;
        if (this._SSourceCategory == "'WC'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SDisplayCategory, is_external=, is_static_class=False, static_prefix=
    private string _SDisplayCategory ="";
    
    
    
    
    // [DEBUG] Field: SCategoryId, is_external=, is_static_class=False, static_prefix=
    private string _SCategoryId ="";
    
    
    
    
    // [DEBUG] Field: STransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _STransactionCategory ="";
    
    
    
    
public STrancheLine() {}

public STrancheLine(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSBdvLineIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSTrancheType(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSMovementDateX(data.Substring(offset, 6).Trim());
    offset += 6;
    _SMovementDate9.SetSMovementDate9AsString(data.Substring(offset, SMovementDate9.GetSize()));
    offset += 6;
    SetSHatchIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSMovementDescription(data.Substring(offset, 16).Trim());
    offset += 16;
    _Filler69.SetFiller69AsString(data.Substring(offset, Filler69.GetSize()));
    offset += 0;
    _Filler72.SetFiller72AsString(data.Substring(offset, Filler72.GetSize()));
    offset += 0;
    _Filler76.SetFiller76AsString(data.Substring(offset, Filler76.GetSize()));
    offset += 0;
    _Filler79.SetFiller79AsString(data.Substring(offset, Filler79.GetSize()));
    offset += 2;
    SetSHoldingUnitsX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSHoldingUnits9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSHoldingCostX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSHoldingCost9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSHoldingCostUnsigned(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSIndexDateX(data.Substring(offset, 4).Trim());
    offset += 4;
    SetSIndexDate9(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetSIndexFactorX(data.Substring(offset, 5).Trim());
    offset += 5;
    SetSIndexFactor9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSIndexationLimit(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSIndexationCostX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSIndexationCost9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSIndexationCostUnsigned(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSDisposalProceedsX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSDisposalProceeds9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSDisposalProceedsUnsigned(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSLongTermIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSCapitalGainOrLossX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSCapitalGainOrLoss9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSBookCostX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSBookCost9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetFiller81(data.Substring(offset, 3).Trim());
    offset += 3;
    SetSSequenceNoX(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSSequenceNo9(int.Parse(data.Substring(offset, 9).Trim()));
    offset += 9;
    SetSYear(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSPercentBusinessUseX(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSPercentBusinessUse9(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSTrancheFlag(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSLostIndexationX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSLostIndexation9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSOffshoreIncomeGainX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSOffshoreIncomeGain9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSDisposalContractNo(data.Substring(offset, 10).Trim());
    offset += 10;
    _SFullTaperDate.SetSFullTaperDateAsString(data.Substring(offset, SFullTaperDate.GetSize()));
    offset += 0;
    SetSTaperUnitsX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSTaperUnits9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSTaperGainX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSTaperGain9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSTaperProcX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSTaperProc9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSSourceCategory(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSDisplayCategory(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSCategoryId(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSTransactionCategory(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSTrancheLineAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SBdvLineIndicator.PadRight(1));
    result.Append(_STrancheType.PadRight(1));
    result.Append(_SMovementDateX.PadRight(6));
    result.Append(_SMovementDate9.GetSMovementDate9AsString());
    result.Append(_SHatchIndicator.PadRight(1));
    result.Append(_SMovementDescription.PadRight(16));
    result.Append(_Filler69.GetFiller69AsString());
    result.Append(_Filler72.GetFiller72AsString());
    result.Append(_Filler76.GetFiller76AsString());
    result.Append(_Filler79.GetFiller79AsString());
    result.Append(_SHoldingUnitsX.PadRight(12));
    result.Append(_SHoldingUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SHoldingCostX.PadRight(12));
    result.Append(_SHoldingCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SHoldingCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SIndexDateX.PadRight(4));
    result.Append(_SIndexDate9.ToString().PadLeft(4, '0'));
    result.Append(_SIndexFactorX.PadRight(5));
    result.Append(_SIndexFactor9.ToString().PadLeft(2, '0'));
    result.Append(_SIndexationLimit.PadRight(1));
    result.Append(_SIndexationCostX.PadRight(12));
    result.Append(_SIndexationCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SIndexationCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SDisposalProceedsX.PadRight(12));
    result.Append(_SDisposalProceeds9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SDisposalProceedsUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SLongTermIndicator.PadRight(1));
    result.Append(_SCapitalGainOrLossX.PadRight(12));
    result.Append(_SCapitalGainOrLoss9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SBookCostX.PadRight(12));
    result.Append(_SBookCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler81.PadRight(3));
    result.Append(_SSequenceNoX.PadRight(0));
    result.Append(_SSequenceNo9.ToString().PadLeft(9, '0'));
    result.Append(_SYear.PadRight(0));
    result.Append(_SPercentBusinessUseX.PadRight(0));
    result.Append(_SPercentBusinessUse9.ToString().PadLeft(3, '0'));
    result.Append(_STrancheFlag.PadRight(0));
    result.Append(_SLostIndexationX.PadRight(12));
    result.Append(_SLostIndexation9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SOffshoreIncomeGainX.PadRight(12));
    result.Append(_SOffshoreIncomeGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SDisposalContractNo.PadRight(10));
    result.Append(_SFullTaperDate.GetSFullTaperDateAsString());
    result.Append(_STaperUnitsX.PadRight(12));
    result.Append(_STaperUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_STaperGainX.PadRight(12));
    result.Append(_STaperGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_STaperProcX.PadRight(12));
    result.Append(_STaperProc9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SSourceCategory.PadRight(0));
    result.Append(_SDisplayCategory.PadRight(0));
    result.Append(_SCategoryId.PadRight(0));
    result.Append(_STransactionCategory.PadRight(0));
    
    return result.ToString();
}

public void SetSTrancheLineAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSBdvLineIndicator(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSTrancheType(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetSMovementDateX(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _SMovementDate9.SetSMovementDate9AsString(data.Substring(offset, 6));
    }
    else
    {
        _SMovementDate9.SetSMovementDate9AsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSHatchIndicator(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        SetSMovementDescription(extracted);
    }
    offset += 16;
    if (offset + 0 <= data.Length)
    {
        _Filler69.SetFiller69AsString(data.Substring(offset, 0));
    }
    else
    {
        _Filler69.SetFiller69AsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _Filler72.SetFiller72AsString(data.Substring(offset, 0));
    }
    else
    {
        _Filler72.SetFiller72AsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _Filler76.SetFiller76AsString(data.Substring(offset, 0));
    }
    else
    {
        _Filler76.SetFiller76AsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 2 <= data.Length)
    {
        _Filler79.SetFiller79AsString(data.Substring(offset, 2));
    }
    else
    {
        _Filler79.SetFiller79AsString(data.Substring(offset));
    }
    offset += 2;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSHoldingUnitsX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSHoldingUnits9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSHoldingCostX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSHoldingCost9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSHoldingCostUnsigned(parsedDec);
    }
    offset += 12;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetSIndexDateX(extracted);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSIndexDate9(parsedInt);
    }
    offset += 4;
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        SetSIndexFactorX(extracted);
    }
    offset += 5;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSIndexFactor9(parsedInt);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSIndexationLimit(extracted);
    }
    offset += 1;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSIndexationCostX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSIndexationCost9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSIndexationCostUnsigned(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSDisposalProceedsX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSDisposalProceeds9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSDisposalProceedsUnsigned(parsedDec);
    }
    offset += 12;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSLongTermIndicator(extracted);
    }
    offset += 1;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSCapitalGainOrLossX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSCapitalGainOrLoss9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSBookCostX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSBookCost9(parsedDec);
    }
    offset += 12;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller81(extracted);
    }
    offset += 3;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSSequenceNoX(extracted);
    }
    offset += 0;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSSequenceNo9(parsedInt);
    }
    offset += 9;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSYear(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSPercentBusinessUseX(extracted);
    }
    offset += 0;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSPercentBusinessUse9(parsedInt);
    }
    offset += 3;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTrancheFlag(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSLostIndexationX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSLostIndexation9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSOffshoreIncomeGainX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSOffshoreIncomeGain9(parsedDec);
    }
    offset += 12;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetSDisposalContractNo(extracted);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        _SFullTaperDate.SetSFullTaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _SFullTaperDate.SetSFullTaperDateAsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSTaperUnitsX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSTaperUnits9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSTaperGainX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSTaperGain9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSTaperProcX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSTaperProc9(parsedDec);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSSourceCategory(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSDisplayCategory(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSCategoryId(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTransactionCategory(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetSBdvLineIndicator()
{
    return _SBdvLineIndicator;
}

// Standard Setter
public void SetSBdvLineIndicator(string value)
{
    _SBdvLineIndicator = value;
}

// Get<>AsString()
public string GetSBdvLineIndicatorAsString()
{
    return _SBdvLineIndicator.PadRight(1);
}

// Set<>AsString()
public void SetSBdvLineIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SBdvLineIndicator = value;
}

// Standard Getter
public string GetSTrancheType()
{
    return _STrancheType;
}

// Standard Setter
public void SetSTrancheType(string value)
{
    _STrancheType = value;
}

// Get<>AsString()
public string GetSTrancheTypeAsString()
{
    return _STrancheType.PadRight(1);
}

// Set<>AsString()
public void SetSTrancheTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheType = value;
}

// Standard Getter
public string GetSMovementDateX()
{
    return _SMovementDateX;
}

// Standard Setter
public void SetSMovementDateX(string value)
{
    _SMovementDateX = value;
}

// Get<>AsString()
public string GetSMovementDateXAsString()
{
    return _SMovementDateX.PadRight(6);
}

// Set<>AsString()
public void SetSMovementDateXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SMovementDateX = value;
}

// Standard Getter
public SMovementDate9 GetSMovementDate9()
{
    return _SMovementDate9;
}

// Standard Setter
public void SetSMovementDate9(SMovementDate9 value)
{
    _SMovementDate9 = value;
}

// Get<>AsString()
public string GetSMovementDate9AsString()
{
    return _SMovementDate9 != null ? _SMovementDate9.GetSMovementDate9AsString() : "";
}

// Set<>AsString()
public void SetSMovementDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SMovementDate9 == null)
    {
        _SMovementDate9 = new SMovementDate9();
    }
    _SMovementDate9.SetSMovementDate9AsString(value);
}

// Standard Getter
public string GetSHatchIndicator()
{
    return _SHatchIndicator;
}

// Standard Setter
public void SetSHatchIndicator(string value)
{
    _SHatchIndicator = value;
}

// Get<>AsString()
public string GetSHatchIndicatorAsString()
{
    return _SHatchIndicator.PadRight(1);
}

// Set<>AsString()
public void SetSHatchIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHatchIndicator = value;
}

// Standard Getter
public string GetSMovementDescription()
{
    return _SMovementDescription;
}

// Standard Setter
public void SetSMovementDescription(string value)
{
    _SMovementDescription = value;
}

// Get<>AsString()
public string GetSMovementDescriptionAsString()
{
    return _SMovementDescription.PadRight(16);
}

// Set<>AsString()
public void SetSMovementDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SMovementDescription = value;
}

// Standard Getter
public Filler69 GetFiller69()
{
    return _Filler69;
}

// Standard Setter
public void SetFiller69(Filler69 value)
{
    _Filler69 = value;
}

// Get<>AsString()
public string GetFiller69AsString()
{
    return _Filler69 != null ? _Filler69.GetFiller69AsString() : "";
}

// Set<>AsString()
public void SetFiller69AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler69 == null)
    {
        _Filler69 = new Filler69();
    }
    _Filler69.SetFiller69AsString(value);
}

// Standard Getter
public Filler72 GetFiller72()
{
    return _Filler72;
}

// Standard Setter
public void SetFiller72(Filler72 value)
{
    _Filler72 = value;
}

// Get<>AsString()
public string GetFiller72AsString()
{
    return _Filler72 != null ? _Filler72.GetFiller72AsString() : "";
}

// Set<>AsString()
public void SetFiller72AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler72 == null)
    {
        _Filler72 = new Filler72();
    }
    _Filler72.SetFiller72AsString(value);
}

// Standard Getter
public Filler76 GetFiller76()
{
    return _Filler76;
}

// Standard Setter
public void SetFiller76(Filler76 value)
{
    _Filler76 = value;
}

// Get<>AsString()
public string GetFiller76AsString()
{
    return _Filler76 != null ? _Filler76.GetFiller76AsString() : "";
}

// Set<>AsString()
public void SetFiller76AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler76 == null)
    {
        _Filler76 = new Filler76();
    }
    _Filler76.SetFiller76AsString(value);
}

// Standard Getter
public Filler79 GetFiller79()
{
    return _Filler79;
}

// Standard Setter
public void SetFiller79(Filler79 value)
{
    _Filler79 = value;
}

// Get<>AsString()
public string GetFiller79AsString()
{
    return _Filler79 != null ? _Filler79.GetFiller79AsString() : "";
}

// Set<>AsString()
public void SetFiller79AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler79 == null)
    {
        _Filler79 = new Filler79();
    }
    _Filler79.SetFiller79AsString(value);
}

// Standard Getter
public string GetSHoldingUnitsX()
{
    return _SHoldingUnitsX;
}

// Standard Setter
public void SetSHoldingUnitsX(string value)
{
    _SHoldingUnitsX = value;
}

// Get<>AsString()
public string GetSHoldingUnitsXAsString()
{
    return _SHoldingUnitsX.PadRight(12);
}

// Set<>AsString()
public void SetSHoldingUnitsXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHoldingUnitsX = value;
}

// Standard Getter
public decimal GetSHoldingUnits9()
{
    return _SHoldingUnits9;
}

// Standard Setter
public void SetSHoldingUnits9(decimal value)
{
    _SHoldingUnits9 = value;
}

// Get<>AsString()
public string GetSHoldingUnits9AsString()
{
    return _SHoldingUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSHoldingUnits9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SHoldingUnits9 = parsed;
}

// Standard Getter
public string GetSHoldingCostX()
{
    return _SHoldingCostX;
}

// Standard Setter
public void SetSHoldingCostX(string value)
{
    _SHoldingCostX = value;
}

// Get<>AsString()
public string GetSHoldingCostXAsString()
{
    return _SHoldingCostX.PadRight(12);
}

// Set<>AsString()
public void SetSHoldingCostXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHoldingCostX = value;
}

// Standard Getter
public decimal GetSHoldingCost9()
{
    return _SHoldingCost9;
}

// Standard Setter
public void SetSHoldingCost9(decimal value)
{
    _SHoldingCost9 = value;
}

// Get<>AsString()
public string GetSHoldingCost9AsString()
{
    return _SHoldingCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSHoldingCost9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SHoldingCost9 = parsed;
}

// Standard Getter
public decimal GetSHoldingCostUnsigned()
{
    return _SHoldingCostUnsigned;
}

// Standard Setter
public void SetSHoldingCostUnsigned(decimal value)
{
    _SHoldingCostUnsigned = value;
}

// Get<>AsString()
public string GetSHoldingCostUnsignedAsString()
{
    return _SHoldingCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSHoldingCostUnsignedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SHoldingCostUnsigned = parsed;
}

// Standard Getter
public string GetSIndexDateX()
{
    return _SIndexDateX;
}

// Standard Setter
public void SetSIndexDateX(string value)
{
    _SIndexDateX = value;
}

// Get<>AsString()
public string GetSIndexDateXAsString()
{
    return _SIndexDateX.PadRight(4);
}

// Set<>AsString()
public void SetSIndexDateXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexDateX = value;
}

// Standard Getter
public int GetSIndexDate9()
{
    return _SIndexDate9;
}

// Standard Setter
public void SetSIndexDate9(int value)
{
    _SIndexDate9 = value;
}

// Get<>AsString()
public string GetSIndexDate9AsString()
{
    return _SIndexDate9.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetSIndexDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SIndexDate9 = parsed;
}

// Standard Getter
public string GetSIndexFactorX()
{
    return _SIndexFactorX;
}

// Standard Setter
public void SetSIndexFactorX(string value)
{
    _SIndexFactorX = value;
}

// Get<>AsString()
public string GetSIndexFactorXAsString()
{
    return _SIndexFactorX.PadRight(5);
}

// Set<>AsString()
public void SetSIndexFactorXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexFactorX = value;
}

// Standard Getter
public int GetSIndexFactor9()
{
    return _SIndexFactor9;
}

// Standard Setter
public void SetSIndexFactor9(int value)
{
    _SIndexFactor9 = value;
}

// Get<>AsString()
public string GetSIndexFactor9AsString()
{
    return _SIndexFactor9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSIndexFactor9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SIndexFactor9 = parsed;
}

// Standard Getter
public string GetSIndexationLimit()
{
    return _SIndexationLimit;
}

// Standard Setter
public void SetSIndexationLimit(string value)
{
    _SIndexationLimit = value;
}

// Get<>AsString()
public string GetSIndexationLimitAsString()
{
    return _SIndexationLimit.PadRight(1);
}

// Set<>AsString()
public void SetSIndexationLimitAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexationLimit = value;
}

// Standard Getter
public string GetSIndexationCostX()
{
    return _SIndexationCostX;
}

// Standard Setter
public void SetSIndexationCostX(string value)
{
    _SIndexationCostX = value;
}

// Get<>AsString()
public string GetSIndexationCostXAsString()
{
    return _SIndexationCostX.PadRight(12);
}

// Set<>AsString()
public void SetSIndexationCostXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexationCostX = value;
}

// Standard Getter
public decimal GetSIndexationCost9()
{
    return _SIndexationCost9;
}

// Standard Setter
public void SetSIndexationCost9(decimal value)
{
    _SIndexationCost9 = value;
}

// Get<>AsString()
public string GetSIndexationCost9AsString()
{
    return _SIndexationCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSIndexationCost9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SIndexationCost9 = parsed;
}

// Standard Getter
public decimal GetSIndexationCostUnsigned()
{
    return _SIndexationCostUnsigned;
}

// Standard Setter
public void SetSIndexationCostUnsigned(decimal value)
{
    _SIndexationCostUnsigned = value;
}

// Get<>AsString()
public string GetSIndexationCostUnsignedAsString()
{
    return _SIndexationCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSIndexationCostUnsignedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SIndexationCostUnsigned = parsed;
}

// Standard Getter
public string GetSDisposalProceedsX()
{
    return _SDisposalProceedsX;
}

// Standard Setter
public void SetSDisposalProceedsX(string value)
{
    _SDisposalProceedsX = value;
}

// Get<>AsString()
public string GetSDisposalProceedsXAsString()
{
    return _SDisposalProceedsX.PadRight(12);
}

// Set<>AsString()
public void SetSDisposalProceedsXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SDisposalProceedsX = value;
}

// Standard Getter
public decimal GetSDisposalProceeds9()
{
    return _SDisposalProceeds9;
}

// Standard Setter
public void SetSDisposalProceeds9(decimal value)
{
    _SDisposalProceeds9 = value;
}

// Get<>AsString()
public string GetSDisposalProceeds9AsString()
{
    return _SDisposalProceeds9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSDisposalProceeds9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SDisposalProceeds9 = parsed;
}

// Standard Getter
public decimal GetSDisposalProceedsUnsigned()
{
    return _SDisposalProceedsUnsigned;
}

// Standard Setter
public void SetSDisposalProceedsUnsigned(decimal value)
{
    _SDisposalProceedsUnsigned = value;
}

// Get<>AsString()
public string GetSDisposalProceedsUnsignedAsString()
{
    return _SDisposalProceedsUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSDisposalProceedsUnsignedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SDisposalProceedsUnsigned = parsed;
}

// Standard Getter
public string GetSLongTermIndicator()
{
    return _SLongTermIndicator;
}

// Standard Setter
public void SetSLongTermIndicator(string value)
{
    _SLongTermIndicator = value;
}

// Get<>AsString()
public string GetSLongTermIndicatorAsString()
{
    return _SLongTermIndicator.PadRight(1);
}

// Set<>AsString()
public void SetSLongTermIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SLongTermIndicator = value;
}

// Standard Getter
public string GetSCapitalGainOrLossX()
{
    return _SCapitalGainOrLossX;
}

// Standard Setter
public void SetSCapitalGainOrLossX(string value)
{
    _SCapitalGainOrLossX = value;
}

// Get<>AsString()
public string GetSCapitalGainOrLossXAsString()
{
    return _SCapitalGainOrLossX.PadRight(12);
}

// Set<>AsString()
public void SetSCapitalGainOrLossXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SCapitalGainOrLossX = value;
}

// Standard Getter
public decimal GetSCapitalGainOrLoss9()
{
    return _SCapitalGainOrLoss9;
}

// Standard Setter
public void SetSCapitalGainOrLoss9(decimal value)
{
    _SCapitalGainOrLoss9 = value;
}

// Get<>AsString()
public string GetSCapitalGainOrLoss9AsString()
{
    return _SCapitalGainOrLoss9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSCapitalGainOrLoss9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SCapitalGainOrLoss9 = parsed;
}

// Standard Getter
public string GetSBookCostX()
{
    return _SBookCostX;
}

// Standard Setter
public void SetSBookCostX(string value)
{
    _SBookCostX = value;
}

// Get<>AsString()
public string GetSBookCostXAsString()
{
    return _SBookCostX.PadRight(12);
}

// Set<>AsString()
public void SetSBookCostXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SBookCostX = value;
}

// Standard Getter
public decimal GetSBookCost9()
{
    return _SBookCost9;
}

// Standard Setter
public void SetSBookCost9(decimal value)
{
    _SBookCost9 = value;
}

// Get<>AsString()
public string GetSBookCost9AsString()
{
    return _SBookCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSBookCost9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SBookCost9 = parsed;
}

// Standard Getter
public string GetFiller81()
{
    return _Filler81;
}

// Standard Setter
public void SetFiller81(string value)
{
    _Filler81 = value;
}

// Get<>AsString()
public string GetFiller81AsString()
{
    return _Filler81.PadRight(3);
}

// Set<>AsString()
public void SetFiller81AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler81 = value;
}

// Standard Getter
public string GetSSequenceNoX()
{
    return _SSequenceNoX;
}

// Standard Setter
public void SetSSequenceNoX(string value)
{
    _SSequenceNoX = value;
}

// Get<>AsString()
public string GetSSequenceNoXAsString()
{
    return _SSequenceNoX.PadRight(0);
}

// Set<>AsString()
public void SetSSequenceNoXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SSequenceNoX = value;
}

// Standard Getter
public int GetSSequenceNo9()
{
    return _SSequenceNo9;
}

// Standard Setter
public void SetSSequenceNo9(int value)
{
    _SSequenceNo9 = value;
}

// Get<>AsString()
public string GetSSequenceNo9AsString()
{
    return _SSequenceNo9.ToString().PadLeft(9, '0');
}

// Set<>AsString()
public void SetSSequenceNo9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SSequenceNo9 = parsed;
}

// Standard Getter
public string GetSYear()
{
    return _SYear;
}

// Standard Setter
public void SetSYear(string value)
{
    _SYear = value;
}

// Get<>AsString()
public string GetSYearAsString()
{
    return _SYear.PadRight(0);
}

// Set<>AsString()
public void SetSYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SYear = value;
}

// Standard Getter
public string GetSPercentBusinessUseX()
{
    return _SPercentBusinessUseX;
}

// Standard Setter
public void SetSPercentBusinessUseX(string value)
{
    _SPercentBusinessUseX = value;
}

// Get<>AsString()
public string GetSPercentBusinessUseXAsString()
{
    return _SPercentBusinessUseX.PadRight(0);
}

// Set<>AsString()
public void SetSPercentBusinessUseXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SPercentBusinessUseX = value;
}

// Standard Getter
public int GetSPercentBusinessUse9()
{
    return _SPercentBusinessUse9;
}

// Standard Setter
public void SetSPercentBusinessUse9(int value)
{
    _SPercentBusinessUse9 = value;
}

// Get<>AsString()
public string GetSPercentBusinessUse9AsString()
{
    return _SPercentBusinessUse9.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSPercentBusinessUse9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SPercentBusinessUse9 = parsed;
}

// Standard Getter
public string GetSTrancheFlag()
{
    return _STrancheFlag;
}

// Standard Setter
public void SetSTrancheFlag(string value)
{
    _STrancheFlag = value;
}

// Get<>AsString()
public string GetSTrancheFlagAsString()
{
    return _STrancheFlag.PadRight(0);
}

// Set<>AsString()
public void SetSTrancheFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheFlag = value;
}

// Standard Getter
public string GetSLostIndexationX()
{
    return _SLostIndexationX;
}

// Standard Setter
public void SetSLostIndexationX(string value)
{
    _SLostIndexationX = value;
}

// Get<>AsString()
public string GetSLostIndexationXAsString()
{
    return _SLostIndexationX.PadRight(12);
}

// Set<>AsString()
public void SetSLostIndexationXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SLostIndexationX = value;
}

// Standard Getter
public decimal GetSLostIndexation9()
{
    return _SLostIndexation9;
}

// Standard Setter
public void SetSLostIndexation9(decimal value)
{
    _SLostIndexation9 = value;
}

// Get<>AsString()
public string GetSLostIndexation9AsString()
{
    return _SLostIndexation9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSLostIndexation9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SLostIndexation9 = parsed;
}

// Standard Getter
public string GetSOffshoreIncomeGainX()
{
    return _SOffshoreIncomeGainX;
}

// Standard Setter
public void SetSOffshoreIncomeGainX(string value)
{
    _SOffshoreIncomeGainX = value;
}

// Get<>AsString()
public string GetSOffshoreIncomeGainXAsString()
{
    return _SOffshoreIncomeGainX.PadRight(12);
}

// Set<>AsString()
public void SetSOffshoreIncomeGainXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SOffshoreIncomeGainX = value;
}

// Standard Getter
public decimal GetSOffshoreIncomeGain9()
{
    return _SOffshoreIncomeGain9;
}

// Standard Setter
public void SetSOffshoreIncomeGain9(decimal value)
{
    _SOffshoreIncomeGain9 = value;
}

// Get<>AsString()
public string GetSOffshoreIncomeGain9AsString()
{
    return _SOffshoreIncomeGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSOffshoreIncomeGain9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SOffshoreIncomeGain9 = parsed;
}

// Standard Getter
public string GetSDisposalContractNo()
{
    return _SDisposalContractNo;
}

// Standard Setter
public void SetSDisposalContractNo(string value)
{
    _SDisposalContractNo = value;
}

// Get<>AsString()
public string GetSDisposalContractNoAsString()
{
    return _SDisposalContractNo.PadRight(10);
}

// Set<>AsString()
public void SetSDisposalContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SDisposalContractNo = value;
}

// Standard Getter
public SFullTaperDate GetSFullTaperDate()
{
    return _SFullTaperDate;
}

// Standard Setter
public void SetSFullTaperDate(SFullTaperDate value)
{
    _SFullTaperDate = value;
}

// Get<>AsString()
public string GetSFullTaperDateAsString()
{
    return _SFullTaperDate != null ? _SFullTaperDate.GetSFullTaperDateAsString() : "";
}

// Set<>AsString()
public void SetSFullTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SFullTaperDate == null)
    {
        _SFullTaperDate = new SFullTaperDate();
    }
    _SFullTaperDate.SetSFullTaperDateAsString(value);
}

// Standard Getter
public string GetSTaperUnitsX()
{
    return _STaperUnitsX;
}

// Standard Setter
public void SetSTaperUnitsX(string value)
{
    _STaperUnitsX = value;
}

// Get<>AsString()
public string GetSTaperUnitsXAsString()
{
    return _STaperUnitsX.PadRight(12);
}

// Set<>AsString()
public void SetSTaperUnitsXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperUnitsX = value;
}

// Standard Getter
public decimal GetSTaperUnits9()
{
    return _STaperUnits9;
}

// Standard Setter
public void SetSTaperUnits9(decimal value)
{
    _STaperUnits9 = value;
}

// Get<>AsString()
public string GetSTaperUnits9AsString()
{
    return _STaperUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSTaperUnits9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _STaperUnits9 = parsed;
}

// Standard Getter
public string GetSTaperGainX()
{
    return _STaperGainX;
}

// Standard Setter
public void SetSTaperGainX(string value)
{
    _STaperGainX = value;
}

// Get<>AsString()
public string GetSTaperGainXAsString()
{
    return _STaperGainX.PadRight(12);
}

// Set<>AsString()
public void SetSTaperGainXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperGainX = value;
}

// Standard Getter
public decimal GetSTaperGain9()
{
    return _STaperGain9;
}

// Standard Setter
public void SetSTaperGain9(decimal value)
{
    _STaperGain9 = value;
}

// Get<>AsString()
public string GetSTaperGain9AsString()
{
    return _STaperGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSTaperGain9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _STaperGain9 = parsed;
}

// Standard Getter
public string GetSTaperProcX()
{
    return _STaperProcX;
}

// Standard Setter
public void SetSTaperProcX(string value)
{
    _STaperProcX = value;
}

// Get<>AsString()
public string GetSTaperProcXAsString()
{
    return _STaperProcX.PadRight(12);
}

// Set<>AsString()
public void SetSTaperProcXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperProcX = value;
}

// Standard Getter
public decimal GetSTaperProc9()
{
    return _STaperProc9;
}

// Standard Setter
public void SetSTaperProc9(decimal value)
{
    _STaperProc9 = value;
}

// Get<>AsString()
public string GetSTaperProc9AsString()
{
    return _STaperProc9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSTaperProc9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _STaperProc9 = parsed;
}

// Standard Getter
public string GetSSourceCategory()
{
    return _SSourceCategory;
}

// Standard Setter
public void SetSSourceCategory(string value)
{
    _SSourceCategory = value;
}

// Get<>AsString()
public string GetSSourceCategoryAsString()
{
    return _SSourceCategory.PadRight(0);
}

// Set<>AsString()
public void SetSSourceCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SSourceCategory = value;
}

// Standard Getter
public string GetSDisplayCategory()
{
    return _SDisplayCategory;
}

// Standard Setter
public void SetSDisplayCategory(string value)
{
    _SDisplayCategory = value;
}

// Get<>AsString()
public string GetSDisplayCategoryAsString()
{
    return _SDisplayCategory.PadRight(0);
}

// Set<>AsString()
public void SetSDisplayCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SDisplayCategory = value;
}

// Standard Getter
public string GetSCategoryId()
{
    return _SCategoryId;
}

// Standard Setter
public void SetSCategoryId(string value)
{
    _SCategoryId = value;
}

// Get<>AsString()
public string GetSCategoryIdAsString()
{
    return _SCategoryId.PadRight(0);
}

// Set<>AsString()
public void SetSCategoryIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SCategoryId = value;
}

// Standard Getter
public string GetSTransactionCategory()
{
    return _STransactionCategory;
}

// Standard Setter
public void SetSTransactionCategory(string value)
{
    _STransactionCategory = value;
}

// Get<>AsString()
public string GetSTransactionCategoryAsString()
{
    return _STransactionCategory.PadRight(0);
}

// Set<>AsString()
public void SetSTransactionCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STransactionCategory = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: SMovementDate9
public class SMovementDate9
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SMovementDateYy, is_external=, is_static_class=False, static_prefix=
    private int _SMovementDateYy =0;
    
    
    
    
    // [DEBUG] Field: SMovementDateMm, is_external=, is_static_class=False, static_prefix=
    private int _SMovementDateMm =0;
    
    
    
    
    // [DEBUG] Field: SMovementDateDd, is_external=, is_static_class=False, static_prefix=
    private int _SMovementDateDd =0;
    
    
    
    
public SMovementDate9() {}

public SMovementDate9(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSMovementDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSMovementDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSMovementDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetSMovementDate9AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SMovementDateYy.ToString().PadLeft(2, '0'));
    result.Append(_SMovementDateMm.ToString().PadLeft(2, '0'));
    result.Append(_SMovementDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetSMovementDate9AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSMovementDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSMovementDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSMovementDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetSMovementDateYy()
{
    return _SMovementDateYy;
}

// Standard Setter
public void SetSMovementDateYy(int value)
{
    _SMovementDateYy = value;
}

// Get<>AsString()
public string GetSMovementDateYyAsString()
{
    return _SMovementDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSMovementDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SMovementDateYy = parsed;
}

// Standard Getter
public int GetSMovementDateMm()
{
    return _SMovementDateMm;
}

// Standard Setter
public void SetSMovementDateMm(int value)
{
    _SMovementDateMm = value;
}

// Get<>AsString()
public string GetSMovementDateMmAsString()
{
    return _SMovementDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSMovementDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SMovementDateMm = parsed;
}

// Standard Getter
public int GetSMovementDateDd()
{
    return _SMovementDateDd;
}

// Standard Setter
public void SetSMovementDateDd(int value)
{
    _SMovementDateDd = value;
}

// Get<>AsString()
public string GetSMovementDateDdAsString()
{
    return _SMovementDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSMovementDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SMovementDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler69
public class Filler69
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler70, is_external=, is_static_class=False, static_prefix=
    private string _Filler70 ="";
    
    
    // 88-level condition checks for Filler70
    public bool IsTransferSale()
    {
        if (this._Filler70 == "'RG TO'") return true;
        if (this._Filler70 == "'TR TO'") return true;
        if (this._Filler70 == "'TD TO'") return true;
        if (this._Filler70 == "'CN TO'") return true;
        if (this._Filler70 == "'RO TO'") return true;
        if (this._Filler70 == "'TS TO'") return true;
        if (this._Filler70 == "'CS TO'") return true;
        if (this._Filler70 == "'GS TO'") return true;
        if (this._Filler70 == "'CL TO'") return true;
        if (this._Filler70 == "'RC TO'") return true;
        if (this._Filler70 == "'RR TO'") return true;
        return false;
    }
    public bool IsTransferAcquisition()
    {
        if (this._Filler70 == "'RG FM'") return true;
        if (this._Filler70 == "'TR FM'") return true;
        if (this._Filler70 == "'TD FM'") return true;
        if (this._Filler70 == "'CN FM'") return true;
        if (this._Filler70 == "'RO FM'") return true;
        if (this._Filler70 == "'TP FM'") return true;
        if (this._Filler70 == "'CP FM'") return true;
        if (this._Filler70 == "'GP FM'") return true;
        if (this._Filler70 == "'CL FM'") return true;
        if (this._Filler70 == "'RC FM'") return true;
        if (this._Filler70 == "'RR FM'") return true;
        return false;
    }
    public bool IsStockExerciseFm()
    {
        if (this._Filler70 == "'EX FM'") return true;
        return false;
    }
    public bool IsOptionExerciseTo()
    {
        if (this._Filler70 == "'EX TO'") return true;
        return false;
    }
    public bool IsChgTransferSale()
    {
        if (this._Filler70 == "'CT TO'") return true;
        return false;
    }
    public bool IsChgTransferAcquisition()
    {
        if (this._Filler70 == "'CT FM'") return true;
        return false;
    }
    public bool IsRcAcquisition()
    {
        if (this._Filler70 == "'RC FM'") return true;
        return false;
    }
    public bool IsRrAcquisition()
    {
        if (this._Filler70 == "'RR FM'") return true;
        return false;
    }
    public bool IsRcDisposal()
    {
        if (this._Filler70 == "'RC TO'") return true;
        return false;
    }
    public bool IsRrDisposal()
    {
        if (this._Filler70 == "'RR TO'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler71, is_external=, is_static_class=False, static_prefix=
    private string _Filler71 ="";
    
    
    
    
public Filler69() {}

public Filler69(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller70(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller71(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller69AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler70.PadRight(0));
    result.Append(_Filler71.PadRight(0));
    
    return result.ToString();
}

public void SetFiller69AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller70(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller71(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller70()
{
    return _Filler70;
}

// Standard Setter
public void SetFiller70(string value)
{
    _Filler70 = value;
}

// Get<>AsString()
public string GetFiller70AsString()
{
    return _Filler70.PadRight(0);
}

// Set<>AsString()
public void SetFiller70AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler70 = value;
}

// Standard Getter
public string GetFiller71()
{
    return _Filler71;
}

// Standard Setter
public void SetFiller71(string value)
{
    _Filler71 = value;
}

// Get<>AsString()
public string GetFiller71AsString()
{
    return _Filler71.PadRight(0);
}

// Set<>AsString()
public void SetFiller71AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler71 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler72
public class Filler72
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler73, is_external=, is_static_class=False, static_prefix=
    private string _Filler73 ="";
    
    
    // 88-level condition checks for Filler73
    public bool IsExerciseTransaction()
    {
        if (this._Filler73 == "'EX'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler74, is_external=, is_static_class=False, static_prefix=
    private string _Filler74 ="";
    
    
    // 88-level condition checks for Filler74
    public bool IsHoldingToHoldingMovement()
    {
        if (this._Filler74 == "' TO '") return true;
        if (this._Filler74 == "' FM '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler75, is_external=, is_static_class=False, static_prefix=
    private string _Filler75 ="";
    
    
    
    
public Filler72() {}

public Filler72(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller73(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller74(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller75(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller72AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler73.PadRight(0));
    result.Append(_Filler74.PadRight(0));
    result.Append(_Filler75.PadRight(0));
    
    return result.ToString();
}

public void SetFiller72AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller73(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller74(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller75(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller73()
{
    return _Filler73;
}

// Standard Setter
public void SetFiller73(string value)
{
    _Filler73 = value;
}

// Get<>AsString()
public string GetFiller73AsString()
{
    return _Filler73.PadRight(0);
}

// Set<>AsString()
public void SetFiller73AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler73 = value;
}

// Standard Getter
public string GetFiller74()
{
    return _Filler74;
}

// Standard Setter
public void SetFiller74(string value)
{
    _Filler74 = value;
}

// Get<>AsString()
public string GetFiller74AsString()
{
    return _Filler74.PadRight(0);
}

// Set<>AsString()
public void SetFiller74AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler74 = value;
}

// Standard Getter
public string GetFiller75()
{
    return _Filler75;
}

// Standard Setter
public void SetFiller75(string value)
{
    _Filler75 = value;
}

// Get<>AsString()
public string GetFiller75AsString()
{
    return _Filler75.PadRight(0);
}

// Set<>AsString()
public void SetFiller75AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler75 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler76
public class Filler76
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler77, is_external=, is_static_class=False, static_prefix=
    private string _Filler77 ="";
    
    
    // 88-level condition checks for Filler77
    public bool IsRightsAllotment()
    {
        if (this._Filler77 == "'RIGHTS'") return true;
        return false;
    }
    public bool IsBonusAllotment()
    {
        if (this._Filler77 == "'BONUS '") return true;
        return false;
    }
    public bool IsCompAdjustment()
    {
        if (this._Filler77 == "'ADJT. '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler78, is_external=, is_static_class=False, static_prefix=
    private string _Filler78 ="";
    
    
    
    
public Filler76() {}

public Filler76(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller77(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller78(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller76AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler77.PadRight(0));
    result.Append(_Filler78.PadRight(0));
    
    return result.ToString();
}

public void SetFiller76AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller77(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller78(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller77()
{
    return _Filler77;
}

// Standard Setter
public void SetFiller77(string value)
{
    _Filler77 = value;
}

// Get<>AsString()
public string GetFiller77AsString()
{
    return _Filler77.PadRight(0);
}

// Set<>AsString()
public void SetFiller77AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler77 = value;
}

// Standard Getter
public string GetFiller78()
{
    return _Filler78;
}

// Standard Setter
public void SetFiller78(string value)
{
    _Filler78 = value;
}

// Get<>AsString()
public string GetFiller78AsString()
{
    return _Filler78.PadRight(0);
}

// Set<>AsString()
public void SetFiller78AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler78 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler79
public class Filler79
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CustomTranCategoryCode, is_external=, is_static_class=False, static_prefix=
    private string _CustomTranCategoryCode ="";
    
    
    
    
    // [DEBUG] Field: Filler80, is_external=, is_static_class=False, static_prefix=
    private string _Filler80 ="";
    
    
    
    
public Filler79() {}

public Filler79(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCustomTranCategoryCode(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller80(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller79AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CustomTranCategoryCode.PadRight(2));
    result.Append(_Filler80.PadRight(0));
    
    return result.ToString();
}

public void SetFiller79AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetCustomTranCategoryCode(extracted);
    }
    offset += 2;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller80(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetCustomTranCategoryCode()
{
    return _CustomTranCategoryCode;
}

// Standard Setter
public void SetCustomTranCategoryCode(string value)
{
    _CustomTranCategoryCode = value;
}

// Get<>AsString()
public string GetCustomTranCategoryCodeAsString()
{
    return _CustomTranCategoryCode.PadRight(2);
}

// Set<>AsString()
public void SetCustomTranCategoryCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CustomTranCategoryCode = value;
}

// Standard Getter
public string GetFiller80()
{
    return _Filler80;
}

// Standard Setter
public void SetFiller80(string value)
{
    _Filler80 = value;
}

// Get<>AsString()
public string GetFiller80AsString()
{
    return _Filler80.PadRight(0);
}

// Set<>AsString()
public void SetFiller80AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler80 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: SFullTaperDate
public class SFullTaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: STaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _STaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: STaperDate, is_external=, is_static_class=False, static_prefix=
    private string _STaperDate ="";
    
    
    
    
public SFullTaperDate() {}

public SFullTaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSTaperDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSTaperDate(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSFullTaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_STaperDateCc.PadRight(0));
    result.Append(_STaperDate.PadRight(0));
    
    return result.ToString();
}

public void SetSFullTaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTaperDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTaperDate(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetSTaperDateCc()
{
    return _STaperDateCc;
}

// Standard Setter
public void SetSTaperDateCc(string value)
{
    _STaperDateCc = value;
}

// Get<>AsString()
public string GetSTaperDateCcAsString()
{
    return _STaperDateCc.PadRight(0);
}

// Set<>AsString()
public void SetSTaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperDateCc = value;
}

// Standard Getter
public string GetSTaperDate()
{
    return _STaperDate;
}

// Standard Setter
public void SetSTaperDate(string value)
{
    _STaperDate = value;
}

// Get<>AsString()
public string GetSTaperDateAsString()
{
    return _STaperDate.PadRight(0);
}

// Set<>AsString()
public void SetSTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperDate = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}
