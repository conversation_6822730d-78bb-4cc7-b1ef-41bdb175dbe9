using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportRecord2 Data Structure

public class ReportRecord2
{
    private static int _size = 133;
    // [DEBUG] Class: ReportRecord2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _PrintControl ="";
    
    
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private string _Filler1 ="";
    
    
    
    
    // [DEBUG] Field: ReportLine2, is_external=, is_static_class=False, static_prefix=
    private string _ReportLine2 ="";
    
    
    
    
    
    // Serialization methods
    public string GetReportRecord2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_PrintControl.PadRight(1));
        result.Append(_Filler1.PadRight(40));
        result.Append(_ReportLine2.PadRight(92));
        
        return result.ToString();
    }
    
    public void SetReportRecord2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetPrintControl(extracted);
        }
        offset += 1;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller1(extracted);
        }
        offset += 40;
        if (offset + 92 <= data.Length)
        {
            string extracted = data.Substring(offset, 92).Trim();
            SetReportLine2(extracted);
        }
        offset += 92;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportRecord2AsString();
    }
    // Set<>String Override function
    public void SetReportRecord2(string value)
    {
        SetReportRecord2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetPrintControl()
    {
        return _PrintControl;
    }
    
    // Standard Setter
    public void SetPrintControl(string value)
    {
        _PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetPrintControlAsString()
    {
        return _PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetPrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _PrintControl = value;
    }
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetReportLine2()
    {
        return _ReportLine2;
    }
    
    // Standard Setter
    public void SetReportLine2(string value)
    {
        _ReportLine2 = value;
    }
    
    // Get<>AsString()
    public string GetReportLine2AsString()
    {
        return _ReportLine2.PadRight(92);
    }
    
    // Set<>AsString()
    public void SetReportLine2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportLine2 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}