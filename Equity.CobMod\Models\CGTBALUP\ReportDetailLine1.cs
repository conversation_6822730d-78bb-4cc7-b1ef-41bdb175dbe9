using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportDetailLine1 Data Structure

public class ReportDetailLine1
{
    private static int _size = 126;
    // [DEBUG] Class: ReportDetailLine1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ReportFund, is_external=, is_static_class=False, static_prefix=
    private string _ReportFund ="";
    
    
    
    
    // [DEBUG] Field: ReportSedol, is_external=, is_static_class=False, static_prefix=
    private string _ReportSedol ="";
    
    
    
    
    // [DEBUG] Field: ReportContractNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportContractNo ="";
    
    
    
    
    // [DEBUG] Field: ReportType, is_external=, is_static_class=False, static_prefix=
    private string _ReportType ="";
    
    
    
    
    // [DEBUG] Field: ReportBargainDate, is_external=, is_static_class=False, static_prefix=
    private string _ReportBargainDate ="";
    
    
    
    
    // [DEBUG] Field: ReportQuantity, is_external=, is_static_class=False, static_prefix=
    private string _ReportQuantity ="";
    
    
    
    
    // [DEBUG] Field: ReportValue, is_external=, is_static_class=False, static_prefix=
    private string _ReportValue ="";
    
    
    
    
    // [DEBUG] Field: ReportAction, is_external=, is_static_class=False, static_prefix=
    private string _ReportAction ="";
    
    
    
    
    
    // Serialization methods
    public string GetReportDetailLine1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ReportFund.PadRight(0));
        result.Append(_ReportSedol.PadRight(11));
        result.Append(_ReportContractNo.PadRight(12));
        result.Append(_ReportType.PadRight(22));
        result.Append(_ReportBargainDate.PadRight(10));
        result.Append(_ReportQuantity.PadRight(15));
        result.Append(_ReportValue.PadRight(17));
        result.Append(_ReportAction.PadRight(39));
        
        return result.ToString();
    }
    
    public void SetReportDetailLine1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetReportFund(extracted);
        }
        offset += 0;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetReportSedol(extracted);
        }
        offset += 11;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetReportContractNo(extracted);
        }
        offset += 12;
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetReportType(extracted);
        }
        offset += 22;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetReportBargainDate(extracted);
        }
        offset += 10;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetReportQuantity(extracted);
        }
        offset += 15;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            SetReportValue(extracted);
        }
        offset += 17;
        if (offset + 39 <= data.Length)
        {
            string extracted = data.Substring(offset, 39).Trim();
            SetReportAction(extracted);
        }
        offset += 39;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportDetailLine1AsString();
    }
    // Set<>String Override function
    public void SetReportDetailLine1(string value)
    {
        SetReportDetailLine1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetReportFund()
    {
        return _ReportFund;
    }
    
    // Standard Setter
    public void SetReportFund(string value)
    {
        _ReportFund = value;
    }
    
    // Get<>AsString()
    public string GetReportFundAsString()
    {
        return _ReportFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetReportFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportFund = value;
    }
    
    // Standard Getter
    public string GetReportSedol()
    {
        return _ReportSedol;
    }
    
    // Standard Setter
    public void SetReportSedol(string value)
    {
        _ReportSedol = value;
    }
    
    // Get<>AsString()
    public string GetReportSedolAsString()
    {
        return _ReportSedol.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetReportSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportSedol = value;
    }
    
    // Standard Getter
    public string GetReportContractNo()
    {
        return _ReportContractNo;
    }
    
    // Standard Setter
    public void SetReportContractNo(string value)
    {
        _ReportContractNo = value;
    }
    
    // Get<>AsString()
    public string GetReportContractNoAsString()
    {
        return _ReportContractNo.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetReportContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportContractNo = value;
    }
    
    // Standard Getter
    public string GetReportType()
    {
        return _ReportType;
    }
    
    // Standard Setter
    public void SetReportType(string value)
    {
        _ReportType = value;
    }
    
    // Get<>AsString()
    public string GetReportTypeAsString()
    {
        return _ReportType.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetReportTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportType = value;
    }
    
    // Standard Getter
    public string GetReportBargainDate()
    {
        return _ReportBargainDate;
    }
    
    // Standard Setter
    public void SetReportBargainDate(string value)
    {
        _ReportBargainDate = value;
    }
    
    // Get<>AsString()
    public string GetReportBargainDateAsString()
    {
        return _ReportBargainDate.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetReportBargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportBargainDate = value;
    }
    
    // Standard Getter
    public string GetReportQuantity()
    {
        return _ReportQuantity;
    }
    
    // Standard Setter
    public void SetReportQuantity(string value)
    {
        _ReportQuantity = value;
    }
    
    // Get<>AsString()
    public string GetReportQuantityAsString()
    {
        return _ReportQuantity.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetReportQuantityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportQuantity = value;
    }
    
    // Standard Getter
    public string GetReportValue()
    {
        return _ReportValue;
    }
    
    // Standard Setter
    public void SetReportValue(string value)
    {
        _ReportValue = value;
    }
    
    // Get<>AsString()
    public string GetReportValueAsString()
    {
        return _ReportValue.PadRight(17);
    }
    
    // Set<>AsString()
    public void SetReportValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportValue = value;
    }
    
    // Standard Getter
    public string GetReportAction()
    {
        return _ReportAction;
    }
    
    // Standard Setter
    public void SetReportAction(string value)
    {
        _ReportAction = value;
    }
    
    // Get<>AsString()
    public string GetReportActionAsString()
    {
        return _ReportAction.PadRight(39);
    }
    
    // Set<>AsString()
    public void SetReportActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportAction = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}