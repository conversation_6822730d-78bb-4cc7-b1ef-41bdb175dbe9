using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing Filler202 Data Structure

public class Filler202
{
    private static int _size = 81;
    // [DEBUG] Class: Filler202, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: YearEndIds, is_external=, is_static_class=False, static_prefix=
    private YearEndIds _YearEndIds = new YearEndIds();
    
    
    
    
    // [DEBUG] Field: Filler210, is_external=, is_static_class=False, static_prefix=
    private string _Filler210 ="";
    
    
    
    
    // [DEBUG] Field: Filler211, is_external=, is_static_class=False, static_prefix=
    private string _Filler211 ="";
    
    
    
    
    
    // Serialization methods
    public string GetFiller202AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_YearEndIds.GetYearEndIdsAsString());
        result.Append(_Filler210.PadRight(24));
        result.Append(_Filler211.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetFiller202AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 56 <= data.Length)
        {
            _YearEndIds.SetYearEndIdsAsString(data.Substring(offset, 56));
        }
        else
        {
            _YearEndIds.SetYearEndIdsAsString(data.Substring(offset));
        }
        offset += 56;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetFiller210(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller211(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetFiller202AsString();
    }
    // Set<>String Override function
    public void SetFiller202(string value)
    {
        SetFiller202AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public YearEndIds GetYearEndIds()
    {
        return _YearEndIds;
    }
    
    // Standard Setter
    public void SetYearEndIds(YearEndIds value)
    {
        _YearEndIds = value;
    }
    
    // Get<>AsString()
    public string GetYearEndIdsAsString()
    {
        return _YearEndIds != null ? _YearEndIds.GetYearEndIdsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetYearEndIdsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YearEndIds == null)
        {
            _YearEndIds = new YearEndIds();
        }
        _YearEndIds.SetYearEndIdsAsString(value);
    }
    
    // Standard Getter
    public string GetFiller210()
    {
        return _Filler210;
    }
    
    // Standard Setter
    public void SetFiller210(string value)
    {
        _Filler210 = value;
    }
    
    // Get<>AsString()
    public string GetFiller210AsString()
    {
        return _Filler210.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetFiller210AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler210 = value;
    }
    
    // Standard Getter
    public string GetFiller211()
    {
        return _Filler211;
    }
    
    // Standard Setter
    public void SetFiller211(string value)
    {
        _Filler211 = value;
    }
    
    // Get<>AsString()
    public string GetFiller211AsString()
    {
        return _Filler211.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller211AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler211 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetYearEndIds(string value)
    {
        _YearEndIds.SetYearEndIdsAsString(value);
    }
    // Nested Class: YearEndIds
    public class YearEndIds
    {
        private static int _size = 56;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler203, is_external=, is_static_class=False, static_prefix=
        private string _Filler203 ="";
        
        
        
        
        // [DEBUG] Field: Filler204, is_external=, is_static_class=False, static_prefix=
        private string _Filler204 ="";
        
        
        
        
        // [DEBUG] Field: Filler205, is_external=, is_static_class=False, static_prefix=
        private string _Filler205 ="";
        
        
        
        
        // [DEBUG] Field: Filler206, is_external=, is_static_class=False, static_prefix=
        private string _Filler206 ="";
        
        
        
        
        // [DEBUG] Field: Filler207, is_external=, is_static_class=False, static_prefix=
        private string _Filler207 ="";
        
        
        
        
        // [DEBUG] Field: Filler208, is_external=, is_static_class=False, static_prefix=
        private string _Filler208 ="";
        
        
        
        
        // [DEBUG] Field: Filler209, is_external=, is_static_class=False, static_prefix=
        private string _Filler209 ="";
        
        
        
        
    public YearEndIds() {}
    
    public YearEndIds(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller203(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller204(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller205(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller206(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller207(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller208(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller209(data.Substring(offset, 8).Trim());
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetYearEndIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler203.PadRight(8));
        result.Append(_Filler204.PadRight(8));
        result.Append(_Filler205.PadRight(8));
        result.Append(_Filler206.PadRight(8));
        result.Append(_Filler207.PadRight(8));
        result.Append(_Filler208.PadRight(8));
        result.Append(_Filler209.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetYearEndIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller203(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller204(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller205(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller206(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller207(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller208(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller209(extracted);
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller203()
    {
        return _Filler203;
    }
    
    // Standard Setter
    public void SetFiller203(string value)
    {
        _Filler203 = value;
    }
    
    // Get<>AsString()
    public string GetFiller203AsString()
    {
        return _Filler203.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller203AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler203 = value;
    }
    
    // Standard Getter
    public string GetFiller204()
    {
        return _Filler204;
    }
    
    // Standard Setter
    public void SetFiller204(string value)
    {
        _Filler204 = value;
    }
    
    // Get<>AsString()
    public string GetFiller204AsString()
    {
        return _Filler204.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller204AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler204 = value;
    }
    
    // Standard Getter
    public string GetFiller205()
    {
        return _Filler205;
    }
    
    // Standard Setter
    public void SetFiller205(string value)
    {
        _Filler205 = value;
    }
    
    // Get<>AsString()
    public string GetFiller205AsString()
    {
        return _Filler205.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller205AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler205 = value;
    }
    
    // Standard Getter
    public string GetFiller206()
    {
        return _Filler206;
    }
    
    // Standard Setter
    public void SetFiller206(string value)
    {
        _Filler206 = value;
    }
    
    // Get<>AsString()
    public string GetFiller206AsString()
    {
        return _Filler206.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller206AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler206 = value;
    }
    
    // Standard Getter
    public string GetFiller207()
    {
        return _Filler207;
    }
    
    // Standard Setter
    public void SetFiller207(string value)
    {
        _Filler207 = value;
    }
    
    // Get<>AsString()
    public string GetFiller207AsString()
    {
        return _Filler207.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller207AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler207 = value;
    }
    
    // Standard Getter
    public string GetFiller208()
    {
        return _Filler208;
    }
    
    // Standard Setter
    public void SetFiller208(string value)
    {
        _Filler208 = value;
    }
    
    // Get<>AsString()
    public string GetFiller208AsString()
    {
        return _Filler208.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller208AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler208 = value;
    }
    
    // Standard Getter
    public string GetFiller209()
    {
        return _Filler209;
    }
    
    // Standard Setter
    public void SetFiller209(string value)
    {
        _Filler209 = value;
    }
    
    // Get<>AsString()
    public string GetFiller209AsString()
    {
        return _Filler209.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller209AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler209 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}