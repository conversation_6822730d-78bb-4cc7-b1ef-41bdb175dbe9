using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// <Section> Class for Fvar
public class Fvar
{
public Fvar() {}

// Fields in the class


// [DEBUG] Field: ReportRecord, is_external=, is_static_class=False, static_prefix=
private ReportRecord _ReportRecord = new ReportRecord();




// [DEBUG] Field: ReportRecord2, is_external=, is_static_class=False, static_prefix=
private ReportRecord2 _ReportRecord2 = new ReportRecord2();




// Getter and Setter methods

// Standard Getter
public ReportRecord GetReportRecord()
{
    return _ReportRecord;
}

// Standard Setter
public void SetReportRecord(ReportRecord value)
{
    _ReportRecord = value;
}

// Get<>AsString()
public string GetReportRecordAsString()
{
    return _ReportRecord != null ? _ReportRecord.GetReportRecordAsString() : "";
}

// Set<>AsString()
public void SetReportRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ReportRecord == null)
    {
        _ReportRecord = new ReportRecord();
    }
    _ReportRecord.SetReportRecordAsString(value);
}

// Standard Getter
public ReportRecord2 GetReportRecord2()
{
    return _ReportRecord2;
}

// Standard Setter
public void SetReportRecord2(ReportRecord2 value)
{
    _ReportRecord2 = value;
}

// Get<>AsString()
public string GetReportRecord2AsString()
{
    return _ReportRecord2 != null ? _ReportRecord2.GetReportRecord2AsString() : "";
}

// Set<>AsString()
public void SetReportRecord2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ReportRecord2 == null)
    {
        _ReportRecord2 = new ReportRecord2();
    }
    _ReportRecord2.SetReportRecord2AsString(value);
}


}}