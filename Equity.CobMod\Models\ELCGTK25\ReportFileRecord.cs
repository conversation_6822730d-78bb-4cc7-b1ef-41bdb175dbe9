using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing ReportFileRecord Data Structure

public class ReportFileRecord
{
    private static int _size = 779;
    // [DEBUG] Class: ReportFileRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: RSort, is_external=, is_static_class=False, static_prefix=
    private RSort _RSort = new RSort();
    
    
    
    
    // [DEBUG] Field: RRecordType, is_external=, is_static_class=False, static_prefix=
    private string _RRecordType ="";
    
    
    // 88-level condition checks for RRecordType
    public bool IsRSedolRecord()
    {
        if (this._RRecordType == "'1'") return true;
        return false;
    }
    public bool IsRDetailRecord()
    {
        if (this._RRecordType == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: RData, is_external=, is_static_class=False, static_prefix=
    private RData _RData = new RData();
    
    
    
    
    // [DEBUG] Field: RSedolData, is_external=, is_static_class=False, static_prefix=
    private RSedolData _RSedolData = new RSedolData();
    
    
    
    
    // [DEBUG] Field: RTrancheDetailData, is_external=, is_static_class=False, static_prefix=
    private RTrancheDetailData _RTrancheDetailData = new RTrancheDetailData();
    
    
    
    
    // [DEBUG] Field: RTransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _RTransactionCategory ="";
    
    
    
    
    
    // Serialization methods
    public string GetReportFileRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_RSort.GetRSortAsString());
        result.Append(_RRecordType.PadRight(1));
        result.Append(_RData.GetRDataAsString());
        result.Append(_RSedolData.GetRSedolDataAsString());
        result.Append(_RTrancheDetailData.GetRTrancheDetailDataAsString());
        result.Append(_RTransactionCategory.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetReportFileRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 34 <= data.Length)
        {
            _RSort.SetRSortAsString(data.Substring(offset, 34));
        }
        else
        {
            _RSort.SetRSortAsString(data.Substring(offset));
        }
        offset += 34;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetRRecordType(extracted);
        }
        offset += 1;
        if (offset + 238 <= data.Length)
        {
            _RData.SetRDataAsString(data.Substring(offset, 238));
        }
        else
        {
            _RData.SetRDataAsString(data.Substring(offset));
        }
        offset += 238;
        if (offset + 250 <= data.Length)
        {
            _RSedolData.SetRSedolDataAsString(data.Substring(offset, 250));
        }
        else
        {
            _RSedolData.SetRSedolDataAsString(data.Substring(offset));
        }
        offset += 250;
        if (offset + 256 <= data.Length)
        {
            _RTrancheDetailData.SetRTrancheDetailDataAsString(data.Substring(offset, 256));
        }
        else
        {
            _RTrancheDetailData.SetRTrancheDetailDataAsString(data.Substring(offset));
        }
        offset += 256;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetRTransactionCategory(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportFileRecordAsString();
    }
    // Set<>String Override function
    public void SetReportFileRecord(string value)
    {
        SetReportFileRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public RSort GetRSort()
    {
        return _RSort;
    }
    
    // Standard Setter
    public void SetRSort(RSort value)
    {
        _RSort = value;
    }
    
    // Get<>AsString()
    public string GetRSortAsString()
    {
        return _RSort != null ? _RSort.GetRSortAsString() : "";
    }
    
    // Set<>AsString()
    public void SetRSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_RSort == null)
        {
            _RSort = new RSort();
        }
        _RSort.SetRSortAsString(value);
    }
    
    // Standard Getter
    public string GetRRecordType()
    {
        return _RRecordType;
    }
    
    // Standard Setter
    public void SetRRecordType(string value)
    {
        _RRecordType = value;
    }
    
    // Get<>AsString()
    public string GetRRecordTypeAsString()
    {
        return _RRecordType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetRRecordTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RRecordType = value;
    }
    
    // Standard Getter
    public RData GetRData()
    {
        return _RData;
    }
    
    // Standard Setter
    public void SetRData(RData value)
    {
        _RData = value;
    }
    
    // Get<>AsString()
    public string GetRDataAsString()
    {
        return _RData != null ? _RData.GetRDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetRDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_RData == null)
        {
            _RData = new RData();
        }
        _RData.SetRDataAsString(value);
    }
    
    // Standard Getter
    public RSedolData GetRSedolData()
    {
        return _RSedolData;
    }
    
    // Standard Setter
    public void SetRSedolData(RSedolData value)
    {
        _RSedolData = value;
    }
    
    // Get<>AsString()
    public string GetRSedolDataAsString()
    {
        return _RSedolData != null ? _RSedolData.GetRSedolDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetRSedolDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_RSedolData == null)
        {
            _RSedolData = new RSedolData();
        }
        _RSedolData.SetRSedolDataAsString(value);
    }
    
    // Standard Getter
    public RTrancheDetailData GetRTrancheDetailData()
    {
        return _RTrancheDetailData;
    }
    
    // Standard Setter
    public void SetRTrancheDetailData(RTrancheDetailData value)
    {
        _RTrancheDetailData = value;
    }
    
    // Get<>AsString()
    public string GetRTrancheDetailDataAsString()
    {
        return _RTrancheDetailData != null ? _RTrancheDetailData.GetRTrancheDetailDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetRTrancheDetailDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_RTrancheDetailData == null)
        {
            _RTrancheDetailData = new RTrancheDetailData();
        }
        _RTrancheDetailData.SetRTrancheDetailDataAsString(value);
    }
    
    // Standard Getter
    public string GetRTransactionCategory()
    {
        return _RTransactionCategory;
    }
    
    // Standard Setter
    public void SetRTransactionCategory(string value)
    {
        _RTransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetRTransactionCategoryAsString()
    {
        return _RTransactionCategory.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetRTransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RTransactionCategory = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetRSort(string value)
    {
        _RSort.SetRSortAsString(value);
    }
    // Nested Class: RSort
    public class RSort
    {
        private static int _size = 34;
        
        // Fields in the class
        
        
        // [DEBUG] Field: RCurrencySort, is_external=, is_static_class=False, static_prefix=
        private string _RCurrencySort ="";
        
        
        
        
        // [DEBUG] Field: RCoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _RCoAcLk ="";
        
        
        
        
        // [DEBUG] Field: RCountryCode, is_external=, is_static_class=False, static_prefix=
        private string _RCountryCode ="";
        
        
        
        
        // [DEBUG] Field: RBondFlag, is_external=, is_static_class=False, static_prefix=
        private string _RBondFlag ="";
        
        
        
        
        // [DEBUG] Field: RMainGroup, is_external=, is_static_class=False, static_prefix=
        private string _RMainGroup ="";
        
        
        
        
        // [DEBUG] Field: RSecurityType, is_external=, is_static_class=False, static_prefix=
        private string _RSecurityType ="";
        
        
        
        
        // [DEBUG] Field: RSecuritySortCode, is_external=, is_static_class=False, static_prefix=
        private string _RSecuritySortCode ="";
        
        
        
        
        // [DEBUG] Field: RSedolSort, is_external=, is_static_class=False, static_prefix=
        private string _RSedolSort ="";
        
        
        
        
    public RSort() {}
    
    public RSort(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetRCurrencySort(data.Substring(offset, 1).Trim());
        offset += 1;
        SetRCoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetRCountryCode(data.Substring(offset, 3).Trim());
        offset += 3;
        SetRBondFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetRMainGroup(data.Substring(offset, 2).Trim());
        offset += 2;
        SetRSecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetRSecuritySortCode(data.Substring(offset, 15).Trim());
        offset += 15;
        SetRSedolSort(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetRSortAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_RCurrencySort.PadRight(1));
        result.Append(_RCoAcLk.PadRight(4));
        result.Append(_RCountryCode.PadRight(3));
        result.Append(_RBondFlag.PadRight(1));
        result.Append(_RMainGroup.PadRight(2));
        result.Append(_RSecurityType.PadRight(1));
        result.Append(_RSecuritySortCode.PadRight(15));
        result.Append(_RSedolSort.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetRSortAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetRCurrencySort(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetRCoAcLk(extracted);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetRCountryCode(extracted);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetRBondFlag(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetRMainGroup(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetRSecurityType(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetRSecuritySortCode(extracted);
        }
        offset += 15;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetRSedolSort(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetRCurrencySort()
    {
        return _RCurrencySort;
    }
    
    // Standard Setter
    public void SetRCurrencySort(string value)
    {
        _RCurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetRCurrencySortAsString()
    {
        return _RCurrencySort.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetRCurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RCurrencySort = value;
    }
    
    // Standard Getter
    public string GetRCoAcLk()
    {
        return _RCoAcLk;
    }
    
    // Standard Setter
    public void SetRCoAcLk(string value)
    {
        _RCoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetRCoAcLkAsString()
    {
        return _RCoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetRCoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RCoAcLk = value;
    }
    
    // Standard Getter
    public string GetRCountryCode()
    {
        return _RCountryCode;
    }
    
    // Standard Setter
    public void SetRCountryCode(string value)
    {
        _RCountryCode = value;
    }
    
    // Get<>AsString()
    public string GetRCountryCodeAsString()
    {
        return _RCountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetRCountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RCountryCode = value;
    }
    
    // Standard Getter
    public string GetRBondFlag()
    {
        return _RBondFlag;
    }
    
    // Standard Setter
    public void SetRBondFlag(string value)
    {
        _RBondFlag = value;
    }
    
    // Get<>AsString()
    public string GetRBondFlagAsString()
    {
        return _RBondFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetRBondFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RBondFlag = value;
    }
    
    // Standard Getter
    public string GetRMainGroup()
    {
        return _RMainGroup;
    }
    
    // Standard Setter
    public void SetRMainGroup(string value)
    {
        _RMainGroup = value;
    }
    
    // Get<>AsString()
    public string GetRMainGroupAsString()
    {
        return _RMainGroup.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetRMainGroupAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RMainGroup = value;
    }
    
    // Standard Getter
    public string GetRSecurityType()
    {
        return _RSecurityType;
    }
    
    // Standard Setter
    public void SetRSecurityType(string value)
    {
        _RSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetRSecurityTypeAsString()
    {
        return _RSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetRSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RSecurityType = value;
    }
    
    // Standard Getter
    public string GetRSecuritySortCode()
    {
        return _RSecuritySortCode;
    }
    
    // Standard Setter
    public void SetRSecuritySortCode(string value)
    {
        _RSecuritySortCode = value;
    }
    
    // Get<>AsString()
    public string GetRSecuritySortCodeAsString()
    {
        return _RSecuritySortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetRSecuritySortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RSecuritySortCode = value;
    }
    
    // Standard Getter
    public string GetRSedolSort()
    {
        return _RSedolSort;
    }
    
    // Standard Setter
    public void SetRSedolSort(string value)
    {
        _RSedolSort = value;
    }
    
    // Get<>AsString()
    public string GetRSedolSortAsString()
    {
        return _RSedolSort.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetRSedolSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RSedolSort = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetRData(string value)
{
    _RData.SetRDataAsString(value);
}
// Nested Class: RData
public class RData
{
    private static int _size = 238;
    
    // Fields in the class
    
    
    // [DEBUG] Field: RCompanyName, is_external=, is_static_class=False, static_prefix=
    private string _RCompanyName ="";
    
    
    
    
    // [DEBUG] Field: RTitle, is_external=, is_static_class=False, static_prefix=
    private string _RTitle ="";
    
    
    
    
    // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
    private string _Filler18 ="";
    
    
    
    
    // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
    private string _Filler19 ="";
    
    
    
    
    // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
    private string _Filler20 ="";
    
    
    
    
    // [DEBUG] Field: RTransCat, is_external=, is_static_class=False, static_prefix=
    private string _RTransCat ="";
    
    
    // 88-level condition checks for RTransCat
    public bool IsRDerivatives()
    {
        if (this._RTransCat == "'WP'") return true;
        if (this._RTransCat == "'WC'") return true;
        if (this._RTransCat == "'EP'") return true;
        if (this._RTransCat == "'EC'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
    private string _Filler21 ="";
    
    
    
    
public RData() {}

public RData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetRCompanyName(data.Substring(offset, 51).Trim());
    offset += 51;
    SetRTitle(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller18(data.Substring(offset, 49).Trim());
    offset += 49;
    SetFiller19(data.Substring(offset, 78).Trim());
    offset += 78;
    SetFiller20(data.Substring(offset, 0).Trim());
    offset += 0;
    SetRTransCat(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller21(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetRDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_RCompanyName.PadRight(51));
    result.Append(_RTitle.PadRight(60));
    result.Append(_Filler18.PadRight(49));
    result.Append(_Filler19.PadRight(78));
    result.Append(_Filler20.PadRight(0));
    result.Append(_RTransCat.PadRight(0));
    result.Append(_Filler21.PadRight(0));
    
    return result.ToString();
}

public void SetRDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 51 <= data.Length)
    {
        string extracted = data.Substring(offset, 51).Trim();
        SetRCompanyName(extracted);
    }
    offset += 51;
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetRTitle(extracted);
    }
    offset += 60;
    if (offset + 49 <= data.Length)
    {
        string extracted = data.Substring(offset, 49).Trim();
        SetFiller18(extracted);
    }
    offset += 49;
    if (offset + 78 <= data.Length)
    {
        string extracted = data.Substring(offset, 78).Trim();
        SetFiller19(extracted);
    }
    offset += 78;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller20(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetRTransCat(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller21(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetRCompanyName()
{
    return _RCompanyName;
}

// Standard Setter
public void SetRCompanyName(string value)
{
    _RCompanyName = value;
}

// Get<>AsString()
public string GetRCompanyNameAsString()
{
    return _RCompanyName.PadRight(51);
}

// Set<>AsString()
public void SetRCompanyNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RCompanyName = value;
}

// Standard Getter
public string GetRTitle()
{
    return _RTitle;
}

// Standard Setter
public void SetRTitle(string value)
{
    _RTitle = value;
}

// Get<>AsString()
public string GetRTitleAsString()
{
    return _RTitle.PadRight(60);
}

// Set<>AsString()
public void SetRTitleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RTitle = value;
}

// Standard Getter
public string GetFiller18()
{
    return _Filler18;
}

// Standard Setter
public void SetFiller18(string value)
{
    _Filler18 = value;
}

// Get<>AsString()
public string GetFiller18AsString()
{
    return _Filler18.PadRight(49);
}

// Set<>AsString()
public void SetFiller18AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler18 = value;
}

// Standard Getter
public string GetFiller19()
{
    return _Filler19;
}

// Standard Setter
public void SetFiller19(string value)
{
    _Filler19 = value;
}

// Get<>AsString()
public string GetFiller19AsString()
{
    return _Filler19.PadRight(78);
}

// Set<>AsString()
public void SetFiller19AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler19 = value;
}

// Standard Getter
public string GetFiller20()
{
    return _Filler20;
}

// Standard Setter
public void SetFiller20(string value)
{
    _Filler20 = value;
}

// Get<>AsString()
public string GetFiller20AsString()
{
    return _Filler20.PadRight(0);
}

// Set<>AsString()
public void SetFiller20AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler20 = value;
}

// Standard Getter
public string GetRTransCat()
{
    return _RTransCat;
}

// Standard Setter
public void SetRTransCat(string value)
{
    _RTransCat = value;
}

// Get<>AsString()
public string GetRTransCatAsString()
{
    return _RTransCat.PadRight(0);
}

// Set<>AsString()
public void SetRTransCatAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RTransCat = value;
}

// Standard Getter
public string GetFiller21()
{
    return _Filler21;
}

// Standard Setter
public void SetFiller21(string value)
{
    _Filler21 = value;
}

// Get<>AsString()
public string GetFiller21AsString()
{
    return _Filler21.PadRight(0);
}

// Set<>AsString()
public void SetFiller21AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler21 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetRSedolData(string value)
{
    _RSedolData.SetRSedolDataAsString(value);
}
// Nested Class: RSedolData
public class RSedolData
{
    private static int _size = 250;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler22, is_external=, is_static_class=False, static_prefix=
    private string _Filler22 ="";
    
    
    
    
    // [DEBUG] Field: RSedolNumber, is_external=, is_static_class=False, static_prefix=
    private string _RSedolNumber ="";
    
    
    
    
    // [DEBUG] Field: RIssuersName, is_external=, is_static_class=False, static_prefix=
    private RSedolData.RIssuersName _RIssuersName = new RSedolData.RIssuersName();
    
    
    
    
    // [DEBUG] Field: RStockDescription, is_external=, is_static_class=False, static_prefix=
    private RSedolData.RStockDescription _RStockDescription = new RSedolData.RStockDescription();
    
    
    
    
    // [DEBUG] Field: RLastTrancheContractNo, is_external=, is_static_class=False, static_prefix=
    private string _RLastTrancheContractNo ="";
    
    
    
    
    // [DEBUG] Field: RProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _RProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: RSedolRecordCount, is_external=, is_static_class=False, static_prefix=
    private int _RSedolRecordCount =0;
    
    
    
    
    // [DEBUG] Field: RTrancheCount, is_external=, is_static_class=False, static_prefix=
    private int _RTrancheCount =0;
    
    
    
    
    // [DEBUG] Field: RPrintFlag, is_external=, is_static_class=False, static_prefix=
    private string _RPrintFlag ="";
    
    
    
    
    // [DEBUG] Field: RHoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _RHoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: RSequenceNo, is_external=, is_static_class=False, static_prefix=
    private string _RSequenceNo ="";
    
    
    
    
    // [DEBUG] Field: RYear, is_external=, is_static_class=False, static_prefix=
    private string _RYear ="";
    
    
    
    
    // [DEBUG] Field: RPercentBusinessUse, is_external=, is_static_class=False, static_prefix=
    private string _RPercentBusinessUse ="";
    
    
    
    
    // [DEBUG] Field: RTrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _RTrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler23, is_external=, is_static_class=False, static_prefix=
    private string _Filler23 ="";
    
    
    
    
    // [DEBUG] Field: Filler24, is_external=, is_static_class=False, static_prefix=
    private string _Filler24 ="";
    
    
    
    
public RSedolData() {}

public RSedolData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller22(data.Substring(offset, 19).Trim());
    offset += 19;
    SetRSedolNumber(data.Substring(offset, 7).Trim());
    offset += 7;
    _RIssuersName.SetRIssuersNameAsString(data.Substring(offset, RIssuersName.GetSize()));
    offset += 35;
    _RStockDescription.SetRStockDescriptionAsString(data.Substring(offset, RStockDescription.GetSize()));
    offset += 40;
    SetRLastTrancheContractNo(data.Substring(offset, 10).Trim());
    offset += 10;
    SetRProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetRSedolRecordCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetRTrancheCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetRPrintFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetRHoldingFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetRSequenceNo(data.Substring(offset, 9).Trim());
    offset += 9;
    SetRYear(data.Substring(offset, 2).Trim());
    offset += 2;
    SetRPercentBusinessUse(data.Substring(offset, 18).Trim());
    offset += 18;
    SetRTrancheFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller23(data.Substring(offset, 78).Trim());
    offset += 78;
    SetFiller24(data.Substring(offset, 12).Trim());
    offset += 12;
    
}

// Serialization methods
public string GetRSedolDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler22.PadRight(19));
    result.Append(_RSedolNumber.PadRight(7));
    result.Append(_RIssuersName.GetRIssuersNameAsString());
    result.Append(_RStockDescription.GetRStockDescriptionAsString());
    result.Append(_RLastTrancheContractNo.PadRight(10));
    result.Append(_RProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_RSedolRecordCount.ToString().PadLeft(3, '0'));
    result.Append(_RTrancheCount.ToString().PadLeft(3, '0'));
    result.Append(_RPrintFlag.PadRight(1));
    result.Append(_RHoldingFlag.PadRight(1));
    result.Append(_RSequenceNo.PadRight(9));
    result.Append(_RYear.PadRight(2));
    result.Append(_RPercentBusinessUse.PadRight(18));
    result.Append(_RTrancheFlag.PadRight(1));
    result.Append(_Filler23.PadRight(78));
    result.Append(_Filler24.PadRight(12));
    
    return result.ToString();
}

public void SetRSedolDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller22(extracted);
    }
    offset += 19;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetRSedolNumber(extracted);
    }
    offset += 7;
    if (offset + 35 <= data.Length)
    {
        _RIssuersName.SetRIssuersNameAsString(data.Substring(offset, 35));
    }
    else
    {
        _RIssuersName.SetRIssuersNameAsString(data.Substring(offset));
    }
    offset += 35;
    if (offset + 40 <= data.Length)
    {
        _RStockDescription.SetRStockDescriptionAsString(data.Substring(offset, 40));
    }
    else
    {
        _RStockDescription.SetRStockDescriptionAsString(data.Substring(offset));
    }
    offset += 40;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetRLastTrancheContractNo(extracted);
    }
    offset += 10;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetRProfitLoss(parsedDec);
    }
    offset += 11;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetRSedolRecordCount(parsedInt);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetRTrancheCount(parsedInt);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetRPrintFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetRHoldingFlag(extracted);
    }
    offset += 1;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetRSequenceNo(extracted);
    }
    offset += 9;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetRYear(extracted);
    }
    offset += 2;
    if (offset + 18 <= data.Length)
    {
        string extracted = data.Substring(offset, 18).Trim();
        SetRPercentBusinessUse(extracted);
    }
    offset += 18;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetRTrancheFlag(extracted);
    }
    offset += 1;
    if (offset + 78 <= data.Length)
    {
        string extracted = data.Substring(offset, 78).Trim();
        SetFiller23(extracted);
    }
    offset += 78;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller24(extracted);
    }
    offset += 12;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller22()
{
    return _Filler22;
}

// Standard Setter
public void SetFiller22(string value)
{
    _Filler22 = value;
}

// Get<>AsString()
public string GetFiller22AsString()
{
    return _Filler22.PadRight(19);
}

// Set<>AsString()
public void SetFiller22AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler22 = value;
}

// Standard Getter
public string GetRSedolNumber()
{
    return _RSedolNumber;
}

// Standard Setter
public void SetRSedolNumber(string value)
{
    _RSedolNumber = value;
}

// Get<>AsString()
public string GetRSedolNumberAsString()
{
    return _RSedolNumber.PadRight(7);
}

// Set<>AsString()
public void SetRSedolNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RSedolNumber = value;
}

// Standard Getter
public RIssuersName GetRIssuersName()
{
    return _RIssuersName;
}

// Standard Setter
public void SetRIssuersName(RIssuersName value)
{
    _RIssuersName = value;
}

// Get<>AsString()
public string GetRIssuersNameAsString()
{
    return _RIssuersName != null ? _RIssuersName.GetRIssuersNameAsString() : "";
}

// Set<>AsString()
public void SetRIssuersNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_RIssuersName == null)
    {
        _RIssuersName = new RIssuersName();
    }
    _RIssuersName.SetRIssuersNameAsString(value);
}

// Standard Getter
public RStockDescription GetRStockDescription()
{
    return _RStockDescription;
}

// Standard Setter
public void SetRStockDescription(RStockDescription value)
{
    _RStockDescription = value;
}

// Get<>AsString()
public string GetRStockDescriptionAsString()
{
    return _RStockDescription != null ? _RStockDescription.GetRStockDescriptionAsString() : "";
}

// Set<>AsString()
public void SetRStockDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_RStockDescription == null)
    {
        _RStockDescription = new RStockDescription();
    }
    _RStockDescription.SetRStockDescriptionAsString(value);
}

// Standard Getter
public string GetRLastTrancheContractNo()
{
    return _RLastTrancheContractNo;
}

// Standard Setter
public void SetRLastTrancheContractNo(string value)
{
    _RLastTrancheContractNo = value;
}

// Get<>AsString()
public string GetRLastTrancheContractNoAsString()
{
    return _RLastTrancheContractNo.PadRight(10);
}

// Set<>AsString()
public void SetRLastTrancheContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RLastTrancheContractNo = value;
}

// Standard Getter
public decimal GetRProfitLoss()
{
    return _RProfitLoss;
}

// Standard Setter
public void SetRProfitLoss(decimal value)
{
    _RProfitLoss = value;
}

// Get<>AsString()
public string GetRProfitLossAsString()
{
    return _RProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetRProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _RProfitLoss = parsed;
}

// Standard Getter
public int GetRSedolRecordCount()
{
    return _RSedolRecordCount;
}

// Standard Setter
public void SetRSedolRecordCount(int value)
{
    _RSedolRecordCount = value;
}

// Get<>AsString()
public string GetRSedolRecordCountAsString()
{
    return _RSedolRecordCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetRSedolRecordCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _RSedolRecordCount = parsed;
}

// Standard Getter
public int GetRTrancheCount()
{
    return _RTrancheCount;
}

// Standard Setter
public void SetRTrancheCount(int value)
{
    _RTrancheCount = value;
}

// Get<>AsString()
public string GetRTrancheCountAsString()
{
    return _RTrancheCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetRTrancheCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _RTrancheCount = parsed;
}

// Standard Getter
public string GetRPrintFlag()
{
    return _RPrintFlag;
}

// Standard Setter
public void SetRPrintFlag(string value)
{
    _RPrintFlag = value;
}

// Get<>AsString()
public string GetRPrintFlagAsString()
{
    return _RPrintFlag.PadRight(1);
}

// Set<>AsString()
public void SetRPrintFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RPrintFlag = value;
}

// Standard Getter
public string GetRHoldingFlag()
{
    return _RHoldingFlag;
}

// Standard Setter
public void SetRHoldingFlag(string value)
{
    _RHoldingFlag = value;
}

// Get<>AsString()
public string GetRHoldingFlagAsString()
{
    return _RHoldingFlag.PadRight(1);
}

// Set<>AsString()
public void SetRHoldingFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RHoldingFlag = value;
}

// Standard Getter
public string GetRSequenceNo()
{
    return _RSequenceNo;
}

// Standard Setter
public void SetRSequenceNo(string value)
{
    _RSequenceNo = value;
}

// Get<>AsString()
public string GetRSequenceNoAsString()
{
    return _RSequenceNo.PadRight(9);
}

// Set<>AsString()
public void SetRSequenceNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RSequenceNo = value;
}

// Standard Getter
public string GetRYear()
{
    return _RYear;
}

// Standard Setter
public void SetRYear(string value)
{
    _RYear = value;
}

// Get<>AsString()
public string GetRYearAsString()
{
    return _RYear.PadRight(2);
}

// Set<>AsString()
public void SetRYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RYear = value;
}

// Standard Getter
public string GetRPercentBusinessUse()
{
    return _RPercentBusinessUse;
}

// Standard Setter
public void SetRPercentBusinessUse(string value)
{
    _RPercentBusinessUse = value;
}

// Get<>AsString()
public string GetRPercentBusinessUseAsString()
{
    return _RPercentBusinessUse.PadRight(18);
}

// Set<>AsString()
public void SetRPercentBusinessUseAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RPercentBusinessUse = value;
}

// Standard Getter
public string GetRTrancheFlag()
{
    return _RTrancheFlag;
}

// Standard Setter
public void SetRTrancheFlag(string value)
{
    _RTrancheFlag = value;
}

// Get<>AsString()
public string GetRTrancheFlagAsString()
{
    return _RTrancheFlag.PadRight(1);
}

// Set<>AsString()
public void SetRTrancheFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RTrancheFlag = value;
}

// Standard Getter
public string GetFiller23()
{
    return _Filler23;
}

// Standard Setter
public void SetFiller23(string value)
{
    _Filler23 = value;
}

// Get<>AsString()
public string GetFiller23AsString()
{
    return _Filler23.PadRight(78);
}

// Set<>AsString()
public void SetFiller23AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler23 = value;
}

// Standard Getter
public string GetFiller24()
{
    return _Filler24;
}

// Standard Setter
public void SetFiller24(string value)
{
    _Filler24 = value;
}

// Get<>AsString()
public string GetFiller24AsString()
{
    return _Filler24.PadRight(12);
}

// Set<>AsString()
public void SetFiller24AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler24 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: RIssuersName
public class RIssuersName
{
    private static int _size = 35;
    
    // Fields in the class
    
    
    // [DEBUG] Field: RIssuersName1, is_external=, is_static_class=False, static_prefix=
    private string _RIssuersName1 ="";
    
    
    
    
    // [DEBUG] Field: RIssuersName2, is_external=, is_static_class=False, static_prefix=
    private string _RIssuersName2 ="";
    
    
    
    
public RIssuersName() {}

public RIssuersName(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetRIssuersName1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetRIssuersName2(data.Substring(offset, 15).Trim());
    offset += 15;
    
}

// Serialization methods
public string GetRIssuersNameAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_RIssuersName1.PadRight(20));
    result.Append(_RIssuersName2.PadRight(15));
    
    return result.ToString();
}

public void SetRIssuersNameAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetRIssuersName1(extracted);
    }
    offset += 20;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetRIssuersName2(extracted);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public string GetRIssuersName1()
{
    return _RIssuersName1;
}

// Standard Setter
public void SetRIssuersName1(string value)
{
    _RIssuersName1 = value;
}

// Get<>AsString()
public string GetRIssuersName1AsString()
{
    return _RIssuersName1.PadRight(20);
}

// Set<>AsString()
public void SetRIssuersName1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RIssuersName1 = value;
}

// Standard Getter
public string GetRIssuersName2()
{
    return _RIssuersName2;
}

// Standard Setter
public void SetRIssuersName2(string value)
{
    _RIssuersName2 = value;
}

// Get<>AsString()
public string GetRIssuersName2AsString()
{
    return _RIssuersName2.PadRight(15);
}

// Set<>AsString()
public void SetRIssuersName2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RIssuersName2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: RStockDescription
public class RStockDescription
{
    private static int _size = 40;
    
    // Fields in the class
    
    
    // [DEBUG] Field: RStockDescription1, is_external=, is_static_class=False, static_prefix=
    private string _RStockDescription1 ="";
    
    
    
    
    // [DEBUG] Field: RStockDescription2, is_external=, is_static_class=False, static_prefix=
    private string _RStockDescription2 ="";
    
    
    
    
public RStockDescription() {}

public RStockDescription(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetRStockDescription1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetRStockDescription2(data.Substring(offset, 20).Trim());
    offset += 20;
    
}

// Serialization methods
public string GetRStockDescriptionAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_RStockDescription1.PadRight(20));
    result.Append(_RStockDescription2.PadRight(20));
    
    return result.ToString();
}

public void SetRStockDescriptionAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetRStockDescription1(extracted);
    }
    offset += 20;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetRStockDescription2(extracted);
    }
    offset += 20;
}

// Getter and Setter methods

// Standard Getter
public string GetRStockDescription1()
{
    return _RStockDescription1;
}

// Standard Setter
public void SetRStockDescription1(string value)
{
    _RStockDescription1 = value;
}

// Get<>AsString()
public string GetRStockDescription1AsString()
{
    return _RStockDescription1.PadRight(20);
}

// Set<>AsString()
public void SetRStockDescription1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RStockDescription1 = value;
}

// Standard Getter
public string GetRStockDescription2()
{
    return _RStockDescription2;
}

// Standard Setter
public void SetRStockDescription2(string value)
{
    _RStockDescription2 = value;
}

// Get<>AsString()
public string GetRStockDescription2AsString()
{
    return _RStockDescription2.PadRight(20);
}

// Set<>AsString()
public void SetRStockDescription2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RStockDescription2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetRTrancheDetailData(string value)
{
    _RTrancheDetailData.SetRTrancheDetailDataAsString(value);
}
// Nested Class: RTrancheDetailData
public class RTrancheDetailData
{
    private static int _size = 256;
    
    // Fields in the class
    
    
    // [DEBUG] Field: RAcquisitionDateX, is_external=, is_static_class=False, static_prefix=
    private string _RAcquisitionDateX ="";
    
    
    
    
    // [DEBUG] Field: RAcquisitionDate9, is_external=, is_static_class=False, static_prefix=
    private RTrancheDetailData.RAcquisitionDate9 _RAcquisitionDate9 = new RTrancheDetailData.RAcquisitionDate9();
    
    
    
    
    // [DEBUG] Field: RTrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _RTrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: RLineNumber, is_external=, is_static_class=False, static_prefix=
    private int _RLineNumber =0;
    
    
    // 88-level condition checks for RLineNumber
    public bool IsRTrancheHeader()
    {
        if (this._RLineNumber == 000) return true;
        return false;
    }
    
    
    // [DEBUG] Field: RTrancheData, is_external=, is_static_class=False, static_prefix=
    private string _RTrancheData ="";
    
    
    
    
    // [DEBUG] Field: Filler25, is_external=, is_static_class=False, static_prefix=
    private string _Filler25 ="";
    
    
    
    
    // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
    private string _Filler26 ="";
    
    
    
    
public RTrancheDetailData() {}

public RTrancheDetailData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetRAcquisitionDateX(data.Substring(offset, 6).Trim());
    offset += 6;
    _RAcquisitionDate9.SetRAcquisitionDate9AsString(data.Substring(offset, RAcquisitionDate9.GetSize()));
    offset += 6;
    SetRTrancheContractNumber(data.Substring(offset, 10).Trim());
    offset += 10;
    SetRLineNumber(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetRTrancheData(data.Substring(offset, 141).Trim());
    offset += 141;
    SetFiller25(data.Substring(offset, 78).Trim());
    offset += 78;
    SetFiller26(data.Substring(offset, 12).Trim());
    offset += 12;
    
}

// Serialization methods
public string GetRTrancheDetailDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_RAcquisitionDateX.PadRight(6));
    result.Append(_RAcquisitionDate9.GetRAcquisitionDate9AsString());
    result.Append(_RTrancheContractNumber.PadRight(10));
    result.Append(_RLineNumber.ToString().PadLeft(3, '0'));
    result.Append(_RTrancheData.PadRight(141));
    result.Append(_Filler25.PadRight(78));
    result.Append(_Filler26.PadRight(12));
    
    return result.ToString();
}

public void SetRTrancheDetailDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetRAcquisitionDateX(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _RAcquisitionDate9.SetRAcquisitionDate9AsString(data.Substring(offset, 6));
    }
    else
    {
        _RAcquisitionDate9.SetRAcquisitionDate9AsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetRTrancheContractNumber(extracted);
    }
    offset += 10;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetRLineNumber(parsedInt);
    }
    offset += 3;
    if (offset + 141 <= data.Length)
    {
        string extracted = data.Substring(offset, 141).Trim();
        SetRTrancheData(extracted);
    }
    offset += 141;
    if (offset + 78 <= data.Length)
    {
        string extracted = data.Substring(offset, 78).Trim();
        SetFiller25(extracted);
    }
    offset += 78;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller26(extracted);
    }
    offset += 12;
}

// Getter and Setter methods

// Standard Getter
public string GetRAcquisitionDateX()
{
    return _RAcquisitionDateX;
}

// Standard Setter
public void SetRAcquisitionDateX(string value)
{
    _RAcquisitionDateX = value;
}

// Get<>AsString()
public string GetRAcquisitionDateXAsString()
{
    return _RAcquisitionDateX.PadRight(6);
}

// Set<>AsString()
public void SetRAcquisitionDateXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RAcquisitionDateX = value;
}

// Standard Getter
public RAcquisitionDate9 GetRAcquisitionDate9()
{
    return _RAcquisitionDate9;
}

// Standard Setter
public void SetRAcquisitionDate9(RAcquisitionDate9 value)
{
    _RAcquisitionDate9 = value;
}

// Get<>AsString()
public string GetRAcquisitionDate9AsString()
{
    return _RAcquisitionDate9 != null ? _RAcquisitionDate9.GetRAcquisitionDate9AsString() : "";
}

// Set<>AsString()
public void SetRAcquisitionDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_RAcquisitionDate9 == null)
    {
        _RAcquisitionDate9 = new RAcquisitionDate9();
    }
    _RAcquisitionDate9.SetRAcquisitionDate9AsString(value);
}

// Standard Getter
public string GetRTrancheContractNumber()
{
    return _RTrancheContractNumber;
}

// Standard Setter
public void SetRTrancheContractNumber(string value)
{
    _RTrancheContractNumber = value;
}

// Get<>AsString()
public string GetRTrancheContractNumberAsString()
{
    return _RTrancheContractNumber.PadRight(10);
}

// Set<>AsString()
public void SetRTrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RTrancheContractNumber = value;
}

// Standard Getter
public int GetRLineNumber()
{
    return _RLineNumber;
}

// Standard Setter
public void SetRLineNumber(int value)
{
    _RLineNumber = value;
}

// Get<>AsString()
public string GetRLineNumberAsString()
{
    return _RLineNumber.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetRLineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _RLineNumber = parsed;
}

// Standard Getter
public string GetRTrancheData()
{
    return _RTrancheData;
}

// Standard Setter
public void SetRTrancheData(string value)
{
    _RTrancheData = value;
}

// Get<>AsString()
public string GetRTrancheDataAsString()
{
    return _RTrancheData.PadRight(141);
}

// Set<>AsString()
public void SetRTrancheDataAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _RTrancheData = value;
}

// Standard Getter
public string GetFiller25()
{
    return _Filler25;
}

// Standard Setter
public void SetFiller25(string value)
{
    _Filler25 = value;
}

// Get<>AsString()
public string GetFiller25AsString()
{
    return _Filler25.PadRight(78);
}

// Set<>AsString()
public void SetFiller25AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler25 = value;
}

// Standard Getter
public string GetFiller26()
{
    return _Filler26;
}

// Standard Setter
public void SetFiller26(string value)
{
    _Filler26 = value;
}

// Get<>AsString()
public string GetFiller26AsString()
{
    return _Filler26.PadRight(12);
}

// Set<>AsString()
public void SetFiller26AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler26 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: RAcquisitionDate9
public class RAcquisitionDate9
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: RAcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private int _RAcquisitionDateYy =0;
    
    
    
    
    // [DEBUG] Field: RAcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private int _RAcquisitionDateMm =0;
    
    
    
    
    // [DEBUG] Field: RAcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private int _RAcquisitionDateDd =0;
    
    
    
    
public RAcquisitionDate9() {}

public RAcquisitionDate9(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetRAcquisitionDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetRAcquisitionDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetRAcquisitionDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetRAcquisitionDate9AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_RAcquisitionDateYy.ToString().PadLeft(2, '0'));
    result.Append(_RAcquisitionDateMm.ToString().PadLeft(2, '0'));
    result.Append(_RAcquisitionDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetRAcquisitionDate9AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetRAcquisitionDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetRAcquisitionDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetRAcquisitionDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetRAcquisitionDateYy()
{
    return _RAcquisitionDateYy;
}

// Standard Setter
public void SetRAcquisitionDateYy(int value)
{
    _RAcquisitionDateYy = value;
}

// Get<>AsString()
public string GetRAcquisitionDateYyAsString()
{
    return _RAcquisitionDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetRAcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _RAcquisitionDateYy = parsed;
}

// Standard Getter
public int GetRAcquisitionDateMm()
{
    return _RAcquisitionDateMm;
}

// Standard Setter
public void SetRAcquisitionDateMm(int value)
{
    _RAcquisitionDateMm = value;
}

// Get<>AsString()
public string GetRAcquisitionDateMmAsString()
{
    return _RAcquisitionDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetRAcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _RAcquisitionDateMm = parsed;
}

// Standard Getter
public int GetRAcquisitionDateDd()
{
    return _RAcquisitionDateDd;
}

// Standard Setter
public void SetRAcquisitionDateDd(int value)
{
    _RAcquisitionDateDd = value;
}

// Get<>AsString()
public string GetRAcquisitionDateDdAsString()
{
    return _RAcquisitionDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetRAcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _RAcquisitionDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}

}}
