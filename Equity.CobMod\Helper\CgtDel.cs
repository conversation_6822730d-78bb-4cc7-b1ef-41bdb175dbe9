﻿using System;
using System.IO;
using EquityProject.CgtdelDTO;
namespace EquityProject.CgtdelPGM
{
    /// <summary>
    /// C# equivalent of the COBOL program CGTDEL.
    /// Deletes a specified file using System.IO.File.Delete.
    /// </summary>
    public class Cgtdel
    {
        // Declare Cgtdel Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare Cgtdel Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }
        // Run() method - Contains the main logic from PROCEDURE DIVISION
        public void Run(Gvar gvar, Ivar ivar)
        {
            _gvar = gvar; // Store passed Gvar if needed elsewhere
            _ivar = ivar; // Store passed Ivar

            // Local variable to hold the status code, similar to COBOL DEL-STATUS-CODE (COMP-5 -> short)
            _gvar.SetDelStatusCodeAsString("01"); // Set initial status code

            // Get the linkage object
            CgtdelLinkage linkage = ivar.GetCgtdelLinkage();

            // --- Input Validation ---
            if (linkage == null)
            {

                // Cannot proceed or set result code
                Console.Error.WriteLine("CGTDEL Error: Input linkage data is null.");
                // In a real scenario, might throw an exception or have a defined error path
                return; // Exit program logic
            }

            string fileToDelete = linkage.GetLDelFile();

            if (string.IsNullOrEmpty(fileToDelete))
            {
                Console.Error.WriteLine("CGTDEL Error: No filename provided in linkage.");
                _gvar.SetDelStatusCodeAsString("01"); // Set error code
                linkage.SetLDelResultAsString(_gvar.GetDelStatusCodeAsString()); // Update linkage immediately
                return; // Exit program logic
            }

            // Trim filename (optional, depends on how COBOL treated spaces)
            fileToDelete = fileToDelete.TrimEnd();

            // --- File Deletion Logic (mimicking CBL_DELETE_FILE) ---
            try
            {
                // Corresponds to: CALL "CBL_DELETE_FILE" USING L-DEL-FILE RETURNING DEL-STATUS-CODE.

                // Step 1: Check existence (CBL_DELETE_FILE returns error if not found)
                if (!File.Exists(fileToDelete))
                {
                    _gvar.SetDelStatusCodeAsString("01"); // File not found error status
                    Console.Error.WriteLine($"CGTDEL Error: File not found '{fileToDelete}'.");
                }
                else
                {
                    // Step 2: File exists, attempt deletion
                    File.Delete(fileToDelete);

                    // If File.Delete succeeds without exception
                    _gvar.SetDelStatusCodeAsString("00"); // Success status
                }
            }
            catch (Exception ex)
            {
                // Any other exception during File.Exists or File.Delete
                _gvar.SetDelStatusCodeAsString("01"); // General failure status
                Console.Error.WriteLine($"CGTDEL Error: Failed to delete file '{fileToDelete}'. Reason: {ex.GetType().Name} - {ex.Message}");
            }

            // Corresponds to: MOVE DEL-STATUS-CODE TO L-DEL-RESULT.
            // Move the locally determined status code to the linkage result field (int).
            linkage.SetLDelResultAsString(_gvar.GetDelStatusCodeAsString()); // Update linkage immediately
          
            // EXIT PROGRAM (implicit return from Run method)
        }
        // CallSub() method
        public void CallSub(Ivar ivar)
        {
           _ivar.GetCgtdelLinkage().SetCgtdelLinkageAsString(ivar.GetCgtdelLinkageAsString());
        }
    }
}
