using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing ElcgmioLinkage2 Data Structure

public class ElcgmioLinkage2
{
    private static int _size = 548;
    // [DEBUG] Class: ElcgmioLinkage2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LRecordArea, is_external=, is_static_class=False, static_prefix=
    private LRecordArea _LRecordArea = new LRecordArea();
    
    
    
    
    // [DEBUG] Field: Filler127, is_external=, is_static_class=False, static_prefix=
    private Filler127 _Filler127 = new Filler127();
    
    
    
    
    
    // Serialization methods
    public string GetElcgmioLinkage2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LRecordArea.GetLRecordAreaAsString());
        result.Append(_Filler127.GetFiller127AsString());
        
        return result.ToString();
    }
    
    public void SetElcgmioLinkage2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset, 270));
        }
        else
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset));
        }
        offset += 270;
        if (offset + 278 <= data.Length)
        {
            _Filler127.SetFiller127AsString(data.Substring(offset, 278));
        }
        else
        {
            _Filler127.SetFiller127AsString(data.Substring(offset));
        }
        offset += 278;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetElcgmioLinkage2AsString();
    }
    // Set<>String Override function
    public void SetElcgmioLinkage2(string value)
    {
        SetElcgmioLinkage2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public LRecordArea GetLRecordArea()
    {
        return _LRecordArea;
    }
    
    // Standard Setter
    public void SetLRecordArea(LRecordArea value)
    {
        _LRecordArea = value;
    }
    
    // Get<>AsString()
    public string GetLRecordAreaAsString()
    {
        return _LRecordArea != null ? _LRecordArea.GetLRecordAreaAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLRecordAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LRecordArea == null)
        {
            _LRecordArea = new LRecordArea();
        }
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    
    // Standard Getter
    public Filler127 GetFiller127()
    {
        return _Filler127;
    }
    
    // Standard Setter
    public void SetFiller127(Filler127 value)
    {
        _Filler127 = value;
    }
    
    // Get<>AsString()
    public string GetFiller127AsString()
    {
        return _Filler127 != null ? _Filler127.GetFiller127AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller127AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler127 == null)
        {
            _Filler127 = new Filler127();
        }
        _Filler127.SetFiller127AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLRecordArea(string value)
    {
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    // Nested Class: LRecordArea
    public class LRecordArea
    {
        private static int _size = 270;
        
        // Fields in the class
        
        
        // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
        private string _FixedPortion ="";
        
        
        
        
        // [DEBUG] Field: Filler126, is_external=, is_static_class=False, static_prefix=
        private string _Filler126 ="";
        
        
        
        
        // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
        private string[] _BalanceCosts = new string[200];
        
        
        
        
    public LRecordArea() {}
    
    public LRecordArea(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFixedPortion(data.Substring(offset, 270).Trim());
        offset += 270;
        SetFiller126(data.Substring(offset, 0).Trim());
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            string value = data.Substring(offset, 0);
            _BalanceCosts[i] = value.Trim();
            offset += 0;
        }
        
    }
    
    // Serialization methods
    public string GetLRecordAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_Filler126.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetLRecordAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller126(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts[i] = val.Trim();
            offset += 0;
        }
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public string GetFiller126()
    {
        return _Filler126;
    }
    
    // Standard Setter
    public void SetFiller126(string value)
    {
        _Filler126 = value;
    }
    
    // Get<>AsString()
    public string GetFiller126AsString()
    {
        return _Filler126.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller126AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler126 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(0);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller127(string value)
{
    _Filler127.SetFiller127AsString(value);
}
// Nested Class: Filler127
public class Filler127
{
    private static int _size = 278;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler128, is_external=, is_static_class=False, static_prefix=
    private string _Filler128 ="";
    
    
    
    
    // [DEBUG] Field: LFnUserNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnUserNo ="";
    
    
    
    
    // [DEBUG] Field: LFnGenerationNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnGenerationNo ="";
    
    
    
    
    // [DEBUG] Field: LFnYy, is_external=, is_static_class=False, static_prefix=
    private int _LFnYy =0;
    
    
    
    
    // [DEBUG] Field: Filler129, is_external=, is_static_class=False, static_prefix=
    private Filler127.Filler129 _Filler129 = new Filler127.Filler129();
    
    
    
    
public Filler127() {}

public Filler127(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller128(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnUserNo(data.Substring(offset, 4).Trim());
    offset += 4;
    SetLFnGenerationNo(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    _Filler129.SetFiller129AsString(data.Substring(offset, Filler129.GetSize()));
    offset += 270;
    
}

// Serialization methods
public string GetFiller127AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler128.PadRight(1));
    result.Append(_LFnUserNo.PadRight(4));
    result.Append(_LFnGenerationNo.PadRight(1));
    result.Append(_LFnYy.ToString().PadLeft(2, '0'));
    result.Append(_Filler129.GetFiller129AsString());
    
    return result.ToString();
}

public void SetFiller127AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller128(extracted);
    }
    offset += 1;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetLFnUserNo(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetLFnGenerationNo(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLFnYy(parsedInt);
    }
    offset += 2;
    if (offset + 270 <= data.Length)
    {
        _Filler129.SetFiller129AsString(data.Substring(offset, 270));
    }
    else
    {
        _Filler129.SetFiller129AsString(data.Substring(offset));
    }
    offset += 270;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller128()
{
    return _Filler128;
}

// Standard Setter
public void SetFiller128(string value)
{
    _Filler128 = value;
}

// Get<>AsString()
public string GetFiller128AsString()
{
    return _Filler128.PadRight(1);
}

// Set<>AsString()
public void SetFiller128AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler128 = value;
}

// Standard Getter
public string GetLFnUserNo()
{
    return _LFnUserNo;
}

// Standard Setter
public void SetLFnUserNo(string value)
{
    _LFnUserNo = value;
}

// Get<>AsString()
public string GetLFnUserNoAsString()
{
    return _LFnUserNo.PadRight(4);
}

// Set<>AsString()
public void SetLFnUserNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnUserNo = value;
}

// Standard Getter
public string GetLFnGenerationNo()
{
    return _LFnGenerationNo;
}

// Standard Setter
public void SetLFnGenerationNo(string value)
{
    _LFnGenerationNo = value;
}

// Get<>AsString()
public string GetLFnGenerationNoAsString()
{
    return _LFnGenerationNo.PadRight(1);
}

// Set<>AsString()
public void SetLFnGenerationNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnGenerationNo = value;
}

// Standard Getter
public int GetLFnYy()
{
    return _LFnYy;
}

// Standard Setter
public void SetLFnYy(int value)
{
    _LFnYy = value;
}

// Get<>AsString()
public string GetLFnYyAsString()
{
    return _LFnYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetLFnYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LFnYy = parsed;
}

// Standard Getter
public Filler129 GetFiller129()
{
    return _Filler129;
}

// Standard Setter
public void SetFiller129(Filler129 value)
{
    _Filler129 = value;
}

// Get<>AsString()
public string GetFiller129AsString()
{
    return _Filler129 != null ? _Filler129.GetFiller129AsString() : "";
}

// Set<>AsString()
public void SetFiller129AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler129 == null)
    {
        _Filler129 = new Filler129();
    }
    _Filler129.SetFiller129AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: Filler129
public class Filler129
{
    private static int _size = 270;
    
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion ="";
    
    
    
    
    // [DEBUG] Field: Filler130, is_external=, is_static_class=False, static_prefix=
    private string _Filler130 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts = new string[200];
    
    
    
    
public Filler129() {}

public Filler129(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFixedPortion(data.Substring(offset, 270).Trim());
    offset += 270;
    SetFiller130(data.Substring(offset, 0).Trim());
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        string value = data.Substring(offset, 0);
        _BalanceCosts[i] = value.Trim();
        offset += 0;
    }
    
}

// Serialization methods
public string GetFiller129AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_FixedPortion.PadRight(270));
    result.Append(_Filler130.PadRight(0));
    for (int i = 0; i < 200; i++)
    {
        result.Append(_BalanceCosts[i].PadRight(0));
    }
    
    return result.ToString();
}

public void SetFiller129AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 270 <= data.Length)
    {
        string extracted = data.Substring(offset, 270).Trim();
        SetFixedPortion(extracted);
    }
    offset += 270;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller130(extracted);
    }
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        if (offset + 0 > data.Length) break;
        string val = data.Substring(offset, 0);
        
        _BalanceCosts[i] = val.Trim();
        offset += 0;
    }
}

// Getter and Setter methods

// Standard Getter
public string GetFixedPortion()
{
    return _FixedPortion;
}

// Standard Setter
public void SetFixedPortion(string value)
{
    _FixedPortion = value;
}

// Get<>AsString()
public string GetFixedPortionAsString()
{
    return _FixedPortion.PadRight(270);
}

// Set<>AsString()
public void SetFixedPortionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _FixedPortion = value;
}

// Standard Getter
public string GetFiller130()
{
    return _Filler130;
}

// Standard Setter
public void SetFiller130(string value)
{
    _Filler130 = value;
}

// Get<>AsString()
public string GetFiller130AsString()
{
    return _Filler130.PadRight(0);
}

// Set<>AsString()
public void SetFiller130AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler130 = value;
}

// Array Accessors for BalanceCosts
public string GetBalanceCostsAt(int index)
{
    return _BalanceCosts[index];
}

public void SetBalanceCostsAt(int index, string value)
{
    _BalanceCosts[index] = value;
}

public string GetBalanceCostsAsStringAt(int index)
{
    return _BalanceCosts[index].PadRight(0);
}

public void SetBalanceCostsAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _BalanceCosts[index] = value;
}

// Flattened accessors (index 0)
public string GetBalanceCosts()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0]
    : default(string);
}

public void SetBalanceCosts(string value)
{
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    _BalanceCosts[0] = value;
}

public string GetBalanceCostsAsString()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0].ToString()
    : string.Empty;
}

public void SetBalanceCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    
    _BalanceCosts[0] = value;
}




public static int GetSize()
{
    return _size;
}

}
}

}}
