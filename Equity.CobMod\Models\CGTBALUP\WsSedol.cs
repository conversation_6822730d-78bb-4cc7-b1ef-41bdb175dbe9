using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WsSedol Data Structure

public class WsSedol
{
    private static int _size = 7;
    // [DEBUG] Class: WsSedol, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsSedol1, is_external=, is_static_class=False, static_prefix=
    private string _WsSedol1 ="";
    
    
    
    
    // [DEBUG] Field: WsSedol24, is_external=, is_static_class=False, static_prefix=
    private string _WsSedol24 ="";
    
    
    
    
    // [DEBUG] Field: WsSedol57, is_external=, is_static_class=False, static_prefix=
    private string _WsSedol57 ="";
    
    
    
    
    
    // Serialization methods
    public string GetWsSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsSedol1.PadRight(1));
        result.Append(_WsSedol24.PadRight(3));
        result.Append(_WsSedol57.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetWsSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsSedol1(extracted);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWsSedol24(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWsSedol57(extracted);
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsSedolAsString();
    }
    // Set<>String Override function
    public void SetWsSedol(string value)
    {
        SetWsSedolAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsSedol1()
    {
        return _WsSedol1;
    }
    
    // Standard Setter
    public void SetWsSedol1(string value)
    {
        _WsSedol1 = value;
    }
    
    // Get<>AsString()
    public string GetWsSedol1AsString()
    {
        return _WsSedol1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsSedol1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsSedol1 = value;
    }
    
    // Standard Getter
    public string GetWsSedol24()
    {
        return _WsSedol24;
    }
    
    // Standard Setter
    public void SetWsSedol24(string value)
    {
        _WsSedol24 = value;
    }
    
    // Get<>AsString()
    public string GetWsSedol24AsString()
    {
        return _WsSedol24.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWsSedol24AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsSedol24 = value;
    }
    
    // Standard Getter
    public string GetWsSedol57()
    {
        return _WsSedol57;
    }
    
    // Standard Setter
    public void SetWsSedol57(string value)
    {
        _WsSedol57 = value;
    }
    
    // Get<>AsString()
    public string GetWsSedol57AsString()
    {
        return _WsSedol57.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWsSedol57AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsSedol57 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}