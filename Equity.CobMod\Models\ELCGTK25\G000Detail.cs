using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing G000Detail Data Structure

public class G000Detail
{
    private static int _size = 181;
    // [DEBUG] Class: G000Detail, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: G001Control, is_external=, is_static_class=False, static_prefix=
    private string _G001Control ="";
    
    
    
    
    // [DEBUG] Field: G002, is_external=, is_static_class=False, static_prefix=
    private string _G002 ="";
    
    
    
    
    // [DEBUG] Field: Filler85, is_external=, is_static_class=False, static_prefix=
    private string _Filler85 ="";
    
    
    
    
    // [DEBUG] Field: G003Description, is_external=, is_static_class=False, static_prefix=
    private string _G003Description ="";
    
    
    
    
    // [DEBUG] Field: G004, is_external=, is_static_class=False, static_prefix=
    private string _G004 ="";
    
    
    
    
    // [DEBUG] Field: G005Cost, is_external=, is_static_class=False, static_prefix=
    private string _G005Cost ="";
    
    
    
    
    // [DEBUG] Field: G006, is_external=, is_static_class=False, static_prefix=
    private string _G006 ="";
    
    
    
    
    // [DEBUG] Field: G007IndexedCost, is_external=, is_static_class=False, static_prefix=
    private G007IndexedCost _G007IndexedCost = new G007IndexedCost();
    
    
    
    
    // [DEBUG] Field: G010, is_external=, is_static_class=False, static_prefix=
    private string _G010 ="";
    
    
    
    
    
    // Serialization methods
    public string GetG000DetailAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_G001Control.PadRight(1));
        result.Append(_G002.PadRight(38));
        result.Append(_Filler85.PadRight(1));
        result.Append(_G003Description.PadRight(16));
        result.Append(_G004.PadRight(18));
        result.Append(_G005Cost.PadRight(19));
        result.Append(_G006.PadRight(13));
        result.Append(_G007IndexedCost.GetG007IndexedCostAsString());
        result.Append(_G010.PadRight(56));
        
        return result.ToString();
    }
    
    public void SetG000DetailAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetG001Control(extracted);
        }
        offset += 1;
        if (offset + 38 <= data.Length)
        {
            string extracted = data.Substring(offset, 38).Trim();
            SetG002(extracted);
        }
        offset += 38;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller85(extracted);
        }
        offset += 1;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetG003Description(extracted);
        }
        offset += 16;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetG004(extracted);
        }
        offset += 18;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetG005Cost(extracted);
        }
        offset += 19;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            SetG006(extracted);
        }
        offset += 13;
        if (offset + 19 <= data.Length)
        {
            _G007IndexedCost.SetG007IndexedCostAsString(data.Substring(offset, 19));
        }
        else
        {
            _G007IndexedCost.SetG007IndexedCostAsString(data.Substring(offset));
        }
        offset += 19;
        if (offset + 56 <= data.Length)
        {
            string extracted = data.Substring(offset, 56).Trim();
            SetG010(extracted);
        }
        offset += 56;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetG000DetailAsString();
    }
    // Set<>String Override function
    public void SetG000Detail(string value)
    {
        SetG000DetailAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetG001Control()
    {
        return _G001Control;
    }
    
    // Standard Setter
    public void SetG001Control(string value)
    {
        _G001Control = value;
    }
    
    // Get<>AsString()
    public string GetG001ControlAsString()
    {
        return _G001Control.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetG001ControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G001Control = value;
    }
    
    // Standard Getter
    public string GetG002()
    {
        return _G002;
    }
    
    // Standard Setter
    public void SetG002(string value)
    {
        _G002 = value;
    }
    
    // Get<>AsString()
    public string GetG002AsString()
    {
        return _G002.PadRight(38);
    }
    
    // Set<>AsString()
    public void SetG002AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G002 = value;
    }
    
    // Standard Getter
    public string GetFiller85()
    {
        return _Filler85;
    }
    
    // Standard Setter
    public void SetFiller85(string value)
    {
        _Filler85 = value;
    }
    
    // Get<>AsString()
    public string GetFiller85AsString()
    {
        return _Filler85.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller85AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler85 = value;
    }
    
    // Standard Getter
    public string GetG003Description()
    {
        return _G003Description;
    }
    
    // Standard Setter
    public void SetG003Description(string value)
    {
        _G003Description = value;
    }
    
    // Get<>AsString()
    public string GetG003DescriptionAsString()
    {
        return _G003Description.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetG003DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G003Description = value;
    }
    
    // Standard Getter
    public string GetG004()
    {
        return _G004;
    }
    
    // Standard Setter
    public void SetG004(string value)
    {
        _G004 = value;
    }
    
    // Get<>AsString()
    public string GetG004AsString()
    {
        return _G004.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetG004AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G004 = value;
    }
    
    // Standard Getter
    public string GetG005Cost()
    {
        return _G005Cost;
    }
    
    // Standard Setter
    public void SetG005Cost(string value)
    {
        _G005Cost = value;
    }
    
    // Get<>AsString()
    public string GetG005CostAsString()
    {
        return _G005Cost.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetG005CostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G005Cost = value;
    }
    
    // Standard Getter
    public string GetG006()
    {
        return _G006;
    }
    
    // Standard Setter
    public void SetG006(string value)
    {
        _G006 = value;
    }
    
    // Get<>AsString()
    public string GetG006AsString()
    {
        return _G006.PadRight(13);
    }
    
    // Set<>AsString()
    public void SetG006AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G006 = value;
    }
    
    // Standard Getter
    public G007IndexedCost GetG007IndexedCost()
    {
        return _G007IndexedCost;
    }
    
    // Standard Setter
    public void SetG007IndexedCost(G007IndexedCost value)
    {
        _G007IndexedCost = value;
    }
    
    // Get<>AsString()
    public string GetG007IndexedCostAsString()
    {
        return _G007IndexedCost != null ? _G007IndexedCost.GetG007IndexedCostAsString() : "";
    }
    
    // Set<>AsString()
    public void SetG007IndexedCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_G007IndexedCost == null)
        {
            _G007IndexedCost = new G007IndexedCost();
        }
        _G007IndexedCost.SetG007IndexedCostAsString(value);
    }
    
    // Standard Getter
    public string GetG010()
    {
        return _G010;
    }
    
    // Standard Setter
    public void SetG010(string value)
    {
        _G010 = value;
    }
    
    // Get<>AsString()
    public string GetG010AsString()
    {
        return _G010.PadRight(56);
    }
    
    // Set<>AsString()
    public void SetG010AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G010 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetG007IndexedCost(string value)
    {
        _G007IndexedCost.SetG007IndexedCostAsString(value);
    }
    // Nested Class: G007IndexedCost
    public class G007IndexedCost
    {
        private static int _size = 19;
        
        // Fields in the class
        
        
        // [DEBUG] Field: G008Limit, is_external=, is_static_class=False, static_prefix=
        private string _G008Limit ="";
        
        
        
        
        // [DEBUG] Field: G009, is_external=, is_static_class=False, static_prefix=
        private string _G009 ="";
        
        
        
        
    public G007IndexedCost() {}
    
    public G007IndexedCost(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetG008Limit(data.Substring(offset, 1).Trim());
        offset += 1;
        SetG009(data.Substring(offset, 18).Trim());
        offset += 18;
        
    }
    
    // Serialization methods
    public string GetG007IndexedCostAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_G008Limit.PadRight(1));
        result.Append(_G009.PadRight(18));
        
        return result.ToString();
    }
    
    public void SetG007IndexedCostAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetG008Limit(extracted);
        }
        offset += 1;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetG009(extracted);
        }
        offset += 18;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetG008Limit()
    {
        return _G008Limit;
    }
    
    // Standard Setter
    public void SetG008Limit(string value)
    {
        _G008Limit = value;
    }
    
    // Get<>AsString()
    public string GetG008LimitAsString()
    {
        return _G008Limit.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetG008LimitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G008Limit = value;
    }
    
    // Standard Getter
    public string GetG009()
    {
        return _G009;
    }
    
    // Standard Setter
    public void SetG009(string value)
    {
        _G009 = value;
    }
    
    // Get<>AsString()
    public string GetG009AsString()
    {
        return _G009.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetG009AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G009 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
