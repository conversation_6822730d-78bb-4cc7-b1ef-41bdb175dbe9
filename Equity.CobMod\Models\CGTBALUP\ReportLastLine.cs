using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportLastLine Data Structure

public class ReportLastLine
{
    private static int _size = 126;
    // [DEBUG] Class: ReportLastLine, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler151, is_external=, is_static_class=False, static_prefix=
    private string _Filler151 ="";
    
    
    
    
    // [DEBUG] Field: Filler152, is_external=, is_static_class=False, static_prefix=
    private string _Filler152 ="";
    
    
    
    
    // [DEBUG] Field: Filler153, is_external=, is_static_class=False, static_prefix=
    private string _Filler153 ="";
    
    
    
    
    // [DEBUG] Field: Filler154, is_external=, is_static_class=False, static_prefix=
    private string _Filler154 ="";
    
    
    
    
    // [DEBUG] Field: Filler155, is_external=, is_static_class=False, static_prefix=
    private string _Filler155 ="*** End of Report ***";
    
    
    
    
    
    // Serialization methods
    public string GetReportLastLineAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler151.PadRight(0));
        result.Append(_Filler152.PadRight(11));
        result.Append(_Filler153.PadRight(12));
        result.Append(_Filler154.PadRight(22));
        result.Append(_Filler155.PadRight(81));
        
        return result.ToString();
    }
    
    public void SetReportLastLineAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller151(extracted);
        }
        offset += 0;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetFiller152(extracted);
        }
        offset += 11;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetFiller153(extracted);
        }
        offset += 12;
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetFiller154(extracted);
        }
        offset += 22;
        if (offset + 81 <= data.Length)
        {
            string extracted = data.Substring(offset, 81).Trim();
            SetFiller155(extracted);
        }
        offset += 81;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportLastLineAsString();
    }
    // Set<>String Override function
    public void SetReportLastLine(string value)
    {
        SetReportLastLineAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller151()
    {
        return _Filler151;
    }
    
    // Standard Setter
    public void SetFiller151(string value)
    {
        _Filler151 = value;
    }
    
    // Get<>AsString()
    public string GetFiller151AsString()
    {
        return _Filler151.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller151AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler151 = value;
    }
    
    // Standard Getter
    public string GetFiller152()
    {
        return _Filler152;
    }
    
    // Standard Setter
    public void SetFiller152(string value)
    {
        _Filler152 = value;
    }
    
    // Get<>AsString()
    public string GetFiller152AsString()
    {
        return _Filler152.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetFiller152AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler152 = value;
    }
    
    // Standard Getter
    public string GetFiller153()
    {
        return _Filler153;
    }
    
    // Standard Setter
    public void SetFiller153(string value)
    {
        _Filler153 = value;
    }
    
    // Get<>AsString()
    public string GetFiller153AsString()
    {
        return _Filler153.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetFiller153AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler153 = value;
    }
    
    // Standard Getter
    public string GetFiller154()
    {
        return _Filler154;
    }
    
    // Standard Setter
    public void SetFiller154(string value)
    {
        _Filler154 = value;
    }
    
    // Get<>AsString()
    public string GetFiller154AsString()
    {
        return _Filler154.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetFiller154AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler154 = value;
    }
    
    // Standard Getter
    public string GetFiller155()
    {
        return _Filler155;
    }
    
    // Standard Setter
    public void SetFiller155(string value)
    {
        _Filler155 = value;
    }
    
    // Get<>AsString()
    public string GetFiller155AsString()
    {
        return _Filler155.PadRight(81);
    }
    
    // Set<>AsString()
    public void SetFiller155AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler155 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}