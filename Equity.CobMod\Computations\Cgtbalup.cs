using System;
using System.ComponentModel;
using System.IO;
using System.Text;
using EquityProject.CgtbalupDTO;
namespace EquityProject.CgtbalupPGM
{
    // Cgtbalup Class Definition

    //Cgtbalup Class Constructor
    public class Cgtbalup
    {
        // Declare Cgtbalup Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
            AControl(fvar, gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// Main controlling method for the program; equivalent to the COBOL paragraph A-CONTROL
        ///
        /// This method is responsible for initiating the main process flow of the program.
        /// </summary>
        /// <remarks>
        /// <para>This method performs the following actions:</para>
        /// <list type="bullet">
        ///     <item>Initializes the program (B-Initial method)</item>
        ///     <item>Enters the main processing loop (C-Main method) until the END-PROCESS condition is met</item>
        ///     <item>Finalizes the program (D-End method)</item>
        /// </list>
        /// <para>It uses the gvar parameter to access the global variables, ivar to manipulate the interface variables, and fvar for file handling.</para>
        /// </remarks>
        /// <param name="fvar">File variables.</param>
        /// <param name="gvar">Global variables.</param>
        /// <param name="ivar">Interface variables.</param>
        public void AControl(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Equivalent to PERFORM B-INITIAL
            BInitial(fvar, gvar, ivar);

            // Equivalent to PERFORM C-MAIN UNTIL END-PROCESS
            while (!gvar.GetWsFlags().IsEndProcess())
            {
                CMain(fvar, gvar, ivar);
            }

            // Equivalent to PERFORM D-END
            DEnd(fvar, gvar, ivar);
        }
        /// <summary>
        /// Performs initial processing including logging program information and opening necessary files.
        /// Contains logic to handle PERFORM calls and convert COBOL constructs to C#.
        /// </summary>
        /// <param name="fvar" type="Fvar">The fvar parameter to access variables.</param>
        /// <param name="gvar" type="Gvar">The gvar parameter to access variables.</param>
        /// <param name="ivar" type="Ivar">The ivar parameter to access variables.</param>
        /// <remarks>
        /// Includes details on how the COBOL paragraph "B-Initial" is converted into methods
        /// with C# handling of PARAGRAPH SUBROUTINES, PERFORM, and GO TO operations.
        /// </remarks>
        public void BInitial(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            var timeStamp = DateTime.Now;
            //must uncomment
            //gvar.SetDate = int.Parse(timeStamp.ToString("yyyyMMdd"));
            //gvar.SetTime = timeStamp.ToString("HHmmss");

            var dateStamp = DateTime.Now;
            gvar.GetDateStamp().SetWsYy(dateStamp.Year - 2000); // Assuming 2-digit year
            gvar.GetDateStamp().SetWsMm(dateStamp.Month);
            gvar.GetDateStamp().SetWsDd(dateStamp.Day);

            gvar.GetWsMessages().GetWsMessage2().SetWsMessDd(dateStamp.Day);
            gvar.GetWsMessages().GetWsMessage2().SetWsMessMm(dateStamp.Month);
            gvar.GetWsMessages().GetWsMessage2().SetWsMessYy(dateStamp.Year - 2000);
            gvar.GetTimeStamp().SetWsHh(timeStamp.Hour);
            gvar.GetTimeStamp().SetWsNn(timeStamp.Minute);

            var whenCompiledStamp = DateTime.Now;

            gvar.GetWsWhenCompiled().SetWsCompDate(whenCompiledStamp.ToString("yyyyMMdd"));
            gvar.GetWsWhenCompiled().SetWsCompTime(whenCompiledStamp.ToString("HHmmss"));

            var messageString = $"CGTBALUP: VERSION {gvar.GetVersionNumber()} {gvar.GetWsWhenCompiled().GetWsCompDate()} {gvar.GetWsWhenCompiled().GetWsCompTime()}";
            var messageOne = messageString + gvar.GetWsMessages().GetWsMessage2();
            gvar.GetWsMessages().SetWsMessage1(messageOne);

            // L-FILE-NAME = USER-FUND-FILE
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.USER_FUND_FILE);
            // L-FILE-ACTION = 'OPEN-INPUT'
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.OPEN_INPUT);
            // PERFORM X-Call-Cgtfiles
            XCallCgtfiles(fvar, gvar, ivar);

            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.USER_FUND_FILE);
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.START_NOT_LESS_THAN);
            //gvar.GetLFileRecordArea = ivar.Fund_File().GetFields().ToFixedPortion(5);            

            // PERFORM X-Call-Cgtfiles

            XCallCgtfiles(fvar, gvar, ivar);

            // L-FILE-NAME = SEQ-BALANCE-FILE
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.SEQ_BALANCE_FILE);
            // L-FILE-ACTION = 'OPEN-INPUT'
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.OPEN_INPUT);
            // PERFORM X-Call-Cgtfiles
            XCallCgtfiles(fvar, gvar, ivar);
            gvar.SetReturnCode(Gvar.SEE_ERROR_LOG);
            if (gvar.GetCgtfilesLinkage().IsOpenOk())//(gvar.GetCgtfilesLinkage().GetLfileSuccess().GetOpenok())
            {
                //must uncomment
                //gvar.SetGvartrue();
                return;
            }
            gvar.SetReturnCode(Gvar.SEE_ERROR_LOG);
            X2Cgtabort(fvar, gvar, ivar);

            gvar.GetElcgmioLinkage1().SetLPriceDate(0);
            gvar.GetElcgmioLinkage1().GetLAct().SetLAction("FN");
            gvar.GetElcgmioLinkage2().SetLRecordAreaAsString("N" + ivar.GetCommonLinkage().GetLUserNo());
            // COMPUTE L-FN-YY = L-MASTER-FILE-YEAR + 1
            gvar.GetElcgmioLinkage2().GetFiller180().SetLFnYy(ivar.GetCommonLinkage().GetLMasterFileYear() + 1);
            // PERFORM X3-Elcgmio
            X3Elcgmio(fvar, gvar, ivar);
            // L-ACTION = 'OPEN-I-O'
            gvar.GetElcgmioLinkage1().GetLAct().SetLAction(Ivar.OPEN_I_O);
            // PERFORM X3-Elcgmio
            X3Elcgmio(fvar, gvar, ivar);
            if (gvar.GetElcgmioLinkage1().IsX24OpenOk()) //(gvar.GetElcgmioLinkage1().GetLReturnCode().IsX24openok())
            {
                if (!gvar.GetElcgmioLinkage1().IsX24Successful())
                {
                    // MOVE 'Y' TO WS-EOF-D13-FLAG
                    gvar.GetWsFlags().SetWsEofD13Flag("Y");
                    // MOVE 'N' TO WS-MASTER-FLAG
                    gvar.GetWsFlags().SetWsMasterFlag("N");
                    // MOVE 'N' TO WS-READ-D13-FLAG
                    gvar.GetWsFlags().SetWsReadD13Flag("N");
                    // MOVE HIGH-VALUES TO D13-1-KEY
                    gvar.GetD13SedolHeaderRecord().SetD131Key("HIGH-VALUES");
                }
                else
                {
                    //must uncomment
                    //gvar.SetGvarTrue();
                    // MOVE 'Y' TO WS-MASTER-FLAG
                    gvar.GetWsFlags().SetWsMasterFlag("Y");
                }
            }
            else
            {
                X2Cgtabort(fvar, gvar, ivar);
            }

            var mess8YY = gvar.GetWsMessages().GetWsMessage8().GetWsMess8Yy();
            var lFNYY = gvar.GetElcgmioLinkage2().GetFiller180().GetLFnYy();

            gvar.GetCgtdate2LinkageDate1().GetFiller51().GetCgtdate2Yymmdd1().SetCgtdate2Yy1((gvar.GetElcgmioLinkage2().GetFiller180().GetLFnYy().ToString()));//SetCgtdate2Yy1(lFNYYYY);
            // gvar.GetCgtdate2LinkageDate1().GetFiller51().SetCgtdate2Yymmdd1(gvar.GetElcgmioLinkage2().GetFiller180().GetLFnYy().ToString());
            gvar.GetCgtdate2LinkageDate1().GetFiller51().GetCgtdate2Yymmdd1().GetCgtdate2Mmdd1().SetCgtdate2Mm1("01");
            gvar.GetCgtdate2LinkageDate1().GetFiller51().GetCgtdate2Yymmdd1().GetCgtdate2Mmdd1().SetCgtdate2Dd1("01 ");

            gvar.SetCgtdate2LinkageDate1(gvar.GetCgtdate2LinkageDate1());
            var cgtDate = gvar.GetCgtdate2LinkageDate1();

            gvar.GetWsMessages().GetWsMessage9().SetWsMess9ToCcyy(cgtDate.GetFiller52().GetCgtdate2CCcyy1().ToString());//.GetCgtdate2Ccyy1());
            gvar.GetWsMessages().GetWsMessage9().SetWsMess9FromCcyy((cgtDate.GetFiller52().GetCgtdate2CCcyy1() - 1).ToString());
            gvar.GetElcgmioLinkage2().GetLRecordAreaAsString();

            gvar.GetElcgmioLinkage1().GetLAct().SetLAction(Ivar.START_NOT_LESS_THAN);
            X3Elcgmio(fvar, gvar, ivar);

            if (gvar.GetElcgmioLinkage1().IsX24Successful())
            {
                gvar.GetWsFlags().SetWsEofD13Flag("N");
                gvar.GetWsFlags().SetWsMasterFlag("Y");
                gvar.GetWsFlags().SetWsReadD13Flag("Y");
                gvar.GetD13SedolHeaderRecord().SetD131Key(" ");
            }
            else
            {
                if (gvar.GetElcgmioLinkage1().IsX24Successful() || gvar.GetElcgmioLinkage1().IsX24InvalidKey())
                {
                    gvar.GetWsFlags().SetWsEofD13Flag("Y");
                    gvar.GetWsFlags().SetWsMasterFlag("N");
                    gvar.GetWsFlags().SetWsReadD13Flag("N");
                    gvar.GetD13SedolHeaderRecord().SetD131Key("HIGH-VALUES");
                }
                else
                {
                    X2Cgtabort(fvar, gvar, ivar);
                }
            }

            gvar.GetCgtlogLinkageArea1().SetLLogProgram(gvar.GetProgramName());
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Ivar.OPEN_OUTPUT);
            gvar.GetCgtlogLinkageArea1().SetLLogFileName(" ");
            X4Cgtlog(fvar, gvar, ivar);

            gvar.SetWsMessageNo(00);
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("I");
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage1());
            X4Cgtlog(fvar, gvar, ivar);
        }
        /// <summary>
        /// This method implements the COBOL paragraph named 'CMAIN'.
        /// It initializes the report, evaluates the update flag, and processes records based on the update flag.
        /// </summary>
        /// <param name="fvar">Function variables</param>
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar">Input variables.</param>
        /// <remarks>
        /// The method performs specific actions based on the value of WS-UPDATE-FLAG.
        /// It calls C1InititiateReport to start the process, then evaluates WS-UPDATE-FLAG
        /// to determine which processing method to call.
        /// If either the PROCESS-USER-FUNDS-ONLY or PROCESS-ALL-FUNDS condition is met,
        /// the appropriate processing method is called in a loop until END-PROCESS or QUIT-PROCESS is true.
        /// If QUIT-PROCESS is true, it calls X5Quit.
        /// </remarks>
        public void CMain(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move L-UPDATE-FLAG to WS-UPDATE-FLAG
            ivar.SetLUpdateFlag(gvar.GetWsFlags().GetWsUpdateFlag());

            // Perform C1-INITIATE-REPORT
            C1InitiateReport(fvar, gvar, ivar);

            // Evaluate WS-UPDATE-FLAG
            switch (gvar.GetWsFlags().GetWsUpdateFlag())
            {
                case Gvar.PROCESS_USER_FUNDS_ONLY:
                    // Perform E-PROCESS-D37-FUND-RECORDS until END-PROCESS or QUIT-PROCESS
                    while (!gvar.GetWsFlags().IsEndProcess() && !gvar.GetWsFlags().IsQuitProcess())
                    {
                        EProcessD37FundRecords(fvar, gvar, ivar);
                    }
                    break;
                case Gvar.PROCESS_ALL_FUNDS:
                    // Perform F-PROCESS-ALL-RECORDS until END-PROCESS or QUIT-PROCESS
                    while (!gvar.GetWsFlags().IsEndProcess() && !gvar.GetWsFlags().IsQuitProcess())
                    {
                        //must uncomment
                        //FProcessAllRecords(fvar, gvar, ivar);
                    }
                    break;
            }

            // If QUIT-PROCESS, perform X5-QUIT
            if (gvar.GetWsFlags().IsQuitProcess())
            {
                X5Quit(fvar, gvar, ivar);
            }
        }
        /// <summary>
        /// The C1InitiateReport method implements the COBOL paragraph
        /// named 'c1InitiateReport'. This method prepares report
        /// generation by initializing report variables, copying
        /// values, calling external processes, and setting up report
        /// headers and data.
        /// </summary>
        /// <remarks>
        /// <para>The COBOL MOVE statements are converted to C# property
        /// setter methods, and the PERFORM statements are converted to
        /// method calls.</para>
        /// <para>The EVALUATE statement in COBOL is converted to a C# switch
        /// statement based on the value of WS-UPDATE-FLAG.</para>
        /// </remarks>
        /// <param name="fvar">Fvar object containing COBOL file variables.</param>
        /// <param name="gvar">Gvar object containing COBOL global variables.</param>
        /// <param name="ivar">Ivar object containing COBOL input variables.</param>
        public void C1InitiateReport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Map L-USER-NO to REPORT-USER-NO
            gvar.GetReportFile().SetReportUserNo(ivar.GetCommonLinkage().GetLUserNo().ToString());

            // Map L-GEN-NO to REPORT-GEN-NO
            gvar.GetReportFile().SetReportGenNo(ivar.GetLGenNo());

            // Map USER-DATA-PATH to EQTPATH-PATH-ENV-VARIABLE
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);

            // Map REPORT-FILE to EQTPATH-FILE-NAME
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetReportFile().ToString());

            // Call XCallEqtpath method
            XCallEqtpath(fvar, gvar, ivar);

            // Map EQTPATH-PATH-FILE-NAME to REPORT-FILE-NAME
            gvar.SetReportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // Open BALUP-REPORT for output
            //must uncomment
            //fvar.SetReportRecord(gvar.GetCgtbalup00());
            //fvar.GetCgtabortLinkage().SetCgtabortLinkageAsString(gvar.balupBALUP_REPORT);

            // Map READ-NEXT to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_NEXT);

            // Map USER-FUND-FILE to L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.USER_FUND_FILE);

            // Call XCallCgtfiles method
            XCallCgtfiles(fvar, gvar, ivar);

            // Map L-FILE-RECORD-AREA to D37-RECORD
            gvar.SetD37Record(gvar.GetD37Record());

            // Map D37-GLOBAL-COMP-NAME to REPORT-REF
            gvar.SetReportHeading1(gvar.GetReportHeading1());//GetD37Record().GetD37GlobalCompName().);

            // Construct REPORT-DATE using WS-DD, WS-MM, and WS-YY
            var reportDate = gvar.GetDateStamp().GetWsDd() + "/" + gvar.GetDateStamp().GetWsMm() + "/" + gvar.GetDateStamp().GetWsYy();

            gvar.GetReportHeading1().SetReportDate(reportDate);

            // Map WS-MESSAGE-9 to R-INPUT-FILE
            //must uncomment
            //gvar.SetReportHeading1(gvar.GetWsMessages().GetWsMessage9());

            // Evaluate WS-UPDATE-FLAG
            switch (gvar.GetWsFlags().GetWsUpdateFlag())
            {
                case Gvar.PROCESS_USER_FUNDS_ONLY:
                    gvar.GetReportOpt01().GetFiller170().SetRFundSelection("User Funds only Updated");
                    break;
                default:
                    gvar.GetReportOpt01().GetFiller170().SetRFundSelection("All Funds Updated      ");
                    break;
            }

            // Call X6PageHeadings method
            //must uncomment
            //X6PageHeadings(fvar, gvar, ivar);

            // Clear REPORT-LINE
            //must check
            fvar.GetReportRecord().SetReportLine(" ");

            // Set R-ADV to 5
            gvar.SetRAdv(5);

            // Call X9WriteReport method
            //must uncomment
            //X9WriteReport(fvar, gvar, ivar);

            // Set R-ADV to 1
            gvar.SetRAdv(1);

            // Loop through REPORT-OPT-LINE using a varying index
            for (int i = 1; i <= gvar.GetReportOptLineOccurs(); i++)
            {
                // Map REPORT-OPT-LINE(i) to REPORT-LINE-2
                fvar.SetReportRecord2AsString(gvar.GetReportOptTable().GetReportOptLine());

                // Call X9WriteReport method
                //must uncomment
                //X9WriteReport(fvar, gvar, ivar);
            }

            // Call X6PageHeadings method
            //must uncomment
            //X6PageHeadings(fvar, gvar, ivar);

            // Call X7ColumnHeadings method
            //must uncomment
            //X7ColumnHeadings(fvar, gvar, ivar);
        }
        /// <summary>
        /// This method corresponds to the COBOL paragraph 'dEnd'.
        /// It performs a series of actions related to logging, file handling, and error checking.
        /// </summary>
        /// <param name="fvar">The fvar parameter to access COBOL variables.</param>
        /// <param name="gvar">The gvar parameter to access COBOL variables.</param>
        /// <param name="ivar">The ivar parameter to access COBOL variables.</param>
        /// <remarks>
        /// The COBOL paragraph 'dEnd' is translated to this C# method.
        /// This method handles logging, file operations, and error checking.
        /// </remarks>
        public void DEnd(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Perform the D1-TERMINATE-REPORT paragraph (placeholder for actual implementation)
            D1TerminateReport(fvar, gvar, ivar);

            // MOVE 'F' TO L-LOG-MESSAGE-TYPE
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("F");

            // MOVE WS-MESSAGE-9 TO L-LOG-MESSAGE
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage9().ToString());

            // PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // MOVE WS-MESSAGE-10 TO L-LOG-MESSAGE
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage10().ToString());

            // PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // MOVE CLOSE-FILE TO L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Ivar.CLOSE_FILE);

            // PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // MOVE USER-FUND-FILE TO L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.USER_FUND_FILE);

            // MOVE CLOSE-FILE TO L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_FILE);

            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(fvar, gvar, ivar);

            // MOVE SEQ-BALANCE-FILE TO L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.SEQ_BALANCE_FILE);

            // MOVE CLOSE-FILE TO L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_FILE);

            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(fvar, gvar, ivar);

            // IF RUN-TIME-ERROR
            if (gvar.GetCgtfilesLinkage().GetLFileReturnCode2().IsRunTimeError())
            {
                // PERFORM X2-CGTABORT
                X2Cgtabort(fvar, gvar, ivar);
            }

            // MOVE CLOSE-FILE TO L-ACTION
            gvar.GetElcgmioLinkage1().GetLAct().SetLAction(Ivar.CLOSE_FILE);

            // PERFORM X3-ELCGMIO
            X3Elcgmio(fvar, gvar, ivar);

            // IF X24-RUN-TIME-ERROR
            if (gvar.GetElcgmioLinkage1().GetLReturnCode2().IsX24RunTimeError())
            {
                // PERFORM X2-CGTABORT
                X2Cgtabort(fvar, gvar, ivar);
            }

            // MOVE REMAP-C-F-TRANSACTIONS TO L-ACTION
            gvar.GetElcgmioLinkage1().GetLAct().SetLAction(Ivar.REMAP_C_F_TRANSACTIONS);

            // PERFORM X3-ELCGMIO
            X3Elcgmio(fvar, gvar, ivar);
        }
        /// <summary>
        /// This method terminates the report processing by printing the last SEDOL summary,
        /// handling page breaks if necessary, and closing the report file.
        /// </summary>
        /// <param name="fvar">Fvar class instance to access the report record.</param>
        /// <param name="gvar">Gvar class instance to access global variables.</param>
        /// <param name="ivar">Ivar class instance for input variables and flags.</param>
        /// <remarks>
        /// This method performs the following tasks:
        /// 1. Print the last SEDOL summary.
        /// 2. Check if the line count exceeds 59, and if so, print page and column headings.
        /// 3. Move values to specific variables.
        /// 4. Write the report.
        /// 5. Close the report file.
        /// </remarks>
        public void D1TerminateReport(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            //  Execute paragraph X10A - PRINT - LAST - SEDOL - SUMMARY as a method call
            X10APrintLastSedolSummary(fvar, gvar, ivar);

            // Check if R-LINE-COUNT exceeds 59 and print page and column headings if necessary
            if (gvar.GetRLineCount() > 59)
            {
                // Execute paragraphs X6-PAGE-HEADINGS and X7-COLUMN-HEADINGS as method calls
                X6PageHeadings(fvar, gvar, ivar);
                X7ColumnHeadings(fvar, gvar, ivar);
            }

            // Set R-ADV to 1 and REPORT-LINE to spaces
            gvar.SetRAdv(1);
            fvar.GetReportRecord().SetReportLine(" ");

            // Execute paragraph X9-WRITE-REPORT as a method call
            X9WriteReport(fvar, gvar, ivar);

            // Set REPORT-LINE to REPORT-LAST-LINE
            fvar.GetReportRecord().SetReportLine(gvar.GetReportLastLine().ToString());

            // Execute paragraph X9-WRITE-REPORT as a method call
            X9WriteReport(fvar, gvar, ivar);

            // Close the report file BALUP-REPORT
            //must uncomment
            //var report = gvar.GetBalupReport();

            //report.Close();
        }
        /// <summary>
        /// The EProcessD37FundRecords method processes D37 fund records through specific actions and logic flow defined by COBOL program
        /// COBOL paragraph name: eProcessD37FundRecords
        /// </summary>
        /// <param name="fvar">Fvar parameters to access COBOL variables defined by Level 01 and 11</param>
        /// <param name="gvar">Gvar parameters to access COBOL variables defined by Level 77</param>
        /// <param name="ivar">Ivar parameters to access COBOL 78-Level Constants</param>
        /// <remarks>
        /// <para>The following COBOL constructs are handled in this method:
        /// <list type="bullet">
        ///     <item><description>COBOL MOVE statements are converted to C# assignments</description></item>
        ///     <item><description>COBOL PERFORM statements are converted to C# method calls</description></item>
        ///     <item><description>COBOL IF statements are converted to C# if statements</description></item>
        ///     <item><description>COBOL 88-level conditions are handled as method calls on the containing variable</description></item>
        ///     <item><description>COBOL SET statements are handled as method calls on the boolean variable</description></item>
        ///     <item><description>COBOL GO TO statements are handled using C# control flow constructs</description></item>
        ///     <item><description>COBOL AND, OR, and NOT logical operations are handled using C# logical operators</description></item>
        ///     <item><description>COBOL qualifier and literals, such as SPACES, LOW-VALUES are converted to equivalent C# literals or method calls.</description></item>
        /// </list>
        /// </para></remarks>
        public void EProcessD37FundRecords(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Set the WS-READ-D45-FLAG to 'Y'
            gvar.GetWsFlags().SetWsReadD45Flag("Y");

            // Move the READ-NEXT literal to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_NEXT);

            // Move the USER-FUND-FILE literal to L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.USER_FUND_FILE);

            // Call the XCallCgtfiles method passing necessary parameters
            XCallCgtfiles(fvar, gvar, ivar);

            // Check if the operation was successful
            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                // Move the L-FILE-RECORD-AREA to D37-RECORD-FORMAT-2
                gvar.GetD37RecordFormat2().SetD37RecordFormat2(gvar.GetLFileRecordArea().ToString());

                // Move the D37-FUND-CODE to D45-1-CO-AC-LK
                gvar.GetD37RecordFormat2().GetD37Key().SetD37FundCode(
                    gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().GetD451CoAcLk().ToString());


                // Check if D13-1-CO-AC-LK is spaces
                string D131CoAcLk = gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131CalSedol().GetD131CoAcLk();
                if (D131CoAcLk == null || D131CoAcLk.Trim().Length == 0)
                {
                    // Move the D37-FUND-CODE to D13-1-CO-AC-LK
                    gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131CalSedol().SetD131CoAcLk(gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode());
                }

                // Move the D37-FUND-TYPE to getCurrencyFundType
                gvar.GetGetCurrencyLinkage().SetGetCurrencyFundType(gvar.GetD37RecordFormat2().GetD37FundType());

                // Call the XCallGetCurrency method passing necessary parameters
                XCallGetCurrency(fvar, gvar, ivar);

                // Check if the currency has changed
                if (gvar.GetGetCurrencyLinkage().IsCurrencyHasChanged())
                {
                    // Move the getCurrencyPrintString to REPORT-LINE
                    fvar.GetReportRecord().SetReportLine(gvar.GetGetCurrencyLinkage().GetGetCurrencyPrintString());

                    // Move 1 to R-ADV
                    gvar.SetRAdv(1);

                    // Call the X9WriteReport method passing necessary parameters
                    X9WriteReport(fvar, gvar, ivar);
                }

                // Move LOW-VALUES to D45-1-SEDOL and D45-1-CONTRACT-NO
                gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().SetD451Sedol("0");
                gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().SetD451ContractNo("0");



                // Move 01 to D45-1-RECORD-CODE
                gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().SetD451RecordCode(1);

                // Call the EAStartD45Records method passing necessary parameters
                EaStartD45Records(fvar, gvar, ivar);

                // Process records until the loop condition is met
                do
                {
                    // Call the GProcessRecords method passing necessary parameters
                    GProcessRecords(fvar, gvar, ivar);
                }
                while (!((gvar.GetD37RecordFormat2().GetD37FundType() != gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().GetD451CoAcLk() && gvar.GetD37RecordFormat2().GetD37FundType() 
                == gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131CalSedol().GetD131CoAcLk())
                      || (gvar.GetWsFlags().IsEofD45() && gvar.GetWsFlags().IsEofD13())
                      || gvar.GetWsFlags().IsQuitProcess()));

                // Move SPACES to WS-EOF-D45-FLAG
                gvar.GetWsFlags().SetWsEofD45Flag(" ");

                // Check if EOF-D45 and EOF-D13 are true
                if (gvar.GetWsFlags().IsEofD45() && gvar.GetWsFlags().IsEofD13())
                {
                    // Set WS-PROCESS-FLAG to 'E'
                    gvar.GetWsFlags().SetWsProcessFlag("E");
                }
            }
            else
            {
                // Check if END-OF-FILE is true
                if (gvar.GetCgtfilesLinkage().IsEndOfFile())
                {
                    // Set WS-PROCESS-FLAG to 'E'
                    gvar.GetWsFlags().SetWsProcessFlag("E");
                }
                else
                {
                    // Call the X2Cgtablort method passing necessary parameters
                    //must uncomment
                    //X2Cgtablort(fvar, gvar, ivar);

                    // Move the SEE-RUN-LOG constant to RETURN-CODE
                    gvar.SetReturnCode(Gvar.SEE_RUN_LOG);

                    // Set QUIT-PROCESS to true
                    // must check method not available
                    //gvar.GetWsFlags().GetWsProcessFlag().SetQuitProcess(true);

                    // This handles the COBOL GO TO F-EXIT. It is not converted to C#. Can't perform as per instructions.
                    // but perform a break to end the method.
                    return;
                }

            }

        }

        /// <summary>
        /// The EA-START-D45-RECORDS paragraph processes the start of D45 records, setting flags,
        /// keys, and performing necessary file operations.
        /// </summary>
        /// <param name="fvar">Functional variables</param>
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar">Internal variables</param>
        /// <remarks>
        /// The method converts the COBOL logic for starting D45 records to C#.
        /// It includes conditional checks, variable assignments, and method calls
        /// to handle the end-of-file scenario and record initialization.
        /// </remarks>
        public void EaStartD45Records(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Check if EOF-D45 is true and set WS-READ-D45-FLAG if necessary
            if (gvar.GetWsFlags().IsReadD45())
            {
                gvar.GetWsFlags().SetWsReadD45Flag("N");

                // Set HIGH-VALUES to D45-1-KEY
                gvar.SetD45RecordAsString(new string(char.MaxValue, 10));//HIGH-VALUES

                // Set EA-EXIT flag
                //gvar.SetEaExit(gvar.GetEaExit());

                return; // This is equivalent to GO TO EA-EXIT in COBOL
            }

            // Move D45-1-KEY to L-FILE-RECORD-AREA
            var d451Key = gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().ToString();
            gvar.SetLFileRecordAreaAsString(d451Key);

            // Move START-NOT-LESS-THAN to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.START_NOT_LESS_THAN);

            // Set SEQ-BALANCE-FILE to L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.SEQ_BALANCE_FILE);

            // Perform X-CALL-CGTFILES method
            XCallCgtfiles(fvar, gvar, ivar);

            // Check if the operation was successful
            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                // Set WS-READ-D45-FLAG to 'Y'
                gvar.GetWsFlags().SetWsReadD45Flag("Y");
            }
            else
            {
                // Check if END-OF-FILE or INVALID-KEY occurred
                if (gvar.GetCgtfilesLinkage().IsEndOfFile() ||
                    gvar.GetCgtfilesLinkage().IsInvalidKey())
                {
                    // Set HIGH-VALUES to D45-1-KEY
                    gvar.SetD45RecordAsString(new string(char.MaxValue, 10)); //HIGH-VALUES

                    // Set WS-READ-D45-FLAG to 'N'
                    gvar.GetWsFlags().SetWsReadD45Flag("N");

                    // Set WS-EOF-D45-FLAG to 'Y'
                    gvar.GetWsFlags().SetWsEofD45Flag("Y");
                }
                else
                {
                    // Perform X2-Cgtabort method
                    X2Cgtabort(fvar, gvar, ivar);

                    // Set RETURN-CODE to SEE-RUN-LOG
                    gvar.SetReturnCode(Gvar.SEE_RUN_LOG);

                    // Set QUIT-PROCESS to TRUE
                    //gvar.GetWsFlags().GetWsProcessFlag().SetQuitProcess(true);

                    // Set EA-EXIT flag
                    //gvar.SetEaExit(gVar.GetEaExit());
                }
            }
        }
        ///
        /// <summary>
        /// This method processes all records and performs the required actions based on
        /// the flags and conditions set. It moves values to flags, checks conditions,
        /// and performs actions in a loop until certain end-of-file conditions are met or
        /// a quit process flag is set. It is derived from the COBOL paragraph named
        /// "fProcessAllRecords" This method includes the logic to handle the processing
        /// loop and setting of flags.
        /// </summary>
        ///
        /// <param name="fvar" type="Fvar">  The function variable which may contain parameters passed by value.</param>
        /// <param name="gvar" type="Gvar">  The global variable which contains items that are passed by reference.</param>
        /// <param name="ivar" type="Ivar">  The iitem variable which may contain parameters passed by reference.</param>
        ///
        /// <remarks>
        /// Code:
        /// MOVE   'Y'   TO   WS-READ-D45-FLAG.
        /// IF   WS-MASTER-FLAG   =   'Y'
        ///   MOVE   'Y'   TO   WS-READ-D13-FLAG.
        /// PERFORM   G-PROCESS-RECORDS   UNTIL
        ///   ( EOF-D45   AND   EOF-D13 )   OR   QUIT-PROCESS.
        /// IF   EOF-D45   AND   EOF-D13
        ///   MOVE   'E'   TO   WS-PROCESS-FLAG.
        ///
        /// Conversion:
        /// The COBOL code is converted to C# by replacing MOVE statements with
        /// setter methods for nested properties and evaluating conditions using if
        /// statements. The loop logic in COBOL is handled by a while loop in C#
        /// that continues until the specified conditions are met.
        /// Variable: 'WS-PROCESS-FLAG', WS-MASTER-FLAG', 'EOF-D45', 'EOF-D13',
        /// 'QUIT-PROCESS', 'WS-READ-D45-FLAG' and 'WS-READ-D13-FLAG'
        /// </remarks>
        public void FProcessAllRecords(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move 'Y' to WS-READ-D45-FLAG
            gvar.GetWsFlags().SetWsReadD45Flag("Y");

            // If WS-MASTER-FLAG is 'Y', move 'Y' to WS-READ-D13-FLAG
            if (gvar.GetWsFlags().GetWsMasterFlag() == "Y")
            {
                gvar.GetWsFlags().SetWsReadD13Flag("Y");
            }

            // Perform G-PROCESS-RECORDS until (EOF-D45 AND EOF-D13) or QUIT-PROCESS
            while (!(
                gvar.GetWsFlags().IsEofD45() &&
                gvar.GetWsFlags().IsEofD13()
            ) && !gvar.GetWsFlags().IsQuitProcess())
            {
                // Call the GProcessRecords method
                GProcessRecords(fvar, gvar, ivar);
            }

            // If EOF-D45 and EOF-D13, move 'E' to WS-PROCESS-FLAG
            if (gvar.GetWsFlags().IsEofD45() &&
                     gvar.GetWsFlags().IsEofD13())
            {
                gvar.GetWsFlags().SetWsProcessFlag("E");
            }
        }
        /// <summary>
        /// GProcessRecords method - equivalent of COBOL paragraph gProcessRecords
        /// </summary>
        /// <param name="fvar">Fvar parameter to access COBOL variables</param>
        /// <param name="gvar">Gvar parameter to access COBOL variables</param>
        /// <param name="ivar">Ivar parameter to access COBOL variables</param>
        /// <remarks>
        /// CONVERSION NOTES:
        /// - COBOL IF statements are converted to C# if statements.
        /// - COBOL MOVE statements are converted to C# variable assignments.
        /// - COBOL PERFORM statements are converted to method calls.
        /// - COBOL EVALUATE statements are converted to C# switch statements.
        /// - COBOL 88-level condition names are converted to C# properties.
        /// - Ability to access the correct C# classes and properties via the provided parameters
        /// </remarks>
        public void GProcessRecords(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // If WS-MASTER-FLAG is 'N'
            if (gvar.GetWsFlags().GetWsMasterFlag().Equals("N"))
            {
                // Move HIGH-VALUES to D13-1-KEY
                gvar.GetD13SedolHeaderRecord().SetD131Key("HIGH-VALUES");

                // If READ-D45 and NOT EOF-D45
                if (gvar.GetWsFlags().IsReadD45() && !gvar.GetWsFlags().IsEofD45())
                {
                    // Perform GA-READ-D45
                    GaReadD45(fvar, gvar, ivar);
                }

                // If READ-D13 and NOT EOF-D13
                if (gvar.GetWsFlags().IsReadD13() && !gvar.GetWsFlags().IsEofD13())
                {
                    // If D13-1-RECORD-CODE-X is '01' and CURRENCY-HAS-CHANGED
                    if (gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131RecordCodeX().Equals("01") && gvar.GetGetCurrencyLinkage().IsCurrencyHasChanged())
                    {
                        // Do nothing (CONTINUE in COBOL)
                    }
                    else
                    {
                        // Perform GB-READ-D13
                        GbReadD13(fvar, gvar, ivar);
                    }
                }

                // Set CURRENCY-HAS-NOT-CHANGED to TRUE
                //must uncomment
                //gvar.GetGetCurrencyLinkage().GetGetCurrencyStatus().SetCurrencyHasNotChanged(Gvar.GetTrue());
            }

            // If EOF-D45 and EOF-D13
            if (gvar.GetWsFlags().IsEofD45() && gvar.GetWsFlags().IsEofD13())
            {
                // Go to G-EXIT                
                return;
            }

            // Evaluate WS-UPDATE-FLAG
            switch (gvar.GetWsFlags().GetWsUpdateFlag())
            {
                case Gvar.PROCESS_USER_FUNDS_ONLY:
                    // If D37-FUND-CODE is not equal to D45-1-CO-AC-LK
                    // AND D13-1-CO-AC-LK is not equal to D37-FUND-CODE
                    string d131CoAcLk = gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131CalSedol().GetD131CoAcLk();
                    string fundCode = gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode();
                    if (!fundCode.Equals(gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().GetD451CoAcLk())
                        && d131CoAcLk != fundCode)
                    {
                        // If D13-1-CO-AC-LK is greater than D37-FUND-CODE
                        if (string.Compare(d131CoAcLk, fundCode) > 0)
                        {
                            // Move 'N' to WS-READ-D13-FLAG
                            gvar.GetWsFlags().SetWsReadD13Flag("N");
                        }
                    }

                    // Go to G-EXIT                   
                    return;

                default:
                    break;
            }

            // Perform GC-CHECK-KEYS
            GcCheckKeys(fvar, gvar, ivar);
        }
        /// <summary>
        /// COBOL paragraph name: gaReadD45
        /// </summary>
        /// <param name="fvar">fvar object containing variable mappings</param>
        /// <param name="gvar">gvar object containing variable mappings</param>
        /// <param name="ivar">ivar object containing variable mappings</param>
        /// <remarks>
        /// This method converts the COBOL paragraph "gaReadD45" to
        /// a C# method that maintains the original logic and functionality.
        /// </remarks>
        public void GaReadD45(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move the value of READ-NEXT to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_NEXT);

            // Move the value of SEQ-BALANCE-FILE to L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.SEQ_BALANCE_FILE);

            // Perform XCallCgtfiles
            XCallCgtfiles(fvar, gvar, ivar);

            // Check if the operation was successful
            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                // Move L-FILE-RECORD-AREA to D45-RECORD
                gvar.SetD45RecordAsString(gvar.GetLFileRecordArea().ToString());
                gvar.SetD45BalAcqDispRecordAsString(gvar.GetLFileRecordArea().ToString());

                // Evaluate WS-UPDATE-FLAG
                string wsUpdateFlag = gvar.GetWsFlags().GetWsUpdateFlag();
                if (wsUpdateFlag == Gvar.PROCESS_ALL_FUNDS)
                {
                    // If D45-2-CO-AC-LK is not equal to WS-MESS-11-FUND
                    string d452CoAcLk = gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452Key().GetD452CalSedol().GetD452CoAcLk();
                    string wsMess11Fund = gvar.GetWsMessages().GetWsMessage11().GetWsMess11Fund();
                    if (d452CoAcLk != wsMess11Fund)
                    {
                        // Move D45-2-CO-AC-LK to WS-MESS-11-FUND
                        gvar.GetWsMessages().GetWsMessage11().SetWsMess11Fund(d452CoAcLk);

                        // Move WS-MESSAGE-11 to L-LOG-MESSAGE
                        gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage11().ToString());

                        // Perform X4Cgtlog
                        X4Cgtlog(fvar, gvar, ivar);
                    }
                }
                else if (wsUpdateFlag == Gvar.PROCESS_USER_FUNDS_ONLY)
                {
                    // If D45-2-CO-AC-LK is equal to D37-FUND-CODE and not equal to WS-MESS-11-FUND
                    string d452CoAcLk = gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452Key().GetD452CalSedol().GetD452CoAcLk();
                    string d37FundCode = gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode();
                    string wsMess11Fund = gvar.GetWsMessages().GetWsMessage11().GetWsMess11Fund();
                    if (d452CoAcLk == d37FundCode && d452CoAcLk != wsMess11Fund)
                    {
                        // Move D45-2-CO-AC-LK to WS-MESS-11-FUND
                        gvar.GetWsMessages().GetWsMessage11().SetWsMess11Fund(d452CoAcLk);

                        // Move WS-MESSAGE-11 to L-LOG-MESSAGE
                        gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage11().ToString());

                        // Perform X4Cgtlog
                        X4Cgtlog(fvar, gvar, ivar);
                    }
                }
            }
            else
            {
                // Check if the file is at the end or has an invalid key
                if (gvar.GetCgtfilesLinkage().IsEndOfFile() || gvar.GetCgtfilesLinkage().IsInvalidKey())
                {
                    // Move HIGH-VALUES to D45-1-KEY
                    gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().SetD451KeyAsString(new string(char.MaxValue, 10)); //HIGH-VALUES

                    // Set WS-EOF-D45-FLAG to 'Y'
                    gvar.GetWsFlags().SetWsEofD45Flag("Y");
                }
                else
                {
                    // Perform X2Cgtabort
                    X2Cgtabort(fvar, gvar, ivar);

                    // Move SEE-RUN-LOG to RETURN-CODE
                    gvar.SetReturnCode(Gvar.SEE_RUN_LOG);

                    // Set QUIT-PROCESS to TRUE
                    //must uncomment
                    //gvar.GetWsFlags().GetWsProcessFlag().SetQuitprocess(true);

                    // GO TO GA-EXIT
                    // Note: In C#, GO TO is not used. Instead, we would structure the code to avoid this.
                    // This line is included for documentation purposes but should be handled in a way
                    // that fits C# best practices.
                    //GaExit(fvar, gvar, ivar);
                }
            }
        }
        /// <summary>
        /// gbReadD13
        /// <para>
        /// COBOL Paragraph: gbReadD13
        /// <para>
        /// Description: This method handles reading D13 records, processes the data based on the success of the read operation,
        /// and manages file end conditions.
        /// </summary>
        ///
        /// <param name="fvar"></param>
        /// <param name="gvar">Generic variable used to access COBOL variables via getter and setter methods.</param>
        /// <param name="ivar">Independent variable to access constant variables</param>
        ///
        /// <remarks>
        ///
        /// Example:
        ///
        /// The method follows a structured flow, first setting an action flag and then calling a specific method
        /// to process. Depending on whether the read operation is successful or encounters an end-of-file or
        /// invalid key condition, it performs specific actions such as moving values or setting flags.
        ///
        /// <para>
        /// COBOL to C# Conversion:
        /// - Performs associated input parsing and record setting similar to COBOL's MOVE.
        /// - The original PERFORM X3-ELCGMIO is a direct method call in C# (X3Elcgmio).
        /// - The IF statements in COBOL are converted directly into equivalent C# if-then-else statements.
        /// </remarks>
        public void GbReadD13(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move the value of READ-NEXT to L-ACTION.
            gvar.GetElcgmioLinkage1().GetLAct().SetLAction(Ivar.READ_NEXT);

            // Perform X3-ELCGMIO.
            X3Elcgmio(fvar, gvar, ivar);

            // Check if the read operation was successful.
            if (gvar.GetElcgmioLinkage1().IsX24Successful())
            {
                // Move the value of L-RECORD-AREA to D13-SEDOL-HEADER-RECORD.
                gvar.SetD13SedolHeaderRecordAsString(gvar.GetElcgmioLinkage2().GetLRecordArea().ToString());

                // Move the value of L-RECORD-AREA to D13-BAL-ACQ-DISP-RECORD.
                gvar.SetD13BalAcqDispRecordAsString(gvar.GetElcgmioLinkage2().GetLRecordArea().ToString());

                gvar.SetWsStoreD13Ids(gvar.GetWIds().ToString());
            }
            else
            {
                if (gvar.GetElcgmioLinkage1().IsX24EndOfFile() || gvar.GetElcgmioLinkage1().IsX24InvalidKey())
                {
                    // Move HIGH-VALUES to D13-1-KEY.
                    gvar.GetD13SedolHeaderRecord().SetD131Key(new string(char.MaxValue, 10));  // HIGH-VALUES

                    // Move 'Y' to WS-EOF-D13-FLAG.
                    gvar.GetWsFlags().SetWsEofD13Flag("Y");
                }
                else
                {
                    // Perform X2-CGTABORT.
                    X2Cgtabort(fvar, gvar, ivar);

                    // Move SEE-RUN-LOG to RETURN-CODE.
                    gvar.SetReturnCode(Gvar.SEE_RUN_LOG);

                    // Set QUIT-PROCESS to TRUE.
                    //must check and uncomment
                    //gvar.GetWsFlags().GetWsprocessflag().SetQuitprocess(true);
                }
            }
        }
        /// <summary>
        /// gcCheckKeys - COBOL paragraph that performs key checks and conditionally calls other paragraphs based on key values.
        /// </summary>
        /// <param name="fvar">The Fvar type parameter containing COBOL variables.</param>
        /// <param name="gvar">The Gvar type parameter containing COBOL variables.</param>
        /// <param name="ivar">The Ivar type parameter containing COBOL variables.</param>
        /// <remarks>
        /// This method translates the COBOL paragraph gcCheckKeys to C#.
        /// It checks the keys and conditionally performs actions based on the key values.
        /// </remarks>
        public void GcCheckKeys(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Check if D45-1-KEY equals D13-1-KEY
            if (gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().ToString() == gvar.GetD13SedolHeaderRecord().GetD131Key().ToString())
            {
                // Check if NOT QUIT-PROCESS
                if (!gvar.GetWsFlags().IsQuitProcess())
                {
                    // Perform GCA-DUPLICATE-KEYS paragraph
                    GcaDuplicateKeys(fvar, gvar, ivar);
                }
            }

            int d451Key = int.Parse(gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().ToString());
            int d131Key = int.Parse(gvar.GetD13SedolHeaderRecord().GetD131Key().ToString());
            // Check if D45-1-KEY is less than D13-1-KEY
            if (d451Key < d131Key)
            {
                // Check if NOT QUIT-PROCESS
                if (!gvar.GetWsFlags().IsQuitProcess())
                {
                    // Perform GCB-D45-LESS-THAN-D13 paragraph
                    GcbD45LessThanD13(fvar, gvar, ivar);
                }
            }

            
            // Check if D45-1-KEY is greater than D13-1-KEY
            if (d451Key > d131Key)
            {
                // Check if NOT QUIT-PROCESS
                if (!gvar.GetWsFlags().IsQuitProcess())
                {
                    // Perform GCC-D45-GREATER-THAN-D13 paragraph
                    GccD45GreaterThanD13(fvar, gvar, ivar);
                }
            }
        }
        /// <summary>
        /// This method duplicates keys as described in the COBOL paragraph 'gcaDuplicateKeys'.
        /// </summary>
        /// <param name="fvar">Field variables</param>
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar">Item variables</param>
        /// <remarks>
        /// The COBOL paragraph 'gcaDuplicateKeys' is converted to the C# method 'GcaDuplicateKeys'.
        /// This method includes the same logic flow and business rules as the original COBOL code.
        /// Modern C# conventions and best practices are used to implement the functionality.
        /// COBOL-specific constructs are handled appropriately, and the original variable mappings are maintained.
        /// </remarks>
        public void GcaDuplicateKeys(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Retrieve the value D45-1-RECORD-CODE
            int evaluateD451RecordCode = gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451RecordCode();
            switch (evaluateD451RecordCode)
            {
                case 1:
                    gvar.SetWsNewHoldingId(""); // MOVE SPACES TO WS-NEW-HOLDING-ID

                    // IF QUIT-PROCESS
                    if (gvar.GetWsFlags().IsQuitProcess())
                    {
                        // GO TO GCA-EXIT
                        //gvar.SetGcaExit(true);
                        return;
                    }
                    // END-IF

                    // IF D13-1-OVER-RIDE-FLAG = 'O'
                    if (gvar.GetD13SedolHeaderRecord().GetD131OverRideFlag() == "O")
                    {
                        // MOVE 'Y' TO WS-OVER-RIDE-FLAG
                        gvar.GetWsFlags().SetWsOverRideFlag("Y");

                        // MOVE '** OVERRIDDEN ' TO WS-MESS-3-TEXT
                        gvar.GetWsMessages().GetWsMessage3().SetWsMess3Text("** OVERRIDDEN ");

                        // MOVE OVERRIDDEN-BALANCE TO W-REPORT-ACTION
                        gvar.SetWReportAction(Gvar.OVERRIDDEN_BALANCE);

                        // PERFORM X10-REPORT
                        X10Report(fvar, gvar, ivar);
                    }
                    else
                    {
                        // MOVE 'N' TO WS-OVER-RIDE-FLAG
                        gvar.GetWsFlags().SetWsOverRideFlag("N");

                        // PERFORM GCAA-REWRITE-D13
                        GcaaRewriteD13(fvar, gvar, ivar);

                        // MOVE '** UPDATED' TO WS-MESS-3-TEXT
                        gvar.GetWsMessages().GetWsMessage3().SetWsMess3Text("** UPDATED");
                    }
                    // END-IF

                    // MOVE D45-1-CO-AC-LK TO WS-MESS-3-FUND WS-MESS-6-FUND
                    string d451COACLK = gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().GetD451CoAcLk();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Fund(d451COACLK);
                    gvar.GetWsMessages().GetWsMessage6().SetWsMess6Fund(d451COACLK);

                    // MOVE D45-1-SEDOL TO WS-SEDOL
                    gvar.SetWsSedolAsString(gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().GetD451Sedol());

                    // MOVE WS-SEDOL-1 TO WS-MESS-3-SEDOL-1
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol1(gvar.GetWsSedol().GetWsSedol1());

                    // MOVE WS-SEDOL-2-4 TO WS-MESS-3-SEDOL-2-4
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol24(gvar.GetWsSedol().GetWsSedol24());

                    // MOVE WS-SEDOL-5-7 TO WS-MESS-3-SEDOL-5-7
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol57(gvar.GetWsSedol().GetWsSedol57());

                    // MOVE D45-1-ISSUERS-NAME TO WS-MESS-3-NAME
                   gvar.GetWsMessages().GetWsMessage3().SetWsMess3Name(gvar.GetD45Record().GetYeD45HeaderFront().GetD451IssuersName());

                    // MOVE D45-1-STOCK-DESCRIPTION TO WS-MESS-3-DESC
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Desc(gvar.GetD45Record().GetYeD45HeaderFront().GetD451StockDescription());

                    break;

                case 2:
                case 3:
                    // IF OVER-RIDE
                    if (gvar.GetWsFlags().IsOverRide())
                    {
                        //gvar.SetGcaExit(true); // GO TO GCA-EXIT
                        return;
                    }
                    // END-IF

                    switch (gvar.GetD45Record().GetYeD45HeaderFront().GetD451TransactionCategory())
                    {
                        case "00":
                        case "PP":
                            // PERFORM GCAA-REWRITE-D13
                            GcaaRewriteD13(fvar, gvar, ivar);
                            break;

                        case "TB":
                        case "01":
                            // GO TO GCA-EXIT
                            //gvar.SetGcaExit(true);
                            return;

                        default:
                            // PERFORM GCAA-REWRITE-D13
                            GcaaRewriteD13(fvar, gvar, ivar);
                            break;
                    }
                    break;

                default:
                    // This part can handle OTHER cases if needed
                    break;
            }
        }
        /// <summary>
        /// gcaExit COBOL paragraph implementation.
        /// </summary>
        ///
        /// <param name="fvar">fvar parameter containing COBOL working storage and fields</param>
        /// <param name="gvar">gvar parameter containing COBOL global variables</param>
        /// <param name="ivar">ivar parameter containing COBOL input variables</param>
        ///
        /// <remarks>
        /// This method converts the COBOL paragraph "gcaExit" to C#.
        /// The original COBOL code:
        ///
        /// MOVE   'Y'   TO   WS-READ-D45-FLAG.
        /// MOVE   'Y'   TO   WS-READ-D13-FLAG.
        /// EXIT.
        ///
        /// </remarks>
        public void GCAExit(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Equivalent to MOVE 'Y' TO WS-READ-D45-FLAG
            gvar.GetWsFlags().SetWsReadD45Flag("Y");

            // Equivalent to MOVE 'Y' TO WS-READ-D13-FLAG
            gvar.GetWsFlags().SetWsReadD13Flag("Y");
        }
        /// <summary>
        /// The GcaaRewriteD13 method handles the COBOL gcaaRewriteD13 paragraph.
        /// It processes D13 records based on their type and writes or rewrites
        /// them to the output record area. Additionally, it handles errors and sets
        /// appropriate flags.
        /// </summary>
        /// <param name="fvar">Fvar class instance containing COBOL working storage variables</param>
        /// <param name="gvar">Gvar class instance containing COBOL global variables</param>
        /// <param name="ivar">Ivar class instance containing COBOL defined constants</param>
        ///
        /// <remarks>
        /// For COBOL variables, the method uses the provided C# classes and their methods.
        /// The conversion includes handling EVALUATE statements, MOVE operations,
        /// PERFORM statements, and conditional checks.
        /// </remarks>
        public void GcaaRewriteD13(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move WS-STORE-D13-IDS to W-IDS
            var wsStoreD13Ids = gvar.GetWsStoreD13Ids();
            gvar.SetWIdsAsString(wsStoreD13Ids);

            // First EVALUATE statement based on D45-1-RECORD-CODE
            switch (gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451RecordCode())
            {
                case 1:
                    // MOVE D45-RECORD TO L-RECORD-AREA
                    gvar.GetElcgmioLinkage2().SetLRecordArea(gvar.GetD45Record().ToString());

                    // MOVE REWRITE-HEADER TO W-REPORT-ACTION
                    gvar.SetWReportAction(Gvar.REWRITE_HEADER);
                    break;

                default:
                    // Move D45-BAL-ACQ-DISP-RECORD to WTT-RECORD
                    var d45BalAcqDispRecord = gvar.GetD45BalAcqDispRecord().ToString();
                    gvar.GetWttOutputRecord().SetWttRecord(d45BalAcqDispRecord);

                    // Move D45-2-REMAINDER to WTT-REMAINDER
                    var d452Remainder = gvar.GetD45BalAcqDispRecord().ToString();
                    gvar.GetWttOutputRecord().GetWttRecord().SetWttRemainder(d452Remainder);

                    // Move WTT-OUTPUT-RECORD to L-RECORD-AREA
                    gvar.GetElcgmioLinkage2().SetLRecordArea(gvar.GetWttOutputRecord().ToString());

                    // Second EVALUATE statement based on D45-1-RECORD-CODE
                    switch (gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451RecordCode())
                    {
                        case 2:
                            // Move REWRITE-BALANCE-ACQUISITION to W-REPORT-ACTION
                            gvar.SetWReportAction(Gvar.REWRITE_BALANCE_ACQUISITION);
                            break;

                        case 3:
                            // Move REWRITE-DISPOSAL to W-REPORT-ACTION
                            gvar.SetWReportAction(Gvar.REWRITE_DISPOSAL);
                            break;
                    }
                    break;
            }

            // Move REWRITE-RECORD to L-ACTION
            var elcgmioLinkage1 = gvar.GetElcgmioLinkage1();
            elcgmioLinkage1.GetLAct().SetLAction(Ivar.REWRITE_RECORD);

            // Implement PERFORM statements
            X10Report(fvar, gvar, ivar); // Call the equivalent C# method for X10-REPORT
            X3Elcgmio(fvar, gvar, ivar); // Call the equivalent C# method for X3-ELCGMIO

            // Check for X24-DUPLICATE-KEY or X24-RUN-TIME-ERROR
            if (gvar.GetElcgmioLinkage1().IsX24DuplicateKey()
                 || gvar.GetElcgmioLinkage1().GetLReturnCode2().IsX24RunTimeError())
            {
                X2Cgtabort(fvar, gvar, ivar); // Call the equivalent C# method for X2-CGTABORT

                // Move SEE-RUN-LOG to RETURN-CODE
                gvar.SetReturnCode(Gvar.SEE_RUN_LOG);

                // Set QUIT-PROCESS to TRUE
                //must uncomment check
                //gvar.GetWsflags().GetWsprocessflag().SetQuitprocess(true);
            }

            // Follow to GCAA-EXIT if an error was found.
            //must uncomment check
            //if (gvar.GetGcaaExit() == true)
            //{
            //    goto GCAA_Exit;
            //}

            // If D45-1-RECORD-CODE is 01 and X24-SUCCESSFUL
            if (gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451RecordCode() == 1
                 && gvar.GetElcgmioLinkage1().IsX24Successful())
            {
                // Move MHDA-HoldingID of Header-IDS to WS-NEW-HOLDING-ID
                gvar.SetWsNewHoldingId(gvar.GetHeaderIds().GetMhdaHoldingId());
            }

            //GCAA_Exit:
            return; // Exit the method
        }
        /// <summary>
        /// COBOL paragraph GCB-D45-LESS-THAN-D13 implements the following method as described
        ///
        /// EVALUATE   D45-1-RECORD-CODE
        /// WHEN   01
        ///  MOVE   SPACES                     TO   WS-NEW-HOLDING-ID
        ///   IF   QUIT-PROCESS
        ///       GO   TO   GCB-EXIT
        ///   END-IF
        ///   MOVE   'N'   TO   WS-OVER-RIDE-FLAG
        ///   PERFORM   GCBA-WRITE-D13
        ///   MOVE   D45-1-CO-AC-LK   TO   WS-MESS-3-FUND   WS-MESS-6-FUND
        ///   MOVE   D45-1-SEDOL   TO   WS-SEDOL
        ///   MOVE   WS-SEDOL-1   TO   WS-MESS-3-SEDOL-1
        ///   MOVE   WS-SEDOL-2-4   TO   WS-MESS-3-SEDOL-2-4
        ///   MOVE   WS-SEDOL-5-7   TO   WS-MESS-3-SEDOL-5-7
        ///   MOVE   D45-1-ISSUERS-NAME   TO   WS-MESS-3-NAME
        ///   MOVE   D45-1-STOCK-DESCRIPTION   TO   WS-MESS-3-DESC
        ///   MOVE   '** UPDATED'   TO   WS-MESS-3-TEXT
        /// WHEN   03
        ///   IF   OVER-RIDE
        ///       GO   TO   GCB-EXIT
        ///   END-IF
        ///   EVALUATE   D45-1-TRANSACTION-CATEGORY
        ///       WHEN   'TB'
        ///            WHEN    '01'
        ///                GO   TO   GCB-EXIT
        ///       WHEN   OTHER
        ///           PERFORM   GCBA-WRITE-D13
        ///   END-EVALUATE
        /// END-EVALUATE.
        /// </summary>
        ///
        /// <remarks>
        /// Handles and evaluates different conditions for different record codes and
        /// performed specific All associated methods when record fields are read from an input.
        /// </remarks>
        ///
        public void GcbD45LessThanD13(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            int recordCode = gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451RecordCode();
            switch (recordCode)
            {
                case 1:
                    // MOVE SPACES TO WS-NEW-HOLDING-ID.
                    gvar.SetWsNewHoldingId(" ");

                    // IF QUIT-PROCESS GO TO GCB-EXIT
                    if (gvar.GetWsFlags().IsQuitProcess())
                    {
                        return;
                    }

                    // MOVE 'N' TO WS-OVER-RIDE-FLAG.
                    gvar.GetWsFlags().SetWsOverRideFlag("N");

                    // PERFORM GCBA-WRITE-D13
                    GcbaWriteD13(fvar, gvar, ivar);

                    // MOVE D45-1-CO-AC-LK TO WS-MESS-3-FUND WS-MESS-6-FUND
                    string coAcLk = gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().GetD451CoAcLk();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Fund(coAcLk);
                    gvar.GetWsMessages().GetWsMessage6().SetWsMess6Fund(coAcLk);

                    // MOVE D45-1-SEDOL TO WS-SEDOL
                    string sedol = gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().GetD451Sedol();
                    gvar.SetWsSedolAsString(sedol);

                    // MOVE WS-SEDOL-1 TO WS-MESS-3-SEDOL-1
                    string sedol1 = gvar.GetWsSedol().GetWsSedol1();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol1(sedol1);

                    // MOVE WS-SEDOL-2-4 TO WS-MESS-3-SEDOL-2-4
                    string sedol24 = gvar.GetWsSedol().GetWsSedol24();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol24(sedol24);

                    // MOVE WS-SEDOL-5-7 TO WS-MESS-3-SEDOL-5-7
                    string sedol57 = gvar.GetWsSedol().GetWsSedol57();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol57(sedol57);

                    // MOVE D45-1-ISSUERS-NAME TO WS-MESS-3-NAME
                    string issuersName = gvar.GetD45Record().GetYeD45HeaderFront().GetD451IssuersName();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Name(issuersName);

                    // MOVE D45-1-STOCK-DESCRIPTION TO WS-MESS-3-DESC
                    string stockDescription = gvar.GetD45Record().GetYeD45HeaderFront().GetD451StockDescription();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Desc(stockDescription);

                    // MOVE '** UPDATED' TO WS-MESS-3-TEXT
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Text("** UPDATED");

                    break;

                case 3:
                    // Override condition
                    if (gvar.GetWsFlags().IsOverRide())
                    {
                        return;
                    }

                    // EVALUATE D45-1-TRANSACTION-CATEGORY
                    switch (gvar.GetD45Record().GetYeD45HeaderFront().GetD451TransactionCategory())
                    {
                        case "TB":
                        case "01":
                            return;

                        default:
                            // PERFORM GCBA-WRITE-D13
                            GcbaWriteD13(fvar, gvar, ivar);
                            break;
                    }
                    break;
            }
        }
        /// <summary>
        /// The GcbExit paragraph moves values to WS-READ-D45-FLAG and WS-READ-D13-FLAG then exits.
        /// </summary>
        /// <param name="fvar">An instance of the Fvar class representing the fvar data group.</param>
        /// <param name="gvar">An instance of the Gvar class representing the gvar data group.</param>
        /// <param name="ivar">An instance of the Ivar class representing the ivar data group.</param>
        /// <remarks>
        /// COBOL paragraph name: gcbExit
        /// </remarks>
        //public void Exit(Fvar fvar, Gvar gvar, Ivar ivar)
        public void GcbExit(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move 'Y' to WS-READ-D45-FLAG
            var wsFlags = gvar.GetWsFlags();
            wsFlags.SetWsReadD45Flag("Y");
            gvar.SetWsFlags(wsFlags);

            // Move 'N' to WS-READ-D13-FLAG
            wsFlags = gvar.GetWsFlags();
            wsFlags.SetWsReadD13Flag("N");
            gvar.SetWsFlags(wsFlags);

            // Exit. In C#, we do not need an equivalent as the method will return naturally
        }
        /// <summary>
        /// Logic implemented by GcbaWriteD13.
        ///
        /// Originally implemented by GcbaWriteD13 within COBOL.
        /// </summary>
        /// <param name="fvar">Parameter to access COBOL variables from F file.</param>
        /// <param name="gvar">Parameter to access COBOL variables from G file.</param>
        /// <param name="ivar">Parameter to access COBOL variables from I file.</param>
        /// <remarks>
        /// This method translates COBOL GcbaWriteD13 paragraph logic to C#.
        /// The original logic evaluates D45-1-RECORD-CODE values,
        /// performs conditional moves and invokes external paragraphs with PERFORM statements.
        ///
        /// For more detailed information, please refer to the equivalent COBOL paragraph.
        /// </remarks>

        public void GcbaWriteD13(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            int recordCode = gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451RecordCode();

            if (recordCode == 1)
            {
                // MOVE D45-RECORD TO L-RECORD-AREA
                gvar.GetElcgmioLinkage2().SetLRecordArea(gvar.GetD45Record().ToString());

                // MOVE SPACES TO D45-1-HoldingID
                gvar.GetD45Record().GetYeD45HeaderIds().GetD451HeaderIds().SetD451HoldingId("");

                // MOVE D45-1-Header-IDS TO W-IDS
                gvar.SetWIdsAsString(gvar.GetD45Record().GetYeD45HeaderIds().ToString());

                // MOVE WRITE-HEADER TO W-REPORT-ACTION
                gvar.SetWReportAction(Gvar.WRITE_HEADER);
            }
            else
            {
                // MOVE D45-BAL-ACQ-DISP-RECORD TO WTT-RECORD
                gvar.GetWttOutputRecord().SetWttRecord(gvar.GetD45BalAcqDispRecord().GetD45BalAcqDispRecordAsString());

                // MOVE D45-2-REMAINDER TO WTT-REMAINDER
                gvar.GetWttOutputRecord().GetWttRecord().SetWttRemainder(gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Remainder().ToString());

                // MOVE WTT-OUTPUT-RECORD TO L-RECORD-AREA
                gvar.GetElcgmioLinkage2().SetLRecordArea(gvar.GetWttOutputRecord().ToString());

                switch (recordCode)
                {
                    case 2:
                        // MOVE REWRITE-BALANCE-ACQUISITION TO W-REPORT-ACTION
                        gvar.SetWReportAction(Gvar.REWRITE_BALANCE_ACQUISITION);

                        // MOVE D45-2-Balance-IDS TO W-IDS
                        gvar.SetWIdsAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452BalanceIds().ToString());

                        // MOVE WRITE-BALANCE-ACQUISITION TO W-REPORT-ACTION
                        gvar.SetWReportAction(Gvar.WRITE_BALANCE_ACQUISITION);

                        // MOVE SPACES TO D45-2-BalanceID
                        gvar.SetWttOutputRecordAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452BalanceIds().GetD452BalanceId().ToString());

                        // MOVE SPACES TO D45-2-HoldingID
                        gvar.SetWttOutputRecordAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452DisposalIds().GetD452HoldingId().ToString());

                        // OF D45-2-Balance-IDS
                        // MOVE D45-2-Balance-IDS TO W-IDS
                        gvar.SetWIdsAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452BalanceIds().ToString());

                        // MOVE WRITE-BALANCE-ACQUISITION TO W-REPORT-ACTION
                        gvar.SetWReportAction(Gvar.WRITE_BALANCE_ACQUISITION);

                        if (gvar.GetWsNewHoldingId().Trim() != "")
                        {
                            gvar.GetBalanceIds().SetMbdaHoldingId(gvar.GetWsNewHoldingId());
                        }
                        break;

                    case 3:
                        // MOVE REWRITE-DISPOSAL TO W-REPORT-ACTION
                        gvar.SetWReportAction(Gvar.REWRITE_DISPOSAL);

                        // MOVE D45-2-Disposal-IDS TO W-IDS
                        gvar.SetWIdsAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452DisposalIds().ToString());

                        // MOVE WRITE-DISPOSAL TO W-REPORT-ACTION
                        gvar.SetWReportAction(Gvar.WRITE_DISPOSAL);

                        // MOVE SPACES TO D45-2-DisposalID
                        gvar.SetWttOutputRecordAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452DisposalIds().GetD452DisposalId().ToString());

                        // MOVE SPACES TO D45-2-HoldingID
                        gvar.SetWttOutputRecordAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452DisposalIds().GetD452HoldingId().ToString());

                        // Of D45-2-Disposal-IDS
                        // MOVE D45-2-Disposal-IDS TO W-IDS
                        gvar.SetWIdsAsString(gvar.GetD45BalAcqDispRecord().GetYeD45DetailIds().GetD452Ids().GetD452DisposalIds().ToString());

                        // MOVE WRITE-DISPOSAL TO W-REPORT-ACTION
                        gvar.SetWReportAction(Gvar.WRITE_DISPOSAL);

                        if (gvar.GetWsNewHoldingId().Trim() != "")
                        {
                            gvar.GetDisposalIds().SetMddaHoldingId(gvar.GetWsNewHoldingId());
                        }
                        break;

                    default:
                        break;
                }
            }

            // MOVE WRITE-RECORD TO L-ACTION
            gvar.GetElcgmioLinkage1().GetLAct().SetLAction(Ivar.WRITE_RECORD);

            // PERFORM X10-REPORT
            X10Report(fvar, gvar, ivar);

            // PERFORM X3-Elcgmio
            X3Elcgmio(fvar, gvar, ivar);

            if (gvar.GetElcgmioLinkage1().IsX24DuplicateKey() ||
                gvar.GetElcgmioLinkage1().GetLReturnCode2().IsX24RunTimeError())
            {
                // PERFORM X2-Cgtabort
                X2Cgtabort(fvar, gvar, ivar);

                // MOVE SEE-RUN-LOG TO RETURN-CODE
                gvar.SetReturnCode(Gvar.SEE_RUN_LOG);

                // SET QUIT-PROCESS TO TRUE
                //must uncomment 
                //gvar.GetWsFlags().GetWsProcessFlag().SetQuitprocess(true);

                // Invalid GO TO GCBA-EXIT in COBOL, skipped in C#
                return;  // Exit the method simulating GO TO
            }

            if (recordCode == 1 && gvar.GetElcgmioLinkage1().IsX24Successful())
            {
                // MOVE MHDA-HoldingID OF Header-IDS TO WS-NEW-HOLDING-ID
                gvar.SetWsNewHoldingId(gvar.GetHeaderIds().GetMhdaHoldingId());
            }
        }
        /// <summary>
        /// GCCD45GREATERTHAND13
        /// </summary>
        /// <param name="fvar"></param>
        /// <param name="gvar"></param>
        /// <param name="ivar"></param>
        /// <remarks>
        /// <para>This method implements the logic from the COBOL paragraph GCCD45GREATERTHAND13.</para>
        /// <para>The COBOL paragraph utilizes conditional logic to determine the appropriate actions based on the value
        /// of <paramref name="gvar.GetD13SedolHeaderRecord().GetD131RecordCode()"/>.  It uses concepts like GO TO, MOVE,
        /// and PERFORM to control the flow and make decisions.</para>
        /// </remarks>
        public void GccD45GreaterThanD13(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131RecordCode())
            {
                case 01:
                    gvar.SetWsNewHoldingId(""); // MOVE SPACES TO WS-NEW-HOLDING-ID
                    if (gvar.GetWsFlags().IsQuitProcess()) return; // IF QUIT-PROCESS GO TO GCC-EXIT

                    if (gvar.GetD13SedolHeaderRecord().GetD131OverRideFlag() == "O")
                    {
                       gvar.GetWsFlags().SetWsOverRideFlag("Y");
                        gvar.GetWsMessages().GetWsMessage3().SetWsMess3Text("** OVERRIDDEN");
                        gvar.SetWReportAction(Gvar.OVERRIDDEN_BALANCE);
                    }
                    else
                    {
                        gvar.GetWsFlags().SetWsOverRideFlag("N");
                        gvar.GetWsMessages().GetWsMessage3().SetWsMess3Text("** NO C/F BAL");
                        gvar.SetWReportAction(Gvar.LEAVE_HEADER);
                    }

                    X10Report(fvar, gvar, ivar); // PERFORM X10-REPORT

                    var numCoAcLk = gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131CalSedol().GetD131CoAcLk();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Fund(numCoAcLk);
                    gvar.GetWsMessages().GetWsMessage6().SetWsMess6Fund(numCoAcLk);

                    var sedol = gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131CalSedol().GetD131Sedol().ToString();
                    gvar.SetWsSedolAsString(sedol);

                    var wsSedol1 = gvar.GetWsSedol().GetWsSedol1();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol1(wsSedol1);

                    var wsSedol24 = gvar.GetWsSedol().GetWsSedol24();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol24(wsSedol24);

                    var wsSedol57 = gvar.GetWsSedol().GetWsSedol57();
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Sedol57(wsSedol57);

                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Name(gvar.GetD13SedolHeaderRecord().GetD131IssuersName());
                    gvar.GetWsMessages().GetWsMessage3().SetWsMess3Desc(gvar.GetD13SedolHeaderRecord().GetD131StockDescription());
                    break;
                case 02:
                    if (gvar.GetWsFlags().IsOverRide()) return; // IF OVER-RIDE GO TO GCC-EXIT
                    switch (gvar.GetD13SedolHeaderRecord().GetD131TransactionCategory())
                    {
                        case "00":
                        case "PP":
                            GccaDeleteD13(fvar, gvar, ivar); // PERFORM GCCA-DELETE-D13
                            break;
                        default:
                            return; // GO TO GCC-EXIT
                    }
                    break;
                default:
                    return; // GO TO GCC-EXIT
            }
        }
        /// <summary>
        /// Implements the logic for the gccExit paragraph.
        /// </summary>
        /// <remarks>
        /// <para>This method corresponds to the gccExit paragraph in the original COBOL code.</para>
        /// <para>This corresponds to the following COBOL code:
        ///      MOVE   'Y'   TO   WS-READ-D13-FLAG.
        ///      MOVE   'N'   TO   WS-READ-D45-FLAG.
        ///      EXIT.</para>
        /// <para>All comments from the original COBOL code have been preserved to reflect history, intent, and logic.</para>
        /// </remarks>
        /// <param name="fvar">The fvar parameter.</param>
        /// <param name="gvar">The gvar parameter.</param>
        /// <param name="ivar">The ivar parameter.</param>
        public void GccExit(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Set WS-READ-D13-FLAG to 'Y' as specified in the original COBOL code:
            gvar.GetWsFlags().SetWsReadD13Flag("Y");
            // Set WS-READ-D45-FLAG to 'N' as specified in the original COBOL code:
            gvar.GetWsFlags().SetWsReadD45Flag("N");
        }
        /// <summary>
        /// This method implements the logic for handling the deletion of a D13 record.
        /// It performs actions similar to the COBOL paragraph <c>GCCA-DELETE-D13</c>.
        /// </summary>
        ///
        /// <param name="fvar">The function variable used to access COBOL variables.</param>
        /// <param name="gvar">The global variable used to access COBOL variables.</param>
        /// <param name="ivar">The internal variable used to access COBOL variables.</param>
        ///
        /// <remarks>
        /// <para>This method follows the C# conventions and follows the logic flow and business rules of the original COBOL code.</para>
        /// <para>It moves data from one variable to another and performs specified paragraphs.</para>
        /// <para>The GO TO statement in COBOL is replaced with a return statement in C#.</para>
        /// </remarks>
        public void GccaDeleteD13(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move D13-BAL-ACQ-DISP-RECORD to L-RECORD-AREA
            gvar.GetElcgmioLinkage2().SetLRecordArea(
                gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispRecordAsString()
            );

            // Move WS-STORE-D13-IDS to W-IDS
            gvar.SetWIdsAsString(gvar.GetWsStoreD13Ids());

            // Move DELETE-RECORD to L-ACTION
            gvar.GetElcgmioLinkage1().GetLAct().SetLAction(Ivar.DELETE_RECORD);

            // Move DELETE-BALANCE to W-REPORT-ACTION
            gvar.SetWReportAction(
              Gvar.DELETE_BALANCE
            );

            // Perform X10-REPORT
            X10Report(fvar, gvar, ivar);

            // Perform X3-ElcgmiO
            X3Elcgmio(fvar, gvar, ivar);

            // Check if X24-DUPLICATE-KEY or X24-RUN-TIME-ERROR
            if (gvar.GetElcgmioLinkage1().IsX24DuplicateKey() ||
                gvar.GetElcgmioLinkage1().GetLReturnCode2().IsX24RunTimeError())
            {
                // Perform X2-Cgtabort
                X2Cgtabort(fvar, gvar, ivar);

                // Move SEE-RUN-LOG to RETURN-CODE
                gvar.SetReturnCode(
                  Gvar.SEE_RUN_LOG
                );

                // Set QUIT-PROCESS to TRUE
                //must uncomment
                //gvar.GetWsFlags().GetWsProcessFlag().SetQuitprocess(true);

                // Return to GCCA-EXIT
                return;
            }
            //must uncomment
            //gvar.AddGCcaExit(newValue = fvar);
        }

        /// <summary>
        /// This method corresponds to the COBOL paragraph "x2Cgtabort".
        /// It prepares the necessary variables and calls the CGTABORT external program.
        /// </summary>
        /// <param name="fvar">Fvar parameter to access COBOL variables</param>
        /// <param name="gvar">Gvar parameter to access COBOL variables</param>
        /// <param name="ivar">Ivar parameter to access COBOL variables</param>
        /// <remarks>
        /// This method performs a series of moves and then calls the CGTABORT external program.
        /// The parameters COMMON-LINKAGE and CGTABORT-LINKAGE are passed to the external program.
        /// </remarks>
        public void X2Cgtabort(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            // Get the current program name
            var programName = gvar.GetProgramName();

            // Set the program name in the CGTABORT linkage structure
            gvar.GetCgtabortLinkage().SetLAbortProgramName(programName);

            // Get the current file return code
            var fileReturnCode = gvar.GetCgtfilesLinkage().GetLFileReturnCode();

            // Set the file return code in the CGTABORT linkage structure
            gvar.GetCgtabortLinkage().SetLAbortFileStatus(fileReturnCode);

            // Get the current file name
            var fileName = gvar.GetCgtfilesLinkage().GetLFileName();

            // Set the file name in the CGTABORT linkage structure
            gvar.GetCgtabortLinkage().SetLAbortFileName(fileName);

            // Create a new instance of the CGTABORT external program
            //must uncomment
            //Cgtabort cgtabort = new Cgtabort();

            //// Call the Run method with the specified parameters
            //cgtabort.Run(
            //    ivar.GetCommonLinkage(),
            //    gvar.GetCgtabortLinkage()
            //);
        }
        /// <summary>
        /// COBOL paragraph: X3ELCGMIO
        /// Moves YEAR-END-COMP to L-KEY, sets DONT-FORCE-WRITE to TRUE,
        /// and calls the ELCGMIO program with specified parameters.
        /// </summary>
        /// <param name="fvar">Instance of Fvar class with inputs and outputs</param>
        /// <param name="gvar">Instance of Gvar class with inputs and outputs</param>
        /// <param name="ivar">Instance of Ivar class with inputs and outputs</param>
        /// <remarks>
        /// This method converts the COBOL paragraph X3ELCGMIO to C#.
        /// The logic flow and business rules from the original COBOL code are maintained.
        /// </remarks>

        public void X3Elcgmio(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            // Move YEAR-END-COMP to L-KEY
            gvar.GetElcgmioLinkage1().GetLAct().SetLKey(Gvar.YEAR_END_COMP.ToString());

            // Set DONT-FORCE-WRITE to TRUE
            //must uncomment
            //gvar.GetLForceWrite().SetDontforcewrite(gvar.GetTrue());

            // Prepare parameters for the ELCGMIO program
            var elcgmioLinkage1 = gvar.GetElcgmioLinkage1();
            var elcgmioLinkage2 = gvar.GetElcgmioLinkage2();
            var wIds = gvar.GetWIds();
            var lkFundSedol = gvar.GetLkFundSedol();
            var lForceWrite = gvar.GetLForceWrite();

            // Call the ELCGMIO program
            //must uncomment
            //Elcgmio elcgmio = new Elcgmio();
            //elcgmio.Run(elcgmioLinkage1, elcgmioLinkage2, wIds, lkFundSedol, lForceWrite);

            // Check if X24-SUCCESSFUL
            if (!gvar.GetElcgmioLinkage1().IsX24Successful())
            {
                // Move 'ELCGMIO' to L-FILE-NAME
                gvar.GetCgtfilesLinkage().SetLFileName("ELCGMIO");

                // Move L-RETURN-CODE to L-FILE-RETURN-CODE
                gvar.GetCgtfilesLinkage().SetLFileReturnCode(gvar.GetElcgmioLinkage1().GetLReturnCode());
            }
        }
        /// <summary>
        /// COBOL paragraph: x4Cgtlog
        /// </summary>
        /// <param name="fvar">fvar parameter</param>
        /// <param name="gvar">gvar parameter</param>
        /// <param name="ivar">ivar parameter</param>
        /// <remarks>
        /// This method increments WS-MESSAGE-NO, moves it to L-LOG-MESSAGE-NO,
        /// calls the 'CGTLOG' program and updates WS-PROCESS-FLAG based on the L-LOG-MESSAGE-TYPE value.
        /// </remarks>
        public void X4Cgtlog(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            // Increment WS-MESSAGE-NO
            string currentWsMessageNo = gvar.GetWsMessageNo().ToString();
            int newWsMessageNo = int.Parse(currentWsMessageNo) + 1;
            gvar.SetWsMessageNo(newWsMessageNo);

            // Move WS-MESSAGE-NO to L-LOG-MESSAGE-NO
            gvar.GetCgtlogLinkageArea2().SetLLogMessageNo(newWsMessageNo.ToString());

            // Call 'CGTLOG' program
            //must uncomment
            //Cgtlog cgtlog = new Cgtlog();
            //cgtlog.Run(
            //    gvar.GetCgtlogLinkageArea1(),
            //    gvar.GetCgtlogLinkageArea2()
            //);

            // Check L-LOG-MESSAGE-TYPE and update WS-PROCESS-FLAG if necessary
            if (gvar.GetCgtlogLinkageArea2().GetLLogMessageType() == "Q")
            {
                // Move 'Q' to WS-PROCESS-FLAG
                gvar.GetWsFlags().SetWsProcessFlag("Q");

                // Move 'P' to L-LOG-MESSAGE-TYPE
                gvar.GetCgtlogLinkageArea2().SetLLogMessageType("P");
            }
        }
        /// <summary>
        /// This method corresponds to the COBOL paragraph named "x5Quit".
        /// </summary>
        /// <param name="fvar">The fvar parameter allowing access to COBOL variables with fvar prefix.</param>
        /// <param name="gvar">The gvar parameter allowing access to COBOL variables with gvar prefix.</param>
        /// <param name="ivar">The ivar parameter providing access to input variables.</param>
        /// <remarks>
        /// This method initializes and logs messages to the L-LOG-MESSAGE variable,
        /// and sets the WS-PROCESS-FLAG to 'E'.
        ///
        /// The method performs the following operations:
        /// 1. Initializes L-LOG-MESSAGE with spaces.
        /// 2. Calls X4Cgtlog to log the message twice.
        /// 3. Sets L-LOG-MESSAGE to WS-MESSAGE-6 and calls X4Cgtlog.
        /// 4. Initializes L-LOG-MESSAGE with spaces again and calls X4Cgtlog.
        /// 5. Sets L-LOG-MESSAGE to WS-MESSAGE-7 and calls X4Cgtlog.
        /// 6. Sets WS-PROCESS-FLAG to 'E'.
        /// </remarks>
        public void X5Quit(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            // Equivalent COBOL: MOVE SPACES TO L-LOG-MESSAGE
            var spaces = " ";
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(spaces);

            // Equivalent COBOL: PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // Equivalent COBOL: PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // Equivalent COBOL: MOVE WS-MESSAGE-6 TO L-LOG-MESSAGE
            var wsMessage6 = gvar.GetWsMessages().GetWsMessage6().ToString();
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(wsMessage6);

            // Equivalent COBOL: PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // Equivalent COBOL: MOVE SPACES TO L-LOG-MESSAGE
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(spaces);

            // Equivalent COBOL: PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // Equivalent COBOL: MOVE WS-MESSAGE-7 TO L-LOG-MESSAGE
            var wsMessage7 = gvar.GetWsMessages().GetWsMessage7().ToString();
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(wsMessage7);

            // Equivalent COBOL: PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // Equivalent COBOL: MOVE 'E' TO WS-PROCESS-FLAG
            gvar.GetWsFlags().SetWsProcessFlag("E");
        }
        /// <summary>
        /// Implements the logic of the COBOL paragraph X6PAGEHEADINGS.
        /// This method handles the incrementing of the report page count, moving heading data
        /// to the report line, performing actions related to new pages and writing report lines,
        /// and setting various control variables.
        /// </summary>
        /// <param name="fvar">The function variable containing report record data.</param>
        /// <param name="gvar">The global variable containing various headers, counts,
        /// and control flags.</param>
        /// <param name="ivar">The input variable (not used in this specific method).</param>
        /// <remarks>
        /// This method follows the logic flow and business rules from the original COBOL code.
        /// It uses modern C# conventions and best practices, including the use of getter and setter
        /// methods to access properties.
        /// </remarks>
        /// 

        public void X6PageHeadings(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Increment R-PAGE-COUNT by 1
            gvar.SetRPageCount(gvar.GetRPageCount() + 1);

            // Move the value of R-PAGE-COUNT to REPORT-PAGE-NO
            gvar.GetReportHeading1().SetReportPageNo(gvar.GetRPageCount());

            // Move REPORT-HEADING-1 to REPORT-LINE
            fvar.SetReportRecordAsString(gvar.GetReportHeading1().GetReportHeading1AsString().ToString());

            // Perform X8-REPORT-NEW-PAGE (call method X8ReportNewPage)
            X8ReportNewPage(fvar, gvar, ivar);

            // Move REPORT-HEADING-2 to REPORT-LINE
            fvar.SetReportRecordAsString(gvar.GetReportHeading2().ToString());

            // Set R-ADV to 2
            gvar.SetRAdv(2);

            // Perform X9-WRITE-REPORT (call method X9WriteReport)
            X9WriteReport(fvar, gvar, ivar);

            // Move REPORT-HEADING-3 to REPORT-LINE
            fvar.SetReportRecordAsString(gvar.GetReportHeading3().ToString());

            // Set R-ADV to 0
            gvar.SetRAdv(0);

            // Perform X9-WRITE-REPORT (call method X9WriteReport)
            X9WriteReport(fvar, gvar, ivar);

            // Set R-LINE-COUNT to 3
            gvar.SetRLineCount(3);

            // Set W-REPORT-1ST-DETAIL to 'Y'
            //must uncomment
            //gvar.GetWReport1StDetail().SetReport1stDetail(true);
        }
        /// <summary>
        /// Handles the X7ColumnHeadings paragraph, which moves specific column headings
        /// to the report line and writes them to the report. It also increments the line count.
        /// </summary>
        /// <remarks>
        /// This method is responsible for implementing the X7ColumnHeadings paragraph logic.
        /// The original COBOL paragraph sets initial values in accord with the report format,
        /// calls the X9_WRITE_REPORT to output these to the formatted report and increments
        /// a line report counter. The COBOL paragraph procedures are simulated correctly
        /// by translating the logic into structured C# code with the proper data exchange.
        /// </remarks>
        /// <param name="fvar">Fvar object containing the ReportRecord fields</param>
        /// <param name="gvar">Gvar object containing global variables</param>
        /// <param name="ivar">
        /// Ivar object containing additional variables if necessary -- used as a placeholder for future use.
        /// </param>
        public void X7ColumnHeadings(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move REPORT-COLUMN-HEADING-1 to REPORT-LINE
            string columnHeading1 = gvar.GetReportColumnHeading1().ToString();
            fvar.GetReportRecord().SetReportLine(columnHeading1);

            // Move 2 to R-ADV
            gvar.SetRAdv(2);

            // Perform X9-WRITE-REPORT
            this.X9WriteReport(fvar, gvar, ivar);

            // Move REPORT-COLUMN-HEADING-2 to REPORT-LINE
            string columnHeading2 = gvar.GetReportColumnHeading2().ToString();
            fvar.GetReportRecord().SetReportLine(columnHeading2);

            // Move 0 to R-ADV
            gvar.SetRAdv(0);

            // Perform X9-WRITE-REPORT
            this.X9WriteReport(fvar, gvar, ivar);
            //Add 2 to R-LINE-COUNT
            gvar.SetRLineCount(gvar.GetRLineCount() + 2);
        }
        /// <summary>
        /// Writes the REPORT-RECORD and advances the report to the next PAGE.
        /// This method corresponds to the COBOL paragraph 'WRITE   REPORT-RECORD   AFTER   ADVANCING   PAGE'.
        /// </summary>
        /// <param name="fvar">Respects various global fields</param>
        /// <param name="gvar">Respects various global fields</param>
        /// <param name="ivar">Respects various internal variables</param>
        ///
        /// <remarks>
        /// <para>COBOL to C# conversion notes:</para>
        /// <list type="bullet">
        /// <item>
        /// - The COBOL 'WRITE' statement is translated to write logic in C#.
        /// - The 'REPORT-RECORD' variable is accessed using the provided getter/setter methods.
        /// - The 'AFTER ADVANCING PAGE' is translated to advancing the page in the report.
        /// </item>
        /// </list>
        /// </remarks>
        public void X8ReportNewPage(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Get the report record value
            string reportRecordValue = fvar.GetReportRecordAsString();

            ////Declare a report writer object
            //must uncomment
            //TextWriter reportWriter = gvar.GetPrintWriter();
            // Add the report record to the output
            //reportWriter.WriteLine(reportRecordValue);

            //// Advance the report to the next page
            //string advText = gvar.GetAdvancing();
            //string pageText = gvar.GetPage();
            //string afterAdvText = gvar.GetAfter();

            // Here you would typically include logic to manipulate the report writer.
            // This would be specific to how the report writer and the report file are handled.
        }
        /// <summary>
        /// Write the report with specified advancements (source: COBOL paragraph X9WRITE)
        /// </summary>
        /// <param name="fvar">First variable to write to the report</param>
        /// <param name="gvar">Global variables to control the report formatting</param>
        /// <param name="ivar">Internal variables for iteration and control</param>
        /// <remarks>
        /// The COBOL code:
        ///      WRITE   REPORT-RECORD   AFTER   ADVANCING   R-ADV   LINES
        ///      ADD     R-ADV           TO      R-LINE-COUNT
        /// is translated into the following C# code:
        /// replacement for the WRITE statement is not directly translatable,
        /// we instead perform the assignment. COMMENTED out the addition to R-LINE-COUNT to prevent unfinished SIPs and these values usually are modified further in the program
        /// since the data types are strings or generic type variables, no handling of data differences is required.
        /// </remarks>
        public void X9WriteReport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Access report record and set the print control

            // Simulate the WRITE operation with the assigned parameters

            // Get the current report record, add the necessary print control and add it to the report.
            //must uncomment
            //var reportRecord = fvar.GetReportRecord();
            //reportRecord.SetPrintControl(gvar.GetAfter());
            //reportRecord.SetPrintControl(gvar.GetAdvancing());
            //// Get the current report record, and create a new string with the current print control and the adv string. This will affect the whole report (all lines)
            //var reportRecordAsString = reportRecord.GetReportRecordAsString();
            //var advString = gvar.GetRAdv();

            //string newString = reportRecordAsString + advString;

            // Simulate the report, changing the "workspace" with the new report data.
            // This is currently simulating behaviour - to have a functional writing to some output, comment out the following lines and modify the lower lines such to create the REPORT as string.
            //string reportOutput = reportRecordAsString + advString;
            //Console.WriteLine(reportOutput);

            // Perform the Add operation
            //var rAdv = gvar.GetRAdv(); // index may need adjustment based on actual data
            //var rLineCount = gvar.GetRLineCount(); // Assumed global counter

            //gvar.SetRLineCount(rLineCount + rAdv);
        }
        /// <summary>
        /// COBOL paragraph: x10Report
        /// </summary>
        /// <param name="fvar">Fvar</param>
        /// <param name="gvar">Gvar</param>
        /// <param name="ivar">Ivar</param>
        /// <remarks>
        /// This C# method includes the exact same business logic as found in the specified COBOL paragraph.
        ///
        /// The perform statements are replaced by method calls (Look at instructions.)
        /// The Evaluate structures are replaced by if-else structures.
        /// </remarks>
        public void X10Report(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // First EVALUATE block
            switch (gvar.GetWReportAction())
            {
                case Gvar.WRITE_HEADER:
                    gvar.SetWReportMode(Gvar.NEW_SEDOL); // Evaluate When=WRITE-HEADER: 1st value
                    X10APrintLastSedolSummary(fvar, gvar, ivar);
                    //must uncomment
                    //X10BInitializeSedol(fvar, gvar, ivar);
                    break;
                case Gvar.REWRITE_HEADER:
                case Gvar.LEAVE_HEADER:
                    gvar.SetWReportMode(Gvar.OLD_SEDOL); // Evaluate When=REWRITE-HEADER and LEAVE-HEADER: 2nd value
                    X10APrintLastSedolSummary(fvar, gvar, ivar);
                    //must uncomment
                    //X10BInitializeSedol(fvar, gvar, ivar);
                    break;
                case Gvar.OVERRIDDEN_BALANCE:
                    X10APrintLastSedolSummary(fvar, gvar, ivar);
                    //must uncomment
                    //X10BInitializeSedol(fvar, gvar, ivar);
                    gvar.SetWReportAction(Gvar.OVERWRITE_BALANCE);
                    break;
            }

            // Second EVALUATE block
            switch (gvar.GetWReportAction())
            {
                case Gvar.WRITE_BALANCE_ACQUISITION:
                    gvar.GetReportDetailLine1().SetReportAction("Added            ");
                    switch (gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452TransactionCategory())
                    {
                        case "PP":
                        case "00":
                            gvar.SetReportBalancesAdded(gvar.GetReportBalancesAdded() + 1);
                            break;
                        default:
                            gvar.SetReportTransactionsAdded(gvar.GetReportTransactionsAdded() + 1);
                            break;
                    }
                    X10CPrintDetails(fvar, gvar, ivar);
                    break;
                case Gvar.REWRITE_BALANCE_ACQUISITION:
                    gvar.GetReportDetailLine1().SetReportAction("Updated        ");
                    var updatedCount = gvar.GetReportBalancesUpdated();
                    switch (gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452TransactionCategory())
                    {
                        case "PP":
                        case "00":
                            gvar.SetReportBalancesUpdated(gvar.GetReportBalancesUpdated() + 1);
                            break;
                        default:
                            gvar.SetReportTransactionsUpdated(gvar.GetReportTransactionsUpdated() + 1);
                            break;
                    }
                    X10CPrintDetails(fvar, gvar, ivar);
                    break;
                case Gvar.WRITE_DISPOSAL:
                    gvar.GetReportDetailLine1().SetReportAction("Added          ");
                    gvar.SetReportTransactionsAdded(gvar.GetReportTransactionsAdded() + 1);
                    X10CPrintDetails(fvar, gvar, ivar);
                    break;
                case Gvar.REWRITE_DISPOSAL:
                    gvar.GetReportDetailLine1().SetReportAction("Updated        ");
                    gvar.SetReportTransactionsUpdated(gvar.GetReportTransactionsUpdated() + 1);
                    X10CPrintDetails(fvar, gvar, ivar);
                    break;
                case Gvar.DELETE_BALANCE:
                    gvar.GetReportDetailLine1().SetReportAction("Deleted          ");
                    gvar.SetReportBalancesDeleted(gvar.GetReportBalancesDeleted() + 1);
                    gvar.SetWRecordStoreAsString(gvar.GetD45BalAcqDispRecord().ToString());
                    gvar.SetD45BalAcqDispRecordAsString(gvar.GetD13BalAcqDispRecord().ToString());
                    gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().SetD452RemainderAsString(
                     gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record02Fields().ToString());
                    X10CPrintDetails(fvar, gvar, ivar);
                    gvar.SetD45BalAcqDispRecordAsString(gvar.GetWRecordStore().ToString());
                    break;
                case Gvar.OVERWRITE_BALANCE:
                    X10CPrintDetails(fvar, gvar, ivar);
                    break;
            }
        }
        /// <summary>
        /// Processes the logic for printing the last SEDOL summary.
        /// This method handles various conditions related to report generation and
        /// performs several data movements and calculations.
        /// </summary>
        /// <param name="fvar">The fvar parameter to access COBOL file variables.</param>
        /// <param name="gvar">The gvar parameter to access COBOL global variables.</param>
        /// <param name="ivar">The ivar parameter to access COBOL input variables.</param>
        /// <remarks>
        /// This method corresponds to the COBOL paragraph x10APrintLastSedolSummary.
        /// It converts the original COBOL logic to C# while maintaining the same functionality.
        /// </remarks>

        public void X10APrintLastSedolSummary(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // If this is the first time the report is being generated
            if (gvar.IsReport1StTime())
            {
                gvar.SetWReport1StTime("N");
                return; // Skip the rest of the method
            }

            // If no balances or transactions have been added, updated, or deleted and report override is not set
            if (gvar.GetReportBalancesAdded() == 0 &&
                gvar.GetReportBalancesUpdated() == 0 &&
                gvar.GetReportBalancesDeleted() == 0 &&
                gvar.GetReportTransactionsAdded() == 0 &&
                gvar.GetReportTransactionsUpdated() == 0 &&
                !gvar.IsReportOverride())
            {
                return; // Skip the rest of the method
            }

            // If the line count exceeds 56, refresh the page and column headings
            if (gvar.GetRLineCount() > 56)
            {
                X6PageHeadings(fvar, gvar, ivar);
                X7ColumnHeadings(fvar, gvar, ivar);
            }

            // If this is the first detail in the report
            if (gvar.IsReport1StDetail())
            {
                gvar.GetReportDetailLine2().SetReportFund2(gvar.GetWStoredFundSedol().GetWStoredFund());
                gvar.GetReportDetailLine2().SetReportSedol2(
                    gvar.GetWsSedol().GetWsSedol1() +
                    "-" +
                    gvar.GetWsSedol().GetWsSedol24() +
                    "-" +
                    gvar.GetWsSedol().GetWsSedol57());
            }

            // Move various balance and transaction counts to respective report fields
            gvar.SetWEditCount(gvar.GetReportBalancesAdded());
            gvar.GetReportDetailLine2().SetReportBalAdd(gvar.GetWEditCount().ToString());

            gvar.SetWEditCount(gvar.GetReportBalancesUpdated());
            gvar.GetReportDetailLine2().SetReportBalUpd(gvar.GetWEditCount().ToString());

            gvar.SetWEditCount(gvar.GetReportBalancesDeleted());
            gvar.GetReportDetailLine2().SetReportBalDel(gvar.GetWEditCount().ToString());

            gvar.SetWEditCount(gvar.GetReportTransactionsAdded());
            gvar.GetReportDetailLine2().SetReportTransAdd(gvar.GetWEditCount().ToString());

            gvar.SetWEditCount(gvar.GetReportTransactionsUpdated());
            gvar.GetReportDetailLine2().SetReportTransUpd(gvar.GetWEditCount().ToString());

            // Set initial value for WORK-SUB
            gvar.SetWorkSub(1);

            // Evaluate W-REPORT-MODE and set appropriate report literal
            if (gvar.GetWReportMode() == Gvar.NEW_SEDOL)
            {
                gvar.GetReportDetailLine3()
                   .SetReportLit("NEW SEDOL CREATED      ");
            }
            else
            {
                gvar.GetReportDetailLine3()
                   .SetReportLit("EXISTING SEDOL UPDATED ");
            }

            // If report override is set, update the report literal
            if (gvar.IsReportOverride())
            {
                gvar.GetReportDetailLine3()
                   .SetReportLit("BALANCES UPDATE OVERRIDDEN");
                gvar.SetWorkSub(gvar.GetWorkSub() + 1);
            }

            // If no balances have been added or updated and override is not set, update the report literal
            if (gvar.GetReportBalancesAdded() == 0 && gvar.GetReportBalancesUpdated() == 0 && !gvar.IsReportOverride())
            {
                gvar.GetReportDetailLine3()
                   .SetReportLit("NO C/F BALANCES THIS SEDOL");
                gvar.SetWorkSub(gvar.GetWorkSub() + 1);
            }

            // If report override is not set, write the detail line 2 to the report
            if (!gvar.IsReportOverride())
            {
                gvar.SetRAdv(1);
                fvar.GetReportRecord().SetReportLine(gvar.GetReportDetailLine2().ToString());
                X9WriteReport(fvar,gvar, ivar);
            }

            // Move and write detail line 3 and two blank lines to the report
            fvar.GetReportRecord().SetReportLine(gvar.GetReportDetailLine3().ToString());
            X9WriteReport(fvar,gvar, ivar);

            fvar.GetReportRecord().SetReportLine(" ");
            X9WriteReport(fvar,gvar, ivar);

            // Clear the report detail lines and SEDOL fields
            gvar.GetReportDetailLine2().SetReportFund2(" ");
            gvar.GetReportDetailLine2().SetReportSedol2(" ");
        }
        /// <summary>
        /// COBOL: X10BInitialiseSedol
        ///
        /// </summary>
        /// <param name="fvar">MockDvar instance containing global and file variables</param>
        /// <param name="gvar">MockGvar instance containing input, local, working, and other global variables</param>
        /// <param name="ivar">MockIvar instance containing input variables</param>
        /// <remarks>
        /// Handles initialisation of sedol and related variables.
        /// Converts COBOL paragraph MOVE and EVALUATE statements.
        /// </remarks>
        public void X10BInitialiseSedol(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Initialize report balances counters to zero
            gvar.SetReportBalancesAdded(0);
            gvar.SetReportBalancesUpdated(0);
            gvar.SetReportBalancesDeleted(0);
            gvar.SetReportTransactionsAdded(0);
            gvar.SetReportTransactionsUpdated(0);

            // Initialize detail line and first detail flag
            gvar.SetReportDetailLine3AsString(" ");
            gvar.SetWReport1StDetail("Y");

            // Evaluate conditions and set W-STORED-FUND-SEDOL accordingly
            var d131CalSedol = int.Parse(gvar.GetD13SedolHeaderRecord().GetD131Key().GetD131CalSedol().ToString());
            var d451CalSedol = int.Parse(gvar.GetD45Record().GetYeD45HeaderFront().GetD45Key().GetD451Key().GetD451CalSedol().ToString());
            int highValues = 0; // must check and update

            if (d131CalSedol <= d451CalSedol && d131CalSedol < highValues)
            {
                gvar.SetWStoredFundSedolAsString(d131CalSedol.ToString());
            }
            else if (d451CalSedol <= d131CalSedol && d451CalSedol < highValues)
            {
                gvar.SetWStoredFundSedolAsString(d451CalSedol.ToString());
            }
            else
            {
                gvar.SetWStoredFundSedolAsString(" ");
            }

            // Set the override flag based on the OVER-RIDE condition
            if (gvar.GetWsFlags().IsOverRide())
            {
                gvar.SetWReportOverride("Y");
            }
            else
            {
                gvar.SetWReportOverride("N");
            }
        }
        /// <summary>
        /// COBOL paragraph: x10CPrintDetails
        /// This method handles the printing of the details in the report.
        /// </summary>
        /// <param name="fvar">Fvar object containing necessary variables</param>
        /// <param name="gvar">Gvar object containing necessary variables</param>
        /// <param name="ivar">Ivar object containing necessary variables</param>
        /// <remarks>
        /// Converted from COBOL paragraph X10CPRINTDETAILS.
        /// </remarks>
        public void X10CPrintDetails(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // If current line count is greater than 59, perform page and column headings
            if (gvar.GetRLineCount() > 59)
            {
                X6PageHeadings(fvar, gvar, ivar);
                X7ColumnHeadings(fvar, gvar, ivar);
            }

            var wReport1stDetail = gvar.GetWReport1StDetail();
            // Initialize REPORT-BARGAIN-DATE
            var tempReportDetailLine1 = gvar.GetReportDetailLine1();
            tempReportDetailLine1.SetReportBargainDate("");

            if (gvar.IsReport1StDetail())
            {
                gvar.SetWReport1StDetail("N");
                //var tempReportDetailLine1 = gvar.GetReportDetailLine1();
                tempReportDetailLine1.SetReportFund(gvar.GetWStoredFundSedol().GetWStoredFund());
                tempReportDetailLine1.SetReportSedol(gvar.GetWStoredFundSedol().GetWStoredSedol());

                // Concatenate WS-SEDOL parts with hyphens
                var reportSedol = $"{gvar.GetWsSedol().GetWsSedol1()}-{gvar.GetWsSedol().GetWsSedol24()}-{gvar.GetWsSedol().GetWsSedol57()}";
                tempReportDetailLine1.SetReportSedol(reportSedol);
                gvar.SetWReportSedol(reportSedol);

            }
            else
            {
                //var tempReportDetailLine1 = gvar.GetReportDetailLine1();
                tempReportDetailLine1.SetReportFund("");
                tempReportDetailLine1.SetReportSedol("");
            }

            

            // If not REPORT-OVERRIDE, concatenate bargain date parts
            if (!gvar.IsReportOverride())
            {
                // Reconcatenate D45-2-BARGAIN-DATE parts if not overriding
                tempReportDetailLine1.SetReportBargainDate($"{gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452BargainDate().GetD452BargainDateDd():D2}/{gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452BargainDate().GetD452BargainDateYymm().GetD452BargainDateMm():D2}/{gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452BargainDate().GetD452BargainDateDd():D2}/{gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452BargainDate().GetD452BargainDateYymm().GetD452BargainDateYy():D2}");

            }
            var tempD45TransactionCategory = gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452TransactionCategory();
            var tempD452ContractNo = gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452Key().GetD452ContractNo().ToString();

            gvar.GetReportDetailLine1().SetReportContractNo(tempD452ContractNo);
            var records = gvar.GetD45BalAcqDispRecord().GetYeD45DetailFront().GetD452Key().GetD452RecordCode();

            switch (records)
            {
                case 02:
                    // EVALUATE based on D45-2-TRANSACTION-CATEGORY for "02" case
                    switch (tempD45TransactionCategory)
                    {
                        case "PP":
                            gvar.GetReportDetailLine1().SetReportType("Pool Balance  ");
                            break;
                        case "00":
                            gvar.GetReportDetailLine1().SetReportType("Normal Balance");
                            break;
                        default:
                            // Else, set up for call to X-Call-CheckCat
                            gvar.GetCheckCatLinkage().SetCheckCatTransactionId(int.Parse(gvar.GetAcquisitionIds().GetMadaTransactionCategoryId()));
                            XCallCheckCat(fvar, gvar, ivar);
                            // Concatenate category code
                            gvar.GetReportDetailLine1().SetReportType($"{gvar.GetCheckCatLinkage().GetCheckCatString().GetCheckCatCategoryCode()} A  Transaction");
                            break;
                    }
                    break;

                case 03:
                    // Handle "03" case
                    gvar.GetCheckCatLinkage().SetCheckCatTransactionId(int.Parse(gvar.GetAcquisitionIds().GetMadaTransactionCategoryId()));

                    XCallCheckCat(fvar, gvar, ivar);
                    // Concatenate category code
                    gvar.GetReportDetailLine1().SetReportType($"{gvar.GetCheckCatLinkage().GetCheckCatString().GetCheckCatCategoryCode()} D  Transaction");

                    X10C2Disposal(fvar, gvar, ivar);
                    break;

                default:
                    // Handle "OTHER" case
                    if (gvar.IsReportOverride())
                    {
                        gvar.GetReportDetailLine1().SetReportType(" ");

                    }
                    else
                    {
                        gvar.GetReportDetailLine1().SetReportType("Header Record!");

                    }
                    break;
            }

            // Move WORK-AMOUNT, WORK-UNITS to W-EDIT-VALUE, W-EDIT-QUANTITY if not REPORT-OVERRIDE
            //must uncomment
            //if (!gvar.IsReportOverride())
            //{
            //  gvar.GSetWEditValue(gvar.GetWorkAmount());
            //    gvar.GetTempReportDetailLine1().SetReportValue(gvar.GetWEditValue());

            //    gvar.GSetWEditQuantity(gVar.GetWorkUnits());
            //    gvar.GetTempReportDetailLine1().SetReportQuantity(gvar.GetWEditQuantity());
            //}

            //gvar.GSetRAdv(gvar.GetRAdv());

            if (gvar.IsReportOverride())
            {
                gvar.GetReportDetailLine2().SetReportBalAdd(@"   0");
                gvar.GetReportDetailLine2().SetReportBalUpd("");
                gvar.GetReportDetailLine2().SetReportBalDel("");
                gvar.GetReportDetailLine2().SetReportTransAdd("");
                gvar.GetReportDetailLine2().SetReportTransUpd("");

                gvar.GetReportDetailLine1().SetReportType(gvar.GetReportDetailLine1().GetReportType());
                gvar.SetReportDetailLine1AsString(gvar.GetReportDetailLine2().ToString());


                gvar.GetReportDetailLine1().SetReportFund(gvar.GetWStoredFundSedol().GetWStoredFund());
                gvar.GetReportDetailLine1().SetReportSedol(gvar.GetWReportSedol());
            }

            gvar.GetReportDetailLine1().SetReportSedol(gvar.GetWReportSedol());

            fvar.GetReportRecord().SetReportLine(gvar.GetReportDetailLine1().GetReportType());

            // Call X9-WRITE-REPORT to write the report line
            X9WriteReport(fvar, gvar, ivar);

            gvar.GetReportDetailLine1().SetReportType("");

        }
        /// <summary>
        /// Implements the logic for the COBOL paragraph X10C1BalanceAcquisition.
        /// </summary>
        ///
        /// <param name="fvar">Global variables accessible in the COBOL program</param>
        /// <param name="gvar">Second global variable data class</param>
        /// <param name="ivar">Third global variable data class</param>
        ///
        /// <remarks>
        /// This method initializes WORK-AMOUNT and WORK-UNITS to zero, then iterates through each cost record.
        /// If the record has a real cost or OS liability, it adds the unindicated cost balance and balance units to WORK-AMOUNT and WORK-UNITS respectively.
        /// </remarks>
        public void X10C1BalanceAcquisition(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Initialize WORK-AMOUNT and WORK-UNITS to zero
            gvar.SetWorkAmount(0);
            gvar.SetWorkUnits(0);

            // Iterate through each cost record
            for (int workSub = 1; workSub <= gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Record02Fields().GetYeD45DetailAcqBalNonCosts().GetD452BfNoOfCostsHeld() && workSub <= 
                Gvar.MAX_NO_OF_COSTS; workSub++)
            {
                // Set WORK-SUB to the current iteration index
                gvar.SetWorkSub(workSub);

                // Check if the record has a real cost or OS liability
                if (gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Record02Fields().GetYeD45DetailAcqBalCostTable().GetD452CostTable().IsD452RealCost()
                    || gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Record02Fields().GetYeD45DetailAcqBalCostTable().GetD452CostTable().IsD452OsLiability())
                {
                    decimal currentWorkAmount = gvar.GetWorkAmount();
                    currentWorkAmount += gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Record02Fields().GetYeD45DetailAcqBalCostTable().GetD452CostTable().GetD452BfUnindCostBalance();
                    gvar.SetWorkAmount(currentWorkAmount);

                    decimal currentWorkUnits = gvar.GetWorkUnits();
                    currentWorkUnits += gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Record02Fields().GetYeD45DetailAcqBalCostTable().GetD452CostTable().GetD452BfBalanceUnits();
                    gvar.SetWorkUnits(currentWorkUnits);

                }
            }
        }
        /// <summary>
        /// COBOL paragraph: x10C2Disposal
        ///
        /// Compute WORK-AMOUNT as ZERO minus D45-2-PROCEEDS.
        /// Compute WORK-UNITS as ZERO minus D45-2-NUMBER-OF-UNITS.
        /// </summary>
        ///
        /// <remarks>
        /// Converted from COBOL paragraph<c> x10C2Disposal </c>.
        /// The COMPUTE statement in COBOL performs arithmetic operations.
        /// </remarks>
        /// <param name="gvar">Global variables object.</param>
        /// <param name="fvar">File variables object.</param>
        /// <param name="ivar">Index variables object.</param>
        public void X10C2Disposal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Declare local variables
            decimal zero = 0;
            decimal d452Proceeds = gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Record03Fields().GetYeD45DetailDisposalFields().GetD452Proceeds();
            decimal d452NumberOfUnits = gvar.GetD45BalAcqDispRecord().GetYeD45DetailRemainder1().GetD452Record03Fields().GetYeD45DetailDisposalFields().GetD452NumberOfUnits();

            // Compute WORK-AMOUNT as ZERO minus D45-2-PROCEEDS
            decimal workAmount = zero - d452Proceeds;
            // Set the computed value to WORK-AMOUNT
            gvar.SetWorkAmount(workAmount);

            // Compute WORK-UNITS as ZERO minus D45-2-NUMBER-OF-UNITS
            decimal workUnits = zero - d452NumberOfUnits;
            // Set the computed value to WORK-UNITS
            gvar.SetWorkUnits(workUnits);
        }

        /// <summary>
        /// The XCallEqtpath method calls the 'EQTPATH' external program using the EQTPATH-LINKAGE parameter.
        /// This method mimics the functionality described in the original COBOL paragraph named "XCALLEQTPATH".
        /// </summary>
        /// <remarks>
        /// <para>The original COBOL code calls the 'EQTPATH' program, passing the EQTPATH-LINKAGE parameter.
        /// </para>
        /// <para>In C#, this is implemented by creating a new instance of the EqtpathLinkage class
        /// and invoking its Run method with the EQTPATH-LINKAGE parameter.
        /// </para>
        /// </remarks>
        /// <param name="fvar">Fvar parameter</param>
        /// <param name="gvar">Gvar parameter containing the EQTPATH-LINKAGE variable</param>
        /// <param name="ivar">Ivar parameter</param>
        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            //must uncomment
            //// Create a new instance of the EqtpathLinkage class
            //EqtpathLinkage eqtpathLinkage = new EqtpathLinkage();

            //// Get the EQTPATH-LINKAGE parameter from gvar
            //var eqtpathLinkageValue = gvar.GetEqtpathLinkage();

            //// Call the Run method on the EqtpathLinkage instance with only EQTPATH-LINKAGE parameter
            //eqtpathLinkage.SetEqtpathLinkage(eqtpathLinkageValue);
            //eqtpathLinkage.Run();

            //// Set the EQTPATH variable after the call
            //gvar.SetEqtpath(eqtpathLinkage.GetEqtpathLinkage());
        }

        //         <summary>
        // COBOL Procedures:
        // CALL 'CGTFILES' USING CGTFILES-LINKAGE
        //                          L-FILE-RECORD-AREA
        //                          COMMON-LINKAGE
        // </summary>
        // <param name = "fvar" > This contains File level variables</param>
        // <param name = "gvar" > This contains Global level variables</param>
        // <param name = "ivar" > This contains Instance level variables</param>
        // <remarks>
        // <para>Converted from COBOL Paragraph: xCallCgtfiles</para>
        // </remarks>
        public void XCallCgtfiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Resolve COBOL CALL 'CGTFILES' using the specified parameters
            // CCGTFILES is the external program (subprogram) that gets called
            //must uncomment
            //Cgtfiles cgtfiles = new Cgtfiles();
            ////Obtain the parameters by referencing the exact specific mappings provided.
            //cgtfiles.Run(gvar.GetCgtfilesLinkage());
            //cgtfiles.Run(gvar.GetLFileRecordArea());
            //cgtfiles.Run(ivar.GetCommonLinkage());
        }
        /// <summary>
        /// Executes the call to the CheckCat program using the CheckCat-Linkage data.
        /// This section translates the COBOL CALL statement to a C# method call.
        /// </summary>
        /// <remarks>
        /// <para>Original COBOL Paragraph Name: xCallCheckCat</para>
        /// <para>This method mimics the behavior of the COBOL CALL statement by creating an instance of the CheckCat class and invoking its Run method with the specified linkage data.</para>
        /// </remarks>
        /// <param name="fvar">A parameter that holds various functional variables.</param>
        /// <param name="gvar">A parameter that holds global variables, including CHECKCAT and CHECKCAT-LINKAGE.</param>
        /// <param name="ivar">A parameter that holds various input variables, if needed.</param>
        public void XCallCheckCat(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            //must uncomment
            //// Retrieve the CheckCat-Linkage data from the gvar parameter
            //var checkCatLinkage = gvar.GetCheckcatLinkage();

            //// Create an instance of the CheckCat class
            //CheckCat checkCat = new CheckCat();

            //// Call the Run method with the CheckCat-Linkage data
            //checkCat.Run(checkCatLinkage);
        }
        /// <summary>
        /// Calls the external program 'GetCurrency' with the specified linkage data.
        /// </summary>
        /// <param name="fvar">The fvar parameter contains the necessary functions and variables to execute business logic.</param>
        /// <param name="gvar">The gvar parameter contains the global variables required to execute this functionality.</param>
        /// <param name="ivar">The ivar parameter contains the key business logic data required to execute this functionality.</param>
        /// <remarks>
        /// COBOL Paragraph Name: xCallGetCurrency.
        /// </remarks>
        public void XCallGetCurrency(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            //must uncomment
            //// Create a new instance of the GetCurrency program
            //GetCurrency getCurrency = new GetCurrency();

            //// Set the GETCURRENCY-LINKAGE parameter
            //// Step 1: Get the GetcurrencyLinkage from gvar
            //string getCurrencyLinkage = gvar.GetGetcurrencyLinkage();
            //// Step 2: Call the GetCurrency program
            //getCurrency.Run(getCurrencyLinkage);
        }

    }
}
