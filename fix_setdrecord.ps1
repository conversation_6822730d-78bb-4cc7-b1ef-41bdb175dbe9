# PowerShell script to fix SetDXRecord pattern errors
$filePath = "Equity.CobMod\FileHandlers\Cgtfiles.cs"

# Read the file content
$content = Get-Content $filePath -Raw

# Replace SetDXRecord pattern with SetDXRecordAsString pattern
# Pattern: fvar.SetDXRecord(ivar.GetLFileRecordArea()) -> fvar.SetDXRecordAsString(ivar.GetLFileRecordAreaAsString())
$content = $content -replace 'fvar\.SetD(\d+)Record\(ivar\.GetLFileRecordArea\(\)\)', 'fvar.SetD$1RecordAsString(ivar.GetLFileRecordAreaAsString())'

# Write the content back to the file
Set-Content $filePath $content -NoNewline

Write-Host "SetDXRecord pattern replacement completed successfully!"
Write-Host "Replaced all instances of fvar.SetDXRecord(ivar.GetLFileRecordArea()) with fvar.SetDXRecordAsString(ivar.GetLFileRecordAreaAsString())"
