using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// <Section> Class for Gvar
public class Gvar
{
public Gvar() {}

// Fields in the class


// [DEBUG] Field: WsGeneralWork, is_external=, is_static_class=False, static_prefix=
private WsGeneralWork _WsGeneralWork = new WsGeneralWork();




// [DEBUG] Field: WsProcessingSale, is_external=, is_static_class=False, static_prefix=
private string _WsProcessingSale ="";


// 88-level condition checks for WsProcessingSale
public bool IsProcessingSale()
{
    if (this._WsProcessingSale == "'Y'") return true;
    return false;
}
public bool IsNotProcessingSale()
{
    if (this._WsProcessingSale == "'N'") return true;
    return false;
}


// [DEBUG] Field: LastDetails, is_external=, is_static_class=False, static_prefix=
private LastDetails _LastDetails = new LastDetails();




// [DEBUG] Field: D8Record, is_external=, is_static_class=False, static_prefix=
private D8Record _D8Record = new D8Record();




// [DEBUG] Field: D8_FULL_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string D8_FULL_SCHEDULE = "F ";




// [DEBUG] Field: D8_SEDOLS_SUPPRESSED, is_external=, is_static_class=False, static_prefix=
public const string D8_SEDOLS_SUPPRESSED = "SS";




// [DEBUG] Field: D8_TRANCHES_SUPPRESSED, is_external=, is_static_class=False, static_prefix=
public const string D8_TRANCHES_SUPPRESSED = "ST";




// [DEBUG] Field: D8_MINIMUM_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string D8_MINIMUM_SCHEDULE = "M ";




// [DEBUG] Field: MAX_NO_OF_COSTS, is_external=, is_static_class=False, static_prefix=
public const int MAX_NO_OF_COSTS = 200;




// [DEBUG] Field: MAX_MASTER_RECORD_LEN, is_external=, is_static_class=False, static_prefix=
public const int MAX_MASTER_RECORD_LEN = 16270;




// [DEBUG] Field: CgtfilesLinkage, is_external=, is_static_class=False, static_prefix=
private CgtfilesLinkage _CgtfilesLinkage = new CgtfilesLinkage();




// [DEBUG] Field: LFileRecordArea, is_external=, is_static_class=False, static_prefix=
private LFileRecordArea _LFileRecordArea = new LFileRecordArea();




// [DEBUG] Field: CommonLinkage, is_external=, is_static_class=False, static_prefix=
private CommonLinkage _CommonLinkage = new CommonLinkage();




// [DEBUG] Field: COUNTRY_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_FILE = "CGTCTRY ";




// [DEBUG] Field: GROUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_FILE = "CGTGRP  ";




// [DEBUG] Field: STOCK_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_FILE = "CGTSTK  ";




// [DEBUG] Field: FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_FILE = "M-U-FUND";




// [DEBUG] Field: RPI_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_FILE = "CGTRPI  ";




// [DEBUG] Field: PARAMETER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_FILE = "M-PARAM ";




// [DEBUG] Field: USER_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FILE = "M-USER  ";




// [DEBUG] Field: OUTPUT_LISTING, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LISTING = "M-OUTLST";




// [DEBUG] Field: MASTER_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_LOG_FILE = "M-LOG   ";




// [DEBUG] Field: REALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_DATA_FILE = "CGTDR   ";




// [DEBUG] Field: UNREALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_DATA_FILE = "CGTDU   ";




// [DEBUG] Field: NOTIONAL_SALE_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_DATA_FILE = "CGTDN   ";




// [DEBUG] Field: REALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_DATA_FILE = "CGTDT   ";




// [DEBUG] Field: UNREALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_DATA_FILE = "CGTDX   ";




// [DEBUG] Field: ERROR_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_DATA_FILE = "CGTERR  ";




// [DEBUG] Field: PRINTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_FILE = "M-PRINT ";




// [DEBUG] Field: STOCK_TYPE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_TYPE_FILE = "M-STOCK ";




// [DEBUG] Field: YE_REC2_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC2_DATA_FILE = "CGTYERR ";




// [DEBUG] Field: TRANSACTION_CODE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_CODE_FILE = "M-TRANS ";




// [DEBUG] Field: OUTPUT_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LOG_FILE = "M-OUTLOG";




// [DEBUG] Field: MESSAGE_FILE, is_external=, is_static_class=False, static_prefix=
public const string MESSAGE_FILE = "M-MESS  ";




// [DEBUG] Field: USER_FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUND_FILE = "CGTFUNDX";




// [DEBUG] Field: HELP_TEXT_FILE, is_external=, is_static_class=False, static_prefix=
public const string HELP_TEXT_FILE = "M-HELP  ";




// [DEBUG] Field: DEFAULT_ACCESS_FILE, is_external=, is_static_class=False, static_prefix=
public const string DEFAULT_ACCESS_FILE = "M-DEF-AC";




// [DEBUG] Field: ACCESS_PROFILE_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_PROFILE_FILE = "M-ACCESS";




// [DEBUG] Field: EXTEL_PRICES_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_PRICES_FILE = "M-EPRICE";




// [DEBUG] Field: EXTEL_CURRENCY_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_CURRENCY_FILE = "M-CURR  ";




// [DEBUG] Field: STOCK_PRICE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_PRICE_FILE = "M-SPRICE";




// [DEBUG] Field: EXTEL_TRANSMISSION_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_TRANSMISSION_FILE = "M-ETRANS";




// [DEBUG] Field: SEQ_BALANCE_FILE, is_external=, is_static_class=False, static_prefix=
public const string SEQ_BALANCE_FILE = "CGTMFO  ";




// [DEBUG] Field: TRANSACTION_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_FILE = "CGTTRANS";




// [DEBUG] Field: BACKUP_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_FILE = "M-BK-LOG";




// [DEBUG] Field: BACKUP_DETAILS_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_FILE = "M-BK-DET";




// [DEBUG] Field: STOCK_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_DATA_FILE = "M-ST-DAT";




// [DEBUG] Field: FUNDS_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_DATA_FILE = "M-FN-DAT";




// [DEBUG] Field: PRICE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_DATA_FILE = "M-PR-DAT";




// [DEBUG] Field: BALANCE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string BALANCE_LOAD_DATA_FILE = "M-BL-DAT";




// [DEBUG] Field: REPLACEMENT_ACQ_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_ACQ_FILE = "M-RP-ACQ";




// [DEBUG] Field: REPLACEMENT_DIS_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_DIS_FILE = "M-RP-DIS";




// [DEBUG] Field: REPORT_RUN_MSG_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORT_RUN_MSG_FILE = "CGTMSG  ";




// [DEBUG] Field: RCF_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_FILE = "CGTRCF  ";




// [DEBUG] Field: RPI_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_DATA_FILE = "M-RPIDAT";




// [DEBUG] Field: COUNTRY_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_DATA_FILE = "M-CY-DAT";




// [DEBUG] Field: GROUP_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_DATA_FILE = "M-GR-DAT";




// [DEBUG] Field: GAINLOSS_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE = "CGTGLDAT";




// [DEBUG] Field: ACQUISITION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACQUISITION_EXPORT_FILE = "MAEXPORT";




// [DEBUG] Field: TAPER_RATE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_RATE_FILE = "TAPERATE";




// [DEBUG] Field: ASSET_USAGE_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_USAGE_CALENDAR_FILE = "ASSETUC ";




// [DEBUG] Field: PERIOD_END_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_FILE = "PENDCAL ";




// [DEBUG] Field: PERIOD_END_CALENDAR_DATES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_DATES_FILE = "PENDCALD";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_FILE, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_FILE = "ICFUNDS ";




// [DEBUG] Field: PRICE_TYPES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_TYPES_FILE = "PRICETYP";




// [DEBUG] Field: PENDING_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_LOG_FILE = "PENDLOG ";




// [DEBUG] Field: PENDING_ITEMS_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_ITEMS_FILE = "PENDITEM";




// [DEBUG] Field: GAINLOSS_DATA_FILE_LR, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE_LR = "CGTGLDLR";




// [DEBUG] Field: CG01_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG01_REPORT_FILE = "M-CG01  ";




// [DEBUG] Field: CG02_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG02_REPORT_FILE = "M-CG02  ";




// [DEBUG] Field: CG03_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG03_REPORT_FILE = "M-CG03  ";




// [DEBUG] Field: ERROR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_REPORT_FILE = "M-ERR-RP";




// [DEBUG] Field: REALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_SCHEDULE_FILE = "CGTPR   ";




// [DEBUG] Field: UNREALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_SCHEDULE_FILE = "CGTPU   ";




// [DEBUG] Field: NOTIONAL_SALE_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_SCHEDULE_FILE = "CGTPN   ";




// [DEBUG] Field: REALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_SCHEDULE_FILE = "CGTPT   ";




// [DEBUG] Field: UNREALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_SCHEDULE_FILE = "CGTPX   ";




// [DEBUG] Field: OFFSHORE_INCOME_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OFFSHORE_INCOME_REPORT = "M-OF-REP";




// [DEBUG] Field: YE_REC_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC_REPORT_FILE = "M-YE-REC";




// [DEBUG] Field: YE_DEL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_DEL_REPORT_FILE = "M-YE-DEL";




// [DEBUG] Field: YE_CON_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_CON_REPORT_FILE = "M-YE-CON";




// [DEBUG] Field: YE_ERR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_ERR_REPORT_FILE = "M-YE-ERR";




// [DEBUG] Field: YE_BAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_BAL_REPORT_FILE = "M-YE-BAL";




// [DEBUG] Field: FOREIGN_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FOREIGN_EXTEL_REPORT = "M-EX-FOR";




// [DEBUG] Field: STERLING_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STERLING_EXTEL_REPORT = "M-EX-STL";




// [DEBUG] Field: CGTR04_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR04_REPORT_FILE = "M-CGTR04";




// [DEBUG] Field: CGTR05_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR05_REPORT_FILE = "M-CGTR05";




// [DEBUG] Field: STOCK_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_REPORT = "M-ST-REP";




// [DEBUG] Field: FUNDS_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_REPORT = "M-FN-REP";




// [DEBUG] Field: PRICE_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_REPORT = "M-PR-REP";




// [DEBUG] Field: SKAN1_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN1_REPORT = "M-SK1REP";




// [DEBUG] Field: SKAN2_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN2_REPORT = "M-SK2REP";




// [DEBUG] Field: NEW_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_REALISED_REPORT = "M-NRGREP";




// [DEBUG] Field: NEW_UNREALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_UNREALISED_REPORT = "M-NUGREP";




// [DEBUG] Field: MGM1_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string MGM1_REPORT_FILE = "M-MGMREP";




// [DEBUG] Field: CAPITAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CAPITAL_REPORT_FILE = "M-CAPREP";




// [DEBUG] Field: BALANCES_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string BALANCES_LOAD_REPORT = "M-BALREP";




// [DEBUG] Field: REPLACEMENT_RELIEF_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_RELIEF_REPORT = "M-RP-REP";




// [DEBUG] Field: RPI_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_REPORT = "M-RPIREP";




// [DEBUG] Field: COUNTRY_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_REPORT = "M-CY-REP";




// [DEBUG] Field: GROUP_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_REPORT = "M-GR-REP";




// [DEBUG] Field: GAINLOSS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_REPORT = "CGTGLREP";




// [DEBUG] Field: LOST_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOST_INDEXATION_REPORT = "M-ILOST ";




// [DEBUG] Field: LOSU_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOSU_INDEXATION_REPORT = "M-ILOSU ";




// [DEBUG] Field: REAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REAL_H_O_GAINS_REPORT = "M-R-HO-G";




// [DEBUG] Field: UNREAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string UNREAL_H_O_GAINS_REPORT = "M-U-HO-G";




// [DEBUG] Field: BATCH_RUN_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_RUN_LOG_FILE = "B-RUNLOG";




// [DEBUG] Field: BATCH_QUIT_RUN_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_QUIT_RUN_FILE = "BQUITRUN";




// [DEBUG] Field: TRACE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRACE_FILE = "M-TRACE ";




// [DEBUG] Field: ERROR_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_LOG_FILE = "M-ERRLOG";




// [DEBUG] Field: TAPER_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_REPORT_FILE = "TAPERREP";




// [DEBUG] Field: TAPER_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_EXPORT_FILE = "TAPEREXP";




// [DEBUG] Field: ALLOWANCES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string ALLOWANCES_FROM_DB_FILE = "ALLOWANC";




// [DEBUG] Field: LOSSES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string LOSSES_FROM_DB_FILE = "LOSSFMDB";




// [DEBUG] Field: DISPOSALS_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string DISPOSALS_FROM_DB_FILE = "OTHERDIS";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_EXPORT = "ICFUNDSE";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_REPORT = "ICFUNDSR";




// [DEBUG] Field: CGTR06_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR06_REPORT_FILE = "M-CGTR06";




// [DEBUG] Field: DAILY_TRANSACTION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string DAILY_TRANSACTION_EXPORT_FILE = "DAILYTRN";




// [DEBUG] Field: COUNTRY_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_BACKUP_FILE = "M-D01-BK";




// [DEBUG] Field: GROUP_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_BACKUP_FILE = "M-D02-BK";




// [DEBUG] Field: STOCK_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_BACKUP_FILE = "M-D03-BK";




// [DEBUG] Field: FUND_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_BACKUP_FILE = "M-D04-BK";




// [DEBUG] Field: RPI_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_BACKUP_FILE = "M-D07-BK";




// [DEBUG] Field: PARAMETER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_BACKUP_FILE = "M-D08-BK";




// [DEBUG] Field: USER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_BACKUP_FILE = "M-D09-BK";




// [DEBUG] Field: MASTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_BACKUP_FILE = "M-D13-BK";




// [DEBUG] Field: PRINTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_BACKUP_FILE = "M-D31-BK";




// [DEBUG] Field: OUT_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUT_LOG_BACKUP_FILE = "M-D35-BK";




// [DEBUG] Field: USER_FUNDS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUNDS_BACKUP_FILE = "M-D37-BK";




// [DEBUG] Field: ACCESS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_BACKUP_FILE = "M-D40-BK";




// [DEBUG] Field: PRICE_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_BACKUP_FILE = "M-D43-BK";




// [DEBUG] Field: BACKUP_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_BACKUP_FILE = "M-D47-BK";




// [DEBUG] Field: BACKUP_DETAILS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_BACKUP_FILE = "M-D48-BK";




// [DEBUG] Field: REPORTS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORTS_BACKUP_FILE = "M-REP-BK";




// [DEBUG] Field: RCF_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_BACKUP_FILE = "M-D86-BK";




// [DEBUG] Field: OPEN_OP, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OP = "OP ";




// [DEBUG] Field: OPEN_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT = "OI ";




// [DEBUG] Field: OPEN_INPUT_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT_REPORT = "OIR";




// [DEBUG] Field: OPEN_OUTPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OUTPUT = "OO ";




// [DEBUG] Field: OPEN_I_O, is_external=, is_static_class=False, static_prefix=
public const string OPEN_I_O = "IO ";




// [DEBUG] Field: OPEN_EXTEND, is_external=, is_static_class=False, static_prefix=
public const string OPEN_EXTEND = "OE ";




// [DEBUG] Field: OPEN_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_SINGLE_MASTER_FILE_INPUT = "OS ";




// [DEBUG] Field: CLOSE_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_SINGLE_MASTER_FILE_INPUT = "CS ";




// [DEBUG] Field: SET_MASTER_FILE_NAMES, is_external=, is_static_class=False, static_prefix=
public const string SET_MASTER_FILE_NAMES = "FN ";




// [DEBUG] Field: START_EQUAL, is_external=, is_static_class=False, static_prefix=
public const string START_EQUAL = "SE ";




// [DEBUG] Field: START_NOT_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN = "SN ";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY2, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY2 = "SN2";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY3, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY3 = "SN3";




// [DEBUG] Field: START_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_GREATER_THAN = "SG ";




// [DEBUG] Field: START_GT_INVERSE_KEY, is_external=, is_static_class=False, static_prefix=
public const string START_GT_INVERSE_KEY = "SGI";




// [DEBUG] Field: START_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_LESS_THAN = "SL ";




// [DEBUG] Field: START_NOT_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_GREATER_THAN = "SNG";




// [DEBUG] Field: READ_NEXT, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT = "RN ";




// [DEBUG] Field: READ_NEXT_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_WITH_LOCK = "RNL";




// [DEBUG] Field: READ_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_RECORD = "RD ";




// [DEBUG] Field: READ_REPORT, is_external=, is_static_class=False, static_prefix=
public const string READ_REPORT = "RDR";




// [DEBUG] Field: READ_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_WITH_LOCK = "RDL";




// [DEBUG] Field: READ_ON_KEY2, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY2 = "RD2";




// [DEBUG] Field: READ_ON_KEY3, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY3 = "RD3";




// [DEBUG] Field: READ_PREVIOUS_MASTER, is_external=, is_static_class=False, static_prefix=
public const string READ_PREVIOUS_MASTER = "RP ";




// [DEBUG] Field: WRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string WRITE_RECORD = "WR ";




// [DEBUG] Field: REWRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string REWRITE_RECORD = "RW ";




// [DEBUG] Field: DELETE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string DELETE_RECORD = "DE ";




// [DEBUG] Field: CLOSE_FILE, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_FILE = "CL ";




// [DEBUG] Field: CLOSE_REPORT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_REPORT = "CLR";




// [DEBUG] Field: CLOSE_ALL_FILES, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_ALL_FILES = "CA ";




// [DEBUG] Field: UNLOCK_RECORD, is_external=, is_static_class=False, static_prefix=
public const string UNLOCK_RECORD = "UN ";




// [DEBUG] Field: BUILD_MASTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string BUILD_MASTER_FILE = "BL ";




// [DEBUG] Field: GET_CALENDAR_DATES, is_external=, is_static_class=False, static_prefix=
public const string GET_CALENDAR_DATES = "GC ";




// [DEBUG] Field: SET_TRANS_EXPORTED_FLAGS, is_external=, is_static_class=False, static_prefix=
public const string SET_TRANS_EXPORTED_FLAGS = "TX ";




// [DEBUG] Field: READ_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_USER_FUND_RECORD = "RU ";




// [DEBUG] Field: READ_NEXT_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_USER_FUND_RECORD = "NU ";




// [DEBUG] Field: GET_CONFIG_VALUE, is_external=, is_static_class=False, static_prefix=
public const string GET_CONFIG_VALUE = "CV ";




// [DEBUG] Field: GET_REQUEST_OPTIONS, is_external=, is_static_class=False, static_prefix=
public const string GET_REQUEST_OPTIONS = "RO ";




// [DEBUG] Field: REMAP_C_F_TRANSACTIONS, is_external=, is_static_class=False, static_prefix=
public const string REMAP_C_F_TRANSACTIONS = "RT ";




// [DEBUG] Field: RETURN_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] RETURN_KEY = new byte[] { 0x30, 0x30 };




// [DEBUG] Field: ESC_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ESC_KEY = new byte[] { 0x31, 0x00 };




// [DEBUG] Field: F1_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F1_KEY = new byte[] { 0x31, 0x01 };




// [DEBUG] Field: F2_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F2_KEY = new byte[] { 0x31, 0x02 };




// [DEBUG] Field: F3_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F3_KEY = new byte[] { 0x31, 0x03 };




// [DEBUG] Field: F4_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F4_KEY = new byte[] { 0x31, 0x04 };




// [DEBUG] Field: F5_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F5_KEY = new byte[] { 0x31, 0x05 };




// [DEBUG] Field: F6_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F6_KEY = new byte[] { 0x31, 0x06 };




// [DEBUG] Field: F7_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F7_KEY = new byte[] { 0x31, 0x07 };




// [DEBUG] Field: F8_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F8_KEY = new byte[] { 0x31, 0x08 };




// [DEBUG] Field: F9_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F9_KEY = new byte[] { 0x31, 0x09 };




// [DEBUG] Field: F10_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F10_KEY = new byte[] { 0x31, 0x0A };




// [DEBUG] Field: CURSOR_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_UP = new byte[] { 0x31, 0x18 };




// [DEBUG] Field: CURSOR_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_DOWN = new byte[] { 0x31, 0x19 };




// [DEBUG] Field: CURSOR_LEFT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_LEFT = new byte[] { 0x31, 0x1B };




// [DEBUG] Field: CURSOR_RIGHT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_RIGHT = new byte[] { 0x31, 0x1A };




// [DEBUG] Field: PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_UP = new byte[] { 0x31, 0x1C };




// [DEBUG] Field: PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_DOWN = new byte[] { 0x31, 0x1D };




// [DEBUG] Field: ACCEPT_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_UP = new byte[] { 0x31, 0x35 };




// [DEBUG] Field: ACCEPT_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_DOWN = new byte[] { 0x31, 0x36 };




// [DEBUG] Field: INSERT_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] INSERT_KEY = new byte[] { 0x31, 0x1E };




// [DEBUG] Field: DELETE_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] DELETE_KEY = new byte[] { 0x31, 0x1F };




// [DEBUG] Field: F11_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F11_KEY = new byte[] { 0x31, 0x20 };




// [DEBUG] Field: F12_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F12_KEY = new byte[] { 0x31, 0x21 };




// [DEBUG] Field: HOME_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] HOME_KEY = new byte[] { 0x31, 0x22 };




// [DEBUG] Field: END_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] END_KEY = new byte[] { 0x31, 0x23 };




// [DEBUG] Field: CONTROL_HOME, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_HOME = new byte[] { 0x31, 0x24 };




// [DEBUG] Field: CONTROL_END, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_END = new byte[] { 0x31, 0x25 };




// [DEBUG] Field: CONTROL_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_UP = new byte[] { 0x31, 0x26 };




// [DEBUG] Field: CONTROL_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_DOWN = new byte[] { 0x31, 0x27 };




// [DEBUG] Field: FIRST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR = 1982;




// [DEBUG] Field: LAST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR = 2044;




// [DEBUG] Field: FIRST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR_YY = 82;




// [DEBUG] Field: LAST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR_YY = 44;




// [DEBUG] Field: FIRST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE = 19820101;




// [DEBUG] Field: LAST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE = 20441231;




// [DEBUG] Field: FIRST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE_YYMMDD = 820101;




// [DEBUG] Field: LAST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE_YYMMDD = 441231;




// [DEBUG] Field: CLIENT_PERIOD_END_DATE_97_98, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_END_DATE_97_98 = 19980405;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_98_99, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_98_99 = 19980406;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_00_01, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_00_01 = 20000406;




// [DEBUG] Field: REIT_PROCESSING_START_DATE, is_external=, is_static_class=False, static_prefix=
public const int REIT_PROCESSING_START_DATE = 20070101;




// [DEBUG] Field: CREATE_2008_POOL_DATE, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE = 20080406;




// [DEBUG] Field: CREATE_2008_POOL_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE_YYMMDD = 080406;




// [DEBUG] Field: FIRST_USABLE_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_USABLE_DATE = 450101;




// [DEBUG] Field: GROUP_NO_ACTION, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NO_ACTION = 1;




// [DEBUG] Field: GROUP_PRO_RATA, is_external=, is_static_class=False, static_prefix=
public const int GROUP_PRO_RATA = 2;




// [DEBUG] Field: GROUP_NON_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NON_OLAB_FUND_DEFAULT = 3;




// [DEBUG] Field: GROUP_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_OLAB_FUND_DEFAULT = 4;




// [DEBUG] Field: USE_EARLIER_PRICE_NONE, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_NONE = " ";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_DAY, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_DAY = "D";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_SESSION, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_SESSION = "S";




// [DEBUG] Field: EqtpathLinkage, is_external=, is_static_class=False, static_prefix=
private EqtpathLinkage _EqtpathLinkage = new EqtpathLinkage();




// [DEBUG] Field: ADMIN_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string ADMIN_DATA_PATH = "EQADMIN";




// [DEBUG] Field: USER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string USER_DATA_PATH = "EQUSER";




// [DEBUG] Field: MASTER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string MASTER_DATA_PATH = "EQMASTER";




// [DEBUG] Field: WsCioLink, is_external=, is_static_class=False, static_prefix=
private WsCioLink _WsCioLink = new WsCioLink();




// [DEBUG] Field: NewFields, is_external=, is_static_class=False, static_prefix=
private NewFields _NewFields = new NewFields();




// [DEBUG] Field: N000Country, is_external=, is_static_class=False, static_prefix=
private N000Country _N000Country = new N000Country();




// [DEBUG] Field: G000Group, is_external=, is_static_class=False, static_prefix=
private G000Group _G000Group = new G000Group();




// [DEBUG] Field: ReportFileRecord, is_external=, is_static_class=False, static_prefix=
private ReportFileRecord _ReportFileRecord = new ReportFileRecord();




// [DEBUG] Field: PrintRecord, is_external=, is_static_class=False, static_prefix=
private PrintRecord _PrintRecord = new PrintRecord();




// [DEBUG] Field: ScheduleRecord, is_external=, is_static_class=False, static_prefix=
private ScheduleRecord _ScheduleRecord = new ScheduleRecord();




// [DEBUG] Field: H000Heading, is_external=, is_static_class=False, static_prefix=
private H000Heading _H000Heading = new H000Heading();




// [DEBUG] Field: D000Detail, is_external=, is_static_class=False, static_prefix=
private D000Detail _D000Detail = new D000Detail();




// [DEBUG] Field: G000Detail, is_external=, is_static_class=False, static_prefix=
private G000Detail _G000Detail = new G000Detail();




// [DEBUG] Field: E000Detail, is_external=, is_static_class=False, static_prefix=
private E000Detail _E000Detail = new E000Detail();




// [DEBUG] Field: EBondDetail, is_external=, is_static_class=False, static_prefix=
private EBondDetail _EBondDetail = new EBondDetail();




// [DEBUG] Field: T000Totals, is_external=, is_static_class=False, static_prefix=
private T000Totals _T000Totals = new T000Totals();




// [DEBUG] Field: F000Footing, is_external=, is_static_class=False, static_prefix=
private F000Footing _F000Footing = new F000Footing();




// [DEBUG] Field: F200Footing, is_external=, is_static_class=False, static_prefix=
private F200Footing _F200Footing = new F200Footing();




// [DEBUG] Field: WctMaxCountries, is_external=, is_static_class=False, static_prefix=
private int _WctMaxCountries =1000;




// [DEBUG] Field: WctCountryTable, is_external=, is_static_class=False, static_prefix=
private WctCountryTable _WctCountryTable = new WctCountryTable();




// [DEBUG] Field: WftFundRecord, is_external=, is_static_class=False, static_prefix=
private WftFundRecord _WftFundRecord = new WftFundRecord();




// [DEBUG] Field: WgtMaxGroups, is_external=, is_static_class=False, static_prefix=
private int _WgtMaxGroups =1000;




// [DEBUG] Field: WgtGroupTable, is_external=, is_static_class=False, static_prefix=
private WgtGroupTable _WgtGroupTable = new WgtGroupTable();




// [DEBUG] Field: WmtMonthTable, is_external=, is_static_class=False, static_prefix=
private WmtMonthTable _WmtMonthTable = new WmtMonthTable();




// [DEBUG] Field: Filler107, is_external=, is_static_class=False, static_prefix=
private Filler107 _Filler107 = new Filler107();




// [DEBUG] Field: WswSwitches, is_external=, is_static_class=False, static_prefix=
private WswSwitches _WswSwitches = new WswSwitches();




// [DEBUG] Field: WtsTotals, is_external=, is_static_class=False, static_prefix=
private WtsTotals _WtsTotals = new WtsTotals();




// [DEBUG] Field: WwaWorkareas, is_external=, is_static_class=False, static_prefix=
private WwaWorkareas _WwaWorkareas = new WwaWorkareas();




// [DEBUG] Field: WTransCat, is_external=, is_static_class=False, static_prefix=
private string _WTransCat =" ";


// 88-level condition checks for WTransCat
public bool IsWDerivatives()
{
    if (this._WTransCat == "'WP'") return true;
    if (this._WTransCat == "'WC'") return true;
    if (this._WTransCat == "'EP'") return true;
    if (this._WTransCat == "'EC'") return true;
    return false;
}


// [DEBUG] Field: CheckCatLinkage, is_external=, is_static_class=False, static_prefix=
private CheckCatLinkage _CheckCatLinkage = new CheckCatLinkage();




// [DEBUG] Field: NEW_DERIVATIVE_EXPORT_FORMAT, is_external=, is_static_class=False, static_prefix=
public const string NEW_DERIVATIVE_EXPORT_FORMAT = "NewDerivativeExportFormat";




// [DEBUG] Field: TEMP_FILE_DISK, is_external=, is_static_class=False, static_prefix=
public const string TEMP_FILE_DISK = "TempFileToDisk";




// [DEBUG] Field: ILG_COMPUTATION_START_DATE, is_external=, is_static_class=False, static_prefix=
public const string ILG_COMPUTATION_START_DATE = "IndexLinkedGiltsComputationStartDate";




// [DEBUG] Field: ILG_COMPUTATION_PERIOD_END_REVALUATIONS, is_external=, is_static_class=False, static_prefix=
public const string ILG_COMPUTATION_PERIOD_END_REVALUATIONS = "IndexLinkedGiltsComputationPeriodEndRevaluations";




// [DEBUG] Field: ASSET_TOTALS_REALISED_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_TOTALS_REALISED_SCHEDULE = "AssetTotalsOnRealisedSchedule";




// [DEBUG] Field: ASSET_TOTALS_REALISED_BOND_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_TOTALS_REALISED_BOND_SCHEDULE = "AssetTotalsOnRealisedBondSchedule";




// [DEBUG] Field: WConfigItem, is_external=, is_static_class=False, static_prefix=
private string _WConfigItem ="";




// [DEBUG] Field: ElcgmioLinkage1, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage1 _ElcgmioLinkage1 = new ElcgmioLinkage1();




// [DEBUG] Field: FULL_COMP, is_external=, is_static_class=False, static_prefix=
public const int FULL_COMP = 6;




// [DEBUG] Field: PARTIAL_COMP, is_external=, is_static_class=False, static_prefix=
public const int PARTIAL_COMP = 9;




// [DEBUG] Field: SEDOL_WHATIF_COMP, is_external=, is_static_class=False, static_prefix=
public const int SEDOL_WHATIF_COMP = 7;




// [DEBUG] Field: SINGLE_SEDOL_COMP, is_external=, is_static_class=False, static_prefix=
public const int SINGLE_SEDOL_COMP = 7;




// [DEBUG] Field: YEAR_END_COMP, is_external=, is_static_class=False, static_prefix=
public const int YEAR_END_COMP = 8;




// [DEBUG] Field: ElcgmioLinkage2, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage2 _ElcgmioLinkage2 = new ElcgmioLinkage2();




// [DEBUG] Field: WRealisedSchedule, is_external=, is_static_class=False, static_prefix=
private string _WRealisedSchedule ="False";


// 88-level condition checks for WRealisedSchedule
public bool IsWRealisedScheduleY()
{
    if (this._WRealisedSchedule == "'True'") return true;
    return false;
}
public bool IsWRealisedScheduleN()
{
    if (this._WRealisedSchedule == "'False'") return true;
    return false;
}


// [DEBUG] Field: WRealisedBondSchedule, is_external=, is_static_class=False, static_prefix=
private string _WRealisedBondSchedule ="True";


// 88-level condition checks for WRealisedBondSchedule
public bool IsWRealisedBondScheduleY()
{
    if (this._WRealisedBondSchedule == "'True'") return true;
    return false;
}
public bool IsWRealisedBondScheduleN()
{
    if (this._WRealisedBondSchedule == "'False'") return true;
    return false;
}


// Getter and Setter methods

// Standard Getter
public WsGeneralWork GetWsGeneralWork()
{
    return _WsGeneralWork;
}

// Standard Setter
public void SetWsGeneralWork(WsGeneralWork value)
{
    _WsGeneralWork = value;
}

// Get<>AsString()
public string GetWsGeneralWorkAsString()
{
    return _WsGeneralWork != null ? _WsGeneralWork.GetWsGeneralWorkAsString() : "";
}

// Set<>AsString()
public void SetWsGeneralWorkAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsGeneralWork == null)
    {
        _WsGeneralWork = new WsGeneralWork();
    }
    _WsGeneralWork.SetWsGeneralWorkAsString(value);
}

// Standard Getter
public string GetWsProcessingSale()
{
    return _WsProcessingSale;
}

// Standard Setter
public void SetWsProcessingSale(string value)
{
    _WsProcessingSale = value;
}

// Get<>AsString()
public string GetWsProcessingSaleAsString()
{
    return _WsProcessingSale.PadRight(0);
}

// Set<>AsString()
public void SetWsProcessingSaleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsProcessingSale = value;
}

// Standard Getter
public LastDetails GetLastDetails()
{
    return _LastDetails;
}

// Standard Setter
public void SetLastDetails(LastDetails value)
{
    _LastDetails = value;
}

// Get<>AsString()
public string GetLastDetailsAsString()
{
    return _LastDetails != null ? _LastDetails.GetLastDetailsAsString() : "";
}

// Set<>AsString()
public void SetLastDetailsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_LastDetails == null)
    {
        _LastDetails = new LastDetails();
    }
    _LastDetails.SetLastDetailsAsString(value);
}

// Standard Getter
public D8Record GetD8Record()
{
    return _D8Record;
}

// Standard Setter
public void SetD8Record(D8Record value)
{
    _D8Record = value;
}

// Get<>AsString()
public string GetD8RecordAsString()
{
    return _D8Record != null ? _D8Record.GetD8RecordAsString() : "";
}

// Set<>AsString()
public void SetD8RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D8Record == null)
    {
        _D8Record = new D8Record();
    }
    _D8Record.SetD8RecordAsString(value);
}

// Standard Getter
public CgtfilesLinkage GetCgtfilesLinkage()
{
    return _CgtfilesLinkage;
}

// Standard Setter
public void SetCgtfilesLinkage(CgtfilesLinkage value)
{
    _CgtfilesLinkage = value;
}

// Get<>AsString()
public string GetCgtfilesLinkageAsString()
{
    return _CgtfilesLinkage != null ? _CgtfilesLinkage.GetCgtfilesLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtfilesLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtfilesLinkage == null)
    {
        _CgtfilesLinkage = new CgtfilesLinkage();
    }
    _CgtfilesLinkage.SetCgtfilesLinkageAsString(value);
}

// Standard Getter
public LFileRecordArea GetLFileRecordArea()
{
    return _LFileRecordArea;
}

// Standard Setter
public void SetLFileRecordArea(LFileRecordArea value)
{
    _LFileRecordArea = value;
}

// Get<>AsString()
public string GetLFileRecordAreaAsString()
{
    return _LFileRecordArea != null ? _LFileRecordArea.GetLFileRecordAreaAsString() : "";
}

// Set<>AsString()
public void SetLFileRecordAreaAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_LFileRecordArea == null)
    {
        _LFileRecordArea = new LFileRecordArea();
    }
    _LFileRecordArea.SetLFileRecordAreaAsString(value);
}

// Standard Getter
public CommonLinkage GetCommonLinkage()
{
    return _CommonLinkage;
}

// Standard Setter
public void SetCommonLinkage(CommonLinkage value)
{
    _CommonLinkage = value;
}

// Get<>AsString()
public string GetCommonLinkageAsString()
{
    return _CommonLinkage != null ? _CommonLinkage.GetCommonLinkageAsString() : "";
}

// Set<>AsString()
public void SetCommonLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CommonLinkage == null)
    {
        _CommonLinkage = new CommonLinkage();
    }
    _CommonLinkage.SetCommonLinkageAsString(value);
}

// Standard Getter
public EqtpathLinkage GetEqtpathLinkage()
{
    return _EqtpathLinkage;
}

// Standard Setter
public void SetEqtpathLinkage(EqtpathLinkage value)
{
    _EqtpathLinkage = value;
}

// Get<>AsString()
public string GetEqtpathLinkageAsString()
{
    return _EqtpathLinkage != null ? _EqtpathLinkage.GetEqtpathLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqtpathLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqtpathLinkage == null)
    {
        _EqtpathLinkage = new EqtpathLinkage();
    }
    _EqtpathLinkage.SetEqtpathLinkageAsString(value);
}

// Standard Getter
public WsCioLink GetWsCioLink()
{
    return _WsCioLink;
}

// Standard Setter
public void SetWsCioLink(WsCioLink value)
{
    _WsCioLink = value;
}

// Get<>AsString()
public string GetWsCioLinkAsString()
{
    return _WsCioLink != null ? _WsCioLink.GetWsCioLinkAsString() : "";
}

// Set<>AsString()
public void SetWsCioLinkAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsCioLink == null)
    {
        _WsCioLink = new WsCioLink();
    }
    _WsCioLink.SetWsCioLinkAsString(value);
}

// Standard Getter
public NewFields GetNewFields()
{
    return _NewFields;
}

// Standard Setter
public void SetNewFields(NewFields value)
{
    _NewFields = value;
}

// Get<>AsString()
public string GetNewFieldsAsString()
{
    return _NewFields != null ? _NewFields.GetNewFieldsAsString() : "";
}

// Set<>AsString()
public void SetNewFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_NewFields == null)
    {
        _NewFields = new NewFields();
    }
    _NewFields.SetNewFieldsAsString(value);
}

// Standard Getter
public N000Country GetN000Country()
{
    return _N000Country;
}

// Standard Setter
public void SetN000Country(N000Country value)
{
    _N000Country = value;
}

// Get<>AsString()
public string GetN000CountryAsString()
{
    return _N000Country != null ? _N000Country.GetN000CountryAsString() : "";
}

// Set<>AsString()
public void SetN000CountryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_N000Country == null)
    {
        _N000Country = new N000Country();
    }
    _N000Country.SetN000CountryAsString(value);
}

// Standard Getter
public G000Group GetG000Group()
{
    return _G000Group;
}

// Standard Setter
public void SetG000Group(G000Group value)
{
    _G000Group = value;
}

// Get<>AsString()
public string GetG000GroupAsString()
{
    return _G000Group != null ? _G000Group.GetG000GroupAsString() : "";
}

// Set<>AsString()
public void SetG000GroupAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_G000Group == null)
    {
        _G000Group = new G000Group();
    }
    _G000Group.SetG000GroupAsString(value);
}

// Standard Getter
public ReportFileRecord GetReportFileRecord()
{
    return _ReportFileRecord;
}

// Standard Setter
public void SetReportFileRecord(ReportFileRecord value)
{
    _ReportFileRecord = value;
}

// Get<>AsString()
public string GetReportFileRecordAsString()
{
    return _ReportFileRecord != null ? _ReportFileRecord.GetReportFileRecordAsString() : "";
}

// Set<>AsString()
public void SetReportFileRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ReportFileRecord == null)
    {
        _ReportFileRecord = new ReportFileRecord();
    }
    _ReportFileRecord.SetReportFileRecordAsString(value);
}

// Standard Getter
public PrintRecord GetPrintRecord()
{
    return _PrintRecord;
}

// Standard Setter
public void SetPrintRecord(PrintRecord value)
{
    _PrintRecord = value;
}

// Get<>AsString()
public string GetPrintRecordAsString()
{
    return _PrintRecord != null ? _PrintRecord.GetPrintRecordAsString() : "";
}

// Set<>AsString()
public void SetPrintRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_PrintRecord == null)
    {
        _PrintRecord = new PrintRecord();
    }
    _PrintRecord.SetPrintRecordAsString(value);
}

// Standard Getter
public ScheduleRecord GetScheduleRecord()
{
    return _ScheduleRecord;
}

// Standard Setter
public void SetScheduleRecord(ScheduleRecord value)
{
    _ScheduleRecord = value;
}

// Get<>AsString()
public string GetScheduleRecordAsString()
{
    return _ScheduleRecord != null ? _ScheduleRecord.GetScheduleRecordAsString() : "";
}

// Set<>AsString()
public void SetScheduleRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ScheduleRecord == null)
    {
        _ScheduleRecord = new ScheduleRecord();
    }
    _ScheduleRecord.SetScheduleRecordAsString(value);
}

// Standard Getter
public H000Heading GetH000Heading()
{
    return _H000Heading;
}

// Standard Setter
public void SetH000Heading(H000Heading value)
{
    _H000Heading = value;
}

// Get<>AsString()
public string GetH000HeadingAsString()
{
    return _H000Heading != null ? _H000Heading.GetH000HeadingAsString() : "";
}

// Set<>AsString()
public void SetH000HeadingAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_H000Heading == null)
    {
        _H000Heading = new H000Heading();
    }
    _H000Heading.SetH000HeadingAsString(value);
}

// Standard Getter
public D000Detail GetD000Detail()
{
    return _D000Detail;
}

// Standard Setter
public void SetD000Detail(D000Detail value)
{
    _D000Detail = value;
}

// Get<>AsString()
public string GetD000DetailAsString()
{
    return _D000Detail != null ? _D000Detail.GetD000DetailAsString() : "";
}

// Set<>AsString()
public void SetD000DetailAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D000Detail == null)
    {
        _D000Detail = new D000Detail();
    }
    _D000Detail.SetD000DetailAsString(value);
}

// Standard Getter
public G000Detail GetG000Detail()
{
    return _G000Detail;
}

// Standard Setter
public void SetG000Detail(G000Detail value)
{
    _G000Detail = value;
}

// Get<>AsString()
public string GetG000DetailAsString()
{
    return _G000Detail != null ? _G000Detail.GetG000DetailAsString() : "";
}

// Set<>AsString()
public void SetG000DetailAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_G000Detail == null)
    {
        _G000Detail = new G000Detail();
    }
    _G000Detail.SetG000DetailAsString(value);
}

// Standard Getter
public E000Detail GetE000Detail()
{
    return _E000Detail;
}

// Standard Setter
public void SetE000Detail(E000Detail value)
{
    _E000Detail = value;
}

// Get<>AsString()
public string GetE000DetailAsString()
{
    return _E000Detail != null ? _E000Detail.GetE000DetailAsString() : "";
}

// Set<>AsString()
public void SetE000DetailAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_E000Detail == null)
    {
        _E000Detail = new E000Detail();
    }
    _E000Detail.SetE000DetailAsString(value);
}

// Standard Getter
public EBondDetail GetEBondDetail()
{
    return _EBondDetail;
}

// Standard Setter
public void SetEBondDetail(EBondDetail value)
{
    _EBondDetail = value;
}

// Get<>AsString()
public string GetEBondDetailAsString()
{
    return _EBondDetail != null ? _EBondDetail.GetEBondDetailAsString() : "";
}

// Set<>AsString()
public void SetEBondDetailAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EBondDetail == null)
    {
        _EBondDetail = new EBondDetail();
    }
    _EBondDetail.SetEBondDetailAsString(value);
}

// Standard Getter
public T000Totals GetT000Totals()
{
    return _T000Totals;
}

// Standard Setter
public void SetT000Totals(T000Totals value)
{
    _T000Totals = value;
}

// Get<>AsString()
public string GetT000TotalsAsString()
{
    return _T000Totals != null ? _T000Totals.GetT000TotalsAsString() : "";
}

// Set<>AsString()
public void SetT000TotalsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_T000Totals == null)
    {
        _T000Totals = new T000Totals();
    }
    _T000Totals.SetT000TotalsAsString(value);
}

// Standard Getter
public F000Footing GetF000Footing()
{
    return _F000Footing;
}

// Standard Setter
public void SetF000Footing(F000Footing value)
{
    _F000Footing = value;
}

// Get<>AsString()
public string GetF000FootingAsString()
{
    return _F000Footing != null ? _F000Footing.GetF000FootingAsString() : "";
}

// Set<>AsString()
public void SetF000FootingAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_F000Footing == null)
    {
        _F000Footing = new F000Footing();
    }
    _F000Footing.SetF000FootingAsString(value);
}

// Standard Getter
public F200Footing GetF200Footing()
{
    return _F200Footing;
}

// Standard Setter
public void SetF200Footing(F200Footing value)
{
    _F200Footing = value;
}

// Get<>AsString()
public string GetF200FootingAsString()
{
    return _F200Footing != null ? _F200Footing.GetF200FootingAsString() : "";
}

// Set<>AsString()
public void SetF200FootingAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_F200Footing == null)
    {
        _F200Footing = new F200Footing();
    }
    _F200Footing.SetF200FootingAsString(value);
}

// Standard Getter
public int GetWctMaxCountries()
{
    return _WctMaxCountries;
}

// Standard Setter
public void SetWctMaxCountries(int value)
{
    _WctMaxCountries = value;
}

// Get<>AsString()
public string GetWctMaxCountriesAsString()
{
    return _WctMaxCountries.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWctMaxCountriesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WctMaxCountries = parsed;
}

// Standard Getter
public WctCountryTable GetWctCountryTable()
{
    return _WctCountryTable;
}

// Standard Setter
public void SetWctCountryTable(WctCountryTable value)
{
    _WctCountryTable = value;
}

// Get<>AsString()
public string GetWctCountryTableAsString()
{
    return _WctCountryTable != null ? _WctCountryTable.GetWctCountryTableAsString() : "";
}

// Set<>AsString()
public void SetWctCountryTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WctCountryTable == null)
    {
        _WctCountryTable = new WctCountryTable();
    }
    _WctCountryTable.SetWctCountryTableAsString(value);
}

// Standard Getter
public WftFundRecord GetWftFundRecord()
{
    return _WftFundRecord;
}

// Standard Setter
public void SetWftFundRecord(WftFundRecord value)
{
    _WftFundRecord = value;
}

// Get<>AsString()
public string GetWftFundRecordAsString()
{
    return _WftFundRecord != null ? _WftFundRecord.GetWftFundRecordAsString() : "";
}

// Set<>AsString()
public void SetWftFundRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WftFundRecord == null)
    {
        _WftFundRecord = new WftFundRecord();
    }
    _WftFundRecord.SetWftFundRecordAsString(value);
}

// Standard Getter
public int GetWgtMaxGroups()
{
    return _WgtMaxGroups;
}

// Standard Setter
public void SetWgtMaxGroups(int value)
{
    _WgtMaxGroups = value;
}

// Get<>AsString()
public string GetWgtMaxGroupsAsString()
{
    return _WgtMaxGroups.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWgtMaxGroupsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WgtMaxGroups = parsed;
}

// Standard Getter
public WgtGroupTable GetWgtGroupTable()
{
    return _WgtGroupTable;
}

// Standard Setter
public void SetWgtGroupTable(WgtGroupTable value)
{
    _WgtGroupTable = value;
}

// Get<>AsString()
public string GetWgtGroupTableAsString()
{
    return _WgtGroupTable != null ? _WgtGroupTable.GetWgtGroupTableAsString() : "";
}

// Set<>AsString()
public void SetWgtGroupTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WgtGroupTable == null)
    {
        _WgtGroupTable = new WgtGroupTable();
    }
    _WgtGroupTable.SetWgtGroupTableAsString(value);
}

// Standard Getter
public WmtMonthTable GetWmtMonthTable()
{
    return _WmtMonthTable;
}

// Standard Setter
public void SetWmtMonthTable(WmtMonthTable value)
{
    _WmtMonthTable = value;
}

// Get<>AsString()
public string GetWmtMonthTableAsString()
{
    return _WmtMonthTable != null ? _WmtMonthTable.GetWmtMonthTableAsString() : "";
}

// Set<>AsString()
public void SetWmtMonthTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WmtMonthTable == null)
    {
        _WmtMonthTable = new WmtMonthTable();
    }
    _WmtMonthTable.SetWmtMonthTableAsString(value);
}

// Standard Getter
public Filler107 GetFiller107()
{
    return _Filler107;
}

// Standard Setter
public void SetFiller107(Filler107 value)
{
    _Filler107 = value;
}

// Get<>AsString()
public string GetFiller107AsString()
{
    return _Filler107 != null ? _Filler107.GetFiller107AsString() : "";
}

// Set<>AsString()
public void SetFiller107AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler107 == null)
    {
        _Filler107 = new Filler107();
    }
    _Filler107.SetFiller107AsString(value);
}

// Standard Getter
public WswSwitches GetWswSwitches()
{
    return _WswSwitches;
}

// Standard Setter
public void SetWswSwitches(WswSwitches value)
{
    _WswSwitches = value;
}

// Get<>AsString()
public string GetWswSwitchesAsString()
{
    return _WswSwitches != null ? _WswSwitches.GetWswSwitchesAsString() : "";
}

// Set<>AsString()
public void SetWswSwitchesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WswSwitches == null)
    {
        _WswSwitches = new WswSwitches();
    }
    _WswSwitches.SetWswSwitchesAsString(value);
}

// Standard Getter
public WtsTotals GetWtsTotals()
{
    return _WtsTotals;
}

// Standard Setter
public void SetWtsTotals(WtsTotals value)
{
    _WtsTotals = value;
}

// Get<>AsString()
public string GetWtsTotalsAsString()
{
    return _WtsTotals != null ? _WtsTotals.GetWtsTotalsAsString() : "";
}

// Set<>AsString()
public void SetWtsTotalsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtsTotals == null)
    {
        _WtsTotals = new WtsTotals();
    }
    _WtsTotals.SetWtsTotalsAsString(value);
}

// Standard Getter
public WwaWorkareas GetWwaWorkareas()
{
    return _WwaWorkareas;
}

// Standard Setter
public void SetWwaWorkareas(WwaWorkareas value)
{
    _WwaWorkareas = value;
}

// Get<>AsString()
public string GetWwaWorkareasAsString()
{
    return _WwaWorkareas != null ? _WwaWorkareas.GetWwaWorkareasAsString() : "";
}

// Set<>AsString()
public void SetWwaWorkareasAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WwaWorkareas == null)
    {
        _WwaWorkareas = new WwaWorkareas();
    }
    _WwaWorkareas.SetWwaWorkareasAsString(value);
}

// Standard Getter
public string GetWTransCat()
{
    return _WTransCat;
}

// Standard Setter
public void SetWTransCat(string value)
{
    _WTransCat = value;
}

// Get<>AsString()
public string GetWTransCatAsString()
{
    return _WTransCat.PadRight(0);
}

// Set<>AsString()
public void SetWTransCatAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WTransCat = value;
}

// Standard Getter
public CheckCatLinkage GetCheckCatLinkage()
{
    return _CheckCatLinkage;
}

// Standard Setter
public void SetCheckCatLinkage(CheckCatLinkage value)
{
    _CheckCatLinkage = value;
}

// Get<>AsString()
public string GetCheckCatLinkageAsString()
{
    return _CheckCatLinkage != null ? _CheckCatLinkage.GetCheckCatLinkageAsString() : "";
}

// Set<>AsString()
public void SetCheckCatLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CheckCatLinkage == null)
    {
        _CheckCatLinkage = new CheckCatLinkage();
    }
    _CheckCatLinkage.SetCheckCatLinkageAsString(value);
}

// Standard Getter
public string GetWConfigItem()
{
    return _WConfigItem;
}

// Standard Setter
public void SetWConfigItem(string value)
{
    _WConfigItem = value;
}

// Get<>AsString()
public string GetWConfigItemAsString()
{
    return _WConfigItem.PadRight(10);
}

// Set<>AsString()
public void SetWConfigItemAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WConfigItem = value;
}

// Standard Getter
public ElcgmioLinkage1 GetElcgmioLinkage1()
{
    return _ElcgmioLinkage1;
}

// Standard Setter
public void SetElcgmioLinkage1(ElcgmioLinkage1 value)
{
    _ElcgmioLinkage1 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage1AsString()
{
    return _ElcgmioLinkage1 != null ? _ElcgmioLinkage1.GetElcgmioLinkage1AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage1 == null)
    {
        _ElcgmioLinkage1 = new ElcgmioLinkage1();
    }
    _ElcgmioLinkage1.SetElcgmioLinkage1AsString(value);
}

// Standard Getter
public ElcgmioLinkage2 GetElcgmioLinkage2()
{
    return _ElcgmioLinkage2;
}

// Standard Setter
public void SetElcgmioLinkage2(ElcgmioLinkage2 value)
{
    _ElcgmioLinkage2 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage2AsString()
{
    return _ElcgmioLinkage2 != null ? _ElcgmioLinkage2.GetElcgmioLinkage2AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage2 == null)
    {
        _ElcgmioLinkage2 = new ElcgmioLinkage2();
    }
    _ElcgmioLinkage2.SetElcgmioLinkage2AsString(value);
}

// Standard Getter
public string GetWRealisedSchedule()
{
    return _WRealisedSchedule;
}

// Standard Setter
public void SetWRealisedSchedule(string value)
{
    _WRealisedSchedule = value;
}

// Get<>AsString()
public string GetWRealisedScheduleAsString()
{
    return _WRealisedSchedule.PadRight(0);
}

// Set<>AsString()
public void SetWRealisedScheduleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WRealisedSchedule = value;
}

// Standard Getter
public string GetWRealisedBondSchedule()
{
    return _WRealisedBondSchedule;
}

// Standard Setter
public void SetWRealisedBondSchedule(string value)
{
    _WRealisedBondSchedule = value;
}

// Get<>AsString()
public string GetWRealisedBondScheduleAsString()
{
    return _WRealisedBondSchedule.PadRight(0);
}

// Set<>AsString()
public void SetWRealisedBondScheduleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WRealisedBondSchedule = value;
}

public void SyncD000DetailFromG000Detail()
{
    this.SetD000DetailAsString(this.GetG000DetailAsString());
}

public void SyncG000DetailFromD000Detail()
{
    this.SetG000DetailAsString(this.GetD000DetailAsString());
}
public void SyncD000DetailFromE000Detail()
{
    this.SetD000DetailAsString(this.GetE000DetailAsString());
}

public void SyncE000DetailFromD000Detail()
{
    this.SetE000DetailAsString(this.GetD000DetailAsString());
}
public void SyncD000DetailFromEBondDetail()
{
    this.SetD000DetailAsString(this.GetEBondDetailAsString());
}

public void SyncEBondDetailFromD000Detail()
{
    this.SetEBondDetailAsString(this.GetD000DetailAsString());
}
public void SyncWmtMonthTableFromFiller107()
{
    this.SetWmtMonthTableAsString(this.GetFiller107AsString());
}

public void SyncFiller107FromWmtMonthTable()
{
    this.SetFiller107AsString(this.GetWmtMonthTableAsString());
}


}}
