using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing EquityParameters Data Structure

public class EquityParameters
{
    private static int _size = 331;
    // [DEBUG] Class: EquityParameters, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: EquityGeneralParms, is_external=, is_static_class=False, static_prefix=
    private EquityGeneralParms _EquityGeneralParms = new EquityGeneralParms();
    
    
    
    
    // [DEBUG] Field: EquityCalcParameters, is_external=, is_static_class=False, static_prefix=
    private EquityCalcParameters _EquityCalcParameters = new EquityCalcParameters();
    
    
    
    
    // [DEBUG] Field: EquityBatchParameters, is_external=, is_static_class=False, static_prefix=
    private EquityBatchParameters _EquityBatchParameters = new EquityBatchParameters();
    
    
    
    
    
    // Serialization methods
    public string GetEquityParametersAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EquityGeneralParms.GetEquityGeneralParmsAsString());
        result.Append(_EquityCalcParameters.GetEquityCalcParametersAsString());
        result.Append(_EquityBatchParameters.GetEquityBatchParametersAsString());
        
        return result.ToString();
    }
    
    public void SetEquityParametersAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _EquityGeneralParms.SetEquityGeneralParmsAsString(data.Substring(offset, 6));
        }
        else
        {
            _EquityGeneralParms.SetEquityGeneralParmsAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 65 <= data.Length)
        {
            _EquityCalcParameters.SetEquityCalcParametersAsString(data.Substring(offset, 65));
        }
        else
        {
            _EquityCalcParameters.SetEquityCalcParametersAsString(data.Substring(offset));
        }
        offset += 65;
        if (offset + 260 <= data.Length)
        {
            _EquityBatchParameters.SetEquityBatchParametersAsString(data.Substring(offset, 260));
        }
        else
        {
            _EquityBatchParameters.SetEquityBatchParametersAsString(data.Substring(offset));
        }
        offset += 260;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetEquityParametersAsString();
    }
    // Set<>String Override function
    public void SetEquityParameters(string value)
    {
        SetEquityParametersAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public EquityGeneralParms GetEquityGeneralParms()
    {
        return _EquityGeneralParms;
    }
    
    // Standard Setter
    public void SetEquityGeneralParms(EquityGeneralParms value)
    {
        _EquityGeneralParms = value;
    }
    
    // Get<>AsString()
    public string GetEquityGeneralParmsAsString()
    {
        return _EquityGeneralParms != null ? _EquityGeneralParms.GetEquityGeneralParmsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetEquityGeneralParmsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_EquityGeneralParms == null)
        {
            _EquityGeneralParms = new EquityGeneralParms();
        }
        _EquityGeneralParms.SetEquityGeneralParmsAsString(value);
    }
    
    // Standard Getter
    public EquityCalcParameters GetEquityCalcParameters()
    {
        return _EquityCalcParameters;
    }
    
    // Standard Setter
    public void SetEquityCalcParameters(EquityCalcParameters value)
    {
        _EquityCalcParameters = value;
    }
    
    // Get<>AsString()
    public string GetEquityCalcParametersAsString()
    {
        return _EquityCalcParameters != null ? _EquityCalcParameters.GetEquityCalcParametersAsString() : "";
    }
    
    // Set<>AsString()
    public void SetEquityCalcParametersAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_EquityCalcParameters == null)
        {
            _EquityCalcParameters = new EquityCalcParameters();
        }
        _EquityCalcParameters.SetEquityCalcParametersAsString(value);
    }
    
    // Standard Getter
    public EquityBatchParameters GetEquityBatchParameters()
    {
        return _EquityBatchParameters;
    }
    
    // Standard Setter
    public void SetEquityBatchParameters(EquityBatchParameters value)
    {
        _EquityBatchParameters = value;
    }
    
    // Get<>AsString()
    public string GetEquityBatchParametersAsString()
    {
        return _EquityBatchParameters != null ? _EquityBatchParameters.GetEquityBatchParametersAsString() : "";
    }
    
    // Set<>AsString()
    public void SetEquityBatchParametersAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_EquityBatchParameters == null)
        {
            _EquityBatchParameters = new EquityBatchParameters();
        }
        _EquityBatchParameters.SetEquityBatchParametersAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetEquityGeneralParms(string value)
    {
        _EquityGeneralParms.SetEquityGeneralParmsAsString(value);
    }
    // Nested Class: EquityGeneralParms
    public class EquityGeneralParms
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: EquityUserNo, is_external=, is_static_class=False, static_prefix=
        private string _EquityUserNo ="";
        
        
        
        
        // [DEBUG] Field: EquityYear, is_external=, is_static_class=False, static_prefix=
        private string _EquityYear ="";
        
        
        
        
        // [DEBUG] Field: EquityReportId, is_external=, is_static_class=False, static_prefix=
        private string _EquityReportId ="";
        
        
        
        
        // [DEBUG] Field: EquityUnrealisedGainsFlag, is_external=, is_static_class=False, static_prefix=
        private string _EquityUnrealisedGainsFlag ="";
        
        
        // 88-level condition checks for EquityUnrealisedGainsFlag
        public bool IsUnrealisedGains()
        {
            if (this._EquityUnrealisedGainsFlag == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: EquityLogSwitch, is_external=, is_static_class=False, static_prefix=
        private string _EquityLogSwitch ="";
        
        
        
        
        // [DEBUG] Field: EquityReturnCodeX, is_external=, is_static_class=False, static_prefix=
        private string _EquityReturnCodeX ="";
        
        
        
        
        // [DEBUG] Field: EquityReturnCode, is_external=, is_static_class=False, static_prefix=
        private int _EquityReturnCode =0;
        
        
        
        
    public EquityGeneralParms() {}
    
    public EquityGeneralParms(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetEquityUserNo(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEquityYear(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEquityReportId(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEquityUnrealisedGainsFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEquityLogSwitch(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEquityReturnCodeX(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEquityReturnCode(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetEquityGeneralParmsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EquityUserNo.PadRight(0));
        result.Append(_EquityYear.PadRight(0));
        result.Append(_EquityReportId.PadRight(0));
        result.Append(_EquityUnrealisedGainsFlag.PadRight(0));
        result.Append(_EquityLogSwitch.PadRight(0));
        result.Append(_EquityReturnCodeX.PadRight(0));
        result.Append(_EquityReturnCode.ToString().PadLeft(6, '0'));
        
        return result.ToString();
    }
    
    public void SetEquityGeneralParmsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityUserNo(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityYear(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityReportId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityUnrealisedGainsFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityLogSwitch(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityReturnCodeX(extracted);
        }
        offset += 0;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetEquityReturnCode(parsedInt);
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetEquityUserNo()
    {
        return _EquityUserNo;
    }
    
    // Standard Setter
    public void SetEquityUserNo(string value)
    {
        _EquityUserNo = value;
    }
    
    // Get<>AsString()
    public string GetEquityUserNoAsString()
    {
        return _EquityUserNo.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityUserNo = value;
    }
    
    // Standard Getter
    public string GetEquityYear()
    {
        return _EquityYear;
    }
    
    // Standard Setter
    public void SetEquityYear(string value)
    {
        _EquityYear = value;
    }
    
    // Get<>AsString()
    public string GetEquityYearAsString()
    {
        return _EquityYear.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityYear = value;
    }
    
    // Standard Getter
    public string GetEquityReportId()
    {
        return _EquityReportId;
    }
    
    // Standard Setter
    public void SetEquityReportId(string value)
    {
        _EquityReportId = value;
    }
    
    // Get<>AsString()
    public string GetEquityReportIdAsString()
    {
        return _EquityReportId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityReportIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityReportId = value;
    }
    
    // Standard Getter
    public string GetEquityUnrealisedGainsFlag()
    {
        return _EquityUnrealisedGainsFlag;
    }
    
    // Standard Setter
    public void SetEquityUnrealisedGainsFlag(string value)
    {
        _EquityUnrealisedGainsFlag = value;
    }
    
    // Get<>AsString()
    public string GetEquityUnrealisedGainsFlagAsString()
    {
        return _EquityUnrealisedGainsFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityUnrealisedGainsFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityUnrealisedGainsFlag = value;
    }
    
    // Standard Getter
    public string GetEquityLogSwitch()
    {
        return _EquityLogSwitch;
    }
    
    // Standard Setter
    public void SetEquityLogSwitch(string value)
    {
        _EquityLogSwitch = value;
    }
    
    // Get<>AsString()
    public string GetEquityLogSwitchAsString()
    {
        return _EquityLogSwitch.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityLogSwitchAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityLogSwitch = value;
    }
    
    // Standard Getter
    public string GetEquityReturnCodeX()
    {
        return _EquityReturnCodeX;
    }
    
    // Standard Setter
    public void SetEquityReturnCodeX(string value)
    {
        _EquityReturnCodeX = value;
    }
    
    // Get<>AsString()
    public string GetEquityReturnCodeXAsString()
    {
        return _EquityReturnCodeX.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityReturnCodeXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityReturnCodeX = value;
    }
    
    // Standard Getter
    public int GetEquityReturnCode()
    {
        return _EquityReturnCode;
    }
    
    // Standard Setter
    public void SetEquityReturnCode(int value)
    {
        _EquityReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetEquityReturnCodeAsString()
    {
        return _EquityReturnCode.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetEquityReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _EquityReturnCode = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetEquityCalcParameters(string value)
{
    _EquityCalcParameters.SetEquityCalcParametersAsString(value);
}
// Nested Class: EquityCalcParameters
public class EquityCalcParameters
{
    private static int _size = 65;
    
    // Fields in the class
    
    
    // [DEBUG] Field: EquityFund, is_external=, is_static_class=False, static_prefix=
    private string _EquityFund ="";
    
    
    
    
    // [DEBUG] Field: EquitySedol, is_external=, is_static_class=False, static_prefix=
    private string _EquitySedol ="";
    
    
    
    
    // [DEBUG] Field: EquityDisposalDate, is_external=, is_static_class=False, static_prefix=
    private string _EquityDisposalDate ="";
    
    
    
    
    // [DEBUG] Field: EquityMarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _EquityMarketPrice =0;
    
    
    
    
    // [DEBUG] Field: EquityParentPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _EquityParentPrice =0;
    
    
    
    
    // [DEBUG] Field: EquityUnitsDisposed, is_external=, is_static_class=False, static_prefix=
    private decimal _EquityUnitsDisposed =0;
    
    
    
    
    // [DEBUG] Field: EquityUnitsDisposedX, is_external=, is_static_class=False, static_prefix=
    private string _EquityUnitsDisposedX ="";
    
    
    
    
    // [DEBUG] Field: EquityProfit, is_external=, is_static_class=False, static_prefix=
    private decimal _EquityProfit =0;
    
    
    
    
    // [DEBUG] Field: EquityGain, is_external=, is_static_class=False, static_prefix=
    private decimal _EquityGain =0;
    
    
    
    
public EquityCalcParameters() {}

public EquityCalcParameters(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetEquityFund(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquitySedol(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityDisposalDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetEquityParentPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetEquityUnitsDisposed(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetEquityUnitsDisposedX(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityProfit(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetEquityGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    
}

// Serialization methods
public string GetEquityCalcParametersAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_EquityFund.PadRight(0));
    result.Append(_EquitySedol.PadRight(0));
    result.Append(_EquityDisposalDate.PadRight(0));
    result.Append(_EquityMarketPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_EquityParentPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_EquityUnitsDisposed.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_EquityUnitsDisposedX.PadRight(0));
    result.Append(_EquityProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_EquityGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetEquityCalcParametersAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityFund(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquitySedol(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityDisposalDate(extracted);
    }
    offset += 0;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetEquityMarketPrice(parsedDec);
    }
    offset += 11;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetEquityParentPrice(parsedDec);
    }
    offset += 11;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetEquityUnitsDisposed(parsedDec);
    }
    offset += 13;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityUnitsDisposedX(extracted);
    }
    offset += 0;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetEquityProfit(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetEquityGain(parsedDec);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public string GetEquityFund()
{
    return _EquityFund;
}

// Standard Setter
public void SetEquityFund(string value)
{
    _EquityFund = value;
}

// Get<>AsString()
public string GetEquityFundAsString()
{
    return _EquityFund.PadRight(0);
}

// Set<>AsString()
public void SetEquityFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityFund = value;
}

// Standard Getter
public string GetEquitySedol()
{
    return _EquitySedol;
}

// Standard Setter
public void SetEquitySedol(string value)
{
    _EquitySedol = value;
}

// Get<>AsString()
public string GetEquitySedolAsString()
{
    return _EquitySedol.PadRight(0);
}

// Set<>AsString()
public void SetEquitySedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquitySedol = value;
}

// Standard Getter
public string GetEquityDisposalDate()
{
    return _EquityDisposalDate;
}

// Standard Setter
public void SetEquityDisposalDate(string value)
{
    _EquityDisposalDate = value;
}

// Get<>AsString()
public string GetEquityDisposalDateAsString()
{
    return _EquityDisposalDate.PadRight(0);
}

// Set<>AsString()
public void SetEquityDisposalDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityDisposalDate = value;
}

// Standard Getter
public decimal GetEquityMarketPrice()
{
    return _EquityMarketPrice;
}

// Standard Setter
public void SetEquityMarketPrice(decimal value)
{
    _EquityMarketPrice = value;
}

// Get<>AsString()
public string GetEquityMarketPriceAsString()
{
    return _EquityMarketPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetEquityMarketPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _EquityMarketPrice = parsed;
}

// Standard Getter
public decimal GetEquityParentPrice()
{
    return _EquityParentPrice;
}

// Standard Setter
public void SetEquityParentPrice(decimal value)
{
    _EquityParentPrice = value;
}

// Get<>AsString()
public string GetEquityParentPriceAsString()
{
    return _EquityParentPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetEquityParentPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _EquityParentPrice = parsed;
}

// Standard Getter
public decimal GetEquityUnitsDisposed()
{
    return _EquityUnitsDisposed;
}

// Standard Setter
public void SetEquityUnitsDisposed(decimal value)
{
    _EquityUnitsDisposed = value;
}

// Get<>AsString()
public string GetEquityUnitsDisposedAsString()
{
    return _EquityUnitsDisposed.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetEquityUnitsDisposedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _EquityUnitsDisposed = parsed;
}

// Standard Getter
public string GetEquityUnitsDisposedX()
{
    return _EquityUnitsDisposedX;
}

// Standard Setter
public void SetEquityUnitsDisposedX(string value)
{
    _EquityUnitsDisposedX = value;
}

// Get<>AsString()
public string GetEquityUnitsDisposedXAsString()
{
    return _EquityUnitsDisposedX.PadRight(0);
}

// Set<>AsString()
public void SetEquityUnitsDisposedXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityUnitsDisposedX = value;
}

// Standard Getter
public decimal GetEquityProfit()
{
    return _EquityProfit;
}

// Standard Setter
public void SetEquityProfit(decimal value)
{
    _EquityProfit = value;
}

// Get<>AsString()
public string GetEquityProfitAsString()
{
    return _EquityProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetEquityProfitAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _EquityProfit = parsed;
}

// Standard Getter
public decimal GetEquityGain()
{
    return _EquityGain;
}

// Standard Setter
public void SetEquityGain(decimal value)
{
    _EquityGain = value;
}

// Get<>AsString()
public string GetEquityGainAsString()
{
    return _EquityGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetEquityGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _EquityGain = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetEquityBatchParameters(string value)
{
    _EquityBatchParameters.SetEquityBatchParametersAsString(value);
}
// Nested Class: EquityBatchParameters
public class EquityBatchParameters
{
    private static int _size = 260;
    
    // Fields in the class
    
    
    // [DEBUG] Field: EquityAction, is_external=, is_static_class=False, static_prefix=
    private int _EquityAction =0;
    
    
    
    
    // [DEBUG] Field: EquityFileName, is_external=, is_static_class=False, static_prefix=
    private string _EquityFileName ="";
    
    
    
    
    // [DEBUG] Field: EquityReplaceRecords, is_external=, is_static_class=False, static_prefix=
    private string _EquityReplaceRecords ="";
    
    
    
    
    // [DEBUG] Field: EquityAllFunds, is_external=, is_static_class=False, static_prefix=
    private string _EquityAllFunds ="";
    
    
    // 88-level condition checks for EquityAllFunds
    public bool IsAllFunds()
    {
        if (this._EquityAllFunds == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityStartDate, is_external=, is_static_class=False, static_prefix=
    private string _EquityStartDate ="";
    
    
    
    
    // [DEBUG] Field: EquityEndDate, is_external=, is_static_class=False, static_prefix=
    private string _EquityEndDate ="";
    
    
    
    
    // [DEBUG] Field: EquityFullReport, is_external=, is_static_class=False, static_prefix=
    private string _EquityFullReport ="";
    
    
    
    
    // [DEBUG] Field: EquityGenerationNo, is_external=, is_static_class=False, static_prefix=
    private string _EquityGenerationNo ="";
    
    
    
    
    // [DEBUG] Field: EquitySuppressCgtr04, is_external=, is_static_class=False, static_prefix=
    private string _EquitySuppressCgtr04 ="";
    
    
    
    
    // [DEBUG] Field: EquitySegregateAssets, is_external=, is_static_class=False, static_prefix=
    private string _EquitySegregateAssets ="";
    
    
    // 88-level condition checks for EquitySegregateAssets
    public bool IsSegregateAssets()
    {
        if (this._EquitySegregateAssets == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityScheduleType, is_external=, is_static_class=False, static_prefix=
    private string _EquityScheduleType ="";
    
    
    // 88-level condition checks for EquityScheduleType
    public bool IsRealisedSchedule()
    {
        if (this._EquityScheduleType == "'R'") return true;
        return false;
    }
    public bool IsUnrealisedSchedule()
    {
        if (this._EquityScheduleType == "'U'") return true;
        return false;
    }
    public bool IsDeemedDisposalSchedule()
    {
        if (this._EquityScheduleType == "'N'") return true;
        return false;
    }
    public bool IsRealisedSchdSchedule()
    {
        if (this._EquityScheduleType == "'T'") return true;
        return false;
    }
    public bool IsUnrealisedSchdSchedule()
    {
        if (this._EquityScheduleType == "'X'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityAnalysisSchedule, is_external=, is_static_class=False, static_prefix=
    private string _EquityAnalysisSchedule ="";
    
    
    // 88-level condition checks for EquityAnalysisSchedule
    public bool IsAnalysisSchedule()
    {
        if (this._EquityAnalysisSchedule == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityFromYear, is_external=, is_static_class=False, static_prefix=
    private string _EquityFromYear ="";
    
    
    
    
    // [DEBUG] Field: EquityToYear, is_external=, is_static_class=False, static_prefix=
    private string _EquityToYear ="";
    
    
    
    
    // [DEBUG] Field: EquityFromPriceDate, is_external=, is_static_class=False, static_prefix=
    private string _EquityFromPriceDate ="";
    
    
    
    
    // [DEBUG] Field: EquityToPriceDate, is_external=, is_static_class=False, static_prefix=
    private string _EquityToPriceDate ="";
    
    
    
    
    // [DEBUG] Field: EquityRealisedVersion, is_external=, is_static_class=False, static_prefix=
    private string _EquityRealisedVersion ="";
    
    
    // 88-level condition checks for EquityRealisedVersion
    public bool IsRealisedVersion()
    {
        if (this._EquityRealisedVersion == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityShowSedolNumbers, is_external=, is_static_class=False, static_prefix=
    private string _EquityShowSedolNumbers ="";
    
    
    // 88-level condition checks for EquityShowSedolNumbers
    public bool IsShowSedolNumbers()
    {
        if (this._EquityShowSedolNumbers == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityCountryExporter, is_external=, is_static_class=False, static_prefix=
    private string _EquityCountryExporter ="";
    
    
    // 88-level condition checks for EquityCountryExporter
    public bool IsCountryExporter()
    {
        if (this._EquityCountryExporter == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityGroupExporter, is_external=, is_static_class=False, static_prefix=
    private string _EquityGroupExporter ="";
    
    
    // 88-level condition checks for EquityGroupExporter
    public bool IsGroupExporter()
    {
        if (this._EquityGroupExporter == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityFundExporter, is_external=, is_static_class=False, static_prefix=
    private string _EquityFundExporter ="";
    
    
    // 88-level condition checks for EquityFundExporter
    public bool IsFundExporter()
    {
        if (this._EquityFundExporter == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityStockExporter, is_external=, is_static_class=False, static_prefix=
    private string _EquityStockExporter ="";
    
    
    // 88-level condition checks for EquityStockExporter
    public bool IsStockExporter()
    {
        if (this._EquityStockExporter == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityPriceExporter, is_external=, is_static_class=False, static_prefix=
    private string _EquityPriceExporter ="";
    
    
    // 88-level condition checks for EquityPriceExporter
    public bool IsPriceExporter()
    {
        if (this._EquityPriceExporter == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityCgtscot1Format, is_external=, is_static_class=False, static_prefix=
    private string _EquityCgtscot1Format ="";
    
    
    
    
    // [DEBUG] Field: EquityShowSedolNumbersLr, is_external=, is_static_class=False, static_prefix=
    private string _EquityShowSedolNumbersLr ="";
    
    
    // 88-level condition checks for EquityShowSedolNumbersLr
    public bool IsShowSedolNumbersLr()
    {
        if (this._EquityShowSedolNumbersLr == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityDerivatives, is_external=, is_static_class=False, static_prefix=
    private string _EquityDerivatives ="";
    
    
    // 88-level condition checks for EquityDerivatives
    public bool IsDerivatives()
    {
        if (this._EquityDerivatives == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityIrishCgt, is_external=, is_static_class=False, static_prefix=
    private string _EquityIrishCgt ="";
    
    
    // 88-level condition checks for EquityIrishCgt
    public bool IsIrishCgt()
    {
        if (this._EquityIrishCgt == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: EquityIrishCgtProRata, is_external=, is_static_class=False, static_prefix=
    private string _EquityIrishCgtProRata ="";
    
    
    // 88-level condition checks for EquityIrishCgtProRata
    public bool IsIrishCgtProRata()
    {
        if (this._EquityIrishCgtProRata == "'Y'") return true;
        return false;
    }
    
    
public EquityBatchParameters() {}

public EquityBatchParameters(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetEquityAction(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetEquityFileName(data.Substring(offset, 256).Trim());
    offset += 256;
    SetEquityReplaceRecords(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityAllFunds(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityStartDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityEndDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityFullReport(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityGenerationNo(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquitySuppressCgtr04(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquitySegregateAssets(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityScheduleType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityAnalysisSchedule(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityFromYear(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityToYear(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityFromPriceDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityToPriceDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityRealisedVersion(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityShowSedolNumbers(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityCountryExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityGroupExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityFundExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityStockExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityPriceExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityCgtscot1Format(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityShowSedolNumbersLr(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityDerivatives(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityIrishCgt(data.Substring(offset, 0).Trim());
    offset += 0;
    SetEquityIrishCgtProRata(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetEquityBatchParametersAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_EquityAction.ToString().PadLeft(4, '0'));
    result.Append(_EquityFileName.PadRight(256));
    result.Append(_EquityReplaceRecords.PadRight(0));
    result.Append(_EquityAllFunds.PadRight(0));
    result.Append(_EquityStartDate.PadRight(0));
    result.Append(_EquityEndDate.PadRight(0));
    result.Append(_EquityFullReport.PadRight(0));
    result.Append(_EquityGenerationNo.PadRight(0));
    result.Append(_EquitySuppressCgtr04.PadRight(0));
    result.Append(_EquitySegregateAssets.PadRight(0));
    result.Append(_EquityScheduleType.PadRight(0));
    result.Append(_EquityAnalysisSchedule.PadRight(0));
    result.Append(_EquityFromYear.PadRight(0));
    result.Append(_EquityToYear.PadRight(0));
    result.Append(_EquityFromPriceDate.PadRight(0));
    result.Append(_EquityToPriceDate.PadRight(0));
    result.Append(_EquityRealisedVersion.PadRight(0));
    result.Append(_EquityShowSedolNumbers.PadRight(0));
    result.Append(_EquityCountryExporter.PadRight(0));
    result.Append(_EquityGroupExporter.PadRight(0));
    result.Append(_EquityFundExporter.PadRight(0));
    result.Append(_EquityStockExporter.PadRight(0));
    result.Append(_EquityPriceExporter.PadRight(0));
    result.Append(_EquityCgtscot1Format.PadRight(0));
    result.Append(_EquityShowSedolNumbersLr.PadRight(0));
    result.Append(_EquityDerivatives.PadRight(0));
    result.Append(_EquityIrishCgt.PadRight(0));
    result.Append(_EquityIrishCgtProRata.PadRight(0));
    
    return result.ToString();
}

public void SetEquityBatchParametersAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetEquityAction(parsedInt);
    }
    offset += 4;
    if (offset + 256 <= data.Length)
    {
        string extracted = data.Substring(offset, 256).Trim();
        SetEquityFileName(extracted);
    }
    offset += 256;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityReplaceRecords(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityAllFunds(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityStartDate(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityEndDate(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityFullReport(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityGenerationNo(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquitySuppressCgtr04(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquitySegregateAssets(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityScheduleType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityAnalysisSchedule(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityFromYear(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityToYear(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityFromPriceDate(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityToPriceDate(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityRealisedVersion(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityShowSedolNumbers(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityCountryExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityGroupExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityFundExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityStockExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityPriceExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityCgtscot1Format(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityShowSedolNumbersLr(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityDerivatives(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityIrishCgt(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetEquityIrishCgtProRata(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public int GetEquityAction()
{
    return _EquityAction;
}

// Standard Setter
public void SetEquityAction(int value)
{
    _EquityAction = value;
}

// Get<>AsString()
public string GetEquityActionAsString()
{
    return _EquityAction.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetEquityActionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _EquityAction = parsed;
}

// Standard Getter
public string GetEquityFileName()
{
    return _EquityFileName;
}

// Standard Setter
public void SetEquityFileName(string value)
{
    _EquityFileName = value;
}

// Get<>AsString()
public string GetEquityFileNameAsString()
{
    return _EquityFileName.PadRight(256);
}

// Set<>AsString()
public void SetEquityFileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityFileName = value;
}

// Standard Getter
public string GetEquityReplaceRecords()
{
    return _EquityReplaceRecords;
}

// Standard Setter
public void SetEquityReplaceRecords(string value)
{
    _EquityReplaceRecords = value;
}

// Get<>AsString()
public string GetEquityReplaceRecordsAsString()
{
    return _EquityReplaceRecords.PadRight(0);
}

// Set<>AsString()
public void SetEquityReplaceRecordsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityReplaceRecords = value;
}

// Standard Getter
public string GetEquityAllFunds()
{
    return _EquityAllFunds;
}

// Standard Setter
public void SetEquityAllFunds(string value)
{
    _EquityAllFunds = value;
}

// Get<>AsString()
public string GetEquityAllFundsAsString()
{
    return _EquityAllFunds.PadRight(0);
}

// Set<>AsString()
public void SetEquityAllFundsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityAllFunds = value;
}

// Standard Getter
public string GetEquityStartDate()
{
    return _EquityStartDate;
}

// Standard Setter
public void SetEquityStartDate(string value)
{
    _EquityStartDate = value;
}

// Get<>AsString()
public string GetEquityStartDateAsString()
{
    return _EquityStartDate.PadRight(0);
}

// Set<>AsString()
public void SetEquityStartDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityStartDate = value;
}

// Standard Getter
public string GetEquityEndDate()
{
    return _EquityEndDate;
}

// Standard Setter
public void SetEquityEndDate(string value)
{
    _EquityEndDate = value;
}

// Get<>AsString()
public string GetEquityEndDateAsString()
{
    return _EquityEndDate.PadRight(0);
}

// Set<>AsString()
public void SetEquityEndDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityEndDate = value;
}

// Standard Getter
public string GetEquityFullReport()
{
    return _EquityFullReport;
}

// Standard Setter
public void SetEquityFullReport(string value)
{
    _EquityFullReport = value;
}

// Get<>AsString()
public string GetEquityFullReportAsString()
{
    return _EquityFullReport.PadRight(0);
}

// Set<>AsString()
public void SetEquityFullReportAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityFullReport = value;
}

// Standard Getter
public string GetEquityGenerationNo()
{
    return _EquityGenerationNo;
}

// Standard Setter
public void SetEquityGenerationNo(string value)
{
    _EquityGenerationNo = value;
}

// Get<>AsString()
public string GetEquityGenerationNoAsString()
{
    return _EquityGenerationNo.PadRight(0);
}

// Set<>AsString()
public void SetEquityGenerationNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityGenerationNo = value;
}

// Standard Getter
public string GetEquitySuppressCgtr04()
{
    return _EquitySuppressCgtr04;
}

// Standard Setter
public void SetEquitySuppressCgtr04(string value)
{
    _EquitySuppressCgtr04 = value;
}

// Get<>AsString()
public string GetEquitySuppressCgtr04AsString()
{
    return _EquitySuppressCgtr04.PadRight(0);
}

// Set<>AsString()
public void SetEquitySuppressCgtr04AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquitySuppressCgtr04 = value;
}

// Standard Getter
public string GetEquitySegregateAssets()
{
    return _EquitySegregateAssets;
}

// Standard Setter
public void SetEquitySegregateAssets(string value)
{
    _EquitySegregateAssets = value;
}

// Get<>AsString()
public string GetEquitySegregateAssetsAsString()
{
    return _EquitySegregateAssets.PadRight(0);
}

// Set<>AsString()
public void SetEquitySegregateAssetsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquitySegregateAssets = value;
}

// Standard Getter
public string GetEquityScheduleType()
{
    return _EquityScheduleType;
}

// Standard Setter
public void SetEquityScheduleType(string value)
{
    _EquityScheduleType = value;
}

// Get<>AsString()
public string GetEquityScheduleTypeAsString()
{
    return _EquityScheduleType.PadRight(0);
}

// Set<>AsString()
public void SetEquityScheduleTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityScheduleType = value;
}

// Standard Getter
public string GetEquityAnalysisSchedule()
{
    return _EquityAnalysisSchedule;
}

// Standard Setter
public void SetEquityAnalysisSchedule(string value)
{
    _EquityAnalysisSchedule = value;
}

// Get<>AsString()
public string GetEquityAnalysisScheduleAsString()
{
    return _EquityAnalysisSchedule.PadRight(0);
}

// Set<>AsString()
public void SetEquityAnalysisScheduleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityAnalysisSchedule = value;
}

// Standard Getter
public string GetEquityFromYear()
{
    return _EquityFromYear;
}

// Standard Setter
public void SetEquityFromYear(string value)
{
    _EquityFromYear = value;
}

// Get<>AsString()
public string GetEquityFromYearAsString()
{
    return _EquityFromYear.PadRight(0);
}

// Set<>AsString()
public void SetEquityFromYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityFromYear = value;
}

// Standard Getter
public string GetEquityToYear()
{
    return _EquityToYear;
}

// Standard Setter
public void SetEquityToYear(string value)
{
    _EquityToYear = value;
}

// Get<>AsString()
public string GetEquityToYearAsString()
{
    return _EquityToYear.PadRight(0);
}

// Set<>AsString()
public void SetEquityToYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityToYear = value;
}

// Standard Getter
public string GetEquityFromPriceDate()
{
    return _EquityFromPriceDate;
}

// Standard Setter
public void SetEquityFromPriceDate(string value)
{
    _EquityFromPriceDate = value;
}

// Get<>AsString()
public string GetEquityFromPriceDateAsString()
{
    return _EquityFromPriceDate.PadRight(0);
}

// Set<>AsString()
public void SetEquityFromPriceDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityFromPriceDate = value;
}

// Standard Getter
public string GetEquityToPriceDate()
{
    return _EquityToPriceDate;
}

// Standard Setter
public void SetEquityToPriceDate(string value)
{
    _EquityToPriceDate = value;
}

// Get<>AsString()
public string GetEquityToPriceDateAsString()
{
    return _EquityToPriceDate.PadRight(0);
}

// Set<>AsString()
public void SetEquityToPriceDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityToPriceDate = value;
}

// Standard Getter
public string GetEquityRealisedVersion()
{
    return _EquityRealisedVersion;
}

// Standard Setter
public void SetEquityRealisedVersion(string value)
{
    _EquityRealisedVersion = value;
}

// Get<>AsString()
public string GetEquityRealisedVersionAsString()
{
    return _EquityRealisedVersion.PadRight(0);
}

// Set<>AsString()
public void SetEquityRealisedVersionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityRealisedVersion = value;
}

// Standard Getter
public string GetEquityShowSedolNumbers()
{
    return _EquityShowSedolNumbers;
}

// Standard Setter
public void SetEquityShowSedolNumbers(string value)
{
    _EquityShowSedolNumbers = value;
}

// Get<>AsString()
public string GetEquityShowSedolNumbersAsString()
{
    return _EquityShowSedolNumbers.PadRight(0);
}

// Set<>AsString()
public void SetEquityShowSedolNumbersAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityShowSedolNumbers = value;
}

// Standard Getter
public string GetEquityCountryExporter()
{
    return _EquityCountryExporter;
}

// Standard Setter
public void SetEquityCountryExporter(string value)
{
    _EquityCountryExporter = value;
}

// Get<>AsString()
public string GetEquityCountryExporterAsString()
{
    return _EquityCountryExporter.PadRight(0);
}

// Set<>AsString()
public void SetEquityCountryExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityCountryExporter = value;
}

// Standard Getter
public string GetEquityGroupExporter()
{
    return _EquityGroupExporter;
}

// Standard Setter
public void SetEquityGroupExporter(string value)
{
    _EquityGroupExporter = value;
}

// Get<>AsString()
public string GetEquityGroupExporterAsString()
{
    return _EquityGroupExporter.PadRight(0);
}

// Set<>AsString()
public void SetEquityGroupExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityGroupExporter = value;
}

// Standard Getter
public string GetEquityFundExporter()
{
    return _EquityFundExporter;
}

// Standard Setter
public void SetEquityFundExporter(string value)
{
    _EquityFundExporter = value;
}

// Get<>AsString()
public string GetEquityFundExporterAsString()
{
    return _EquityFundExporter.PadRight(0);
}

// Set<>AsString()
public void SetEquityFundExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityFundExporter = value;
}

// Standard Getter
public string GetEquityStockExporter()
{
    return _EquityStockExporter;
}

// Standard Setter
public void SetEquityStockExporter(string value)
{
    _EquityStockExporter = value;
}

// Get<>AsString()
public string GetEquityStockExporterAsString()
{
    return _EquityStockExporter.PadRight(0);
}

// Set<>AsString()
public void SetEquityStockExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityStockExporter = value;
}

// Standard Getter
public string GetEquityPriceExporter()
{
    return _EquityPriceExporter;
}

// Standard Setter
public void SetEquityPriceExporter(string value)
{
    _EquityPriceExporter = value;
}

// Get<>AsString()
public string GetEquityPriceExporterAsString()
{
    return _EquityPriceExporter.PadRight(0);
}

// Set<>AsString()
public void SetEquityPriceExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityPriceExporter = value;
}

// Standard Getter
public string GetEquityCgtscot1Format()
{
    return _EquityCgtscot1Format;
}

// Standard Setter
public void SetEquityCgtscot1Format(string value)
{
    _EquityCgtscot1Format = value;
}

// Get<>AsString()
public string GetEquityCgtscot1FormatAsString()
{
    return _EquityCgtscot1Format.PadRight(0);
}

// Set<>AsString()
public void SetEquityCgtscot1FormatAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityCgtscot1Format = value;
}

// Standard Getter
public string GetEquityShowSedolNumbersLr()
{
    return _EquityShowSedolNumbersLr;
}

// Standard Setter
public void SetEquityShowSedolNumbersLr(string value)
{
    _EquityShowSedolNumbersLr = value;
}

// Get<>AsString()
public string GetEquityShowSedolNumbersLrAsString()
{
    return _EquityShowSedolNumbersLr.PadRight(0);
}

// Set<>AsString()
public void SetEquityShowSedolNumbersLrAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityShowSedolNumbersLr = value;
}

// Standard Getter
public string GetEquityDerivatives()
{
    return _EquityDerivatives;
}

// Standard Setter
public void SetEquityDerivatives(string value)
{
    _EquityDerivatives = value;
}

// Get<>AsString()
public string GetEquityDerivativesAsString()
{
    return _EquityDerivatives.PadRight(0);
}

// Set<>AsString()
public void SetEquityDerivativesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityDerivatives = value;
}

// Standard Getter
public string GetEquityIrishCgt()
{
    return _EquityIrishCgt;
}

// Standard Setter
public void SetEquityIrishCgt(string value)
{
    _EquityIrishCgt = value;
}

// Get<>AsString()
public string GetEquityIrishCgtAsString()
{
    return _EquityIrishCgt.PadRight(0);
}

// Set<>AsString()
public void SetEquityIrishCgtAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityIrishCgt = value;
}

// Standard Getter
public string GetEquityIrishCgtProRata()
{
    return _EquityIrishCgtProRata;
}

// Standard Setter
public void SetEquityIrishCgtProRata(string value)
{
    _EquityIrishCgtProRata = value;
}

// Get<>AsString()
public string GetEquityIrishCgtProRataAsString()
{
    return _EquityIrishCgtProRata.PadRight(0);
}

// Set<>AsString()
public void SetEquityIrishCgtProRataAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityIrishCgtProRata = value;
}



public static int GetSize()
{
    return _size;
}

}

}}