using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WsCrt Data Structure

public class WsCrt
{
    private static int _size = 4;
    // [DEBUG] Class: WsCrt, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsCrt1, is_external=, is_static_class=False, static_prefix=
    private WsCrt1 _WsCrt1 = new WsCrt1();
    
    
    
    
    // [DEBUG] Field: WsCrt3, is_external=, is_static_class=False, static_prefix=
    private string _WsCrt3 ="";
    
    
    
    
    
    // Serialization methods
    public string GetWsCrtAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsCrt1.GetWsCrt1AsString());
        result.Append(_WsCrt3.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsCrtAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            _WsCrt1.SetWsCrt1AsString(data.Substring(offset, 3));
        }
        else
        {
            _WsCrt1.SetWsCrt1AsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsCrt3(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsCrtAsString();
    }
    // Set<>String Override function
    public void SetWsCrt(string value)
    {
        SetWsCrtAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WsCrt1 GetWsCrt1()
    {
        return _WsCrt1;
    }
    
    // Standard Setter
    public void SetWsCrt1(WsCrt1 value)
    {
        _WsCrt1 = value;
    }
    
    // Get<>AsString()
    public string GetWsCrt1AsString()
    {
        return _WsCrt1 != null ? _WsCrt1.GetWsCrt1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsCrt1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsCrt1 == null)
        {
            _WsCrt1 = new WsCrt1();
        }
        _WsCrt1.SetWsCrt1AsString(value);
    }
    
    // Standard Getter
    public string GetWsCrt3()
    {
        return _WsCrt3;
    }
    
    // Standard Setter
    public void SetWsCrt3(string value)
    {
        _WsCrt3 = value;
    }
    
    // Get<>AsString()
    public string GetWsCrt3AsString()
    {
        return _WsCrt3.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsCrt3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCrt3 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsCrt1(string value)
    {
        _WsCrt1.SetWsCrt1AsString(value);
    }
    // Nested Class: WsCrt1
    public class WsCrt1
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Crt1, is_external=, is_static_class=False, static_prefix=
        private string _Crt1 ="";
        
        
        
        
        // [DEBUG] Field: Crt2, is_external=, is_static_class=False, static_prefix=
        private int _Crt2 =0;
        
        
        
        
    public WsCrt1() {}
    
    public WsCrt1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCrt1(data.Substring(offset, 1).Trim());
        offset += 1;
        SetCrt2(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWsCrt1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Crt1.PadRight(1));
        result.Append(_Crt2.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWsCrt1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCrt1(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCrt2(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCrt1()
    {
        return _Crt1;
    }
    
    // Standard Setter
    public void SetCrt1(string value)
    {
        _Crt1 = value;
    }
    
    // Get<>AsString()
    public string GetCrt1AsString()
    {
        return _Crt1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCrt1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Crt1 = value;
    }
    
    // Standard Getter
    public int GetCrt2()
    {
        return _Crt2;
    }
    
    // Standard Setter
    public void SetCrt2(int value)
    {
        _Crt2 = value;
    }
    
    // Get<>AsString()
    public string GetCrt2AsString()
    {
        return _Crt2.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetCrt2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Crt2 = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}