using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WsMessages Data Structure

public class WsMessages
{
    private static int _size = 588;
    // [DEBUG] Class: WsMessages, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsMessage1, is_external=, is_static_class=False, static_prefix=
    private string _WsMessage1 ="";
    
    
    
    
    // [DEBUG] Field: WsMessage2, is_external=, is_static_class=False, static_prefix=
    private WsMessage2 _WsMessage2 = new WsMessage2();
    
    
    
    
    // [DEBUG] Field: WsMessage3, is_external=, is_static_class=False, static_prefix=
    private WsMessage3 _WsMessage3 = new WsMessage3();
    
    
    
    
    // [DEBUG] Field: WsMessage4, is_external=, is_static_class=False, static_prefix=
    private WsMessage4 _WsMessage4 = new WsMessage4();
    
    
    
    
    // [DEBUG] Field: WsMessage6, is_external=, is_static_class=False, static_prefix=
    private WsMessage6 _WsMessage6 = new WsMessage6();
    
    
    
    
    // [DEBUG] Field: WsMessage7, is_external=, is_static_class=False, static_prefix=
    private WsMessage7 _WsMessage7 = new WsMessage7();
    
    
    
    
    // [DEBUG] Field: WsMessage8, is_external=, is_static_class=False, static_prefix=
    private WsMessage8 _WsMessage8 = new WsMessage8();
    
    
    
    
    // [DEBUG] Field: WsMessage9, is_external=, is_static_class=False, static_prefix=
    private WsMessage9 _WsMessage9 = new WsMessage9();
    
    
    
    
    // [DEBUG] Field: WsMessage10, is_external=, is_static_class=False, static_prefix=
    private WsMessage10 _WsMessage10 = new WsMessage10();
    
    
    
    
    // [DEBUG] Field: WsMessage11, is_external=, is_static_class=False, static_prefix=
    private WsMessage11 _WsMessage11 = new WsMessage11();
    
    
    
    
    
    // Serialization methods
    public string GetWsMessagesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsMessage1.PadRight(69));
        result.Append(_WsMessage2.GetWsMessage2AsString());
        result.Append(_WsMessage3.GetWsMessage3AsString());
        result.Append(_WsMessage4.GetWsMessage4AsString());
        result.Append(_WsMessage6.GetWsMessage6AsString());
        result.Append(_WsMessage7.GetWsMessage7AsString());
        result.Append(_WsMessage8.GetWsMessage8AsString());
        result.Append(_WsMessage9.GetWsMessage9AsString());
        result.Append(_WsMessage10.GetWsMessage10AsString());
        result.Append(_WsMessage11.GetWsMessage11AsString());
        
        return result.ToString();
    }
    
    public void SetWsMessagesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 69 <= data.Length)
        {
            string extracted = data.Substring(offset, 69).Trim();
            SetWsMessage1(extracted);
        }
        offset += 69;
        if (offset + 22 <= data.Length)
        {
            _WsMessage2.SetWsMessage2AsString(data.Substring(offset, 22));
        }
        else
        {
            _WsMessage2.SetWsMessage2AsString(data.Substring(offset));
        }
        offset += 22;
        if (offset + 69 <= data.Length)
        {
            _WsMessage3.SetWsMessage3AsString(data.Substring(offset, 69));
        }
        else
        {
            _WsMessage3.SetWsMessage3AsString(data.Substring(offset));
        }
        offset += 69;
        if (offset + 21 <= data.Length)
        {
            _WsMessage4.SetWsMessage4AsString(data.Substring(offset, 21));
        }
        else
        {
            _WsMessage4.SetWsMessage4AsString(data.Substring(offset));
        }
        offset += 21;
        if (offset + 69 <= data.Length)
        {
            _WsMessage6.SetWsMessage6AsString(data.Substring(offset, 69));
        }
        else
        {
            _WsMessage6.SetWsMessage6AsString(data.Substring(offset));
        }
        offset += 69;
        if (offset + 69 <= data.Length)
        {
            _WsMessage7.SetWsMessage7AsString(data.Substring(offset, 69));
        }
        else
        {
            _WsMessage7.SetWsMessage7AsString(data.Substring(offset));
        }
        offset += 69;
        if (offset + 69 <= data.Length)
        {
            _WsMessage8.SetWsMessage8AsString(data.Substring(offset, 69));
        }
        else
        {
            _WsMessage8.SetWsMessage8AsString(data.Substring(offset));
        }
        offset += 69;
        if (offset + 59 <= data.Length)
        {
            _WsMessage9.SetWsMessage9AsString(data.Substring(offset, 59));
        }
        else
        {
            _WsMessage9.SetWsMessage9AsString(data.Substring(offset));
        }
        offset += 59;
        if (offset + 69 <= data.Length)
        {
            _WsMessage10.SetWsMessage10AsString(data.Substring(offset, 69));
        }
        else
        {
            _WsMessage10.SetWsMessage10AsString(data.Substring(offset));
        }
        offset += 69;
        if (offset + 72 <= data.Length)
        {
            _WsMessage11.SetWsMessage11AsString(data.Substring(offset, 72));
        }
        else
        {
            _WsMessage11.SetWsMessage11AsString(data.Substring(offset));
        }
        offset += 72;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsMessagesAsString();
    }
    // Set<>String Override function
    public void SetWsMessages(string value)
    {
        SetWsMessagesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsMessage1()
    {
        return _WsMessage1;
    }
    
    // Standard Setter
    public void SetWsMessage1(string value)
    {
        _WsMessage1 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage1AsString()
    {
        return _WsMessage1.PadRight(69);
    }
    
    // Set<>AsString()
    public void SetWsMessage1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsMessage1 = value;
    }
    
    // Standard Getter
    public WsMessage2 GetWsMessage2()
    {
        return _WsMessage2;
    }
    
    // Standard Setter
    public void SetWsMessage2(WsMessage2 value)
    {
        _WsMessage2 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage2AsString()
    {
        return _WsMessage2 != null ? _WsMessage2.GetWsMessage2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage2 == null)
        {
            _WsMessage2 = new WsMessage2();
        }
        _WsMessage2.SetWsMessage2AsString(value);
    }
    
    // Standard Getter
    public WsMessage3 GetWsMessage3()
    {
        return _WsMessage3;
    }
    
    // Standard Setter
    public void SetWsMessage3(WsMessage3 value)
    {
        _WsMessage3 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage3AsString()
    {
        return _WsMessage3 != null ? _WsMessage3.GetWsMessage3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage3 == null)
        {
            _WsMessage3 = new WsMessage3();
        }
        _WsMessage3.SetWsMessage3AsString(value);
    }
    
    // Standard Getter
    public WsMessage4 GetWsMessage4()
    {
        return _WsMessage4;
    }
    
    // Standard Setter
    public void SetWsMessage4(WsMessage4 value)
    {
        _WsMessage4 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage4AsString()
    {
        return _WsMessage4 != null ? _WsMessage4.GetWsMessage4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage4 == null)
        {
            _WsMessage4 = new WsMessage4();
        }
        _WsMessage4.SetWsMessage4AsString(value);
    }
    
    // Standard Getter
    public WsMessage6 GetWsMessage6()
    {
        return _WsMessage6;
    }
    
    // Standard Setter
    public void SetWsMessage6(WsMessage6 value)
    {
        _WsMessage6 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage6AsString()
    {
        return _WsMessage6 != null ? _WsMessage6.GetWsMessage6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage6 == null)
        {
            _WsMessage6 = new WsMessage6();
        }
        _WsMessage6.SetWsMessage6AsString(value);
    }
    
    // Standard Getter
    public WsMessage7 GetWsMessage7()
    {
        return _WsMessage7;
    }
    
    // Standard Setter
    public void SetWsMessage7(WsMessage7 value)
    {
        _WsMessage7 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage7AsString()
    {
        return _WsMessage7 != null ? _WsMessage7.GetWsMessage7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage7 == null)
        {
            _WsMessage7 = new WsMessage7();
        }
        _WsMessage7.SetWsMessage7AsString(value);
    }
    
    // Standard Getter
    public WsMessage8 GetWsMessage8()
    {
        return _WsMessage8;
    }
    
    // Standard Setter
    public void SetWsMessage8(WsMessage8 value)
    {
        _WsMessage8 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage8AsString()
    {
        return _WsMessage8 != null ? _WsMessage8.GetWsMessage8AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage8 == null)
        {
            _WsMessage8 = new WsMessage8();
        }
        _WsMessage8.SetWsMessage8AsString(value);
    }
    
    // Standard Getter
    public WsMessage9 GetWsMessage9()
    {
        return _WsMessage9;
    }
    
    // Standard Setter
    public void SetWsMessage9(WsMessage9 value)
    {
        _WsMessage9 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage9AsString()
    {
        return _WsMessage9 != null ? _WsMessage9.GetWsMessage9AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage9 == null)
        {
            _WsMessage9 = new WsMessage9();
        }
        _WsMessage9.SetWsMessage9AsString(value);
    }
    
    // Standard Getter
    public WsMessage10 GetWsMessage10()
    {
        return _WsMessage10;
    }
    
    // Standard Setter
    public void SetWsMessage10(WsMessage10 value)
    {
        _WsMessage10 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage10AsString()
    {
        return _WsMessage10 != null ? _WsMessage10.GetWsMessage10AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage10 == null)
        {
            _WsMessage10 = new WsMessage10();
        }
        _WsMessage10.SetWsMessage10AsString(value);
    }
    
    // Standard Getter
    public WsMessage11 GetWsMessage11()
    {
        return _WsMessage11;
    }
    
    // Standard Setter
    public void SetWsMessage11(WsMessage11 value)
    {
        _WsMessage11 = value;
    }
    
    // Get<>AsString()
    public string GetWsMessage11AsString()
    {
        return _WsMessage11 != null ? _WsMessage11.GetWsMessage11AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsMessage11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsMessage11 == null)
        {
            _WsMessage11 = new WsMessage11();
        }
        _WsMessage11.SetWsMessage11AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsMessage2(string value)
    {
        _WsMessage2.SetWsMessage2AsString(value);
    }
    // Nested Class: WsMessage2
    public class WsMessage2
    {
        private static int _size = 22;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler103, is_external=, is_static_class=False, static_prefix=
        private string _Filler103 ="RUNTIME ";
        
        
        
        
        // [DEBUG] Field: WsMessDd, is_external=, is_static_class=False, static_prefix=
        private int _WsMessDd =0;
        
        
        
        
        // [DEBUG] Field: Filler104, is_external=, is_static_class=False, static_prefix=
        private string _Filler104 ="/";
        
        
        
        
        // [DEBUG] Field: WsMessMm, is_external=, is_static_class=False, static_prefix=
        private int _WsMessMm =0;
        
        
        
        
        // [DEBUG] Field: Filler105, is_external=, is_static_class=False, static_prefix=
        private string _Filler105 ="/";
        
        
        
        
        // [DEBUG] Field: WsMessYy, is_external=, is_static_class=False, static_prefix=
        private int _WsMessYy =0;
        
        
        
        
        // [DEBUG] Field: Filler106, is_external=, is_static_class=False, static_prefix=
        private string _Filler106 ="";
        
        
        
        
        // [DEBUG] Field: WsMessHh, is_external=, is_static_class=False, static_prefix=
        private int _WsMessHh =0;
        
        
        
        
        // [DEBUG] Field: Filler107, is_external=, is_static_class=False, static_prefix=
        private string _Filler107 =":";
        
        
        
        
        // [DEBUG] Field: WsMessNn, is_external=, is_static_class=False, static_prefix=
        private int _WsMessNn =0;
        
        
        
        
    public WsMessage2() {}
    
    public WsMessage2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller103(data.Substring(offset, 8).Trim());
        offset += 8;
        SetWsMessDd(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetFiller104(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsMessMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetFiller105(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsMessYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetFiller106(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsMessHh(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetFiller107(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsMessNn(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWsMessage2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler103.PadRight(8));
        result.Append(_WsMessDd.ToString().PadLeft(2, '0'));
        result.Append(_Filler104.PadRight(1));
        result.Append(_WsMessMm.ToString().PadLeft(2, '0'));
        result.Append(_Filler105.PadRight(1));
        result.Append(_WsMessYy.ToString().PadLeft(2, '0'));
        result.Append(_Filler106.PadRight(1));
        result.Append(_WsMessHh.ToString().PadLeft(2, '0'));
        result.Append(_Filler107.PadRight(1));
        result.Append(_WsMessNn.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWsMessage2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller103(extracted);
        }
        offset += 8;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessDd(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller104(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessMm(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller105(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessYy(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller106(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessHh(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller107(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsMessNn(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller103()
    {
        return _Filler103;
    }
    
    // Standard Setter
    public void SetFiller103(string value)
    {
        _Filler103 = value;
    }
    
    // Get<>AsString()
    public string GetFiller103AsString()
    {
        return _Filler103.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller103AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler103 = value;
    }
    
    // Standard Getter
    public int GetWsMessDd()
    {
        return _WsMessDd;
    }
    
    // Standard Setter
    public void SetWsMessDd(int value)
    {
        _WsMessDd = value;
    }
    
    // Get<>AsString()
    public string GetWsMessDdAsString()
    {
        return _WsMessDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessDd = parsed;
    }
    
    // Standard Getter
    public string GetFiller104()
    {
        return _Filler104;
    }
    
    // Standard Setter
    public void SetFiller104(string value)
    {
        _Filler104 = value;
    }
    
    // Get<>AsString()
    public string GetFiller104AsString()
    {
        return _Filler104.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller104AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler104 = value;
    }
    
    // Standard Getter
    public int GetWsMessMm()
    {
        return _WsMessMm;
    }
    
    // Standard Setter
    public void SetWsMessMm(int value)
    {
        _WsMessMm = value;
    }
    
    // Get<>AsString()
    public string GetWsMessMmAsString()
    {
        return _WsMessMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessMm = parsed;
    }
    
    // Standard Getter
    public string GetFiller105()
    {
        return _Filler105;
    }
    
    // Standard Setter
    public void SetFiller105(string value)
    {
        _Filler105 = value;
    }
    
    // Get<>AsString()
    public string GetFiller105AsString()
    {
        return _Filler105.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller105AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler105 = value;
    }
    
    // Standard Getter
    public int GetWsMessYy()
    {
        return _WsMessYy;
    }
    
    // Standard Setter
    public void SetWsMessYy(int value)
    {
        _WsMessYy = value;
    }
    
    // Get<>AsString()
    public string GetWsMessYyAsString()
    {
        return _WsMessYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessYy = parsed;
    }
    
    // Standard Getter
    public string GetFiller106()
    {
        return _Filler106;
    }
    
    // Standard Setter
    public void SetFiller106(string value)
    {
        _Filler106 = value;
    }
    
    // Get<>AsString()
    public string GetFiller106AsString()
    {
        return _Filler106.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller106AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler106 = value;
    }
    
    // Standard Getter
    public int GetWsMessHh()
    {
        return _WsMessHh;
    }
    
    // Standard Setter
    public void SetWsMessHh(int value)
    {
        _WsMessHh = value;
    }
    
    // Get<>AsString()
    public string GetWsMessHhAsString()
    {
        return _WsMessHh.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessHhAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessHh = parsed;
    }
    
    // Standard Getter
    public string GetFiller107()
    {
        return _Filler107;
    }
    
    // Standard Setter
    public void SetFiller107(string value)
    {
        _Filler107 = value;
    }
    
    // Get<>AsString()
    public string GetFiller107AsString()
    {
        return _Filler107.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller107AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler107 = value;
    }
    
    // Standard Getter
    public int GetWsMessNn()
    {
        return _WsMessNn;
    }
    
    // Standard Setter
    public void SetWsMessNn(int value)
    {
        _WsMessNn = value;
    }
    
    // Get<>AsString()
    public string GetWsMessNnAsString()
    {
        return _WsMessNn.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsMessNnAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsMessNn = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWsMessage3(string value)
{
    _WsMessage3.SetWsMessage3AsString(value);
}
// Nested Class: WsMessage3
public class WsMessage3
{
    private static int _size = 69;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsMess3Fund, is_external=, is_static_class=False, static_prefix=
    private string _WsMess3Fund ="";
    
    
    
    
    // [DEBUG] Field: Filler108, is_external=, is_static_class=False, static_prefix=
    private string _Filler108 =" ";
    
    
    
    
    // [DEBUG] Field: WsMess3Sedol1, is_external=, is_static_class=False, static_prefix=
    private string _WsMess3Sedol1 ="";
    
    
    
    
    // [DEBUG] Field: Filler109, is_external=, is_static_class=False, static_prefix=
    private string _Filler109 ="-";
    
    
    
    
    // [DEBUG] Field: WsMess3Sedol24, is_external=, is_static_class=False, static_prefix=
    private string _WsMess3Sedol24 ="";
    
    
    
    
    // [DEBUG] Field: Filler110, is_external=, is_static_class=False, static_prefix=
    private string _Filler110 ="-";
    
    
    
    
    // [DEBUG] Field: WsMess3Sedol57, is_external=, is_static_class=False, static_prefix=
    private string _WsMess3Sedol57 ="";
    
    
    
    
    // [DEBUG] Field: Filler111, is_external=, is_static_class=False, static_prefix=
    private string _Filler111 ="";
    
    
    
    
    // [DEBUG] Field: WsMess3Name, is_external=, is_static_class=False, static_prefix=
    private string _WsMess3Name ="";
    
    
    
    
    // [DEBUG] Field: Filler112, is_external=, is_static_class=False, static_prefix=
    private string _Filler112 ="";
    
    
    
    
    // [DEBUG] Field: WsMess3Desc, is_external=, is_static_class=False, static_prefix=
    private string _WsMess3Desc ="";
    
    
    
    
    // [DEBUG] Field: Filler113, is_external=, is_static_class=False, static_prefix=
    private string _Filler113 ="";
    
    
    
    
    // [DEBUG] Field: WsMess3Text, is_external=, is_static_class=False, static_prefix=
    private string _WsMess3Text =" ";
    
    
    
    
public WsMessage3() {}

public WsMessage3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsMess3Fund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller108(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsMess3Sedol1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller109(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsMess3Sedol24(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller110(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsMess3Sedol57(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller111(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsMess3Name(data.Substring(offset, 23).Trim());
    offset += 23;
    SetFiller112(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsMess3Desc(data.Substring(offset, 16).Trim());
    offset += 16;
    SetFiller113(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsMess3Text(data.Substring(offset, 13).Trim());
    offset += 13;
    
}

// Serialization methods
public string GetWsMessage3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsMess3Fund.PadRight(4));
    result.Append(_Filler108.PadRight(1));
    result.Append(_WsMess3Sedol1.PadRight(1));
    result.Append(_Filler109.PadRight(1));
    result.Append(_WsMess3Sedol24.PadRight(3));
    result.Append(_Filler110.PadRight(1));
    result.Append(_WsMess3Sedol57.PadRight(3));
    result.Append(_Filler111.PadRight(1));
    result.Append(_WsMess3Name.PadRight(23));
    result.Append(_Filler112.PadRight(1));
    result.Append(_WsMess3Desc.PadRight(16));
    result.Append(_Filler113.PadRight(1));
    result.Append(_WsMess3Text.PadRight(13));
    
    return result.ToString();
}

public void SetWsMessage3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsMess3Fund(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller108(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWsMess3Sedol1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller109(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWsMess3Sedol24(extracted);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller110(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWsMess3Sedol57(extracted);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller111(extracted);
    }
    offset += 1;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetWsMess3Name(extracted);
    }
    offset += 23;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller112(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        SetWsMess3Desc(extracted);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller113(extracted);
    }
    offset += 1;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetWsMess3Text(extracted);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public string GetWsMess3Fund()
{
    return _WsMess3Fund;
}

// Standard Setter
public void SetWsMess3Fund(string value)
{
    _WsMess3Fund = value;
}

// Get<>AsString()
public string GetWsMess3FundAsString()
{
    return _WsMess3Fund.PadRight(4);
}

// Set<>AsString()
public void SetWsMess3FundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess3Fund = value;
}

// Standard Getter
public string GetFiller108()
{
    return _Filler108;
}

// Standard Setter
public void SetFiller108(string value)
{
    _Filler108 = value;
}

// Get<>AsString()
public string GetFiller108AsString()
{
    return _Filler108.PadRight(1);
}

// Set<>AsString()
public void SetFiller108AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler108 = value;
}

// Standard Getter
public string GetWsMess3Sedol1()
{
    return _WsMess3Sedol1;
}

// Standard Setter
public void SetWsMess3Sedol1(string value)
{
    _WsMess3Sedol1 = value;
}

// Get<>AsString()
public string GetWsMess3Sedol1AsString()
{
    return _WsMess3Sedol1.PadRight(1);
}

// Set<>AsString()
public void SetWsMess3Sedol1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess3Sedol1 = value;
}

// Standard Getter
public string GetFiller109()
{
    return _Filler109;
}

// Standard Setter
public void SetFiller109(string value)
{
    _Filler109 = value;
}

// Get<>AsString()
public string GetFiller109AsString()
{
    return _Filler109.PadRight(1);
}

// Set<>AsString()
public void SetFiller109AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler109 = value;
}

// Standard Getter
public string GetWsMess3Sedol24()
{
    return _WsMess3Sedol24;
}

// Standard Setter
public void SetWsMess3Sedol24(string value)
{
    _WsMess3Sedol24 = value;
}

// Get<>AsString()
public string GetWsMess3Sedol24AsString()
{
    return _WsMess3Sedol24.PadRight(3);
}

// Set<>AsString()
public void SetWsMess3Sedol24AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess3Sedol24 = value;
}

// Standard Getter
public string GetFiller110()
{
    return _Filler110;
}

// Standard Setter
public void SetFiller110(string value)
{
    _Filler110 = value;
}

// Get<>AsString()
public string GetFiller110AsString()
{
    return _Filler110.PadRight(1);
}

// Set<>AsString()
public void SetFiller110AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler110 = value;
}

// Standard Getter
public string GetWsMess3Sedol57()
{
    return _WsMess3Sedol57;
}

// Standard Setter
public void SetWsMess3Sedol57(string value)
{
    _WsMess3Sedol57 = value;
}

// Get<>AsString()
public string GetWsMess3Sedol57AsString()
{
    return _WsMess3Sedol57.PadRight(3);
}

// Set<>AsString()
public void SetWsMess3Sedol57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess3Sedol57 = value;
}

// Standard Getter
public string GetFiller111()
{
    return _Filler111;
}

// Standard Setter
public void SetFiller111(string value)
{
    _Filler111 = value;
}

// Get<>AsString()
public string GetFiller111AsString()
{
    return _Filler111.PadRight(1);
}

// Set<>AsString()
public void SetFiller111AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler111 = value;
}

// Standard Getter
public string GetWsMess3Name()
{
    return _WsMess3Name;
}

// Standard Setter
public void SetWsMess3Name(string value)
{
    _WsMess3Name = value;
}

// Get<>AsString()
public string GetWsMess3NameAsString()
{
    return _WsMess3Name.PadRight(23);
}

// Set<>AsString()
public void SetWsMess3NameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess3Name = value;
}

// Standard Getter
public string GetFiller112()
{
    return _Filler112;
}

// Standard Setter
public void SetFiller112(string value)
{
    _Filler112 = value;
}

// Get<>AsString()
public string GetFiller112AsString()
{
    return _Filler112.PadRight(1);
}

// Set<>AsString()
public void SetFiller112AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler112 = value;
}

// Standard Getter
public string GetWsMess3Desc()
{
    return _WsMess3Desc;
}

// Standard Setter
public void SetWsMess3Desc(string value)
{
    _WsMess3Desc = value;
}

// Get<>AsString()
public string GetWsMess3DescAsString()
{
    return _WsMess3Desc.PadRight(16);
}

// Set<>AsString()
public void SetWsMess3DescAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess3Desc = value;
}

// Standard Getter
public string GetFiller113()
{
    return _Filler113;
}

// Standard Setter
public void SetFiller113(string value)
{
    _Filler113 = value;
}

// Get<>AsString()
public string GetFiller113AsString()
{
    return _Filler113.PadRight(1);
}

// Set<>AsString()
public void SetFiller113AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler113 = value;
}

// Standard Getter
public string GetWsMess3Text()
{
    return _WsMess3Text;
}

// Standard Setter
public void SetWsMess3Text(string value)
{
    _WsMess3Text = value;
}

// Get<>AsString()
public string GetWsMess3TextAsString()
{
    return _WsMess3Text.PadRight(13);
}

// Set<>AsString()
public void SetWsMess3TextAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess3Text = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsMessage4(string value)
{
    _WsMessage4.SetWsMessage4AsString(value);
}
// Nested Class: WsMessage4
public class WsMessage4
{
    private static int _size = 21;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler114, is_external=, is_static_class=False, static_prefix=
    private string _Filler114 ="";
    
    
    
    
    // [DEBUG] Field: WsMess4Fund, is_external=, is_static_class=False, static_prefix=
    private string _WsMess4Fund ="";
    
    
    
    
public WsMessage4() {}

public WsMessage4(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller114(data.Substring(offset, 17).Trim());
    offset += 17;
    SetWsMess4Fund(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWsMessage4AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler114.PadRight(17));
    result.Append(_WsMess4Fund.PadRight(4));
    
    return result.ToString();
}

public void SetWsMessage4AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        SetFiller114(extracted);
    }
    offset += 17;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsMess4Fund(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller114()
{
    return _Filler114;
}

// Standard Setter
public void SetFiller114(string value)
{
    _Filler114 = value;
}

// Get<>AsString()
public string GetFiller114AsString()
{
    return _Filler114.PadRight(17);
}

// Set<>AsString()
public void SetFiller114AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler114 = value;
}

// Standard Getter
public string GetWsMess4Fund()
{
    return _WsMess4Fund;
}

// Standard Setter
public void SetWsMess4Fund(string value)
{
    _WsMess4Fund = value;
}

// Get<>AsString()
public string GetWsMess4FundAsString()
{
    return _WsMess4Fund.PadRight(4);
}

// Set<>AsString()
public void SetWsMess4FundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess4Fund = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsMessage6(string value)
{
    _WsMessage6.SetWsMessage6AsString(value);
}
// Nested Class: WsMessage6
public class WsMessage6
{
    private static int _size = 69;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler115, is_external=, is_static_class=False, static_prefix=
    private string _Filler115 ="BALANCE UPDATE FOR FUND ";
    
    
    
    
    // [DEBUG] Field: WsMess6Fund, is_external=, is_static_class=False, static_prefix=
    private string _WsMess6Fund ="";
    
    
    
    
    // [DEBUG] Field: Filler116, is_external=, is_static_class=False, static_prefix=
    private string _Filler116 =" COMPLETE";
    
    
    
    
public WsMessage6() {}

public WsMessage6(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller115(data.Substring(offset, 24).Trim());
    offset += 24;
    SetWsMess6Fund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller116(data.Substring(offset, 41).Trim());
    offset += 41;
    
}

// Serialization methods
public string GetWsMessage6AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler115.PadRight(24));
    result.Append(_WsMess6Fund.PadRight(4));
    result.Append(_Filler116.PadRight(41));
    
    return result.ToString();
}

public void SetWsMessage6AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller115(extracted);
    }
    offset += 24;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsMess6Fund(extracted);
    }
    offset += 4;
    if (offset + 41 <= data.Length)
    {
        string extracted = data.Substring(offset, 41).Trim();
        SetFiller116(extracted);
    }
    offset += 41;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller115()
{
    return _Filler115;
}

// Standard Setter
public void SetFiller115(string value)
{
    _Filler115 = value;
}

// Get<>AsString()
public string GetFiller115AsString()
{
    return _Filler115.PadRight(24);
}

// Set<>AsString()
public void SetFiller115AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler115 = value;
}

// Standard Getter
public string GetWsMess6Fund()
{
    return _WsMess6Fund;
}

// Standard Setter
public void SetWsMess6Fund(string value)
{
    _WsMess6Fund = value;
}

// Get<>AsString()
public string GetWsMess6FundAsString()
{
    return _WsMess6Fund.PadRight(4);
}

// Set<>AsString()
public void SetWsMess6FundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess6Fund = value;
}

// Standard Getter
public string GetFiller116()
{
    return _Filler116;
}

// Standard Setter
public void SetFiller116(string value)
{
    _Filler116 = value;
}

// Get<>AsString()
public string GetFiller116AsString()
{
    return _Filler116.PadRight(41);
}

// Set<>AsString()
public void SetFiller116AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler116 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsMessage7(string value)
{
    _WsMessage7.SetWsMessage7AsString(value);
}
// Nested Class: WsMessage7
public class WsMessage7
{
    private static int _size = 69;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler117, is_external=, is_static_class=False, static_prefix=
    private string _Filler117 ="***   BALANCES UPDATE TERMINATED   ***";
    
    
    
    
public WsMessage7() {}

public WsMessage7(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller117(data.Substring(offset, 69).Trim());
    offset += 69;
    
}

// Serialization methods
public string GetWsMessage7AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler117.PadRight(69));
    
    return result.ToString();
}

public void SetWsMessage7AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 69 <= data.Length)
    {
        string extracted = data.Substring(offset, 69).Trim();
        SetFiller117(extracted);
    }
    offset += 69;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller117()
{
    return _Filler117;
}

// Standard Setter
public void SetFiller117(string value)
{
    _Filler117 = value;
}

// Get<>AsString()
public string GetFiller117AsString()
{
    return _Filler117.PadRight(69);
}

// Set<>AsString()
public void SetFiller117AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler117 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsMessage8(string value)
{
    _WsMessage8.SetWsMessage8AsString(value);
}
// Nested Class: WsMessage8
public class WsMessage8
{
    private static int _size = 69;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler118, is_external=, is_static_class=False, static_prefix=
    private string _Filler118 ="NEW MASTER FILE: MAST";
    
    
    
    
    // [DEBUG] Field: WsMess8Yy, is_external=, is_static_class=False, static_prefix=
    private string _WsMess8Yy ="";
    
    
    
    
    // [DEBUG] Field: Filler119, is_external=, is_static_class=False, static_prefix=
    private string _Filler119 =".DAT CREATED";
    
    
    
    
public WsMessage8() {}

public WsMessage8(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller118(data.Substring(offset, 21).Trim());
    offset += 21;
    SetWsMess8Yy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller119(data.Substring(offset, 46).Trim());
    offset += 46;
    
}

// Serialization methods
public string GetWsMessage8AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler118.PadRight(21));
    result.Append(_WsMess8Yy.PadRight(2));
    result.Append(_Filler119.PadRight(46));
    
    return result.ToString();
}

public void SetWsMessage8AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller118(extracted);
    }
    offset += 21;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWsMess8Yy(extracted);
    }
    offset += 2;
    if (offset + 46 <= data.Length)
    {
        string extracted = data.Substring(offset, 46).Trim();
        SetFiller119(extracted);
    }
    offset += 46;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller118()
{
    return _Filler118;
}

// Standard Setter
public void SetFiller118(string value)
{
    _Filler118 = value;
}

// Get<>AsString()
public string GetFiller118AsString()
{
    return _Filler118.PadRight(21);
}

// Set<>AsString()
public void SetFiller118AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler118 = value;
}

// Standard Getter
public string GetWsMess8Yy()
{
    return _WsMess8Yy;
}

// Standard Setter
public void SetWsMess8Yy(string value)
{
    _WsMess8Yy = value;
}

// Get<>AsString()
public string GetWsMess8YyAsString()
{
    return _WsMess8Yy.PadRight(2);
}

// Set<>AsString()
public void SetWsMess8YyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess8Yy = value;
}

// Standard Getter
public string GetFiller119()
{
    return _Filler119;
}

// Standard Setter
public void SetFiller119(string value)
{
    _Filler119 = value;
}

// Get<>AsString()
public string GetFiller119AsString()
{
    return _Filler119.PadRight(46);
}

// Set<>AsString()
public void SetFiller119AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler119 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsMessage9(string value)
{
    _WsMessage9.SetWsMessage9AsString(value);
}
// Nested Class: WsMessage9
public class WsMessage9
{
    private static int _size = 59;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler120, is_external=, is_static_class=False, static_prefix=
    private string _Filler120 ="Database for Year ";
    
    
    
    
    // [DEBUG] Field: WsMess9ToCcyy, is_external=, is_static_class=False, static_prefix=
    private string _WsMess9ToCcyy ="";
    
    
    
    
    // [DEBUG] Field: Filler121, is_external=, is_static_class=False, static_prefix=
    private string _Filler121 =" updated with Balances from Year ";
    
    
    
    
    // [DEBUG] Field: WsMess9FromCcyy, is_external=, is_static_class=False, static_prefix=
    private string _WsMess9FromCcyy ="";
    
    
    
    
public WsMessage9() {}

public WsMessage9(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller120(data.Substring(offset, 18).Trim());
    offset += 18;
    SetWsMess9ToCcyy(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller121(data.Substring(offset, 33).Trim());
    offset += 33;
    SetWsMess9FromCcyy(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWsMessage9AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler120.PadRight(18));
    result.Append(_WsMess9ToCcyy.PadRight(4));
    result.Append(_Filler121.PadRight(33));
    result.Append(_WsMess9FromCcyy.PadRight(4));
    
    return result.ToString();
}

public void SetWsMessage9AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 18 <= data.Length)
    {
        string extracted = data.Substring(offset, 18).Trim();
        SetFiller120(extracted);
    }
    offset += 18;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsMess9ToCcyy(extracted);
    }
    offset += 4;
    if (offset + 33 <= data.Length)
    {
        string extracted = data.Substring(offset, 33).Trim();
        SetFiller121(extracted);
    }
    offset += 33;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsMess9FromCcyy(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller120()
{
    return _Filler120;
}

// Standard Setter
public void SetFiller120(string value)
{
    _Filler120 = value;
}

// Get<>AsString()
public string GetFiller120AsString()
{
    return _Filler120.PadRight(18);
}

// Set<>AsString()
public void SetFiller120AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler120 = value;
}

// Standard Getter
public string GetWsMess9ToCcyy()
{
    return _WsMess9ToCcyy;
}

// Standard Setter
public void SetWsMess9ToCcyy(string value)
{
    _WsMess9ToCcyy = value;
}

// Get<>AsString()
public string GetWsMess9ToCcyyAsString()
{
    return _WsMess9ToCcyy.PadRight(4);
}

// Set<>AsString()
public void SetWsMess9ToCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess9ToCcyy = value;
}

// Standard Getter
public string GetFiller121()
{
    return _Filler121;
}

// Standard Setter
public void SetFiller121(string value)
{
    _Filler121 = value;
}

// Get<>AsString()
public string GetFiller121AsString()
{
    return _Filler121.PadRight(33);
}

// Set<>AsString()
public void SetFiller121AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler121 = value;
}

// Standard Getter
public string GetWsMess9FromCcyy()
{
    return _WsMess9FromCcyy;
}

// Standard Setter
public void SetWsMess9FromCcyy(string value)
{
    _WsMess9FromCcyy = value;
}

// Get<>AsString()
public string GetWsMess9FromCcyyAsString()
{
    return _WsMess9FromCcyy.PadRight(4);
}

// Set<>AsString()
public void SetWsMess9FromCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess9FromCcyy = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsMessage10(string value)
{
    _WsMessage10.SetWsMessage10AsString(value);
}
// Nested Class: WsMessage10
public class WsMessage10
{
    private static int _size = 69;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler122, is_external=, is_static_class=False, static_prefix=
    private string _Filler122 ="***   MASTER FILE BALANCES UPDATE COMPLETE   ***";
    
    
    
    
public WsMessage10() {}

public WsMessage10(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller122(data.Substring(offset, 69).Trim());
    offset += 69;
    
}

// Serialization methods
public string GetWsMessage10AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler122.PadRight(69));
    
    return result.ToString();
}

public void SetWsMessage10AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 69 <= data.Length)
    {
        string extracted = data.Substring(offset, 69).Trim();
        SetFiller122(extracted);
    }
    offset += 69;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller122()
{
    return _Filler122;
}

// Standard Setter
public void SetFiller122(string value)
{
    _Filler122 = value;
}

// Get<>AsString()
public string GetFiller122AsString()
{
    return _Filler122.PadRight(69);
}

// Set<>AsString()
public void SetFiller122AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler122 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsMessage11(string value)
{
    _WsMessage11.SetWsMessage11AsString(value);
}
// Nested Class: WsMessage11
public class WsMessage11
{
    private static int _size = 72;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler123, is_external=, is_static_class=False, static_prefix=
    private string _Filler123 ="...PROCESSING FUND  ";
    
    
    
    
    // [DEBUG] Field: WsMess11Fund, is_external=, is_static_class=False, static_prefix=
    private string _WsMess11Fund =" ";
    
    
    
    
    // [DEBUG] Field: Filler124, is_external=, is_static_class=False, static_prefix=
    private string _Filler124 =" ";
    
    
    
    
public WsMessage11() {}

public WsMessage11(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller123(data.Substring(offset, 20).Trim());
    offset += 20;
    SetWsMess11Fund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller124(data.Substring(offset, 48).Trim());
    offset += 48;
    
}

// Serialization methods
public string GetWsMessage11AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler123.PadRight(20));
    result.Append(_WsMess11Fund.PadRight(4));
    result.Append(_Filler124.PadRight(48));
    
    return result.ToString();
}

public void SetWsMessage11AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller123(extracted);
    }
    offset += 20;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsMess11Fund(extracted);
    }
    offset += 4;
    if (offset + 48 <= data.Length)
    {
        string extracted = data.Substring(offset, 48).Trim();
        SetFiller124(extracted);
    }
    offset += 48;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller123()
{
    return _Filler123;
}

// Standard Setter
public void SetFiller123(string value)
{
    _Filler123 = value;
}

// Get<>AsString()
public string GetFiller123AsString()
{
    return _Filler123.PadRight(20);
}

// Set<>AsString()
public void SetFiller123AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler123 = value;
}

// Standard Getter
public string GetWsMess11Fund()
{
    return _WsMess11Fund;
}

// Standard Setter
public void SetWsMess11Fund(string value)
{
    _WsMess11Fund = value;
}

// Get<>AsString()
public string GetWsMess11FundAsString()
{
    return _WsMess11Fund.PadRight(4);
}

// Set<>AsString()
public void SetWsMess11FundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMess11Fund = value;
}

// Standard Getter
public string GetFiller124()
{
    return _Filler124;
}

// Standard Setter
public void SetFiller124(string value)
{
    _Filler124 = value;
}

// Get<>AsString()
public string GetFiller124AsString()
{
    return _Filler124.PadRight(48);
}

// Set<>AsString()
public void SetFiller124AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler124 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}