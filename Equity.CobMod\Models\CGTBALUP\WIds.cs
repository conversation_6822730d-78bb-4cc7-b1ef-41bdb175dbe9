using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WIds Data Structure

public class WIds
{
    private static int _size = 81;
    // [DEBUG] Class: WIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler184, is_external=, is_static_class=False, static_prefix=
    private string _Filler184 ="";
    
    
    
    
    // [DEBUG] Field: Filler185, is_external=, is_static_class=False, static_prefix=
    private string _Filler185 ="";
    
    
    
    
    // [DEBUG] Field: Filler186, is_external=, is_static_class=False, static_prefix=
    private string _Filler186 ="";
    
    
    
    
    // [DEBUG] Field: Filler187, is_external=, is_static_class=False, static_prefix=
    private string _Filler187 ="";
    
    
    
    
    // [DEBUG] Field: Filler188, is_external=, is_static_class=False, static_prefix=
    private string _Filler188 ="";
    
    
    
    
    // [DEBUG] Field: Filler189, is_external=, is_static_class=False, static_prefix=
    private string _Filler189 ="";
    
    
    
    
    // [DEBUG] Field: Filler190, is_external=, is_static_class=False, static_prefix=
    private string _Filler190 ="";
    
    
    
    
    // [DEBUG] Field: Filler191, is_external=, is_static_class=False, static_prefix=
    private string _Filler191 ="";
    
    
    
    
    // [DEBUG] Field: Filler192, is_external=, is_static_class=False, static_prefix=
    private string _Filler192 ="";
    
    
    
    
    
    // Serialization methods
    public string GetWIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler184.PadRight(8));
        result.Append(_Filler185.PadRight(8));
        result.Append(_Filler186.PadRight(8));
        result.Append(_Filler187.PadRight(8));
        result.Append(_Filler188.PadRight(8));
        result.Append(_Filler189.PadRight(8));
        result.Append(_Filler190.PadRight(8));
        result.Append(_Filler191.PadRight(24));
        result.Append(_Filler192.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller184(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller185(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller186(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller187(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller188(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller189(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller190(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetFiller191(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller192(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWIdsAsString();
    }
    // Set<>String Override function
    public void SetWIds(string value)
    {
        SetWIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller184()
    {
        return _Filler184;
    }
    
    // Standard Setter
    public void SetFiller184(string value)
    {
        _Filler184 = value;
    }
    
    // Get<>AsString()
    public string GetFiller184AsString()
    {
        return _Filler184.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller184AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler184 = value;
    }
    
    // Standard Getter
    public string GetFiller185()
    {
        return _Filler185;
    }
    
    // Standard Setter
    public void SetFiller185(string value)
    {
        _Filler185 = value;
    }
    
    // Get<>AsString()
    public string GetFiller185AsString()
    {
        return _Filler185.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller185AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler185 = value;
    }
    
    // Standard Getter
    public string GetFiller186()
    {
        return _Filler186;
    }
    
    // Standard Setter
    public void SetFiller186(string value)
    {
        _Filler186 = value;
    }
    
    // Get<>AsString()
    public string GetFiller186AsString()
    {
        return _Filler186.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller186AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler186 = value;
    }
    
    // Standard Getter
    public string GetFiller187()
    {
        return _Filler187;
    }
    
    // Standard Setter
    public void SetFiller187(string value)
    {
        _Filler187 = value;
    }
    
    // Get<>AsString()
    public string GetFiller187AsString()
    {
        return _Filler187.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller187AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler187 = value;
    }
    
    // Standard Getter
    public string GetFiller188()
    {
        return _Filler188;
    }
    
    // Standard Setter
    public void SetFiller188(string value)
    {
        _Filler188 = value;
    }
    
    // Get<>AsString()
    public string GetFiller188AsString()
    {
        return _Filler188.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller188AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler188 = value;
    }
    
    // Standard Getter
    public string GetFiller189()
    {
        return _Filler189;
    }
    
    // Standard Setter
    public void SetFiller189(string value)
    {
        _Filler189 = value;
    }
    
    // Get<>AsString()
    public string GetFiller189AsString()
    {
        return _Filler189.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller189AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler189 = value;
    }
    
    // Standard Getter
    public string GetFiller190()
    {
        return _Filler190;
    }
    
    // Standard Setter
    public void SetFiller190(string value)
    {
        _Filler190 = value;
    }
    
    // Get<>AsString()
    public string GetFiller190AsString()
    {
        return _Filler190.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller190AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler190 = value;
    }
    
    // Standard Getter
    public string GetFiller191()
    {
        return _Filler191;
    }
    
    // Standard Setter
    public void SetFiller191(string value)
    {
        _Filler191 = value;
    }
    
    // Get<>AsString()
    public string GetFiller191AsString()
    {
        return _Filler191.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetFiller191AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler191 = value;
    }
    
    // Standard Getter
    public string GetFiller192()
    {
        return _Filler192;
    }
    
    // Standard Setter
    public void SetFiller192(string value)
    {
        _Filler192 = value;
    }
    
    // Get<>AsString()
    public string GetFiller192AsString()
    {
        return _Filler192.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller192AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler192 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}