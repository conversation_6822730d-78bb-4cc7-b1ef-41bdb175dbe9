using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WStoredFundSedol Data Structure

public class WStoredFundSedol
{
    private static int _size = 11;
    // [DEBUG] Class: WStoredFundSedol, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WStoredFund, is_external=, is_static_class=False, static_prefix=
    private string _WStoredFund ="";
    
    
    
    
    // [DEBUG] Field: WStoredSedol, is_external=, is_static_class=False, static_prefix=
    private string _WStoredSedol ="";
    
    
    
    
    
    // Serialization methods
    public string GetWStoredFundSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WStoredFund.PadRight(4));
        result.Append(_WStoredSedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetWStoredFundSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWStoredFund(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWStoredSedol(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWStoredFundSedolAsString();
    }
    // Set<>String Override function
    public void SetWStoredFundSedol(string value)
    {
        SetWStoredFundSedolAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWStoredFund()
    {
        return _WStoredFund;
    }
    
    // Standard Setter
    public void SetWStoredFund(string value)
    {
        _WStoredFund = value;
    }
    
    // Get<>AsString()
    public string GetWStoredFundAsString()
    {
        return _WStoredFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWStoredFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WStoredFund = value;
    }
    
    // Standard Getter
    public string GetWStoredSedol()
    {
        return _WStoredSedol;
    }
    
    // Standard Setter
    public void SetWStoredSedol(string value)
    {
        _WStoredSedol = value;
    }
    
    // Get<>AsString()
    public string GetWStoredSedolAsString()
    {
        return _WStoredSedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWStoredSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WStoredSedol = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}