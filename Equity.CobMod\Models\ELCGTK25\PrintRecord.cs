using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing PrintRecord Data Structure

public class PrintRecord
{
    private static int _size = 181;
    // [DEBUG] Class: PrintRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _PrintControl ="";
    
    
    
    
    // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
    private string _Filler27 ="";
    
    
    
    
    // [DEBUG] Field: PrintLaserControl, is_external=, is_static_class=False, static_prefix=
    private string _PrintLaserControl ="";
    
    
    
    
    
    // Serialization methods
    public string GetPrintRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_PrintControl.PadRight(1));
        result.Append(_Filler27.PadRight(179));
        result.Append(_PrintLaserControl.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetPrintRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetPrintControl(extracted);
        }
        offset += 1;
        if (offset + 179 <= data.Length)
        {
            string extracted = data.Substring(offset, 179).Trim();
            SetFiller27(extracted);
        }
        offset += 179;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetPrintLaserControl(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetPrintRecordAsString();
    }
    // Set<>String Override function
    public void SetPrintRecord(string value)
    {
        SetPrintRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetPrintControl()
    {
        return _PrintControl;
    }
    
    // Standard Setter
    public void SetPrintControl(string value)
    {
        _PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetPrintControlAsString()
    {
        return _PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetPrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _PrintControl = value;
    }
    
    // Standard Getter
    public string GetFiller27()
    {
        return _Filler27;
    }
    
    // Standard Setter
    public void SetFiller27(string value)
    {
        _Filler27 = value;
    }
    
    // Get<>AsString()
    public string GetFiller27AsString()
    {
        return _Filler27.PadRight(179);
    }
    
    // Set<>AsString()
    public void SetFiller27AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler27 = value;
    }
    
    // Standard Getter
    public string GetPrintLaserControl()
    {
        return _PrintLaserControl;
    }
    
    // Standard Setter
    public void SetPrintLaserControl(string value)
    {
        _PrintLaserControl = value;
    }
    
    // Get<>AsString()
    public string GetPrintLaserControlAsString()
    {
        return _PrintLaserControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetPrintLaserControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _PrintLaserControl = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
