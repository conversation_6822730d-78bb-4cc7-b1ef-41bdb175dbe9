﻿using System;
using System.Linq; // Needed for Array.IndexOf
using EquityProject.CommonDTO;
using EquityProject.CgtdateDTO;

namespace EquityProject.CgtdatePGM
{
    // Cgtdate Class Definition
    public class Cgtdate
    {
        // Declare Cgtdate Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare Cgtdate Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        // Constructor - Initializes Gvar which contains the month table default
        public Cgtdate()
        {
            // Ensure the month table is initialized correctly from the default string
            InitializeMonthTable();
        }

        // Helper to initialize or reset the month table array from the string
        private void InitializeMonthTable()
        {
            string monthData = _gvar.GetWs2MonthTable().GetWs21(); // "312831303130313130313031"
            if (monthData.Length == 24)
            {
                for (int i = 0; i < 12; i++)
                {
                    if (int.TryParse(monthData.Substring(i * 2, 2), out int days))
                    {
                        // C# array is 0-based, COBOL OCCURS is 1-based
                        _gvar.GetWs2MonthTable().GetFiller1().SetWs2DaysInMonthAt(i, days);
                    }
                    else
                    {
                        // Handle error - default string is invalid? Should not happen with VALUE clause.
                        // Set to a default or throw? For now, let's assume VALUE is correct.
                        _gvar.GetWs2MonthTable().GetFiller1().SetWs2DaysInMonthAt(i, 0);
                    }
                }
            }
            else
            {
                // Handle error - default string length is wrong
                // Initialize with some safe defaults?
                int[] defaultDays = { 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 };
                for (int i = 0; i < 12; i++)
                {
                    _gvar.GetWs2MonthTable().GetFiller1().SetWs2DaysInMonthAt(i, defaultDays[i]);
                }
            }
        }


        // Run() method - Main logic corresponding to PROCEDURE DIVISION
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Use passed-in instances
            this._gvar = gvar;
            this._ivar = ivar;

            // Ensure the month table is reset to defaults before validation
            InitializeMonthTable();

            // Access Linkage Section data
            LValidDate lValidDate = ivar.GetLValidDate();
            LValidDate.LDate lDate = lValidDate.GetLDate(); // Convenience

            // Access Working Storage data
            Ws3WorkArea ws3WorkArea = gvar.GetWs3WorkArea();
            // We need a local copy of the days array because we modify February for leap years
            int[] daysInMonth = new int[12];
            for (int i = 0; i < 12; i++)
            {
                daysInMonth[i] = gvar.GetWs2MonthTable().GetFiller1().GetWs2DaysInMonthAt(i);
            }


            // A-CONTROL SECTION.
            // A10.
            // MOVE ZERO TO L-DATE-RETURN-CODE.
            lValidDate.SetLDateReturnCode(0);

            // Since DTO parsing converts to int, "NOT NUMERIC" check is replaced by range check.
            // Assuming DTO parsing handles truly non-numeric input (e.g., sets to 0 or throws).
            // We check if the resulting integer is within the valid range for YY (00-99).
            // IF L-YY NOT NUMERIC -> Check range 0-99
            if (lDate.GetLYy() < 0 || lDate.GetLYy() > 99)
            {
                // MOVE 1 TO L-DATE-RETURN-CODE
                lValidDate.SetLDateReturnCode(1);
                // GO TO A40-ERROR. -> translated to return
                goto A90_Exit; // Use goto for direct equivalent of COBOL structure
            }

            // IF (NOT VALID-MONTH) OR (L-MM NOT NUMERIC) -> Check range 1-12
            if (lDate.GetLMm() < 1 || lDate.GetLMm() > 12)
            {
                // MOVE 2 TO L-DATE-RETURN-CODE
                lValidDate.SetLDateReturnCode(2);
                // GO TO A40-ERROR.
                goto A90_Exit;
            }

            // IF (L-DD = ZERO) OR (L-DD NOT NUMERIC) -> Check if day is 1 or greater
            if (lDate.GetLDd() < 1) // Also covers the ZERO case
            {
                // MOVE 3 TO L-DATE-RETURN-CODE
                lValidDate.SetLDateReturnCode(3);
                // GO TO A40-ERROR.
                goto A90_Exit;
            }

            // IF L-MM NOT = 2
            if (lDate.GetLMm() != 2)
            {
                // GO TO A30. -> Fall through to A30 logic
            }
            else // Month is February, perform leap year check
            {
                // A20.
                // DIVIDE 4 INTO L-YY GIVING WS-3-TEMP-YEAR REMAINDER WS-3-REM.
                // In C#, use the modulo operator %
                ws3WorkArea.SetWs3Rem(lDate.GetLYy() % 4);
                // Note: WS-3-TEMP-YEAR (the quotient) isn't used further in COBOL.

                // IF WS-3-REM = ZERO
                if (ws3WorkArea.GetWs3Rem() == 0)
                {
                    // MOVE 29 TO WS-2-DAYS-IN-MONTH (2) -> Modify our local copy
                    // C# index for February is 1 (0-based)
                    daysInMonth[1] = 29;
                }
                else
                {
                    // MOVE 28 TO WS-2-DAYS-IN-MONTH (2) -> Modify our local copy
                    daysInMonth[1] = 28;
                }
                // Leap year check complete, proceed to A30 logic
            }

            // A30.
            // Check if the day is valid for the month (using the potentially updated daysInMonth array)
            // COBOL index L-MM (1-12), C# index lMm - 1 (0-11)
            int monthIndex = lDate.GetLMm() - 1;
            // IF L-DD GREATER THAN WS-2-DAYS-IN-MONTH (L-MM)
            if (lDate.GetLDd() > daysInMonth[monthIndex])
            {
                // MOVE 3 TO L-DATE-RETURN-CODE
                lValidDate.SetLDateReturnCode(3);
                // GO TO A40-ERROR.
                goto A90_Exit;
            }

        // GO TO A90. -> Fall through to A90_Exit label

        // A40-ERROR. - This label is only a target in COBOL, logic is handled before the goto.

        A90_Exit:
            // A90.
            // EXIT PROGRAM. -> Return from the Run method
            return;

            // A99. EXIT. - End of section, implicit in C# method structure
        }


        // CallSub() method (from template, not used by COBOL logic shown)
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here if needed
        }

        // Main() method of the Cgtdate Class - Example entry point for testing
        /*public static void Main(string[] args)
        {
            Cgtdate program = new Cgtdate();
            Gvar gvar = program.GetGvar(); // Use the Gvar from the instance
            Ivar ivar = new Ivar();

            // Test Cases
            int[] testYears = { 24, 23, 24, 00, 25, 25, 25, 25, 99, 04 };
            int[] testMonths = { 01, 02, 02, 02, 04, 13, 06, 02, 12, 00 };
            int[] testDays = { 31, 28, 29, 29, 31, 10, 00, 30, 32, 15 };
            int[] expectedCodes = { 0, 0, 0, 0, 3, 2, 3, 3, 3, 2 }; // Expected return codes

            Console.WriteLine("--- CGTDATE Test Cases ---");
            Console.WriteLine("Date (DDMMYY) | Expected | Actual");
            Console.WriteLine("--------------|----------|--------");

            for (int i = 0; i < testYears.Length; i++)
            {
                LValidDate testDate = ivar.GetLValidDate();
                testDate.GetLDate().SetLDd(testDays[i]);
                testDate.GetLDate().SetLMm(testMonths[i]);
                testDate.GetLDate().SetLYy(testYears[i]);
                testDate.SetLDateReturnCode(9); // Set initial code to see change

                program.Run(gvar, ivar); // Call the validation logic

                Console.WriteLine($"{testDate.GetLDateAsString(),-13} | {expectedCodes[i],-8} | {testDate.GetLDateReturnCode()}");
            }
            Console.WriteLine("--------------------------");
        }*/
    }
}