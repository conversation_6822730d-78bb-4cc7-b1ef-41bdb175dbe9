using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// <Section> Class for Ivar
public class Ivar
{
public Ivar() {}

// Fields in the class


// [DEBUG] Field: ParmInfo, is_external=, is_static_class=False, static_prefix=
private ParmInfo _ParmInfo = new ParmInfo();




// Getter and Setter methods

// Standard Getter
public ParmInfo GetParmInfo()
{
    return _ParmInfo;
}

// Standard Setter
public void SetParmInfo(ParmInfo value)
{
    _ParmInfo = value;
}

// Get<>AsString()
public string GetParmInfoAsString()
{
    return _ParmInfo != null ? _ParmInfo.GetParmInfoAsString() : "";
}

// Set<>AsString()
public void SetParmInfoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ParmInfo == null)
    {
        _ParmInfo = new ParmInfo();
    }
    _ParmInfo.SetParmInfoAsString(value);
}



}}
