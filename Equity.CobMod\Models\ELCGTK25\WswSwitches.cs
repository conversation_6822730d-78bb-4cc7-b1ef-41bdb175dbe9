using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing WswSwitches Data Structure

public class WswSwitches
{
    private static int _size = 6;
    // [DEBUG] Class: WswSwitches, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WswEndofile, is_external=, is_static_class=False, static_prefix=
    private string _WswEndofile ="\0";
    
    
    // 88-level condition checks for WswEndofile
    public bool IsK25EndOfFile()
    {
        if (this._WswEndofile == "HIGH-VALUES") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WswBdv, is_external=, is_static_class=False, static_prefix=
    private string _WswBdv ="N";
    
    
    
    
    // [DEBUG] Field: WswLimit, is_external=, is_static_class=False, static_prefix=
    private string _WswLimit ="N";
    
    
    
    
    // [DEBUG] Field: WswProfitOrLoss, is_external=, is_static_class=False, static_prefix=
    private string _WswProfitOrLoss ="N";
    
    
    
    
    // [DEBUG] Field: WswFundFound, is_external=, is_static_class=False, static_prefix=
    private string _WswFundFound ="Y";
    
    
    // 88-level condition checks for WswFundFound
    public bool IsFundFound()
    {
        if (this._WswFundFound == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsTrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsTrancheFlag ="";
    
    
    
    
    
    // Serialization methods
    public string GetWswSwitchesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WswEndofile.PadRight(1));
        result.Append(_WswBdv.PadRight(1));
        result.Append(_WswLimit.PadRight(1));
        result.Append(_WswProfitOrLoss.PadRight(1));
        result.Append(_WswFundFound.PadRight(1));
        result.Append(_WsTrancheFlag.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWswSwitchesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWswEndofile(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWswBdv(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWswLimit(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWswProfitOrLoss(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWswFundFound(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsTrancheFlag(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWswSwitchesAsString();
    }
    // Set<>String Override function
    public void SetWswSwitches(string value)
    {
        SetWswSwitchesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWswEndofile()
    {
        return _WswEndofile;
    }
    
    // Standard Setter
    public void SetWswEndofile(string value)
    {
        _WswEndofile = value;
    }
    
    // Get<>AsString()
    public string GetWswEndofileAsString()
    {
        return _WswEndofile.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWswEndofileAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WswEndofile = value;
    }
    
    // Standard Getter
    public string GetWswBdv()
    {
        return _WswBdv;
    }
    
    // Standard Setter
    public void SetWswBdv(string value)
    {
        _WswBdv = value;
    }
    
    // Get<>AsString()
    public string GetWswBdvAsString()
    {
        return _WswBdv.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWswBdvAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WswBdv = value;
    }
    
    // Standard Getter
    public string GetWswLimit()
    {
        return _WswLimit;
    }
    
    // Standard Setter
    public void SetWswLimit(string value)
    {
        _WswLimit = value;
    }
    
    // Get<>AsString()
    public string GetWswLimitAsString()
    {
        return _WswLimit.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWswLimitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WswLimit = value;
    }
    
    // Standard Getter
    public string GetWswProfitOrLoss()
    {
        return _WswProfitOrLoss;
    }
    
    // Standard Setter
    public void SetWswProfitOrLoss(string value)
    {
        _WswProfitOrLoss = value;
    }
    
    // Get<>AsString()
    public string GetWswProfitOrLossAsString()
    {
        return _WswProfitOrLoss.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWswProfitOrLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WswProfitOrLoss = value;
    }
    
    // Standard Getter
    public string GetWswFundFound()
    {
        return _WswFundFound;
    }
    
    // Standard Setter
    public void SetWswFundFound(string value)
    {
        _WswFundFound = value;
    }
    
    // Get<>AsString()
    public string GetWswFundFoundAsString()
    {
        return _WswFundFound.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWswFundFoundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WswFundFound = value;
    }
    
    // Standard Getter
    public string GetWsTrancheFlag()
    {
        return _WsTrancheFlag;
    }
    
    // Standard Setter
    public void SetWsTrancheFlag(string value)
    {
        _WsTrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsTrancheFlagAsString()
    {
        return _WsTrancheFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsTrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsTrancheFlag = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
