      $SET ANS85

      *************************************************************************
      *                                                                       *
      * Copyright:      Wolters Kluwer UK 2004                                *
      *                                                                       *
      * This Software is provided under the terms of a Licence Agreement and  *
      * may only be used  and/or copied in accordance with the terms of such  *
      * Agreement. Neither this Software nor any copy thereof may be provided *
      * or otherwise made available to any other person.  No title to or      *
      * ownership of this software is hereby transferred.                     *
      *                                                                       *
      *************************************************************************

       IDENTIFICATION DIVISION.
*******PROGRAM-ID.            CGTFILES.
       AUTHOR.                JULIE.
       DATE-WRITTEN.          10 JAN 89.
      *
      **********************************************************
      *  To compile pgm need to set segsize to 64000           *
      *  i.e. XCG CGTFILES SEGSIZE(64000)                      *
      *  Also pgm is one of those built into MICROCGT.EXE      *
      **********************************************************
      *  This module is called from throughout the system to   *
      *  perform all I/O operations except those already done  *
      *  through the Investor+ Master File handler, ELCGTX24.  *
      *                                                        *
      *  A linkage request area is passed to this program.     *
      *  This area details : Table ID, the I/O operation,      *
      *  record area and return code.                          *
      *                                                        *
      *   The relevant operation is performed, with the        *
      *   linkage return code and record area being updated.   *
      **********************************************************
      *****************************************************************
      * AMENDMENT LOG :                                               *
      * CCR NO.   DATE            COMMENTS/REASON                     *
      *****************************************************************
      *        |30/04/92|EJ1 Amendment for D4-NEW-SUMMARY-CODE, a key.*
      *---------------------------------------------------------------*
BB10  *        |26/05/92|    All references to BALANCE-LOAD-REPORT    *
      *        |        |           changed to BALANCES-LOAD-REPORT.  *
      *---------------------------------------------------------------*
EJ2   *        |02/06/92|    Add new Report file (D85)                *
      *        |        |           OFFSHORE-INCOME-REPORT.           *
      *---------------------------------------------------------------*
MJ1   *        |??/??/92|    Add new        file processing for RCF   *
      *        |        |          S.46 Fraction (D86)                *
      *---------------------------------------------------------------*
DGD   *        |14/10/92|    Add new Backup file processing for RCF   *
      *        |        |          S.46 Fraction (D87)                *
      *---------------------------------------------------------------*
BB11  *        | 6/1/93 |    Add new sequential files for RPI, Group  *
BB11  *        |        |      and Country Loads (data & report files)*
      *---------------------------------------------------------------*
BB12  *CGT/0040|26/5/93 |    Add new sequential files for Charterhouse*
BB12  *        |        |      Tilney gainloss report & data files    *
      *---------------------------------------------------------------*
BB13  *CGT/0063|10/6/93 | Change G/L file name from $uuuuRLg.DAT/IDX  *
BB13  *        |        |                        to $uuuuDLg.DAT/IDX  *
      *---------------------------------------------------------------*
BB14  *CGT/0110| 6/7/94 | New report - Lost Indexation                *
      *---------------------------------------------------------------*
BB15  *CGT/0130|18/8/94 | New report - Lost Indexation (Unrealised)   *
      *---------------------------------------------------------------*
EJ3   *S&FCCR03|22/09/94| Change size of sedol header from 250 to 274 *
      *        |        | for SEQ-BALANCE-FILE.                       *
      *---------------------------------------------------------------*
BB16  *WINDOWS |26/5/95 | Fix problems with CGTVIEW which is now      *
BB16  *        |        | passing different action codes to this pgm  *
      *---------------------------------------------------------------*
BB17  *CGT/170 |24/7/96 | Add new files for Bond changes              *
DGD1  *CGT/170 |13/9/96 | Insert CONTINUE for error free compile.     *
      *CGT/703 |20/11/96| Increase rec-length for sedol header to 300 *
      *---------------------------------------------------------------*
      *NOTE - FOLLOWING CHANGES BB18/19 ARE MADE TO RCS VERSION 1.12  * 
      *     - BB18 CHANGES COPIED FROM RCS BRANCH 1.8.1.1             * 
      *        |        |                                             *
BB18  *CGT/189 |24/03/97| Year-end balance filename changed from MF-YE*
BB18  *        |        |  to $uuuuMYE (where uuuu is user number).   *
      *        |        |                                             *
BB19  *CGT/198 |24/3/97 | Add held over gain reports for Gilts & Bonds*
      *        |        |                                             *
BB20  *CGT/222 |27/5/97 | Add acquisition export report.              *
      *---------------------------------------------------------------*
BB21  *WINDOWS |24/12/99| File names to use paths from environment.   *
      *        |        | Add file action close all.                  *
      *        |        | Add new file: Batch Run Log.                *
      *---------------------------------------------------------------*
BB22  *CGT/258 |30/01/00| Add Taperising rate/report/export files.    *
      *---------------------------------------------------------------*
BB23  *CGT/10220| 20/3/03 | Key definitions of schedule data files    *
BB23  *         |         | expanded to include taper date after      *
BB23  *         |         | acquisition date.                         *
      *---------------------------------------------------------------*
BB24  *CGT/10237| 30/6/04 | New definitions for user defined periods  *
      *---------------------------------------------------------------*
BB25  *S&F171104|12/12/04 | New definitions for user losses           *
      *---------------------------------------------------------------*
BB26  *HBOS28022005|11/5/05| New definitions for Inter connected funds*
      *****************************************************************
BB27  *CITI0001 | 03/11/05 | New definitions for Price Types, Pending *
BB27  *         |          |  files and Daily Transaction Export.     *
      *****************************************************************
      * DTV     | 25/06/12 | Unused code commented/deleted            *
      *****************************************************************
dtv004* DTV     | 16/08/12 | new line write in run log file           *
      *****************************************************************   
      *
       ENVIRONMENT DIVISION.
       CONFIGURATION SECTION.
       SOURCE-COMPUTER.       IBM-PC-XT.
       OBJECT-COMPUTER.       IBM-PC-XT.
      /
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
      * D1 - Country File
BB21       SELECT D1 ASSIGN D1-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D1-KEY
               ALTERNATE RECORD KEY IS D1-INVERSE-KEY
               STATUS IS FILE-STATUS.
      * D2 - Group File
BB21       SELECT D2 ASSIGN D2-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D2-KEY
               ALTERNATE RECORD KEY IS D2-INVERSE-KEY
               STATUS IS FILE-STATUS.
      * D3 - Stock File
BB21       SELECT D3 ASSIGN D3-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D3-KEY
               ALTERNATE RECORD KEY IS D3-INVERSE-KEY
   BB5         ALTERNATE RECORD KEY IS D3-SPLIT-KEY = D3-ISSUER
   BB5                                                D3-DESCRIPTION
   BB5                                                D3-SEDOL-CODE
               STATUS IS FILE-STATUS.
      * D4 - Fund File
BB21       SELECT D4 ASSIGN D4-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
               RECORD KEY D4-KEY
BB3            LOCK MODE IS MANUAL WITH LOCK ON MULTIPLE RECORDS
               ALTERNATE RECORD KEY IS D4-SUMMARY-FUND WITH DUPLICATES
EJ1            ALTERNATE RECORD KEY IS D4-NEW-SUMMARY-FUND
EJ1                                                    WITH DUPLICATES
               ALTERNATE RECORD KEY IS D4-INVERSE-KEY
               STATUS IS FILE-STATUS.
      * D5 - Cgtr04 Report File
BB21       SELECT D5 ASSIGN D5-FILE-NAME
BB2            ORGANIZATION LINE SEQUENTIAL
BB2            STATUS IS FILE-STATUS.
      * D6 - Cgtr05 Report File
BB21        SELECT D6 ASSIGN D6-FILE-NAME
BB2            ORGANIZATION LINE SEQUENTIAL
BB2            STATUS IS FILE-STATUS.
      * D7 - Rpi File
BB21       SELECT D7 ASSIGN D7-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D7-KEY
               STATUS IS FILE-STATUS.
      * D8 - Parameter File
BB21       SELECT D8 ASSIGN D8-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D8-KEY
               STATUS IS FILE-STATUS.
      * D9 - User File
BB21       SELECT D9 ASSIGN D9-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
               RECORD KEY D9-KEY
BB3            LOCK MODE IS MANUAL WITH LOCK ON MULTIPLE RECORDS
               ALTERNATE RECORD KEY IS D9-INVERSE-KEY
               STATUS IS FILE-STATUS.
      * D10 - Sterling Extel Report
BB21       SELECT D10 ASSIGN D10-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D11 - Foreign Extel Report
BB21        SELECT D11 ASSIGN D11-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D12 - Output Listing
BB21       SELECT D12 ASSIGN D12-FILE-NAME
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
               RECORD KEY D12-KEY
               STATUS IS FILE-STATUS.
      * D17 - Master Log File
BB21       SELECT D17 ASSIGN D17-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D17-KEY
               STATUS IS FILE-STATUS.
      * D18 - Error Report File
BB21        SELECT D18 ASSIGN D18-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D19 - Realised Data File
BB21       SELECT D19 ASSIGN D19-FILE-NAME
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
Y2K            RECORD KEY D19-SPLIT-KEY = D19-SPLIT-KEY-01
BB23                                      D19-SPLIT-KEY-02
BB23                                      D19-SPLIT-KEY-03
BB23                                      D19-SPLIT-KEY-04 
BB23                                      D19-SPLIT-KEY-05 
BB23                                      D19-SPLIT-KEY-06 
BB23                                      D19-SPLIT-KEY-07 
               ALTERNATE RECORD KEY 
                     IS D19-SPLIT-KEY-2 = D19-TRANCHE-FLAG
                                          D19-CO-AC-LK
                                          D19-SEDOL-SORT
                                          D19-TRANCHE-CONTRACT-NUMBER
                     WITH DUPLICATES
               STATUS IS FILE-STATUS.
      * D20 - Unrealised Data File
BB21       SELECT D20 ASSIGN D20-FILE-NAME
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
Y2K            RECORD KEY D20-SPLIT-KEY = D20-SPLIT-KEY-01
BB23                                      D20-SPLIT-KEY-02
BB23                                      D20-SPLIT-KEY-03
BB23                                      D20-SPLIT-KEY-04
BB23                                      D20-SPLIT-KEY-05 
BB23                                      D20-SPLIT-KEY-06 
BB23                                      D20-SPLIT-KEY-07 
               ALTERNATE RECORD KEY 
                     IS D20-SPLIT-KEY-2 = D20-TRANCHE-FLAG
                                          D20-CO-AC-LK
                                          D20-SEDOL-SORT
                                          D20-TRANCHE-CONTRACT-NUMBER
                     WITH DUPLICATES
               STATUS IS FILE-STATUS.
      * D21 - Realised Schedule File
BB21       SELECT D21 ASSIGN D21-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D22 - Unrealised Schedule File
BB21       SELECT D22 ASSIGN D22-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D23 - Cg01 Report File
BB21       SELECT D23 ASSIGN D23-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D24 - Cg02 Report File
BB21       SELECT D24 ASSIGN D24-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D25 - Cg03 Report File
BB21       SELECT D25 ASSIGN D25-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D26 - Ye Rec Report File
BB21       SELECT D26 ASSIGN D26-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D27 - Ye Del Report File
BB21       SELECT D27 ASSIGN D27-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D28 - Ye Con Report File
BB21       SELECT D28 ASSIGN D28-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D29 - Error Data File
BB21       SELECT D29 ASSIGN D29-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D30 - Ye Err Report File
BB21       SELECT D30 ASSIGN D30-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D31 - Printer File
BB21       SELECT D31 ASSIGN D31-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D31-KEY
   BB4         ALTERNATE RECORD KEY IS D31-INVERSE-KEY
               STATUS IS FILE-STATUS.
      * D32 - Stock Type File
BB21       SELECT D32 ASSIGN D32-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D32-KEY
               STATUS IS FILE-STATUS.
      * D33 - Ye Rec2 Data File
BB21       SELECT D33 ASSIGN D33-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D34 - Transaction Code File
BB21       SELECT D34 ASSIGN D34-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D34-KEY
               STATUS IS FILE-STATUS.
      * D35 - Output Log File
BB21       SELECT D35 ASSIGN D35-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D35-KEY
               STATUS IS FILE-STATUS.
      * D36 - Message File
BB21       SELECT D36 ASSIGN D36-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D36-KEY
               STATUS IS FILE-STATUS.
      * D38 - Help Text File
BB21       SELECT D38 ASSIGN D38-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D38-KEY
               STATUS IS FILE-STATUS.
      * D39 - Default Access File
BB21       SELECT D39 ASSIGN D39-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D39-KEY
               STATUS IS FILE-STATUS.
      * D40 - Access Profile File
BB21       SELECT D40 ASSIGN D40-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D40-KEY
               STATUS IS FILE-STATUS.
      * D41 - Extel Prices File
BB21       SELECT D41 ASSIGN D41-FILE-NAME 
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D42 - Extel Currency File
BB21       SELECT D42 ASSIGN D42-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D42-KEY
               STATUS IS FILE-STATUS.
      * D43 - Stock Price File
BB21       SELECT D43 ASSIGN D43-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
BB3            LOCK MODE IS MANUAL
               RECORD KEY D43-KEY
               STATUS IS FILE-STATUS.
      * D44 - Extel Transmission File
BB21       SELECT D44 ASSIGN D44-FILE-NAME 
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D45 - Seq Balance File
BB21       SELECT D45 ASSIGN D45-FILE-NAME
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
               RECORD KEY D45-KEY
               STATUS IS FILE-STATUS.
      * D46 - Transaction File
BB21       SELECT D46 ASSIGN D46-FILE-NAME
BB2            ORGANIZATION LINE SEQUENTIAL
BB2            STATUS IS FILE-STATUS.
      * D49 - Ye Bal Report File
BB21       SELECT D49 ASSIGN D49-FILE-NAME
   BB4         ORGANIZATION LINE SEQUENTIAL
   BB4         STATUS IS FILE-STATUS.
      * D50 - Stock Load Report
BB21       SELECT D50 ASSIGN D50-FILE-NAME
   BB4         ORGANIZATION LINE SEQUENTIAL
   BB4         STATUS IS FILE-STATUS.
      * D51 - Stock Load Data File
BB21       SELECT D51 ASSIGN D51-FILE-NAME
   BB4         ORGANIZATION LINE SEQUENTIAL
   BB4         STATUS IS FILE-STATUS.
      * D68 - Funds Load Data File
BB21       SELECT D68 ASSIGN D68-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D69 - Funds Load Report
BB21       SELECT D69 ASSIGN D69-FILE-NAME
               ORGANIZATION LINE SEQUENTIAL
               STATUS IS FILE-STATUS.
      * D70 - Price Load Data File
BB21       SELECT D70 ASSIGN D70-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D71 - Price Load Report
BB21       SELECT D71 ASSIGN D71-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D72 - Skan1 Report
BB21       SELECT D72 ASSIGN D72-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D73 - Skan2 Report
BB21       SELECT D73 ASSIGN D73-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D74 - New Realised Report
BB21       SELECT D74 ASSIGN D74-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D75 - New Unrealised Report
BB21       SELECT D75 ASSIGN D75-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D76 - Mgm1 Report File
BB21       SELECT D76 ASSIGN D76-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D77 - Capital Report File
BB21       SELECT D77 ASSIGN D77-FILE-NAME
BB8            ORGANIZATION LINE SEQUENTIAL
BB8            STATUS IS FILE-STATUS.
      * D78 - Replacement Relief Report
BB21       SELECT D78 ASSIGN D78-FILE-NAME
 BB9           ORGANIZATION LINE SEQUENTIAL
 BB9           STATUS IS FILE-STATUS.
      * D79 - Replacement Acq File
BB21       SELECT D79 ASSIGN D79-FILE-NAME
 BB9           ORGANIZATION INDEXED
 BB9           ACCESS DYNAMIC
 BB9           LOCK MODE IS MANUAL
 BB9           RECORD KEY D79-KEY
 BB9           STATUS IS FILE-STATUS.
      * D80 - Replacement Dis File
BB21       SELECT D80 ASSIGN D80-FILE-NAME
 BB9           ORGANIZATION INDEXED
 BB9           ACCESS DYNAMIC
 BB9           LOCK MODE IS MANUAL
 BB9           RECORD KEY D80-KEY
 BB9           STATUS IS FILE-STATUS.
      * D81 - Notional Sale Data File
BB21       SELECT D81 ASSIGN D81-FILE-NAME
 BB9           ORGANIZATION INDEXED
 BB9           ACCESS DYNAMIC
Y2K            RECORD KEY D81-SPLIT-KEY = D81-SPLIT-KEY-01
BB23                                      D81-SPLIT-KEY-02
BB23                                      D81-SPLIT-KEY-03
BB23                                      D81-SPLIT-KEY-04 
BB23                                      D81-SPLIT-KEY-05 
BB23                                      D81-SPLIT-KEY-06 
BB23                                      D81-SPLIT-KEY-07 
               ALTERNATE RECORD KEY 
                     IS D81-SPLIT-KEY-2 = D81-TRANCHE-FLAG
                                          D81-CO-AC-LK
                                          D81-SEDOL-SORT
                                          D81-TRANCHE-CONTRACT-NUMBER
                     WITH DUPLICATES
 BB9           STATUS IS FILE-STATUS.
      * D82 - Notional Sale Schedule File
BB21       SELECT D82 ASSIGN D82-FILE-NAME
 BB9           ORGANIZATION LINE SEQUENTIAL
 BB9           STATUS IS FILE-STATUS.
      * D83 - Balance Load Data File
BB21       SELECT D83 ASSIGN D83-FILE-NAME
 BB9           ORGANIZATION LINE SEQUENTIAL
 BB9           STATUS IS FILE-STATUS.
      * D84 - Balances Load Report
BB21       SELECT D84 ASSIGN D84-FILE-NAME
 BB9           ORGANIZATION LINE SEQUENTIAL
 BB9           STATUS IS FILE-STATUS.
      * D85 - Offshore Income Report
BB21       SELECT D85 ASSIGN D85-FILE-NAME
EJ2            ORGANIZATION LINE SEQUENTIAL
EJ2            STATUS IS FILE-STATUS.
      * D86 - Rcf File
BB21       SELECT D86 ASSIGN D86-FILE-NAME 
MJ1            ORGANIZATION INDEXED
MJ1            ACCESS DYNAMIC
MJ1            LOCK MODE IS MANUAL
MJ1            RECORD KEY D86-KEY
MJ1            STATUS IS FILE-STATUS.
      * D87 - Rcf Backup File
BB21       SELECT D87 ASSIGN D87-FILE-NAME
DGD            ORGANIZATION LINE SEQUENTIAL
DGD            STATUS IS FILE-STATUS.
      * D88 - Group Load Report
BB21       SELECT D88 ASSIGN D88-FILE-NAME
BB11           ORGANIZATION LINE SEQUENTIAL
BB11           STATUS IS FILE-STATUS.
      * D89 - Group Load Data File
BB21       SELECT D89 ASSIGN D89-FILE-NAME
BB11           ORGANIZATION LINE SEQUENTIAL
BB11           STATUS IS FILE-STATUS.
      * D90 - Country Load Report
BB21       SELECT D90 ASSIGN D90-FILE-NAME
BB11           ORGANIZATION LINE SEQUENTIAL
BB11           STATUS IS FILE-STATUS.
      * D91 - Country Load Data File
BB21       SELECT D91 ASSIGN D91-FILE-NAME
BB11           ORGANIZATION LINE SEQUENTIAL
BB11           STATUS IS FILE-STATUS.
      * D92 - Rpi Load Report
BB21       SELECT D92 ASSIGN D92-FILE-NAME
BB11           ORGANIZATION LINE SEQUENTIAL
BB11           STATUS IS FILE-STATUS.
      * D93 - Rpi Load Data File
BB21       SELECT D93 ASSIGN D93-FILE-NAME
BB11           ORGANIZATION LINE SEQUENTIAL
BB11           STATUS IS FILE-STATUS.
      * D94 - Gainloss Data File
BB21       SELECT D94 ASSIGN D94-FILE-NAME
BB12           ORGANIZATION INDEXED
BB12           ACCESS DYNAMIC
BB12           RECORD KEY D94-KEY
BB12           STATUS IS FILE-STATUS.
      * D95 - Gainloss Report
BB21       SELECT D95 ASSIGN D95-FILE-NAME
BB12           ORGANIZATION LINE SEQUENTIAL
BB12           STATUS IS FILE-STATUS.
      *
      * D96 - Lost Indexation Report
BB17  * Note - D96 below is accessed via D99
      * D97 - Lost Indexation Report
BB17  * Note - D97 below is accessed via D99
      *
      * D98 - Realised Tax Data File
BB21       SELECT D98 ASSIGN D98-FILE-NAME
BB17           ORGANIZATION INDEXED
BB17           ACCESS DYNAMIC
Y2K            RECORD KEY D98-SPLIT-KEY = D98-SPLIT-KEY-01
BB23                                      D98-SPLIT-KEY-02
BB23                                      D98-SPLIT-KEY-03
BB23                                      D98-SPLIT-KEY-04 
BB23                                      D98-SPLIT-KEY-05 
BB23                                      D98-SPLIT-KEY-06 
BB23                                      D98-SPLIT-KEY-07 
               ALTERNATE RECORD KEY 
                     IS D98-SPLIT-KEY-2 = D98-TRANCHE-FLAG
                                          D98-CO-AC-LK
                                          D98-SEDOL-SORT
                                          D98-TRANCHE-CONTRACT-NUMBER
                     WITH DUPLICATES
BB17           STATUS IS FILE-STATUS.
      * D99 - Bytewise Report Reader
BB21       SELECT D99 ASSIGN W-REPORT-NAME
               ORGANIZATION SEQUENTIAL
               STATUS IS FILE-STATUS.
      *
      * D100 - Realised Tax Schedule File
BB17  * Note - D100 below is accessed via D99
      *
      * D101 - Unrealised Tax Data File
BB21        SELECT D101 ASSIGN D101-FILE-NAME
BB17           ORGANIZATION INDEXED
BB17           ACCESS DYNAMIC
Y2K            RECORD KEY D101-SPLIT-KEY = D101-SPLIT-KEY-01
BB23                                       D101-SPLIT-KEY-02
BB23                                       D101-SPLIT-KEY-03
BB23                                       D101-SPLIT-KEY-04 
BB23                                       D101-SPLIT-KEY-05 
BB23                                       D101-SPLIT-KEY-06 
BB23                                       D101-SPLIT-KEY-07 
               ALTERNATE RECORD KEY 
                     IS D101-SPLIT-KEY-2 = D101-TRANCHE-FLAG
                                           D101-CO-AC-LK
                                           D101-SEDOL-SORT
                                           D101-TRANCHE-CONTRACT-NUMBER
                     WITH DUPLICATES
BB17           STATUS IS FILE-STATUS.
      *
      * D102 - Unrealised Tax Schedule File
BB17  * Note - D102 below is accessed via D99
      *
      * D103 - Real H O Gains Report
BB19  * Note - D103 below is accessed via D99
      *
      * D104 - Unreal H O Gains Report
BB19  * Note - D104 below is accessed via D99
      *
      * D105 - Acquisition Export File
BB20  * Note - D105 below is accessed via D99
      *
      * D107 - Batch Quit Run File
BB21       SELECT D107 ASSIGN D107-FILE-NAME
BB21           ORGANIZATION SEQUENTIAL
BB21           STATUS IS FILE-STATUS.
      * D108 - Trace File
BB21       SELECT D108 ASSIGN D108-FILE-NAME
BB21           ORGANIZATION SEQUENTIAL
BB21           STATUS IS FILE-STATUS.
      * D109 - Error Log File
BB21       SELECT D109 ASSIGN D109-FILE-NAME
BB21           ORGANIZATION SEQUENTIAL
BB21           STATUS IS FILE-STATUS.
      *
      * D110 - Taper Report File
BB22  * Note - D110 below is accessed via D99
      *
      * D111 - Taper Export File
BB22  * Note - D111 below is accessed via D99
      *
      * D112 - Taper Rate File
BB22       SELECT D112 ASSIGN D112-FILE-NAME
BB22           ORGANIZATION INDEXED
BB22           ACCESS DYNAMIC
BB22           RECORD KEY D112-KEY
BB22           ALTERNATE RECORD KEY IS D112-INVERSE-DATE 
BB22                                   WITH DUPLICATES
BB22           STATUS IS FILE-STATUS.
      * D133 - Asset Usage Calendar File
           SELECT D133 ASSIGN D133-FILE-NAME 
               ORGANIZATION INDEXED
               ACCESS DYNAMIC
               LOCK MODE IS MANUAL
               RECORD KEY D133-KEY
               ALTERNATE RECORD KEY IS D133-INVERSE-KEY
               STATUS IS FILE-STATUS.
      * D153 - Period End Calendar File
BB24       SELECT D153 ASSIGN D153-FILE-NAME 
BB24           ORGANIZATION INDEXED
BB24           ACCESS DYNAMIC
BB24           LOCK MODE IS MANUAL
BB24           RECORD KEY D153-KEY
BB24           ALTERNATE RECORD KEY IS D153-INVERSE-KEY
BB24           STATUS IS FILE-STATUS.
      * D154 - Period End Calendar Dates File
BB24       SELECT D154 ASSIGN D154-FILE-NAME 
BB24           ORGANIZATION INDEXED
BB24           ACCESS DYNAMIC
BB24           LOCK MODE IS MANUAL
BB24           RECORD KEY D154-KEY
BB24           STATUS IS FILE-STATUS.
      *
      * D159 - Allowances From Db File
BB25  * Note - D159 below is accessed via D99
      *
BB25  * Note - D160 below is accessed via D99
      *
BB25       SELECT D161 ASSIGN D161-FILE-NAME
BB25           ORGANIZATION SEQUENTIAL
BB25           STATUS IS FILE-STATUS.
      *
      * D162 - YTD Losses Import
BB26  * Note - D162 is not accsesed by COBOL !
      *
      * D163 - Inter Connected Funds
BB26       SELECT D163 ASSIGN D163-FILE-NAME 
BB26           ORGANIZATION INDEXED
BB26           ACCESS DYNAMIC
BB26           LOCK MODE IS MANUAL
BB26           RECORD KEY D163-KEY
BB26           ALTERNATE RECORD KEY IS D163-OLAB-Fund-Code
BB26           ALTERNATE RECORD KEY IS D163-Inverse-Key
BB26           STATUS IS FILE-STATUS.
      *
      * D164 - Inter Connected Funds Export
BB26  * Note - D164 below is accessed via D99
      *
      * D165 - Inter Connected Funds Report
BB26  * Note - D165 below is accessed via D99
      *
      * D166 - Policy Validation Report
BB26  * Note - D166 below is accessed via D99
      *
      * D167 - Price Types
BB27       SELECT D167 ASSIGN D167-FILE-NAME 
BB27           ORGANIZATION INDEXED
BB27           ACCESS DYNAMIC
BB27           LOCK MODE IS MANUAL
BB27           RECORD KEY D167-KEY
BB27           ALTERNATE RECORD KEY IS D167-Sequence-Code
BB27           ALTERNATE RECORD KEY IS D167-Inverse-Key
BB27           STATUS IS FILE-STATUS.

      * D169 - Pending Log
BB27       SELECT D169 ASSIGN D169-FILE-NAME 
BB27           ORGANIZATION INDEXED
BB27           ACCESS DYNAMIC
BB27           LOCK MODE IS MANUAL
BB27           RECORD KEY D169-Key
BB27           ALTERNATE RECORD KEY IS D169-Key2 = D169-Key2A
BB27                                               D169-Key
BB27           ALTERNATE RECORD KEY IS D169-Inverse-Key2
BB27           STATUS IS FILE-STATUS.

      * D170 - Pending Items
BB27       SELECT D170 ASSIGN D170-FILE-NAME 
BB27           ORGANIZATION INDEXED
BB27           ACCESS DYNAMIC
BB27           LOCK MODE IS MANUAL
BB27           RECORD KEY D170-KEY
BB27           STATUS IS FILE-STATUS.

      * D171 - Daily Transaction Export
BB27  * Note - D171 below is accessed via D99
      /
       DATA DIVISION.
       FILE SECTION.
      *
       FD  D1.
       COPY 'COUNTRY.CPY'.
      *
       FD  D2.
       COPY 'GROUP.CPY'.
      *
       FD  D3.
       COPY 'STOCK.CPY'.
      /
       FD  D4.
       COPY 'FUND.CPY'.
      *
      /
BB2    FD  D5.
BB2    COPY 'CGTR04.CPY'.
      *
BB2    FD  D6.
BB2    COPY 'CGTR05.CPY'.
      *
      /
       FD  D7.
       COPY 'RPI.CPY'.
      *
       FD  D8.
       COPY 'PARAM.CPY'.
      /
       FD  D9.
       COPY 'USERID.CPY'.
      *
       FD  D10.
       COPY 'ST-EXTEL.CPY'.
      *
       FD  D11.
       COPY 'FR-EXTEL.CPY'.
      *
       FD  D12.
       COPY 'OUT-LIST.CPY'.
      *
       FD  D17.
       COPY 'LOG.CPY'.
      /
       FD  D18.
       COPY 'ERROR-RP.CPY'.
      *
       FD  D19.
       COPY 'R-GAINS.CPY'.
      *
       FD  D20.
       COPY 'U-GAINS.CPY'.
      *
       FD  D21.
       COPY 'R-SCHED.CPY'.
      *
       FD  D22.
       COPY 'U-SCHED.CPY'.
      *
       FD  D23.
       COPY 'CG01.CPY'.
      *
       FD  D24.
       COPY 'CG02.CPY'.
      *
       FD  D25.
       COPY 'CG03.CPY'.
      *
       FD  D26.
       COPY 'YE-REC.CPY'.
      *
       FD  D27.
       COPY 'YE-DEL.CPY'.
      *
       FD  D28.
       COPY 'YE-CON.CPY'.
      /
       FD  D29.
       COPY 'ERROR-DT.CPY'.
      *
       FD  D30.
       COPY 'YE-ERR.CPY'.
      *
       FD  D31.
       COPY 'PRINTER.CPY'.
      *
       FD  D33.
       COPY 'YE-REC2.CPY'.
      /
       FD  D32.
       COPY 'S-TYPE.CPY'.
      *
       FD  D34.
       COPY 'TR-CODE.CPY'.
      *
       FD  D35.
       COPY 'OUT-LOG.CPY'.
      *
       FD  D36.
       COPY 'MESSAGE.CPY'.
      /
      *
       FD  D38.
       COPY 'HELP.CPY'.
      *
       FD  D39.
       COPY 'DF-AXESS.CPY'.
      *
       FD  D40.
       COPY 'ACCESS.CPY'.
      /
       FD  D41.
       COPY 'EX-PRICE.CPY'.
      *
       FD  D42.
       COPY 'EX-CURR.CPY'.
      /
       FD  D43.
       COPY 'ST-HIST.CPY'.
      *
       FD  D44.
       COPY 'EX-TRANS.CPY'.
      /
       FD  D45
BB7        RECORD VARYING FROM 319 TO 16342 DEPENDING ON REC-LENGTH
BB7        RECORDING MODE VARIABLE.
       COPY 'MF-YE.CPY'.
      /
BB2    FD  D46.
BB2    COPY 'TRANS.CPY'.
      /
   BB4 FD  D49.
   BB4 COPY 'BALUP-RP.CPY'.
      /
   BB4 FD  D50.
   BB4 COPY 'ST-LOADR.CPY'.
      /
   BB4 FD  D51.
   BB4 COPY 'ST-LOAD.CPY'.
MIK110 FD  D68.
MIK110 COPY 'FUNDLOAD.CPY'.
      /
MIK110 FD  D69.
MIK110 COPY 'FUND-REP.CPY'.
      /
BB8    FD  D70.
BB8    COPY 'PRICE-LD.CPY'.
      /
BB8    FD  D71.
BB8    COPY 'PRICE-RP.CPY'.
      /
BB8    FD  D72.
BB8    COPY 'SKANREP1.CPY'.
      /
BB8    FD  D73.
BB8    COPY 'SKANREP2.CPY'.
      /
BB8    FD  D74.
BB8    COPY 'R-GAIN-R.CPY'.
      /
BB8    FD  D75.
BB8    COPY 'U-GAIN-R.CPY'.
      /
BB8    FD  D76.
BB8    COPY 'MGM-REP1.CPY'.
      /
BB8    FD  D77.
BB8    COPY 'CAP-REP1.CPY'.
      /
 BB9   FD  D78.
 BB9   COPY 'REPL-REP.CPY'.
      /
 BB9   FD  D79.
 BB9   COPY 'REPL-ACQ.CPY'.
      /
 BB9   FD  D80.
 BB9   COPY 'REPL-DIS.CPY'.
      /
 BB9   FD  D81.
 BB9   COPY 'N-GAINS.CPY'.
      /
 BB9   FD  D82.
 BB9   COPY 'N-SCHED.CPY'.
      /
 BB9   FD  D83.
 BB9   COPY 'BAL-LOAD.CPY'.
      /
 BB9   FD  D84.
 BB9   COPY 'BAL-REP.CPY'.
      / 
EJ2    FD  D85.
EJ2    COPY 'O-SCHED.CPY'.
      /
MJ1    FD  D86.
MJ1    COPY 'RCF.CPY'.
      /
DGD    FD  D87.
DGD    COPY 'D86-BK.CPY'.
      /
BB11   FD  D88.
BB11   COPY GROUPREP.
      /
BB11   FD  D89.
BB11   COPY GROUPLD.
      /
BB11   FD  D90.
BB11   COPY CTRYREP.
      /
BB11   FD  D91.
BB11   COPY COUNTRLD.
      /
BB11   FD  D92.
BB11   COPY RPIREP.
      /
BB11   FD  D93.
BB11   COPY RPILD.
      /
BB12   FD  D94.
BB12   COPY GLDATA.
      /
BB12   FD  D95.
BB12   COPY GLREPORT.
      *
BB17  * Note - D96 below is accessed via D99
      *
BB17  * Note - D97 below is accessed via D99
      *
      /
BB17   FD  D98.
BB17   COPY 'T-GAINS.CPY'.
      /
       FD  D99.
       01  D99-RECORD.
           03  D99-RECORD-BYTE PIC X.
      *
BB17  * Note - D100 below is accessed via D99
      *
      /
BB17   FD  D101.
BB17   COPY 'X-GAINS.CPY'.
      *
BB17  * Note - D102 below is accessed via D99
      *
BB19  * Note - D103 below is accessed via D99
      *
BB19  * Note - D104 below is accessed via D99
      *
BB20  * Note - D105 below is accessed via D99
      *
      /
BB21   FD  D107.
BB21   COPY BQUITRUN.
      /
BB21   FD  D108.
BB21   COPY TRACE.
      /
BB21   FD  D109.
BB21   COPY ERRORLOG.
      /
      *
BB22  * Note - D110 below is accessed via D99
      *
BB22  * Note - D111 below is accessed via D99
      *
      /
BB22   FD  D112.
BB22   COPY TAPERATE.
      /
       FD  D133.
       COPY CALENDAR.
      /
BB24   FD  D153.
BB24   COPY PENDCAL.
      /
BB24   FD  D154.
BB24   COPY PENDCALD.
      /
      *
BB25  * Note - D159 below is accessed via D99
      *
BB25  * Note - D160 below is accessed via D99
      *
BB25  /
BB25   FD  D161.
BB25   01  D161-RECORD.
BB25       03  D161-RECORD-BYTE PIC X.
      *
      * D162 - YTD Losses Import
BB26  * Note - D162 is not accsesed by COBOL !
      *
      * D163 - Inter Connected Funds
BB26   FD D163.
BB26   COPY ICFUNDS.
      *
      * D164 - Inter Connected Funds Export
BB26  * Note - D164 below is accessed via D99
      *
      * D165 - Inter Connected Funds Report
BB26  * Note - D165 below is accessed via D99
      *
      * D167 - Price Type
BB27   FD D167.
BB27   COPY PriceTyp.
      *
      * D169 - Pending Log
BB27   FD D169.
BB27   COPY PendLog.
      *
      * D170 - Pending Items
BB27   FD D170.
BB27   COPY PendItem.
      *
      * D171 - Daily Transaction Export
BB27  * Note - D171 below is accessed via D99
      *
      
       WORKING-STORAGE SECTION.
      *
dtv004 01  WS-MESSAGE-NO               PIC  9(015)   VALUE 0.
dtv004 01  WS-PROCESS-FLAG             PIC  X(001).
dtv004     88  QUIT-PROCESS VALUE 'Q'.
       
       77  WS-PROGRAM-NAME     PIC X(8)    VALUE 'CGTFILES'.
       77  L-KEY               PIC X(100).
BB7    77  REC-LENGTH          PIC 9(5).
  BB6  77  W-SUB               PIC 9(4)    COMP-3.
      *
       01  W-SCHEDULE-COUNTERS.
           05  W-D19-COUNT     PIC 9(9).
           05  W-D20-COUNT     PIC 9(9).
BB9        05  W-D81-COUNT     PIC 9(9).
BB17       05  W-D98-COUNT     PIC 9(9).
BB17       05  W-D101-COUNT    PIC 9(9).
      *
       01  FILE-STATUS.
           88 SUCCESSFUL                   VALUE ZERO.
           05 STATUS-1         PIC X.
           05 STATUS-2         PIC X.
      *
       01  W-BINARY-FIELD.
           05 FILLER           PIC X       VALUE LOW-VALUE.
           05 W-BINARY-CHAR      PIC X.
      *
       01  W-BINARY-STATUS-2 REDEFINES W-BINARY-FIELD PIC 9(4) COMP.

      * Schedule print file 158 bytes max length, + page limit 60 for
      * line feeds = 218.
       01  W-PRINT-RECORD.
           05  W-PRINT-CHAR    PIC X(1)  OCCURS 218.
       01  W-PRINT-CHAR-SUB    PIC 9(4) COMP-3.
      * separate definition for external disposals as need file open at 
      * same time as client period, so can't use D99 for both
BB5    01  W-D161-RECORD.
BB5        05  W-D161-CHAR     PIC X(1)  OCCURS 500.
BB5    01  W-D161-CHAR-SUB     PIC 9(4) COMP-3.
      *
      * Variables for full file names
      *
BB21   01  D1-FILE-NAME   PIC X(256).
BB21   01  D2-FILE-NAME   PIC X(256).
BB21   01  D3-FILE-NAME   PIC X(256).
BB21   01  D4-FILE-NAME   PIC X(256).
BB21   01  D5-FILE-NAME   PIC X(256).
BB21   01  D6-FILE-NAME   PIC X(256).
BB21   01  D7-FILE-NAME   PIC X(256).
BB21   01  D8-FILE-NAME   PIC X(256).
BB21   01  D9-FILE-NAME   PIC X(256).
BB21   01  D10-FILE-NAME  PIC X(256).
BB21   01  D11-FILE-NAME  PIC X(256).
BB21   01  D12-FILE-NAME  PIC X(256).
BB21   01  D17-FILE-NAME  PIC X(256).
BB21   01  D18-FILE-NAME  PIC X(256).
BB21   01  D19-FILE-NAME  PIC X(256).
BB21   01  D20-FILE-NAME  PIC X(256).
BB21   01  D21-FILE-NAME  PIC X(256).
BB21   01  D22-FILE-NAME  PIC X(256).
BB21   01  D23-FILE-NAME  PIC X(256).
BB21   01  D24-FILE-NAME  PIC X(256).
BB21   01  D25-FILE-NAME  PIC X(256).
BB21   01  D26-FILE-NAME  PIC X(256).
BB21   01  D27-FILE-NAME  PIC X(256).
BB21   01  D28-FILE-NAME  PIC X(256).
BB21   01  D29-FILE-NAME  PIC X(256).
BB21   01  D30-FILE-NAME  PIC X(256).
BB21   01  D31-FILE-NAME  PIC X(256).
BB21   01  D32-FILE-NAME  PIC X(256).
BB21   01  D33-FILE-NAME  PIC X(256).
BB21   01  D34-FILE-NAME  PIC X(256).
BB21   01  D35-FILE-NAME  PIC X(256).
BB21   01  D36-FILE-NAME  PIC X(256).
BB21   01  D38-FILE-NAME  PIC X(256).
BB21   01  D39-FILE-NAME  PIC X(256).
BB21   01  D40-FILE-NAME  PIC X(256).
BB21   01  D41-FILE-NAME  PIC X(256).
BB21   01  D42-FILE-NAME  PIC X(256).
BB21   01  D43-FILE-NAME  PIC X(256).
BB21   01  D44-FILE-NAME  PIC X(256).
BB21   01  D45-FILE-NAME  PIC X(256).
BB21   01  D46-FILE-NAME  PIC X(256).
BB21   01  D49-FILE-NAME  PIC X(256).
BB21   01  D50-FILE-NAME  PIC X(256).
BB21   01  D51-FILE-NAME  PIC X(256).
BB21   01  D68-FILE-NAME  PIC X(256).
BB21   01  D69-FILE-NAME  PIC X(256).
BB21   01  D70-FILE-NAME  PIC X(256).
BB21   01  D71-FILE-NAME  PIC X(256).
BB21   01  D72-FILE-NAME  PIC X(256).
BB21   01  D73-FILE-NAME  PIC X(256).
BB21   01  D74-FILE-NAME  PIC X(256).
BB21   01  D75-FILE-NAME  PIC X(256).
BB21   01  D76-FILE-NAME  PIC X(256).
BB21   01  D77-FILE-NAME  PIC X(256).
BB21   01  D78-FILE-NAME  PIC X(256).
BB21   01  D79-FILE-NAME  PIC X(256).
BB21   01  D80-FILE-NAME  PIC X(256).
BB21   01  D81-FILE-NAME  PIC X(256).
BB21   01  D82-FILE-NAME  PIC X(256).
BB21   01  D83-FILE-NAME  PIC X(256).
BB21   01  D84-FILE-NAME  PIC X(256).
BB21   01  D85-FILE-NAME  PIC X(256).
BB21   01  D86-FILE-NAME  PIC X(256).
BB21   01  D87-FILE-NAME  PIC X(256).
BB21   01  D88-FILE-NAME  PIC X(256).
BB21   01  D89-FILE-NAME  PIC X(256).
BB21   01  D90-FILE-NAME  PIC X(256).
BB21   01  D91-FILE-NAME  PIC X(256).
BB21   01  D92-FILE-NAME  PIC X(256).
BB21   01  D93-FILE-NAME  PIC X(256).
BB21   01  D94-FILE-NAME  PIC X(256).
BB21   01  D95-FILE-NAME  PIC X(256).
       01  D96-FILE-NAME  PIC X(256).
       01  D97-FILE-NAME  PIC X(256).
BB21   01  D98-FILE-NAME  PIC X(256).
       01  D100-FILE-NAME PIC X(256).
BB21   01  D101-FILE-NAME PIC X(256).
       01  D102-FILE-NAME PIC X(256).
       01  D103-FILE-NAME PIC X(256).
       01  D104-FILE-NAME PIC X(256).
       01  D105-FILE-NAME PIC X(256).
dtv    01  filler         pic x(256).        
BB21   01  D107-FILE-NAME PIC X(256).
BB21   01  D108-FILE-NAME PIC X(256).
BB21   01  D109-FILE-NAME PIC X(256).
       01  D110-FILE-NAME PIC X(256).
       01  D111-FILE-NAME PIC X(256).
       01  D112-FILE-NAME PIC X(256).
       01  D133-FILE-NAME PIC X(256).
BB24   01  D153-FILE-NAME PIC X(256).
BB24   01  D154-FILE-NAME PIC X(256).
BB25   01  D159-FILE-NAME PIC X(256).
BB25   01  D160-FILE-NAME PIC X(256).
BB25   01  D161-FILE-NAME PIC X(256).
BB26   01  D163-FILE-NAME PIC X(256).
BB26   01  D164-FILE-NAME PIC X(256).
BB26   01  D165-FILE-NAME PIC X(256).
BB27   01  D167-FILE-NAME PIC X(256).
BB27   01  D169-FILE-NAME PIC X(256).
BB27   01  D170-FILE-NAME PIC X(256).
BB27   01  D171-FILE-NAME PIC X(256).
      *
      * Files with fixed name/extension
      *
BB21   01  D1-FILE PIC X(12) VALUE 'COUNTRY.DAT'.
BB21   01  D2-FILE PIC X(12) VALUE 'GROUP.DAT'.
BB21   01  D3-FILE PIC X(12) VALUE 'STOCK.DAT'.
BB21   01  D4-FILE PIC X(12) VALUE 'FUND.DAT'.
BB21   01  D7-FILE PIC X(12) VALUE 'RPI.DAT'.
BB21   01  D8-FILE PIC X(12) VALUE 'PARAM.DAT'.
BB21   01  D9-FILE PIC X(12) VALUE 'USERID.DAT'.
BB21   01  D17-FILE PIC X(12) VALUE 'LOG.DAT'.
BB21   01  D31-FILE PIC X(12) VALUE 'PRINTER.DAT'.
BB21   01  D32-FILE PIC X(12) VALUE 'S-TYPE.DAT'.
BB21   01  D34-FILE PIC X(12) VALUE 'TR-CODE.DAT'.
BB21   01  D35-FILE PIC X(12) VALUE 'OUT-LOG.DAT'.
BB21   01  D36-FILE PIC X(12) VALUE 'MESSAGE.DAT'.
BB21   01  D38-FILE PIC X(12) VALUE 'HELP.DAT'.
BB21   01  D39-FILE PIC X(12) VALUE 'DF-AXESS.DAT'.
BB21   01  D40-FILE PIC X(12) VALUE 'ACCESS.DAT'.
BB21   01  D41-FILE PIC X(12) VALUE 'EX-PRICE.DAT'.
BB21   01  D42-FILE PIC X(12) VALUE 'EX-CURR.DAT'.
BB21   01  D43-FILE PIC X(12) VALUE 'ST-HIST.DAT'.
BB21   01  D44-FILE PIC X(12) VALUE 'EX-TRANS.DAT'.
BB21   01  D86-FILE PIC X(12) VALUE 'CGTRCF.DAT'.
BB22   01  D112-FILE PIC X(12) VALUE 'TAPERATE.DAT'.
       01  D133-FILE PIC X(12) VALUE 'CALENDAR.DAT'.
BB24   01  D153-FILE PIC X(12) VALUE 'PENDCAL.DAT'.
BB24   01  D154-FILE PIC X(12) VALUE 'PENDCALD.DAT'.
BB26   01  D163-FILE PIC X(12) VALUE 'ICFUNDS.DAT'.
BB27   01  D167-FILE PIC X(12) VALUE 'PriceTyp.DAT'.
BB27   01  D169-FILE PIC X(12) VALUE 'PendLog.DAT'.
BB27   01  D170-FILE PIC X(12) VALUE 'PendItem.DAT'.
      *
      * User files with variable name/extension
      *
BB2    01  D5-FILE.
BB2        05  FILLER          PIC X VALUE '$'.
BB2        05  D5-USER-NO      PIC 9(4).
BB2        05  FILLER          PIC X(7) VALUE 'R04.REP'.
      *
BB2    01  D6-FILE.
BB2        05  FILLER          PIC X VALUE '$'.
BB2        05  D6-USER-NO      PIC 9(4).
BB2        05  FILLER          PIC X(7) VALUE 'R05.REP'.
      *
       01  D10-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D10-USER-NO     PIC 9(4).
           05  FILLER          PIC X(7) VALUE 'EXS.REP'.
      *
       01  D11-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D11-USER-NO     PIC 9(4).
           05  FILLER          PIC X(7) VALUE 'EXF.REP'.
      *
       01  D12-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D12-USER-NO     PIC 9(4).
           05  FILLER          PIC X(7) VALUE 'OUT.LST'.
      *
       01  D18-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D18-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R4'.
           05  D18-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D19-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D19-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'D5'.
           05  D19-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.DAT'.
      *
       01  D20-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D20-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'D6'.
           05  D20-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.DAT'.
      /
       01  D21-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D21-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R5'.
           05  D21-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D22-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D22-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R6'.
           05  D22-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D23-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D23-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R1'.
           05  D23-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D24-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D24-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R2'.
           05  D24-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D25-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D25-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R3'.
           05  D25-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D26-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D26-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R7'.
           05  D26-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      /
       01  D27-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D27-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R8'.
           05  D27-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D28-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D28-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'R9'.
           05  D28-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D29-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D29-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'D4'.
           05  D29-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.DAT'.
      *
       01  D30-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D30-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'RA'.
           05  D30-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.REP'.
      *
       01  D33-FILE.
           05  FILLER          PIC X VALUE '$'.
           05  D33-USER-NO     PIC 9(4).
           05  FILLER          PIC XX VALUE 'DB'.
           05  D33-REPORT-NO   PIC 9.
           05  FILLER          PIC X(4) VALUE '.DAT'.
      *
BB18   01  D45-FILE.
BB18       05  FILLER          PIC X    VALUE '$'.
BB18       05  D45-USER-NO     PIC 9(4).
BB18       05  FILLER          PIC X(7) VALUE 'MYE.DAT'.
      *
BB2    01  D46-FILE.
BB21       05  FILLER          PIC X(128).
      *
   BB4 01  D49-FILE.
   BB4     05  FILLER          PIC X VALUE '$'.
   BB4     05  D49-USER-NO     PIC 9(4).
   BB4     05  FILLER          PIC XX VALUE 'BU'.
   BB4     05  D49-REPORT-NO   PIC 9.
   BB4     05  FILLER          PIC X(4) VALUE '.REP'.
      *
   BB4 01  D50-FILE.
   BB4     05  FILLER          PIC X VALUE '$'.
   BB4     05  D50-USER-NO     PIC 9(4).
   BB4     05  FILLER          PIC X(7) VALUE 'STL.REP'.
      *
   BB4 01  D51-FILE.
BB21B4     05  FILLER          PIC X(128).
      *
MIK110 01  D68-FILE.
BB21       05  FILLER          PIC X(128).
      *
MIK110 01  D69-FILE.
BB8        05  FILLER          PIC X VALUE '$'.
BB8        05  D69-USER-NO     PIC 9(4).
BB8        05  FILLER          PIC X(7) VALUE 'FND.REP'.
      *
   BB8 01  D70-FILE.
BB21B8     05  FILLER          PIC X(128).
      *
   BB8 01  D71-FILE.
   BB4     05  FILLER          PIC X VALUE '$'.
   BB4     05  D71-USER-NO     PIC 9(4).
   BB4     05  FILLER          PIC X(7) VALUE 'PRL.REP'.
      *
   BB8 01  D72-FILE.
   BB8     05  FILLER          PIC X VALUE '$'.
   BB8     05  D72-USER-NO     PIC 9(4).
   BB8     05  FILLER          PIC XX VALUE 'RC'.
   BB8     05  D72-REPORT-NO   PIC 9.
   BB8     05  FILLER          PIC X(4) VALUE '.REP'.
      *
   BB8 01  D73-FILE.
   BB8     05  FILLER          PIC X VALUE '$'.
   BB8     05  D73-USER-NO     PIC 9(4).
   BB8     05  FILLER          PIC XX VALUE 'RD'.
   BB8     05  D73-REPORT-NO   PIC 9.
   BB8     05  FILLER          PIC X(4) VALUE '.REP'.
      *
   BB8 01  D74-FILE.
   BB8     05  FILLER          PIC X VALUE '$'.
   BB8     05  D74-USER-NO     PIC 9(4).
   BB8     05  FILLER          PIC XX VALUE 'RE'.
   BB8     05  D74-REPORT-NO   PIC 9.
   BB8     05  FILLER          PIC X(4) VALUE '.REP'.
      *
   BB8 01  D75-FILE.
   BB8     05  FILLER          PIC X VALUE '$'.
   BB8     05  D75-USER-NO     PIC 9(4).
   BB8     05  FILLER          PIC XX VALUE 'RF'.
   BB8     05  D75-REPORT-NO   PIC 9.
   BB8     05  FILLER          PIC X(4) VALUE '.REP'.
      *
   BB8 01  D76-FILE.
   BB8     05  FILLER          PIC X VALUE '$'.
   BB8     05  D76-USER-NO     PIC 9(4).
   BB8     05  FILLER          PIC XX VALUE 'RG'.
   BB8     05  D76-REPORT-NO   PIC 9.
   BB8     05  FILLER          PIC X(4) VALUE '.REP'.
      *
   BB8 01  D77-FILE.
   BB8     05  FILLER          PIC X VALUE '$'.
   BB8     05  D77-USER-NO     PIC 9(4).
   BB8     05  FILLER          PIC XX VALUE 'RH'.
   BB8     05  D77-REPORT-NO   PIC 9.
   BB8     05  FILLER          PIC X(4) VALUE '.REP'.
      *
 BB9   01  D78-FILE.
 BB9       05  FILLER          PIC X VALUE '$'.
 BB9       05  D78-USER-NO     PIC 9(4).
 BB9       05  FILLER          PIC XX VALUE 'RI'.
 BB9       05  D78-REPORT-NO   PIC 9.
 BB9       05  FILLER          PIC X(4) VALUE '.REP'.
      *
 BB9   01  D79-FILE.
 BB9       05  FILLER          PIC X VALUE '$'.
 BB9       05  D79-USER-NO     PIC 9(4).
 BB9       05  FILLER          PIC X(7) VALUE 'ACQ.DAT'.
      *
 BB9   01  D80-FILE.
 BB9       05  FILLER          PIC X VALUE '$'.
 BB9       05  D80-USER-NO     PIC 9(4).
 BB9       05  FILLER          PIC X(7) VALUE 'DIS.DAT'.
      *
 BB9   01  D81-FILE.
 BB9       05  FILLER          PIC X VALUE '$'.
 BB9       05  D81-USER-NO     PIC 9(4).
 BB9       05  FILLER          PIC XX VALUE 'DJ'.
 BB9       05  D81-REPORT-NO   PIC 9.
 BB9       05  FILLER          PIC X(4) VALUE '.DAT'.
      *
 BB9   01  D82-FILE.
 BB9       05  FILLER          PIC X VALUE '$'.
 BB9       05  D82-USER-NO     PIC 9(4).
 BB9       05  FILLER          PIC XX VALUE 'RJ'.
 BB9       05  D82-REPORT-NO   PIC 9.
 BB9       05  FILLER          PIC X(4) VALUE '.REP'.
      *
 BB9   01  D83-FILE.
BB21       05  FILLER          PIC X(128).
      *
BB2    01  D84-FILE.
BB2        05  FILLER          PIC X VALUE '$'.
BB2        05  D84-USER-NO     PIC 9(4).
BB2        05  FILLER          PIC X(7) VALUE 'BLD.REP'.
      *
EJ2    01  D85-FILE.
EJ2        05  FILLER          PIC X VALUE '$'.
EJ2        05  D85-USER-NO     PIC 9(4).
EJ2        05  FILLER          PIC XX VALUE 'RK'.
EJ2        05  D85-REPORT-NO   PIC 9.
EJ2        05  FILLER          PIC X(4) VALUE '.REP'.
      *
DGD    01  D87-FILE.
BB21       05  FILLER          PIC X(128).
      *
BB11   01  D88-FILE.
BB11       05  FILLER          PIC X       VALUE '$'.
BB11       05  D88-USER-NO     PIC 9(4).
BB11       05  FILLER          PIC X(7)    VALUE 'GLD.REP'.
      *
BB11   01  D89-FILE.
BB21       05  FILLER          PIC X(128).
      *
BB11   01  D90-FILE.
BB11       05  FILLER          PIC X       VALUE '$'.
BB11       05  D90-USER-NO     PIC 9(4).
BB11       05  FILLER          PIC X(7)    VALUE 'CLD.REP'.
      *
BB11   01  D91-FILE.
BB21       05  FILLER          PIC X(128).
      *
BB11   01  D92-FILE.
BB11       05  FILLER          PIC X       VALUE '$'.
BB11       05  D92-USER-NO     PIC 9(4).
BB11       05  FILLER          PIC X(7)    VALUE 'RLD.REP'.
      *
BB11   01  D93-FILE.
BB21       05  FILLER          PIC X(128).
      *
BB12   01  D94-FILE.
BB12       05  FILLER          PIC X VALUE '$'.
BB12       05  D94-USER-NO     PIC 9(4).
  BB13     05  FILLER          PIC XX VALUE 'DL'.
BB12       05  D94-REPORT-NO   PIC 9.
BB12       05  FILLER          PIC X(4) VALUE '.DAT'.
      *
BB12   01  D95-FILE.
BB12       05  FILLER          PIC X VALUE '$'.
BB12       05  D95-USER-NO     PIC 9(4).
BB12       05  FILLER          PIC XX VALUE 'RL'.
BB12       05  D95-REPORT-NO   PIC 9.
BB12       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB14   01  D96-FILE.
BB14       05  FILLER          PIC X VALUE '$'.
BB14       05  D96-USER-NO     PIC 9(4).
BB14       05  FILLER          PIC XX VALUE 'RM'.
BB14       05  D96-REPORT-NO   PIC 9.
BB14       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB15   01  D97-FILE.
BB15       05  FILLER          PIC X VALUE '$'.
BB15       05  D97-USER-NO     PIC 9(4).
BB15       05  FILLER          PIC XX VALUE 'RN'.
BB15       05  D97-REPORT-NO   PIC 9.
BB15       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB17   01  D98-FILE.
BB17       05  FILLER          PIC X VALUE '$'.
BB17       05  D98-USER-NO     PIC 9(4).
BB17       05  FILLER          PIC XX VALUE 'DP'.
BB17       05  D98-REPORT-NO   PIC 9.
BB17       05  FILLER          PIC X(4) VALUE '.DAT'.
      *
BB17   01  D100-FILE.
BB17       05  FILLER          PIC X VALUE '$'.
BB17       05  D100-USER-NO    PIC 9(4).
BB17       05  FILLER          PIC XX VALUE 'RP'.
BB17       05  D100-REPORT-NO  PIC 9.
BB17       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB17   01  D101-FILE.
BB17       05  FILLER          PIC X VALUE '$'.
BB17       05  D101-USER-NO    PIC 9(4).
BB17       05  FILLER          PIC XX VALUE 'DQ'.
BB17       05  D101-REPORT-NO  PIC 9.
BB17       05  FILLER          PIC X(4) VALUE '.DAT'.
      *
BB17   01  D102-FILE.
BB17       05  FILLER          PIC X VALUE '$'.
BB17       05  D102-USER-NO     PIC 9(4).
BB17       05  FILLER          PIC XX VALUE 'RQ'.
BB17       05  D102-REPORT-NO   PIC 9.
BB17       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB19   01  D103-FILE.
BB19       05  FILLER          PIC X VALUE '$'.
BB19       05  D103-USER-NO     PIC 9(4).
BB19       05  FILLER          PIC XX VALUE 'RR'.
BB19       05  D103-REPORT-NO   PIC 9.
BB19       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB19   01  D104-FILE.
BB19       05  FILLER          PIC X VALUE '$'.
BB19       05  D104-USER-NO     PIC 9(4).
BB19       05  FILLER          PIC XX VALUE 'RS'.
BB19       05  D104-REPORT-NO   PIC 9.
BB19       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB20   01  D105-FILE.
BB20       05  FILLER          PIC X VALUE '$'.
BB20       05  D105-USER-NO     PIC 9(4).
BB20       05  FILLER          PIC XX VALUE 'RT'.
BB20       05  D105-REPORT-NO   PIC 9.
BB20       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB21   01  D107-FILE.
BB21       05  FILLER          PIC X VALUE '$'.
BB21       05  D107-USER-NO    PIC 9(4).
BB21       05  FILLER          PIC XXX VALUE 'END'.
BB21       05  FILLER          PIC X(4) VALUE '.TXT'.
      *
BB21   01  D108-FILE.
BB21       05  FILLER          PIC X VALUE '$'.
BB21       05  D108-USER-NO    PIC 9(4).
BB21       05  FILLER          PIC XXX VALUE 'TRC'.
BB21       05  FILLER          PIC X(4) VALUE '.TXT'.
      *
BB21   01  D109-FILE.
BB21       05  FILLER          PIC X VALUE '$'.
BB21       05  D109-USER-NO    PIC 9(4).
BB21       05  FILLER          PIC XXX VALUE 'ERR'.
BB21       05  FILLER          PIC X(4) VALUE '.TXT'.
      *
BB22   01  D110-FILE.
BB22       05  FILLER          PIC X VALUE '$'.
BB22       05  D110-USER-NO    PIC 9(4).
BB22       05  FILLER          PIC XX VALUE 'R5'.
BB22       05  D110-REPORT-NO  PIC 9.
BB22       05  FILLER          PIC X(4) VALUE '.REP'.
      *
BB22   01  D111-FILE.
BB22       05  FILLER          PIC X VALUE '$'.
BB22       05  D111-USER-NO    PIC 9(4).
BB22       05  FILLER          PIC XX VALUE 'R5'.
BB22       05  D111-REPORT-NO  PIC 9.
BB22       05  FILLER          PIC X(4) VALUE '.TAP'.
      *
BB25   01  D159-FILE.
BB25       05  FILLER          PIC X VALUE '$'.
BB25       05  D159-USER-NO    PIC 9(4).
BB25       05  FILLER          PIC X(7) VALUE 'ALW.DAT'.
      *
BB25   01  D160-FILE.
BB25       05  FILLER          PIC X VALUE '$'.
BB25       05  D160-USER-NO    PIC 9(4).
BB25       05  FILLER          PIC X(7) VALUE 'UFN.DAT'.
      *
BB25   01  D161-FILE.
BB25       05  FILLER          PIC X VALUE '$'.
BB25       05  D161-USER-NO    PIC 9(4).
BB25       05  FILLER          PIC X(7) VALUE 'EXD.DAT'.
      *
BB26   01  D164-FILE.
BB26       05  FILLER          PIC X VALUE '$'.
BB26       05  D164-USER-NO    PIC 9(4).
BB26       05  FILLER          PIC X(7) VALUE 'ICE.DAT'.
      *
BB26   01  D165-FILE.
BB26       05  FILLER          PIC X VALUE '$'.
BB26       05  D165-USER-NO    PIC 9(4).
BB26       05  FILLER          PIC X(7) VALUE 'ICR.DAT'.
      *
BB27   01  D171-FILE.
BB27       05  FILLER          PIC X VALUE '$'.
BB27       05  D171-USER-NO    PIC 9(4).
BB27       05  FILLER          PIC XX VALUE 'TE'.
BB27       05  D171-REPORT-NO  PIC 9.
BB27       05  FILLER          PIC X(4) VALUE '.DAT'.
      *
BB12   01  W-STORE-ACTION      PIC X(3) VALUE SPACES.
      *
       01  W-SCHED-SUB         PIC XXXX COMP-X.
           88  SUB-REALISED        VALUE 1.
           88  SUB-UNREALISED      VALUE 2.
           88  SUB-DEEMED-DISP     VALUE 3.
           88  SUB-REALISED-TAX    VALUE 4.
           88  SUB-UNREALISED-TAX  VALUE 5.
       78  SUB-CURRENT-RECORD      VALUE 6.
       01  W-SCHED-REC-TABLE.
           05  W-REC-STORE-REALISED                    PIC X(285).
           05  W-REC-STORE-UNREALISED                  PIC X(285).
           05  W-REC-STORE-DEEMED-DISP                 PIC X(285).
           05  W-REC-STORE-REALISED-TAX                PIC X(285).
           05  W-REC-STORE-UNREALISED-TAX              PIC X(285).
           05  W-REC-STORE-CURRENT                     PIC X(285).
       01  FILLER REDEFINES W-SCHED-REC-TABLE.
           05  S-SCHED-REC-ELEMENT OCCURS 6.
             10  S-RECORD-CACHED                       PIC XXXX COMP-X.
                 88  RECORD-NOT-CACHED     VALUE 0.
                 88  RECORD-CACHED         VALUE 1.
             10  S-RECORD.
               15  S-SORT.                                                  
                   20 S-CURRENCY-SORT                   PIC 9(1).           
                       88 S-CURRENCY-STERLING               VALUE 0.
                       88 S-CURRENCY-EURO                   VALUE 1.
                   20 S-CO-AC-LK                        PIC X(4).           
                   20 S-COUNTRY-CODE                    PIC X(3).           
                   20 S-MAIN-GROUP                      PIC X(3).           
                   20 S-SECURITY-TYPE                   PIC X.              
                   20 S-SECURITY-SORT-CODE              PIC X(15).          
                   20 S-SEDOL-SORT                      PIC X(7).           
               15 S-RECORD-TYPE                         PIC X.              
                       88 S-SEDOL-RECORD                    VALUE '1'.      
                       88 S-DETAIL-RECORD                   VALUE '2'.      
               15  S-DATA.                                                  
                   20  S-COMPANY-NAME                  PIC X(51).           
                   20  S-TITLE                         PIC X(60).           
                   20  FILLER                          PIC X(19).           
                   20  FILLER                          PIC X(009).
                   20  FILLER                          PIC X(002).
                   20  FILLER                          PIC X(018).
                   20  FILLER                          PIC X(001).
                   20  FILLER                          PIC X(012).
                   20  FILLER                          PIC X(012).
                   20  FILLER                          PIC X(010).
                   20  FILLER                          PIC X(008).
                   20  FILLER                          PIC X(012).
                   20  FILLER                          PIC X(012).
                   20  FILLER                          PIC X(012).
                   20  FILLER                          PIC X(012).
               15  S-SEDOL-DATA REDEFINES S-DATA.                           
                   20  FILLER                           PIC X(19).          
                   20  S-SEDOL-NUMBER                   PIC X(7).           
                   20  S-ISSUERS-NAME.                                      
                       25  S-ISSUERS-NAME-1             PIC X(20).          
                       25  S-ISSUERS-NAME-2             PIC X(15).          
                   20  S-STOCK-DESCRIPTION.                                 
                       25  S-STOCK-DESCRIPTION-1        PIC X(20).          
                       25  S-STOCK-DESCRIPTION-2        PIC X(20).          
                   20  S-LAST-TRANCHE-CONTRACT-NO       PIC X(10).          
                   20  S-PROFIT-LOSS                    PIC S9(9)V99.       
                   20  S-SEDOL-RECORD-COUNT             PIC S999.           
                   20  S-TRANCHE-COUNT                  PIC S999.           
                   20  S-PRINT-FLAG                     PIC X.              
      *                 SET TO 'P' IF PRINT LINE TO BE FORCED               ELCGTSCH
                   20  S-HOLDING-FLAG                   PIC X.              
                   20  FILLER                           PIC X(009).
                   20  FILLER                           PIC X(002).
                   20  S-REIT-SECURITY-TYPE             PIC X(001).
                   20  S-QUOTED-INDICATOR               PIC X(001).
                   20  FILLER                           PIC X(017).
                   20  S-LOST-INDEXATION                PIC S9(10)V99.
                   20  S-OFFSHORE-INCOME-GAIN           PIC S9(10)V99.
                   20  FILLER                           PIC X(010).
                   20  FILLER                           PIC X(008).
                   20  FILLER                           PIC X(012).
                   20  FILLER                           PIC X(012).
                   20  FILLER                           PIC X(012).
                   20  FILLER                           PIC X(012).
       
       01  W-RECORD-RETRIEVED                           PIC XXXX COMP-X.
           88  RECORD-NOT-RETRIEVED        VALUE 0.
           88  RECORD-RETRIEVED            VALUE 1.
      *
       01  W-REPORT            PIC X(12).
      *
       01  W-80-BYTE-FIELD     PIC X(80).
Y2K    COPY 'CGTDATE2.COB'.
BB21   COPY 'EQTPATH.COB'.
BB21   COPY 'EQTDEBUG.COB'.
       copy Timing.Cpy.
dtv004 COPY CGTLOG.COB.        *> to write lines in log-file           

       01 l-message       PIC X(200).
      /
       LINKAGE SECTION.
      *
       COPY 'CGTFILES.COB'.
      /
       COPY 'LINKAGE1.COB'.
      /
       PROCEDURE DIVISION USING CGTFILES-LINKAGE
                                L-FILE-RECORD-AREA
                                COMMON-LINKAGE.
       CGTFILES-CONTROL SECTION.

           PERFORM A-INITIALISE.
      *
           PERFORM Z5-RETRIEVE-CACHED-SCHED-DATA
           IF RECORD-RETRIEVED
               GO TO CGTFILES-CONTROL-EXIT
           END-IF

           EVALUATE L-FILE-ACTION
                    WHEN OPEN-INPUT
                         PERFORM B-OPEN-INPUT
                    WHEN OPEN-OUTPUT
                         PERFORM C-OPEN-OUTPUT
                    WHEN OPEN-I-O
                         PERFORM D-OPEN-I-O
                    WHEN OPEN-EXTEND
                         PERFORM E-OPEN-EXTEND
                    WHEN START-EQUAL
                         PERFORM F-START-EQUAL
                    WHEN START-NOT-LESS-THAN
                         PERFORM G-START-NOT-LESS-THAN
                    WHEN START-GREATER-THAN
                         PERFORM H-START-GREATER-THAN
                    WHEN START-GT-INVERSE-KEY
                         PERFORM I-START-GT-INVERSE-KEY
                    WHEN START-LESS-THAN
                         PERFORM J-START-LESS-THAN
                    WHEN START-NOT-GREATER-THAN
                         PERFORM K-START-NOT-GREATER-THAN
                    WHEN READ-NEXT
                         PERFORM L-READ-NEXT
                    WHEN READ-NEXT-WITH-LOCK
                         PERFORM M-READ-NEXT-WITH-LOCK
                    WHEN READ-RECORD
                         PERFORM N-READ
                    WHEN READ-WITH-LOCK
                         PERFORM O-READ-WITH-LOCK
                    WHEN READ-ON-KEY2
                         PERFORM P-READ-ON-KEY2
EJ1                 WHEN READ-ON-KEY3 
EJ1                      PERFORM PA-READ-ON-KEY3
                    WHEN WRITE-RECORD
                         PERFORM Q-WRITE
                    WHEN REWRITE-RECORD
                         PERFORM R-REWRITE
                    WHEN DELETE-RECORD
                         PERFORM S-DELETE
                    WHEN CLOSE-FILE
                         PERFORM T-CLOSE
BB21                WHEN CLOSE-ALL-FILES
BB21                     PERFORM Z3-CLOSE-ALL-FILES
                    WHEN UNLOCK-RECORD
                         PERFORM U-UNLOCK
                    WHEN START-NOT-LESS-THAN-KEY2
                         PERFORM V-START-NOT-LESS-THAN-KEY2
EJ1                 WHEN START-NOT-LESS-THAN-KEY3
EJ1                      PERFORM VA-START-NOT-LESS-THAN-KEY3
                    WHEN OPEN-INPUT-REPORT
                         PERFORM W-OPEN-INPUT-REPORT
                    WHEN READ-REPORT
                         MOVE SPACES TO D99-RECORD
BB25                     MOVE ZERO   TO FILE-STATUS
                         PERFORM X-READ-REPORT
                                 UNTIL D99-RECORD  EQUAL X'0D'
                                    OR FILE-STATUS EQUAL  '10'
                    WHEN CLOSE-REPORT
                         PERFORM Y-CLOSE-REPORT
                    WHEN OTHER
                         MOVE 'IA' TO FILE-STATUS
           END-EVALUATE

           IF W-STORE-ACTION NOT = SPACES
               MOVE W-STORE-ACTION TO L-FILE-ACTION
               MOVE SPACES         TO W-STORE-ACTION
           END-IF

           MOVE FILE-STATUS TO L-FILE-RETURN-CODE.

           PERFORM Z6-STORE-CACHED-SCHED-DATA.
           
       CGTFILES-CONTROL-EXIT.
           EXIT PROGRAM.
      /

       Z5-RETRIEVE-CACHED-SCHED-DATA SECTION.
           MOVE ZERO TO W-SCHED-SUB
           SET  RECORD-NOT-RETRIEVED TO TRUE

           EVALUATE L-FILE-ACTION
           WHEN READ-NEXT
           WHEN READ-NEXT-WITH-LOCK
           WHEN READ-RECORD
           WHEN READ-WITH-LOCK
                EVALUATE L-FILE-NAME
                WHEN REALISED-DATA-FILE
                     SET SUB-REALISED          TO TRUE
                WHEN UNREALISED-DATA-FILE
                     SET SUB-UNREALISED        TO TRUE
                WHEN NOTIONAL-SALE-DATA-FILE
                     SET SUB-DEEMED-DISP       TO TRUE
                WHEN REALISED-TAX-DATA-FILE
                     SET SUB-REALISED-TAX      TO TRUE
                WHEN UNREALISED-TAX-DATA-FILE
                     SET SUB-UNREALISED-TAX    TO TRUE
                END-EVALUATE
                IF W-SCHED-SUB <> ZERO 
                    IF RECORD-CACHED(W-SCHED-SUB)
                         MOVE S-RECORD(W-SCHED-SUB)        TO L-FILE-RECORD-AREA
                         SET RECORD-NOT-CACHED(W-SCHED-SUB)TO TRUE
                         MOVE '00' TO L-FILE-RETURN-CODE
                         SET  RECORD-RETRIEVED             TO TRUE
                    END-IF
                END-IF
           END-EVALUATE.
       Z5-EXIT.
           EXIT.

       Z6-STORE-CACHED-SCHED-DATA SECTION.
           MOVE ZERO TO W-SCHED-SUB
           
           EVALUATE L-FILE-NAME
           WHEN REALISED-DATA-FILE
                SET SUB-REALISED        TO TRUE
                MOVE D19-RECORD         TO S-RECORD(SUB-CURRENT-RECORD)
           WHEN UNREALISED-DATA-FILE
                SET SUB-UNREALISED      TO TRUE
                MOVE D20-RECORD         TO S-RECORD(SUB-CURRENT-RECORD)
           WHEN NOTIONAL-SALE-DATA-FILE
                SET SUB-DEEMED-DISP     TO TRUE
                MOVE D81-RECORD         TO S-RECORD(SUB-CURRENT-RECORD)
           WHEN REALISED-TAX-DATA-FILE
                SET SUB-REALISED-TAX    TO TRUE
                MOVE D98-RECORD         TO S-RECORD(SUB-CURRENT-RECORD)
           WHEN UNREALISED-TAX-DATA-FILE
                SET SUB-UNREALISED-TAX  TO TRUE
                MOVE D101-RECORD        TO S-RECORD(SUB-CURRENT-RECORD)
           END-EVALUATE
           
           IF W-SCHED-SUB <> ZERO 
                SET RECORD-NOT-CACHED(W-SCHED-SUB) TO TRUE
                
                EVALUATE L-FILE-ACTION
                WHEN READ-NEXT
                WHEN READ-NEXT-WITH-LOCK
                WHEN READ-RECORD
                WHEN READ-WITH-LOCK
                     IF STATUS-1 = ZERO
                         *> record was read successfully
                         IF  (S-CO-AC-LK    (SUB-CURRENT-RECORD) <> S-CO-AC-LK   (W-SCHED-SUB)
                           OR S-SEDOL-SORT  (SUB-CURRENT-RECORD) <> S-SEDOL-SORT (W-SCHED-SUB))
                         AND (S-RECORD-TYPE (SUB-CURRENT-RECORD) = '2')
                               STRING 'SCHEDULE DATA TYPE 1 RECORD MISSING FOR ' 
                                       S-CO-AC-LK (SUB-CURRENT-RECORD) ' ' 
                                       S-SEDOL-SORT (SUB-CURRENT-RECORD)
dtv004                                 DELIMITED BY SIZE INTO L-LOG-MESSAGE
dtv004                         PERFORM X4-CGTLOG       

                              *> fund/sedol not same as previous record and type is detail record
                              *> create/return dummy header based on current record
                               MOVE S-RECORD     (SUB-CURRENT-RECORD)  TO S-RECORD                    (W-SCHED-SUB)
                               MOVE S-SEDOL-SORT (SUB-CURRENT-RECORD)  TO S-SEDOL-NUMBER              (W-SCHED-SUB)
                               MOVE '1'                                TO S-RECORD-TYPE               (W-SCHED-SUB)
                               MOVE '* Schedule data  *'               TO S-ISSUERS-NAME              (W-SCHED-SUB)
                               MOVE '* header missing *'               TO S-STOCK-DESCRIPTION         (W-SCHED-SUB)
                               MOVE SPACES                             TO S-LAST-TRANCHE-CONTRACT-NO  (W-SCHED-SUB)
                               MOVE '1'                                TO S-SEDOL-RECORD-COUNT        (W-SCHED-SUB)
                               MOVE '1'                                TO S-TRANCHE-COUNT             (W-SCHED-SUB)
                               MOVE 'P'                                TO S-PRINT-FLAG                (W-SCHED-SUB)
                               MOVE ' '                                TO S-HOLDING-FLAG              (W-SCHED-SUB)
                               MOVE ' '                                TO S-REIT-SECURITY-TYPE        (W-SCHED-SUB)
                               MOVE '0'                                TO S-QUOTED-INDICATOR          (W-SCHED-SUB)
                               MOVE ZERO                               TO S-PROFIT-LOSS               (W-SCHED-SUB)
                                                                          S-LOST-INDEXATION           (W-SCHED-SUB)
                                                                          S-OFFSHORE-INCOME-GAIN      (W-SCHED-SUB)
                               EVALUATE L-FILE-NAME
                               WHEN REALISED-DATA-FILE
                                    MOVE S-RECORD(W-SCHED-SUB)         TO D19-RECORD
                                                                          L-FILE-RECORD-AREA
                               WHEN UNREALISED-DATA-FILE
                                    MOVE S-RECORD(W-SCHED-SUB)         TO D20-RECORD
                                                                          L-FILE-RECORD-AREA
                               WHEN NOTIONAL-SALE-DATA-FILE
                                    MOVE S-RECORD(W-SCHED-SUB)         TO D81-RECORD
                                                                          L-FILE-RECORD-AREA
                               WHEN REALISED-TAX-DATA-FILE
                                    MOVE S-RECORD(W-SCHED-SUB)         TO D98-RECORD
                                                                          L-FILE-RECORD-AREA
                               WHEN UNREALISED-TAX-DATA-FILE
                                    MOVE S-RECORD(W-SCHED-SUB)         TO D101-RECORD
                                                                          L-FILE-RECORD-AREA
                               END-EVALUATE

                               SET RECORD-CACHED(W-SCHED-SUB)          TO TRUE
                           END-IF
                           *> store the current record
                           MOVE S-RECORD(SUB-CURRENT-RECORD)           TO S-RECORD(W-SCHED-SUB)
                     END-IF
                END-EVALUATE
           END-IF.
       Z6-EXIT.
           EXIT.


       A-INITIALISE SECTION.
           MOVE '00'   TO L-FILE-RETURN-CODE
                          FILE-STATUS.

           MOVE SPACES TO W-STORE-ACTION
      *
           EVALUATE L-FILE-NAME
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      MOVE L-USER-NO   TO D5-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB2                      MOVE D5-FILE-NAME TO W-REPORT
 BB21                                         W-REPORT-NAME
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      MOVE L-USER-NO   TO D6-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB2                      MOVE D6-FILE-NAME TO W-REPORT
 BB21                                         W-REPORT-NAME
                    WHEN STERLING-EXTEL-REPORT
                         MOVE L-USER-NO   TO D10-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D10-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN FOREIGN-EXTEL-REPORT
                         MOVE L-USER-NO   TO D11-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D11-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN OUTPUT-LISTING
                         MOVE L-USER-NO   TO D12-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                    WHEN ERROR-REPORT-FILE
                         MOVE L-USER-NO   TO D18-USER-NO
                         MOVE L-REPORT-NO TO D18-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D18-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN REALISED-DATA-FILE                    
                         MOVE L-USER-NO   TO D19-USER-NO
                         MOVE L-REPORT-NO TO D19-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                    WHEN UNREALISED-DATA-FILE
                         MOVE L-USER-NO   TO D20-USER-NO
                         MOVE L-REPORT-NO TO D20-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                    WHEN REALISED-SCHEDULE-FILE
                         MOVE L-USER-NO   TO D21-USER-NO
                         MOVE L-REPORT-NO TO D21-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D21-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN UNREALISED-SCHEDULE-FILE
                         MOVE L-USER-NO   TO D22-USER-NO
                         MOVE L-REPORT-NO TO D22-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D22-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN CG01-REPORT-FILE
                         MOVE L-USER-NO   TO D23-USER-NO
                         MOVE L-REPORT-NO TO D23-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D23-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN CG02-REPORT-FILE
                         MOVE L-USER-NO   TO D24-USER-NO
                         MOVE L-REPORT-NO TO D24-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D24-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN CG03-REPORT-FILE
                         MOVE L-USER-NO   TO D25-USER-NO
                         MOVE L-REPORT-NO TO D25-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D25-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN YE-REC-REPORT-FILE
                         MOVE L-USER-NO   TO D26-USER-NO
                         MOVE L-REPORT-NO TO D26-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D26-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN YE-DEL-REPORT-FILE
                         MOVE L-USER-NO   TO D27-USER-NO
                         MOVE L-REPORT-NO TO D27-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D27-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN YE-CON-REPORT-FILE
                         MOVE L-USER-NO   TO D28-USER-NO
                         MOVE L-REPORT-NO TO D28-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D28-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN ERROR-DATA-FILE
                         MOVE L-USER-NO   TO D29-USER-NO
                         MOVE L-REPORT-NO TO D29-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                    WHEN YE-ERR-REPORT-FILE
                         MOVE L-USER-NO   TO D30-USER-NO
                         MOVE L-REPORT-NO TO D30-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                         MOVE D30-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
                    WHEN YE-REC2-DATA-FILE
                         MOVE L-USER-NO   TO D33-USER-NO
                         MOVE L-REPORT-NO TO D33-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
                    WHEN USER-FUND-FILE
                         CONTINUE
BB18                WHEN SEQ-BALANCE-FILE
BB18                     MOVE L-USER-NO   TO D45-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB2                 WHEN TRANSACTION-FILE
BB2                      MOVE L-FILE-RECORD-AREA TO D46-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   MOVE L-USER-NO   TO D49-USER-NO
   BB4                   MOVE L-REPORT-NO TO D49-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
   BB4                   MOVE D49-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   MOVE L-USER-NO   TO D50-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
   BB4                   MOVE D50-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   MOVE L-FILE-RECORD-AREA TO D51-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
   BB4              WHEN FUNDS-LOAD-DATA-FILE
BB8BB4                   MOVE L-FILE-RECORD-AREA TO D68-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
   BB4              WHEN FUNDS-LOAD-REPORT
BB8BB4                   MOVE L-USER-NO   TO D69-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB8BB4                   MOVE D69-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     MOVE L-FILE-RECORD-AREA TO D70-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     MOVE L-USER-NO   TO D71-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                     MOVE D71-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB8                WHEN SKAN1-REPORT
 BB8                     MOVE L-USER-NO   TO D72-USER-NO
 BB8                     MOVE L-REPORT-NO TO D72-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                     MOVE D72-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB8                WHEN SKAN2-REPORT
 BB8                     MOVE L-USER-NO   TO D73-USER-NO
 BB8                     MOVE L-REPORT-NO TO D73-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                     MOVE D73-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     MOVE L-USER-NO   TO D74-USER-NO
 BB8                     MOVE L-REPORT-NO TO D74-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                     MOVE D74-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     MOVE L-USER-NO   TO D75-USER-NO
 BB8                     MOVE L-REPORT-NO TO D75-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                     MOVE D75-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     MOVE L-USER-NO   TO D76-USER-NO
 BB8                     MOVE L-REPORT-NO TO D76-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                     MOVE D76-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     MOVE L-USER-NO   TO D77-USER-NO
 BB8                     MOVE L-REPORT-NO TO D77-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB8                     MOVE D77-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    MOVE L-USER-NO   TO D78-USER-NO
  BB9                    MOVE L-REPORT-NO TO D78-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB9                    MOVE D78-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    MOVE L-USER-NO   TO D81-USER-NO
  BB9                    MOVE L-REPORT-NO TO D81-REPORT-NO
 BB21                                          W-REPORT-NAME
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    MOVE L-USER-NO   TO D82-USER-NO
  BB9                    MOVE L-REPORT-NO TO D82-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB9                    MOVE D82-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    MOVE L-FILE-RECORD-AREA TO D83-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    MOVE L-USER-NO   TO D84-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB9                    MOVE D84-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      MOVE L-USER-NO   TO D85-USER-NO
EJ2                      MOVE L-REPORT-NO TO D85-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
EJ2                      MOVE D85-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
DGD                 WHEN RCF-BACKUP-FILE
DGD                      MOVE L-FILE-RECORD-AREA TO D87-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    MOVE L-USER-NO   TO D88-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB11                    MOVE D88-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    MOVE L-FILE-RECORD-AREA TO D89-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    MOVE L-USER-NO   TO D90-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB11                    MOVE D90-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    MOVE L-FILE-RECORD-AREA TO D91-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    MOVE L-USER-NO   TO D92-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB11                    MOVE D92-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    MOVE L-FILE-RECORD-AREA TO D93-FILE
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   MOVE L-USER-NO   TO D94-USER-NO
  BB12                   MOVE L-REPORT-NO TO D94-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB12                   MOVE D94-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
  BB12              WHEN GAINLOSS-REPORT
  BB12                   MOVE L-USER-NO   TO D95-USER-NO
  BB12                   MOVE L-REPORT-NO TO D95-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
  BB12                   MOVE D95-FILE-NAME TO W-REPORT
 BB21                                          W-REPORT-NAME
BB14                WHEN LOST-INDEXATION-REPORT
BB14                     MOVE L-USER-NO        TO D96-USER-NO
BB14                     MOVE L-REPORT-NO      TO D96-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB14                     MOVE D96-FILE-NAME    TO W-REPORT
 BB21                                             W-REPORT-NAME
BB14                     EVALUATE L-FILE-ACTION
BB14                     WHEN OPEN-INPUT
BB14                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB14                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB14                     WHEN CLOSE-FILE
BB14                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB14                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB14                     WHEN READ-NEXT
BB14                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB14                          MOVE READ-REPORT       TO L-FILE-ACTION
BB14                     END-EVALUATE
BB15                WHEN LOSU-INDEXATION-REPORT
BB15                     MOVE L-USER-NO        TO D97-USER-NO
BB15                     MOVE L-REPORT-NO      TO D97-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB15                     MOVE D97-FILE-NAME    TO W-REPORT
 BB21                                             W-REPORT-NAME
BB15                     EVALUATE L-FILE-ACTION
BB15                     WHEN OPEN-INPUT
BB15                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB15                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB15                     WHEN CLOSE-FILE
BB15                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB15                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB14                     WHEN READ-NEXT
BB15                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB15                          MOVE READ-REPORT       TO L-FILE-ACTION
BB15                     END-EVALUATE
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     MOVE L-USER-NO   TO D98-USER-NO
BB17                     MOVE L-REPORT-NO TO D98-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB17                WHEN REALISED-TAX-SCHEDULE-FILE
BB17                     MOVE L-USER-NO        TO D100-USER-NO
BB17                     MOVE L-REPORT-NO      TO D100-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB17                     MOVE D100-FILE-NAME    TO W-REPORT
 BB21                                              W-REPORT-NAME
BB17                     EVALUATE L-FILE-ACTION
BB17                     WHEN OPEN-INPUT
BB17                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB17                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB17                     WHEN CLOSE-FILE
BB17                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB17                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB17                     WHEN READ-NEXT
BB17                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB17                          MOVE READ-REPORT       TO L-FILE-ACTION
BB17                     END-EVALUATE
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     MOVE L-USER-NO   TO D101-USER-NO
BB17                     MOVE L-REPORT-NO TO D101-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB17                WHEN UNREALISED-TAX-SCHEDULE-FILE
BB17                     MOVE L-USER-NO        TO D102-USER-NO
BB17                     MOVE L-REPORT-NO      TO D102-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB17                     MOVE D102-FILE-NAME    TO W-REPORT
 BB21                                              W-REPORT-NAME
BB17                     EVALUATE L-FILE-ACTION
BB17                     WHEN OPEN-INPUT
BB17                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB17                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB17                     WHEN CLOSE-FILE
BB17                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB17                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB17                     WHEN READ-NEXT
BB17                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB17                          MOVE READ-REPORT       TO L-FILE-ACTION
BB17                     END-EVALUATE
BB19                WHEN REAL-H-O-GAINS-REPORT
BB19                     MOVE L-USER-NO        TO D103-USER-NO
BB19                     MOVE L-REPORT-NO      TO D103-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB19                     MOVE D103-FILE-NAME   TO W-REPORT
 BB21                                             W-REPORT-NAME
BB19                     EVALUATE L-FILE-ACTION
BB19                     WHEN OPEN-INPUT
BB19                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB19                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB19                     WHEN CLOSE-FILE
BB19                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB19                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB19                     WHEN READ-NEXT
BB19                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB19                          MOVE READ-REPORT       TO L-FILE-ACTION
BB19                     END-EVALUATE
BB19                WHEN UNREAL-H-O-GAINS-REPORT
BB19                     MOVE L-USER-NO        TO D104-USER-NO
BB19                     MOVE L-REPORT-NO      TO D104-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB19                     MOVE D104-FILE-NAME   TO W-REPORT
 BB21                                             W-REPORT-NAME
BB19                     EVALUATE L-FILE-ACTION
BB19                     WHEN OPEN-INPUT
BB19                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB19                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB19                     WHEN CLOSE-FILE
BB19                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB19                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB19                     WHEN READ-NEXT
BB19                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB19                          MOVE READ-REPORT       TO L-FILE-ACTION
 BB20                    END-EVALUATE
 BB20               WHEN ACQUISITION-EXPORT-FILE
 BB20                    MOVE L-USER-NO        TO D105-USER-NO
 BB20                    MOVE L-REPORT-NO      TO D105-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB20                    MOVE D105-FILE-NAME   TO W-REPORT
 BB21                                             W-REPORT-NAME
 BB20                    EVALUATE L-FILE-ACTION
 BB20                    WHEN OPEN-INPUT
 BB20                         MOVE L-FILE-ACTION     TO W-STORE-ACTION
 BB20                         MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
 BB20                    WHEN CLOSE-FILE
 BB20                         MOVE L-FILE-ACTION     TO W-STORE-ACTION
 BB20                         MOVE CLOSE-REPORT      TO L-FILE-ACTION
 BB20                    WHEN READ-NEXT
 BB20                         MOVE L-FILE-ACTION     TO W-STORE-ACTION
 BB20                         MOVE READ-REPORT       TO L-FILE-ACTION
BB19                     END-EVALUATE
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                         continue  
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    MOVE L-USER-NO TO D107-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB21               WHEN TRACE-FILE
 BB21                    MOVE L-USER-NO TO D108-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
 BB21               WHEN ERROR-LOG-FILE
 BB21                    MOVE L-USER-NO TO D109-USER-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB22                WHEN TAPER-REPORT-FILE
BB22                     MOVE L-USER-NO        TO D110-USER-NO
BB22                     MOVE L-REPORT-NO      TO D110-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB22                     MOVE D110-FILE-NAME   TO W-REPORT
 BB21                                             W-REPORT-NAME
BB22                     EVALUATE L-FILE-ACTION
BB22                     WHEN OPEN-INPUT
BB22                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB22                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB22                     WHEN CLOSE-FILE
BB22                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB22                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB22                     WHEN READ-NEXT
BB22                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB22                          MOVE READ-REPORT       TO L-FILE-ACTION
BB22                     END-EVALUATE
BB22                WHEN TAPER-EXPORT-FILE
BB22                     MOVE L-USER-NO        TO D111-USER-NO
BB22                     MOVE L-REPORT-NO      TO D111-REPORT-NO
 BB21                    PERFORM Z4-MAKE-FILE-NAME
BB22                     MOVE D111-FILE-NAME   TO W-REPORT
 BB21                                             W-REPORT-NAME
BB22                     EVALUATE L-FILE-ACTION
BB22                     WHEN OPEN-INPUT
BB22                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB22                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB22                     WHEN CLOSE-FILE
BB22                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB22                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB22                     WHEN READ-NEXT
BB22                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB22                          MOVE READ-REPORT       TO L-FILE-ACTION
BB22                     END-EVALUATE
BB26                WHEN INTER-CONNECTED-FUNDS-EXPORT
BB26                     MOVE L-USER-NO        TO D164-USER-NO
BB26                     PERFORM Z4-MAKE-FILE-NAME
BB26                     MOVE D164-FILE-NAME   TO W-REPORT
BB26                                              W-REPORT-NAME
BB26                     EVALUATE L-FILE-ACTION
BB26                     WHEN OPEN-INPUT
BB26                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB26                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB26                     WHEN CLOSE-FILE
BB26                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB26                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB26                     WHEN READ-NEXT
BB26                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB26                          MOVE READ-REPORT       TO L-FILE-ACTION
BB26                     END-EVALUATE
BB26                WHEN INTER-CONNECTED-FUNDS-REPORT
BB26                     MOVE L-USER-NO        TO D165-USER-NO
BB26                     PERFORM Z4-MAKE-FILE-NAME
BB26                     MOVE D165-FILE-NAME   TO W-REPORT
BB26                                              W-REPORT-NAME
BB26                     EVALUATE L-FILE-ACTION
BB26                     WHEN OPEN-INPUT
BB26                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB26                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB26                     WHEN CLOSE-FILE
BB26                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB26                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB26                     WHEN READ-NEXT
BB26                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB26                          MOVE READ-REPORT       TO L-FILE-ACTION
BB26                     END-EVALUATE
BB25                WHEN ALLOWANCES-FROM-DB-FILE
BB25                     MOVE L-USER-NO        TO D159-USER-NO
BB25                     PERFORM Z4-MAKE-FILE-NAME
BB25                     MOVE D159-FILE-NAME    TO W-REPORT
BB25                                               W-REPORT-NAME
BB25                     EVALUATE L-FILE-ACTION
BB25                     WHEN OPEN-INPUT
BB25                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB25                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB25                     WHEN CLOSE-FILE
BB25                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB25                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB25                     WHEN READ-NEXT
BB25                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB25                          MOVE READ-REPORT       TO L-FILE-ACTION
BB25                     END-EVALUATE
BB25                WHEN LOSSES-FROM-DB-FILE
BB25                     MOVE L-USER-NO        TO D160-USER-NO
BB25                     PERFORM Z4-MAKE-FILE-NAME
BB25                     MOVE D160-FILE-NAME    TO W-REPORT
BB25                                               W-REPORT-NAME
BB25                     EVALUATE L-FILE-ACTION
BB25                     WHEN OPEN-INPUT
BB25                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB25                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB25                     WHEN CLOSE-FILE
BB25                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB25                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB25                     WHEN READ-NEXT
BB25                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB25                          MOVE READ-REPORT       TO L-FILE-ACTION
BB25                     END-EVALUATE
BB25                WHEN DISPOSALS-FROM-DB-FILE
BB25                     MOVE L-USER-NO        TO D161-USER-NO
BB25                     PERFORM Z4-MAKE-FILE-NAME
BB27                WHEN DAILY-TRANSACTION-EXPORT-FILE
BB27                     MOVE L-USER-NO        TO D171-USER-NO
BB27                     PERFORM Z4-MAKE-FILE-NAME
BB27                     MOVE D171-FILE-NAME   TO W-REPORT
BB27                                              W-REPORT-NAME
BB27                     EVALUATE L-FILE-ACTION
BB27                     WHEN OPEN-INPUT
BB27                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB27                          MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
BB27                     WHEN CLOSE-FILE
BB27                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB27                          MOVE CLOSE-REPORT      TO L-FILE-ACTION
BB27                     WHEN READ-NEXT
BB27                          MOVE L-FILE-ACTION     TO W-STORE-ACTION
BB27                          MOVE READ-REPORT       TO L-FILE-ACTION
BB27                     END-EVALUATE
 BB21               WHEN OTHER
 BB21                    PERFORM Z4-MAKE-FILE-NAME
           END-EVALUATE
      /
           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         MOVE L-FILE-RECORD-AREA TO D1-RECORD
                    WHEN GROUP-FILE
                         MOVE L-FILE-RECORD-AREA TO D2-RECORD
                    WHEN STOCK-FILE
                         MOVE L-FILE-RECORD-AREA TO D3-RECORD
                    WHEN FUND-FILE
                         MOVE L-FILE-RECORD-AREA TO D4-RECORD
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      MOVE L-FILE-RECORD-AREA TO D5-RECORD
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      MOVE L-FILE-RECORD-AREA TO D6-RECORD
                    WHEN RPI-FILE
                         MOVE L-FILE-RECORD-AREA TO D7-RECORD
                    WHEN PARAMETER-FILE
                         MOVE L-FILE-RECORD-AREA TO D8-RECORD
                    WHEN USER-FILE
                         MOVE L-FILE-RECORD-AREA TO D9-RECORD
                    WHEN STERLING-EXTEL-REPORT
                         MOVE L-FILE-RECORD-AREA TO D10-RECORD
                    WHEN FOREIGN-EXTEL-REPORT
                         MOVE L-FILE-RECORD-AREA TO D11-RECORD
                    WHEN OUTPUT-LISTING
                         MOVE L-FILE-RECORD-AREA TO D12-RECORD
                    WHEN MASTER-LOG-FILE
                         MOVE L-FILE-RECORD-AREA TO D17-RECORD
                    WHEN REALISED-DATA-FILE
                         MOVE L-FILE-RECORD-AREA TO D19-RECORD
                    WHEN UNREALISED-DATA-FILE
                         MOVE L-FILE-RECORD-AREA TO D20-RECORD
                    WHEN PRINTER-FILE
                         MOVE L-FILE-RECORD-AREA TO D31-RECORD
                    WHEN STOCK-TYPE-FILE
                         MOVE L-FILE-RECORD-AREA TO D32-RECORD
                    WHEN TRANSACTION-CODE-FILE
                         MOVE L-FILE-RECORD-AREA TO D34-RECORD
                    WHEN OUTPUT-LOG-FILE
                         MOVE L-FILE-RECORD-AREA TO D35-RECORD
                    WHEN MESSAGE-FILE
                         MOVE L-FILE-RECORD-AREA TO D36-RECORD
                    WHEN USER-FUND-FILE
dtv                      continue
                    WHEN HELP-TEXT-FILE
                         MOVE L-FILE-RECORD-AREA TO D38-RECORD
                    WHEN DEFAULT-ACCESS-FILE
                         MOVE L-FILE-RECORD-AREA TO D39-RECORD
                    WHEN ACCESS-PROFILE-FILE
                         MOVE L-FILE-RECORD-AREA TO D40-RECORD
                    WHEN EXTEL-CURRENCY-FILE
                         MOVE L-FILE-RECORD-AREA TO D42-RECORD
                    WHEN STOCK-PRICE-FILE
                         MOVE L-FILE-RECORD-AREA TO D43-RECORD
                    WHEN SEQ-BALANCE-FILE
BB7                      MOVE L-FILE-RECORD-AREA
BB7                           TO D45-BAL-ACQ-DISP-RECORD
BB2                 WHEN TRANSACTION-FILE
BB2                      MOVE L-FILE-RECORD-AREA TO D46-RECORD
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    MOVE L-FILE-RECORD-AREA TO D79-RECORD
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    MOVE L-FILE-RECORD-AREA TO D80-RECORD
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    MOVE L-FILE-RECORD-AREA TO D81-RECORD
MJ1                 WHEN RCF-FILE                  
MJ1                      MOVE L-FILE-RECORD-AREA TO D86-RECORD
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    MOVE L-FILE-RECORD-AREA TO D88-RECORD
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    MOVE L-FILE-RECORD-AREA TO D89-RECORD
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    MOVE L-FILE-RECORD-AREA TO D90-RECORD
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    MOVE L-FILE-RECORD-AREA TO D91-RECORD
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    MOVE L-FILE-RECORD-AREA TO D92-RECORD
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    MOVE L-FILE-RECORD-AREA TO D93-RECORD
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   MOVE L-FILE-RECORD-AREA TO D94-RECORD
  BB12              WHEN GAINLOSS-REPORT
  BB12                   MOVE L-FILE-RECORD-AREA TO D95-RECORD
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     MOVE L-FILE-RECORD-AREA TO D98-RECORD
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     MOVE L-FILE-RECORD-AREA TO D101-RECORD
BB22                WHEN TAPER-RATE-FILE                  
BB22                     MOVE L-FILE-RECORD-AREA TO D112-RECORD
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     MOVE L-FILE-RECORD-AREA TO D153-RECORD
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     MOVE L-FILE-RECORD-AREA TO D154-RECORD
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     MOVE L-FILE-RECORD-AREA TO D163-RECORD
BB27                WHEN PRICE-TYPES-FILE
BB27                     MOVE L-FILE-RECORD-AREA TO D167-RECORD
BB27                WHEN PENDING-LOG-FILE
BB27                     MOVE L-FILE-RECORD-AREA TO D169-RECORD
BB27                WHEN PENDING-ITEMS-FILE
BB27                     MOVE L-FILE-RECORD-AREA TO D170-RECORD
           END-EVALUATE.
       A-EXIT.
           EXIT.
      /
       B-OPEN-INPUT SECTION.
           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         call "COUNTRYDAL" using L-FILE-ACTION
                                                 L-FILE-RECORD-AREA
                    WHEN GROUP-FILE
                         call "GroupDAL" using L-FILE-ACTION
                                               L-FILE-RECORD-AREA
                    WHEN STOCK-FILE
                         OPEN INPUT D3
                    WHEN FUND-FILE
                         OPEN INPUT D4
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      OPEN INPUT D5
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      OPEN INPUT D6
                    WHEN RPI-FILE
                         call "RPIDAL" using L-FILE-ACTION
                                             L-FILE-RECORD-AREA
                    WHEN PARAMETER-FILE
                         call "ParametersDAL" using L-FILE-ACTION
                                                    L-FILE-RECORD-AREA
                    WHEN USER-FILE
                         OPEN INPUT D9
                    WHEN STERLING-EXTEL-REPORT
                         OPEN INPUT D10
                    WHEN FOREIGN-EXTEL-REPORT
                         OPEN INPUT D11
                    WHEN MASTER-LOG-FILE
                         OPEN INPUT D17
                    WHEN ERROR-REPORT-FILE
                         OPEN INPUT D18
                    WHEN REALISED-DATA-FILE
                         OPEN INPUT D19
                    WHEN UNREALISED-DATA-FILE
                         OPEN INPUT D20
                    WHEN REALISED-SCHEDULE-FILE
                         OPEN INPUT D21
                    WHEN UNREALISED-SCHEDULE-FILE
                         OPEN INPUT D22
                    WHEN CG01-REPORT-FILE
                         OPEN INPUT D23
                    WHEN CG02-REPORT-FILE
                         OPEN INPUT D24
                    WHEN CG03-REPORT-FILE
                         OPEN INPUT D25
                    WHEN YE-REC-REPORT-FILE
                         OPEN INPUT D26
                    WHEN YE-DEL-REPORT-FILE
                         OPEN INPUT D27
                    WHEN YE-CON-REPORT-FILE
                         OPEN INPUT D28
                    WHEN ERROR-DATA-FILE
                         OPEN INPUT D29
                    WHEN YE-ERR-REPORT-FILE
                         OPEN INPUT D30
                    WHEN PRINTER-FILE
                         OPEN INPUT D31
                    WHEN STOCK-TYPE-FILE
                         OPEN INPUT D32
                    WHEN YE-REC2-DATA-FILE
                         OPEN INPUT D33
                    WHEN TRANSACTION-CODE-FILE
                         OPEN INPUT D34
                    WHEN OUTPUT-LOG-FILE
                         OPEN INPUT D35
                    WHEN MESSAGE-FILE
                         OPEN INPUT D36
                    WHEN USER-FUND-FILE
                         call "UserFundDAL" using L-FILE-ACTION
                                                  L-FILE-RECORD-AREA
                                                  L-USER-NO
                    WHEN HELP-TEXT-FILE
                         OPEN INPUT D38
                    WHEN DEFAULT-ACCESS-FILE
                         OPEN INPUT D39
                    WHEN ACCESS-PROFILE-FILE
                         OPEN INPUT D40
                    WHEN EXTEL-PRICES-FILE
                         OPEN INPUT D41
                    WHEN EXTEL-CURRENCY-FILE
                         OPEN INPUT D42
                    WHEN STOCK-PRICE-FILE
                         OPEN INPUT D43
                    WHEN EXTEL-TRANSMISSION-FILE
                         OPEN INPUT D44
                    WHEN SEQ-BALANCE-FILE
                         OPEN INPUT D45
BB2                 WHEN TRANSACTION-FILE
BB2                      OPEN INPUT D46
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   OPEN INPUT D49
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   OPEN INPUT D50
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   OPEN INPUT D51
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      OPEN INPUT D68
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      OPEN INPUT D69
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     OPEN INPUT D70
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     OPEN INPUT D71
 BB8                WHEN SKAN1-REPORT
 BB8                     OPEN INPUT D72
 BB8                WHEN SKAN2-REPORT
 BB8                     OPEN INPUT D73
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     OPEN INPUT D74
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     OPEN INPUT D75
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     OPEN INPUT D76
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     OPEN INPUT D77
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    OPEN INPUT D78
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    OPEN INPUT D79
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    OPEN INPUT D80
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    OPEN INPUT D81
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    OPEN INPUT D82
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    OPEN INPUT D83
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    OPEN INPUT D84
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      OPEN INPUT D85 
MJ1                 WHEN RCF-FILE
MJ1                      OPEN INPUT D86
DGD                 WHEN RCF-BACKUP-FILE
DGD                      OPEN INPUT D87
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    OPEN INPUT D88
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    OPEN INPUT D89
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    OPEN INPUT D90
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    OPEN INPUT D91
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    OPEN INPUT D92
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    OPEN INPUT D93
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   OPEN INPUT D94
  BB12              WHEN GAINLOSS-REPORT
  BB12                   OPEN INPUT D95
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     OPEN INPUT D98
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     OPEN INPUT D101
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    OPEN INPUT D107
 BB21               WHEN TRACE-FILE
 BB21                    OPEN INPUT D108
 BB21               WHEN ERROR-LOG-FILE
 BB21                    OPEN INPUT D109
BB22                WHEN TAPER-RATE-FILE                  
                         call "TaperRateDAL" using L-FILE-ACTION
                                                   L-FILE-RECORD-AREA
                                                   L-USER-NO
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     OPEN INPUT D153
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     OPEN INPUT D154
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     OPEN INPUT D163
BB25                WHEN DISPOSALS-FROM-DB-FILE
BB25                     OPEN INPUT D161
BB25                     MOVE    1   TO W-D161-CHAR-SUB
BB25                     MOVE SPACES TO W-D161-RECORD
BB27                WHEN PRICE-TYPES-FILE
BB27                     OPEN INPUT D167
BB27                WHEN PENDING-LOG-FILE
BB27                     OPEN INPUT D169
BB27                WHEN PENDING-ITEMS-FILE
BB27                     OPEN INPUT D170
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       B-EXIT.
           EXIT.
      /
       C-OPEN-OUTPUT SECTION.
           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         OPEN OUTPUT D3
                    WHEN FUND-FILE
                         OPEN OUTPUT D4
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      OPEN OUTPUT D5
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      OPEN OUTPUT D6
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         OPEN OUTPUT D9
                    WHEN STERLING-EXTEL-REPORT
                         OPEN OUTPUT D10
                    WHEN FOREIGN-EXTEL-REPORT
                         OPEN OUTPUT D11
                    WHEN OUTPUT-LISTING
                         OPEN OUTPUT D12
                    WHEN MASTER-LOG-FILE
                         OPEN OUTPUT D17
                    WHEN ERROR-REPORT-FILE
                         OPEN OUTPUT D18
                    WHEN REALISED-DATA-FILE
                         OPEN OUTPUT D19
                         MOVE ZERO TO W-D19-COUNT
                    WHEN UNREALISED-DATA-FILE
                         OPEN OUTPUT D20
                         MOVE ZERO TO W-D20-COUNT
                    WHEN REALISED-SCHEDULE-FILE
                         OPEN OUTPUT D21
                    WHEN UNREALISED-SCHEDULE-FILE
                         OPEN OUTPUT D22
                    WHEN CG01-REPORT-FILE
                         OPEN OUTPUT D23
                    WHEN CG02-REPORT-FILE
                         OPEN OUTPUT D24
                    WHEN CG03-REPORT-FILE
                         OPEN OUTPUT D25
                    WHEN YE-REC-REPORT-FILE
                         OPEN OUTPUT D26
                    WHEN YE-DEL-REPORT-FILE
                         OPEN OUTPUT D27
                    WHEN YE-CON-REPORT-FILE
                         OPEN OUTPUT D28
                    WHEN ERROR-DATA-FILE
                         OPEN OUTPUT D29
                    WHEN YE-ERR-REPORT-FILE
                         OPEN OUTPUT D30
                    WHEN PRINTER-FILE
                         OPEN OUTPUT D31
                    WHEN STOCK-TYPE-FILE
                         OPEN OUTPUT D32
                    WHEN YE-REC2-DATA-FILE
                         OPEN OUTPUT D33
                    WHEN TRANSACTION-CODE-FILE
                         OPEN OUTPUT D34
                    WHEN OUTPUT-LOG-FILE
                         OPEN OUTPUT D35
                    WHEN MESSAGE-FILE
                         OPEN OUTPUT D36
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         OPEN OUTPUT D38
                    WHEN DEFAULT-ACCESS-FILE
                         OPEN OUTPUT D39
                    WHEN ACCESS-PROFILE-FILE
                         OPEN OUTPUT D40
                    WHEN EXTEL-PRICES-FILE
                         OPEN OUTPUT D41
                    WHEN EXTEL-CURRENCY-FILE
                         OPEN OUTPUT D42
                    WHEN STOCK-PRICE-FILE
                         OPEN OUTPUT D43
                    WHEN EXTEL-TRANSMISSION-FILE
                         OPEN OUTPUT D44
                    WHEN SEQ-BALANCE-FILE
                         OPEN OUTPUT D45
BB2                 WHEN TRANSACTION-FILE
BB2                      OPEN OUTPUT D46
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   OPEN OUTPUT D49
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   OPEN OUTPUT D50
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   OPEN OUTPUT D51
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      OPEN OUTPUT D68
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      OPEN OUTPUT D69
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     OPEN OUTPUT D70
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     OPEN OUTPUT D71
 BB8                WHEN SKAN1-REPORT
 BB8                     OPEN OUTPUT D72
 BB8                WHEN SKAN2-REPORT
 BB8                     OPEN OUTPUT D73
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     OPEN OUTPUT D74
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     OPEN OUTPUT D75
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     OPEN OUTPUT D76
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     OPEN OUTPUT D77
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    OPEN OUTPUT D78
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    OPEN OUTPUT D79
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    OPEN OUTPUT D80
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    OPEN OUTPUT D81
  BB9                    MOVE ZERO TO W-D81-COUNT
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    OPEN OUTPUT D82
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    OPEN OUTPUT D83
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    OPEN OUTPUT D84
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      OPEN OUTPUT D85 
MJ1                 WHEN RCF-FILE
MJ1                      OPEN OUTPUT D86
DGD                 WHEN RCF-BACKUP-FILE
DGD                      OPEN OUTPUT D87
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    OPEN OUTPUT D88
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    OPEN OUTPUT D89
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    OPEN OUTPUT D90
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    OPEN OUTPUT D91
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    OPEN OUTPUT D92
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    OPEN OUTPUT D93
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   OPEN OUTPUT D94
  BB12              WHEN GAINLOSS-REPORT
  BB12                   OPEN OUTPUT D95
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     OPEN OUTPUT D98
BB17                     MOVE ZERO TO W-D98-COUNT
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     OPEN OUTPUT D101
BB17                     MOVE ZERO TO W-D101-COUNT
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    OPEN OUTPUT D107
 BB21               WHEN TRACE-FILE
 BB21                    OPEN OUTPUT D108
 BB21               WHEN ERROR-LOG-FILE
 BB21                    OPEN OUTPUT D109
BB22                WHEN TAPER-RATE-FILE                  
BB22                     OPEN OUTPUT D112
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     OPEN OUTPUT D153
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     OPEN OUTPUT D154
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     OPEN OUTPUT D163
BB27                WHEN PRICE-TYPES-FILE
BB27                     OPEN OUTPUT D167
BB27                WHEN PENDING-LOG-FILE
BB27                     OPEN OUTPUT D169
BB27                WHEN PENDING-ITEMS-FILE
BB27                     OPEN OUTPUT D170
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       C-EXIT.
           EXIT.
      /
       D-OPEN-I-O SECTION.
           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         OPEN I-O D3
                    WHEN FUND-FILE
                         OPEN I-O D4
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         call "ParametersDAL" using L-FILE-ACTION
                                                    L-FILE-RECORD-AREA
                    WHEN USER-FILE
                         OPEN I-O D9
                    WHEN OUTPUT-LISTING
                         OPEN I-O D12
                    WHEN MASTER-LOG-FILE
                         OPEN I-O D17
                    WHEN REALISED-DATA-FILE
                         OPEN I-O D19
                         MOVE ZERO TO W-D19-COUNT
                    WHEN UNREALISED-DATA-FILE
                         OPEN I-O D20
                         MOVE ZERO TO W-D20-COUNT
                    WHEN PRINTER-FILE
                         OPEN I-O D31
                    WHEN STOCK-TYPE-FILE
                         OPEN I-O D32
                    WHEN TRANSACTION-CODE-FILE
                         OPEN I-O D34
                    WHEN OUTPUT-LOG-FILE
                         OPEN I-O D35
                    WHEN MESSAGE-FILE
                         OPEN I-O D36
                    WHEN USER-FUND-FILE
                         call "UserFundDAL" using L-FILE-ACTION
                                                  L-FILE-RECORD-AREA
                                                  L-USER-NO
                    WHEN HELP-TEXT-FILE
                         OPEN I-O D38
                    WHEN DEFAULT-ACCESS-FILE
                         OPEN I-O D39
                    WHEN ACCESS-PROFILE-FILE
                         OPEN I-O D40
                    WHEN EXTEL-CURRENCY-FILE
                         OPEN I-O D42
                    WHEN STOCK-PRICE-FILE
                         OPEN I-O D43
                    WHEN SEQ-BALANCE-FILE
                         OPEN I-O D45
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    OPEN I-O D79
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    OPEN I-O D80
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    OPEN I-O D81
  BB9                    MOVE ZERO TO W-D81-COUNT
MJ1                 WHEN RCF-FILE
MJ1                      OPEN I-O D86
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     OPEN I-O D98
BB17                     MOVE ZERO TO W-D98-COUNT
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     OPEN I-O D101
BB17                     MOVE ZERO TO W-D101-COUNT
BB22                WHEN TAPER-RATE-FILE                  
BB22                     OPEN I-O D112
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     OPEN I-O D153
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     OPEN I-O D154
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     OPEN I-O D163
BB27                WHEN PRICE-TYPES-FILE
BB27                     OPEN I-O D167
BB27                WHEN PENDING-LOG-FILE
BB27                     OPEN I-O D169
BB27                WHEN PENDING-ITEMS-FILE
BB27                     OPEN I-O D170
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       D-EXIT.
           EXIT.
      /
       E-OPEN-EXTEND SECTION.
           EVALUATE L-FILE-NAME
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      OPEN EXTEND D5
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      OPEN EXTEND D6
                    WHEN STERLING-EXTEL-REPORT
                         OPEN EXTEND D10
                    WHEN FOREIGN-EXTEL-REPORT
                         OPEN EXTEND D11
                    WHEN ERROR-REPORT-FILE
                         OPEN EXTEND D18
                    WHEN REALISED-SCHEDULE-FILE
                         OPEN EXTEND D21
                    WHEN UNREALISED-SCHEDULE-FILE
                         OPEN EXTEND D22
                    WHEN CG01-REPORT-FILE
                         OPEN EXTEND D23
                    WHEN CG02-REPORT-FILE
                         OPEN EXTEND D24
                    WHEN CG03-REPORT-FILE
                         OPEN EXTEND D25
                    WHEN YE-REC-REPORT-FILE
                         OPEN EXTEND D26
                    WHEN YE-DEL-REPORT-FILE
                         OPEN EXTEND D27
                    WHEN YE-CON-REPORT-FILE
                         OPEN EXTEND D28
                    WHEN ERROR-DATA-FILE
                         OPEN EXTEND D29
                    WHEN YE-ERR-REPORT-FILE
                         OPEN EXTEND D30
                    WHEN YE-REC2-DATA-FILE
                         OPEN EXTEND D33
                    WHEN EXTEL-PRICES-FILE
                         OPEN EXTEND D41
                    WHEN EXTEL-TRANSMISSION-FILE
                         OPEN EXTEND D44
BB2                 WHEN TRANSACTION-FILE
BB2                      OPEN EXTEND D46
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   OPEN EXTEND D49
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   OPEN EXTEND D50
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   OPEN EXTEND D51
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      OPEN EXTEND D68
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      OPEN EXTEND D69
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     OPEN EXTEND D70
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     OPEN EXTEND D71
 BB8                WHEN SKAN1-REPORT
 BB8                     OPEN EXTEND D72
 BB8                WHEN SKAN2-REPORT
 BB8                     OPEN EXTEND D73
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     OPEN EXTEND D74
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     OPEN EXTEND D75
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     OPEN EXTEND D76
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     OPEN EXTEND D77
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    OPEN EXTEND D78
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    OPEN EXTEND D82
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    OPEN EXTEND D83
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    OPEN EXTEND D84
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      OPEN EXTEND D85 
DGD                 WHEN RCF-BACKUP-FILE
DGD                      OPEN EXTEND D87
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    OPEN EXTEND D88
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    OPEN EXTEND D89
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    OPEN EXTEND D90
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    OPEN EXTEND D91
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    OPEN EXTEND D92
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    OPEN EXTEND D93
  BB12              WHEN GAINLOSS-REPORT
  BB12                   OPEN EXTEND D95
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    OPEN EXTEND D107
 BB21               WHEN TRACE-FILE
 BB21                    OPEN EXTEND D108
 BB21               WHEN ERROR-LOG-FILE
 BB21                    OPEN EXTEND D109
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       E-EXIT.
           EXIT.
      /
       F-START-EQUAL SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE 
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         START D3 KEY = D3-KEY
                    WHEN FUND-FILE
                         START D4 KEY = D4-KEY
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         START D9 KEY = D9-KEY
                    WHEN MASTER-LOG-FILE
                         START D17 KEY = D17-KEY
                    WHEN REALISED-DATA-FILE
                         START D19 KEY = D19-SPLIT-KEY
                    WHEN UNREALISED-DATA-FILE
                         START D20 KEY = D20-SPLIT-KEY
                    WHEN PRINTER-FILE
                         START D31 KEY = D31-KEY
                    WHEN STOCK-TYPE-FILE
                         START D32 KEY = D32-KEY
                    WHEN TRANSACTION-CODE-FILE
                         START D34 KEY = D34-KEY
                    WHEN OUTPUT-LOG-FILE
                         START D35 KEY = D35-KEY
                    WHEN MESSAGE-FILE
                         START D36 KEY = D36-KEY
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         START D38 KEY = D38-KEY
                    WHEN DEFAULT-ACCESS-FILE
                         START D39 KEY = D39-KEY
                    WHEN ACCESS-PROFILE-FILE
                         START D40 KEY = D40-KEY
                    WHEN EXTEL-CURRENCY-FILE
                         START D42 KEY = D42-KEY
                    WHEN STOCK-PRICE-FILE
                         START D43 KEY = D43-KEY
                    WHEN SEQ-BALANCE-FILE
                         START D45 KEY = D45-KEY
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    START D79 KEY = D79-KEY
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    START D80 KEY = D80-KEY
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    START D81 KEY = D81-SPLIT-KEY
MJ1                 WHEN RCF-FILE             
MJ1                      START D86 KEY = D86-KEY
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     START D98 KEY = D98-SPLIT-KEY
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     START D101 KEY = D101-SPLIT-KEY
BB22                WHEN TAPER-RATE-FILE                  
BB22                     START D112 KEY = D112-KEY
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     START D153 KEY = D153-KEY
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     START D154 KEY = D154-KEY
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     START D163 KEY = D163-KEY
BB27                WHEN PRICE-TYPES-FILE
BB27                     START D167 KEY = D167-KEY
BB27                WHEN PENDING-LOG-FILE
BB27                     START D169 KEY = D169-KEY
BB27                WHEN PENDING-ITEMS-FILE
BB27                     START D170 KEY = D170-KEY
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       F-EXIT.
           EXIT.
      /
       G-START-NOT-LESS-THAN SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         START D3 KEY NOT < D3-KEY
                    WHEN FUND-FILE
                         START D4 KEY NOT < D4-KEY
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         START D9 KEY NOT < D9-KEY
                    WHEN MASTER-LOG-FILE
                         START D17 KEY NOT < D17-KEY
                    WHEN REALISED-DATA-FILE
                         START D19 KEY NOT < D19-SPLIT-KEY
                    WHEN UNREALISED-DATA-FILE
                         START D20 KEY NOT < D20-SPLIT-KEY
                    WHEN PRINTER-FILE
                         START D31 KEY NOT < D31-KEY
                    WHEN STOCK-TYPE-FILE
                         START D32 KEY NOT < D32-KEY
                    WHEN TRANSACTION-CODE-FILE
                         START D34 KEY NOT < D34-KEY
                    WHEN OUTPUT-LOG-FILE
                         START D35 KEY NOT < D35-KEY
                    WHEN MESSAGE-FILE
                         START D36 KEY NOT < D36-KEY
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         START D38 KEY NOT < D38-KEY
                    WHEN DEFAULT-ACCESS-FILE
                         START D39 KEY NOT < D39-KEY
                    WHEN ACCESS-PROFILE-FILE
                         START D40 KEY NOT < D40-KEY
                    WHEN EXTEL-CURRENCY-FILE
                         START D42 KEY NOT < D42-KEY
                    WHEN STOCK-PRICE-FILE
                         START D43 KEY NOT < D43-KEY
                    WHEN SEQ-BALANCE-FILE
                         START D45 KEY NOT < D45-KEY
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    START D79 KEY NOT < D79-KEY
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    START D80 KEY NOT < D80-KEY
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    START D81 KEY NOT < D81-SPLIT-KEY
MJ1                 WHEN RCF-FILE              
MJ1                      START D86 KEY NOT < D86-KEY
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     START D98 KEY NOT < D98-SPLIT-KEY
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     START D101 KEY NOT < D101-SPLIT-KEY
BB22                WHEN TAPER-RATE-FILE                  
BB22                     START D112 KEY NOT < D112-KEY
                    WHEN ASSET-USAGE-CALENDAR-FILE
                         call "AssetUsageCalendarDAL" using L-FILE-ACTION
                                                            L-FILE-RECORD-AREA
dtv001                   if L-FILE-RECORD-AREA = ""
dtv001                        move "10" to FILE-STATUS 
dtv001                   end-if                                   
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     START D153 KEY NOT < D153-KEY
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     START D154 KEY NOT < D154-KEY
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     START D163 KEY NOT < D163-KEY
BB27                WHEN PRICE-TYPES-FILE
BB27                     START D167 KEY NOT < D167-KEY
BB27                WHEN PENDING-LOG-FILE
BB27                     START D169 KEY NOT < D169-KEY
BB27                WHEN PENDING-ITEMS-FILE
BB27                     START D170 KEY NOT < D170-KEY
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       G-EXIT.
           EXIT.

      /
       H-START-GREATER-THAN SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         START D3 KEY > D3-KEY
                    WHEN FUND-FILE
                         START D4 KEY > D4-KEY
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         START D9 KEY > D9-KEY
                    WHEN MASTER-LOG-FILE
                         START D17 KEY > D17-KEY
                    WHEN REALISED-DATA-FILE
                         START D19 KEY > D19-SPLIT-KEY
                    WHEN UNREALISED-DATA-FILE
                         START D20 KEY > D20-SPLIT-KEY
                    WHEN PRINTER-FILE
                         START D31 KEY > D31-KEY
                    WHEN STOCK-TYPE-FILE
                         START D32 KEY > D32-KEY
                    WHEN TRANSACTION-CODE-FILE
                         START D34 KEY > D34-KEY
                    WHEN OUTPUT-LOG-FILE
                         START D35 KEY > D35-KEY
                    WHEN MESSAGE-FILE
                         START D36 KEY > D36-KEY
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         START D38 KEY > D38-KEY
                    WHEN DEFAULT-ACCESS-FILE
                         START D39 KEY > D39-KEY
                    WHEN ACCESS-PROFILE-FILE
                         START D40 KEY > D40-KEY
                    WHEN EXTEL-CURRENCY-FILE
                         START D42 KEY > D42-KEY
                    WHEN STOCK-PRICE-FILE
                         START D43 KEY > D43-KEY
                    WHEN SEQ-BALANCE-FILE
                         START D45 KEY > D45-KEY
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    START D79 KEY > D79-KEY
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    START D80 KEY > D80-KEY
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    START D81 KEY > D81-SPLIT-KEY
MJ1                 WHEN RCF-FILE             
MJ1                      START D86 KEY > D86-KEY
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     START D98 KEY > D98-SPLIT-KEY
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     START D101 KEY > D101-SPLIT-KEY
BB22                WHEN TAPER-RATE-FILE                  
BB22                     START D112 KEY > D112-KEY
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     START D153 KEY > D153-KEY
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     START D154 KEY > D154-KEY
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     START D163 KEY > D163-KEY
BB27                WHEN PRICE-TYPES-FILE
BB27                     START D167 KEY > D167-KEY
BB27                WHEN PENDING-LOG-FILE
BB27                     START D169 KEY > D169-KEY
BB27                WHEN PENDING-ITEMS-FILE
BB27                     START D170 KEY > D170-KEY
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       H-EXIT.
           EXIT.
      /
       I-START-GT-INVERSE-KEY SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         START D3 KEY > D3-INVERSE-KEY
                    WHEN FUND-FILE
                         START D4 KEY > D4-INVERSE-KEY
                    WHEN USER-FILE
                         START D9 KEY > D9-INVERSE-KEY
   BB4              WHEN PRINTER-FILE
   BB4                   START D31 KEY > D31-INVERSE-KEY
                    WHEN USER-FUND-FILE
                         CONTINUE
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     START D153 KEY > D153-INVERSE-KEY
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     START D163 KEY > D163-INVERSE-KEY
BB27                WHEN PRICE-TYPES-FILE
BB27                     START D167 KEY > D167-INVERSE-KEY
BB27                WHEN PENDING-LOG-FILE
BB27                     START D169 KEY > D169-INVERSE-KEY2
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       I-EXIT.
           EXIT.
      /
       J-START-LESS-THAN SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         START D3 KEY < D3-KEY
                    WHEN FUND-FILE
                         START D4 KEY < D4-KEY
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         START D9 KEY < D9-KEY
                    WHEN MASTER-LOG-FILE
                         START D17 KEY < D17-KEY
                    WHEN REALISED-DATA-FILE
                         START D19 KEY < D19-SPLIT-KEY
                    WHEN UNREALISED-DATA-FILE
                         START D20 KEY < D20-SPLIT-KEY
                    WHEN PRINTER-FILE
                         START D31 KEY < D31-KEY
                    WHEN STOCK-TYPE-FILE
                         START D32 KEY < D32-KEY
                    WHEN TRANSACTION-CODE-FILE
                         START D34 KEY < D34-KEY
                    WHEN OUTPUT-LOG-FILE
                         START D35 KEY < D35-KEY
                    WHEN MESSAGE-FILE
                         START D36 KEY < D36-KEY
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         START D38 KEY < D38-KEY
                    WHEN DEFAULT-ACCESS-FILE
                         START D39 KEY < D39-KEY
                    WHEN ACCESS-PROFILE-FILE
                         START D40 KEY < D40-KEY
                    WHEN EXTEL-CURRENCY-FILE
                         START D42 KEY < D42-KEY
                    WHEN STOCK-PRICE-FILE
                         START D43 KEY < D43-KEY
                    WHEN SEQ-BALANCE-FILE
                         START D45 KEY < D45-KEY
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    START D79 KEY < D79-KEY
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    START D80 KEY < D80-KEY
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    START D81 KEY < D81-SPLIT-KEY
MJ1                 WHEN RCF-FILE            
MJ1                      START D86 KEY < D86-KEY
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     START D98 KEY < D98-SPLIT-KEY
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     START D101 KEY < D101-SPLIT-KEY
BB22                WHEN TAPER-RATE-FILE                  
BB22                     START D112 KEY < D112-KEY
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     START D153 KEY < D153-KEY
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     START D154 KEY < D154-KEY
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     START D163 KEY < D163-KEY
BB27                WHEN PRICE-TYPES-FILE
BB27                     START D167 KEY < D167-KEY
BB27                WHEN PENDING-LOG-FILE
BB27                     START D169 KEY < D169-KEY
BB27                WHEN PENDING-ITEMS-FILE
BB27                     START D170 KEY < D170-KEY
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       J-EXIT.
           EXIT.
      /
       K-START-NOT-GREATER-THAN SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         START D3 KEY NOT > D3-KEY
                    WHEN FUND-FILE
                         START D4 KEY NOT > D4-KEY
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         START D9 KEY NOT > D9-KEY
                    WHEN MASTER-LOG-FILE
                         START D17 KEY NOT > D17-KEY
                    WHEN REALISED-DATA-FILE
                         START D19 KEY NOT > D19-SPLIT-KEY
                    WHEN UNREALISED-DATA-FILE
                         START D20 KEY NOT > D20-SPLIT-KEY
                    WHEN PRINTER-FILE
                         START D31 KEY NOT > D31-KEY
                    WHEN STOCK-TYPE-FILE
                         START D32 KEY NOT > D32-KEY
                    WHEN TRANSACTION-CODE-FILE
                         START D34 KEY NOT > D34-KEY
                    WHEN OUTPUT-LOG-FILE
                         START D35 KEY NOT > D35-KEY
                    WHEN MESSAGE-FILE
                         START D36 KEY NOT > D36-KEY
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         START D38 KEY NOT > D38-KEY
                    WHEN DEFAULT-ACCESS-FILE
                         START D39 KEY NOT > D39-KEY
                    WHEN ACCESS-PROFILE-FILE
                         START D40 KEY NOT > D40-KEY
                    WHEN EXTEL-CURRENCY-FILE
                         START D42 KEY NOT > D42-KEY
                    WHEN STOCK-PRICE-FILE
                         START D43 KEY NOT > D43-KEY
                    WHEN SEQ-BALANCE-FILE
                         START D45 KEY NOT > D45-KEY
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    START D79 KEY NOT > D79-KEY
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    START D80 KEY NOT > D80-KEY
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    START D81 KEY NOT > D81-SPLIT-KEY
MJ1                 WHEN RCF-FILE              
MJ1                      START D86 KEY NOT > D86-KEY
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     START D98 KEY NOT > D98-SPLIT-KEY
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     START D101 KEY NOT > D101-SPLIT-KEY
BB22                WHEN TAPER-RATE-FILE                  
BB22                     START D112 KEY NOT > D112-KEY
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     START D153 KEY NOT > D153-KEY
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     START D154 KEY NOT > D154-KEY
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     START D163 KEY NOT > D163-KEY
BB27                WHEN PRICE-TYPES-FILE
BB27                     START D167 KEY NOT > D167-KEY
BB27                WHEN PENDING-LOG-FILE
BB27                     START D169 KEY NOT > D169-KEY
BB27                WHEN PENDING-ITEMS-FILE
BB27                     START D170 KEY NOT > D170-KEY
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       K-EXIT.
           EXIT.
      /
       L-READ-NEXT SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         call "COUNTRYDAL" using L-FILE-ACTION
                                                 L-FILE-RECORD-AREA
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN GROUP-FILE
                         call "GroupDAL" using L-FILE-ACTION
                                               L-FILE-RECORD-AREA
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN STOCK-FILE
                         READ D3  NEXT INTO L-FILE-RECORD-AREA
                    WHEN FUND-FILE
                         READ D4  NEXT INTO L-FILE-RECORD-AREA
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      READ D5  NEXT INTO L-FILE-RECORD-AREA
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      READ D6  NEXT INTO L-FILE-RECORD-AREA
                    WHEN RPI-FILE
                         call "RPIDAL" using L-FILE-ACTION
                                             L-FILE-RECORD-AREA
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN PARAMETER-FILE
                         call "ParametersDAL" using L-FILE-ACTION
                                                    L-FILE-RECORD-AREA
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN USER-FILE
                         READ D9  NEXT INTO L-FILE-RECORD-AREA
                    WHEN STERLING-EXTEL-REPORT
                         READ D10 NEXT INTO L-FILE-RECORD-AREA
                    WHEN FOREIGN-EXTEL-REPORT
                         READ D11 NEXT INTO L-FILE-RECORD-AREA
                    WHEN MASTER-LOG-FILE
                         READ D17 NEXT INTO L-FILE-RECORD-AREA
                    WHEN ERROR-REPORT-FILE
                         READ D18 NEXT INTO L-FILE-RECORD-AREA
                    WHEN REALISED-DATA-FILE
                         READ D19 NEXT INTO L-FILE-RECORD-AREA
                    WHEN UNREALISED-DATA-FILE
                         READ D20 NEXT INTO L-FILE-RECORD-AREA
                    WHEN REALISED-SCHEDULE-FILE
                         READ D21 NEXT INTO L-FILE-RECORD-AREA
                    WHEN UNREALISED-SCHEDULE-FILE
                         READ D22 NEXT INTO L-FILE-RECORD-AREA
                    WHEN CG01-REPORT-FILE
                         READ D23 NEXT INTO L-FILE-RECORD-AREA
                    WHEN CG02-REPORT-FILE
                         READ D24 NEXT INTO L-FILE-RECORD-AREA
                    WHEN CG03-REPORT-FILE
                         READ D25 NEXT INTO L-FILE-RECORD-AREA
                    WHEN YE-REC-REPORT-FILE
                         READ D26 NEXT INTO L-FILE-RECORD-AREA
                    WHEN YE-DEL-REPORT-FILE
                         READ D27 NEXT INTO L-FILE-RECORD-AREA
                    WHEN YE-CON-REPORT-FILE
                         READ D28 NEXT INTO L-FILE-RECORD-AREA
                    WHEN ERROR-DATA-FILE
                         READ D29 NEXT INTO L-FILE-RECORD-AREA
                    WHEN YE-ERR-REPORT-FILE
                         READ D30 NEXT INTO L-FILE-RECORD-AREA
                    WHEN PRINTER-FILE
                         READ D31 NEXT INTO L-FILE-RECORD-AREA
                    WHEN STOCK-TYPE-FILE
                         READ D32 NEXT INTO L-FILE-RECORD-AREA
                    WHEN YE-REC2-DATA-FILE
                         READ D33 NEXT INTO L-FILE-RECORD-AREA
                    WHEN TRANSACTION-CODE-FILE
                         READ D34 NEXT INTO L-FILE-RECORD-AREA
                    WHEN OUTPUT-LOG-FILE
                         READ D35 NEXT INTO L-FILE-RECORD-AREA
                    WHEN MESSAGE-FILE
                         READ D36 NEXT INTO L-FILE-RECORD-AREA
                    WHEN USER-FUND-FILE
                         call "UserFundDAL" using L-FILE-ACTION
                                                  L-FILE-RECORD-AREA
                                                  L-USER-NO
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN HELP-TEXT-FILE
                         READ D38 NEXT INTO L-FILE-RECORD-AREA
                    WHEN DEFAULT-ACCESS-FILE
                         READ D39 NEXT INTO L-FILE-RECORD-AREA
                    WHEN ACCESS-PROFILE-FILE
                         READ D40 NEXT INTO L-FILE-RECORD-AREA
                    WHEN EXTEL-PRICES-FILE
                         READ D41 NEXT INTO L-FILE-RECORD-AREA
                    WHEN EXTEL-CURRENCY-FILE
                         READ D42 NEXT INTO L-FILE-RECORD-AREA
                    WHEN STOCK-PRICE-FILE
                         READ D43 NEXT INTO L-FILE-RECORD-AREA
                    WHEN EXTEL-TRANSMISSION-FILE
                         READ D44 NEXT INTO L-FILE-RECORD-AREA
                    WHEN SEQ-BALANCE-FILE
                         READ D45 NEXT INTO L-FILE-RECORD-AREA
BB2                 WHEN TRANSACTION-FILE
BB2                      READ D46 NEXT INTO L-FILE-RECORD-AREA
  BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   READ D49 NEXT INTO L-FILE-RECORD-AREA
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   READ D50 NEXT INTO L-FILE-RECORD-AREA
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   READ D51 NEXT INTO L-FILE-RECORD-AREA
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      READ D68 NEXT INTO L-FILE-RECORD-AREA
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      READ D69 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     READ D70 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     READ D71 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN SKAN1-REPORT
 BB8                     READ D72 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN SKAN2-REPORT
 BB8                     READ D73 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     READ D74 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     READ D75 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     READ D76 NEXT INTO L-FILE-RECORD-AREA
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     READ D77 NEXT INTO L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    READ D78 NEXT INTO L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    READ D79 NEXT INTO L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    READ D80 NEXT INTO L-FILE-RECORD-AREA
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    READ D81 NEXT INTO L-FILE-RECORD-AREA
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    READ D82 NEXT INTO L-FILE-RECORD-AREA
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    READ D83 NEXT INTO L-FILE-RECORD-AREA
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    READ D84 NEXT INTO L-FILE-RECORD-AREA
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      READ D85 NEXT INTO L-FILE-RECORD-AREA
MJ1                 WHEN RCF-FILE
MJ1                      READ D86 NEXT INTO L-FILE-RECORD-AREA
DGD                 WHEN RCF-BACKUP-FILE
DGD                      READ D87 NEXT INTO L-FILE-RECORD-AREA
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    READ D88 NEXT INTO L-FILE-RECORD-AREA
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    READ D89 NEXT INTO L-FILE-RECORD-AREA
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    READ D90 NEXT INTO L-FILE-RECORD-AREA
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    READ D91 NEXT INTO L-FILE-RECORD-AREA
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    READ D92 NEXT INTO L-FILE-RECORD-AREA
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    READ D93 NEXT INTO L-FILE-RECORD-AREA
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   READ D94 NEXT INTO L-FILE-RECORD-AREA
  BB12              WHEN GAINLOSS-REPORT
  BB12                   READ D95 NEXT INTO L-FILE-RECORD-AREA
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     READ D98 NEXT INTO L-FILE-RECORD-AREA
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     READ D101 NEXT INTO L-FILE-RECORD-AREA
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    READ D107 NEXT INTO L-FILE-RECORD-AREA
 BB21               WHEN TRACE-FILE
 BB21                    READ D108 NEXT INTO L-FILE-RECORD-AREA
 BB21               WHEN ERROR-LOG-FILE
 BB21                    READ D109 NEXT INTO L-FILE-RECORD-AREA
BB22                WHEN TAPER-RATE-FILE                  
                         call "TaperRateDAL" using L-FILE-ACTION
                                                   L-FILE-RECORD-AREA
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN ASSET-USAGE-CALENDAR-FILE
dtv001                   call "AssetUsageCalendarDAL" using L-FILE-ACTION
dtv001                                                      L-FILE-RECORD-AREA 
dtv001                   if L-FILE-RECORD-AREA = ""
dtv001                        move "10" to FILE-STATUS 
dtv001                   end-if                                    
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     READ D153 NEXT INTO L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     READ D154 NEXT INTO L-FILE-RECORD-AREA
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     READ D163 NEXT INTO L-FILE-RECORD-AREA
BB25                WHEN DISPOSALS-FROM-DB-FILE
BB25                     MOVE SPACES TO D161-RECORD
BB25                     MOVE ZERO   TO FILE-STATUS
BB25                     PERFORM UNTIL D161-RECORD  EQUAL X'0D'
BB25                                OR FILE-STATUS EQUAL  '10'
BB25                        READ D161

BB25                        EVALUATE D161-RECORD-BYTE
BB25                        WHEN X'0D'
BB25                           MOVE W-D161-RECORD TO L-FILE-RECORD-AREA
BB25                           MOVE    1   TO W-D161-CHAR-SUB
BB25                           MOVE SPACES TO W-D161-RECORD
BB25                        WHEN OTHER
BB25                           IF (D161-RECORD-BYTE = X'0A' OR X'0C')
BB25                              CONTINUE
BB25                           ELSE
BB25                              MOVE D161-RECORD TO 
BB25                                       W-D161-CHAR(W-D161-CHAR-SUB)
BB25                              ADD 1 TO W-D161-CHAR-SUB
BB25                           END-IF
BB25                        END-EVALUATE
BB25                     END-PERFORM
BB27                WHEN PRICE-TYPES-FILE
BB27                     READ D167 NEXT INTO L-FILE-RECORD-AREA
BB27                WHEN PENDING-LOG-FILE
BB27                     READ D169 NEXT INTO L-FILE-RECORD-AREA
BB27                WHEN PENDING-ITEMS-FILE
BB27                     READ D170 NEXT INTO L-FILE-RECORD-AREA
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       L-EXIT.
           EXIT.
      /
       M-READ-NEXT-WITH-LOCK SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         READ D3  NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN FUND-FILE
                         READ D4  NEXT INTO L-FILE-RECORD-AREA
 BB3                              WITH KEPT LOCK
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      READ D5  NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      READ D6  NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         READ D9  NEXT INTO L-FILE-RECORD-AREA
 BB3                              WITH KEPT LOCK
                    WHEN MASTER-LOG-FILE
                         READ D17 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN REALISED-DATA-FILE
                         READ D19 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN UNREALISED-DATA-FILE
                         READ D20 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN PRINTER-FILE
                         READ D31 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN STOCK-TYPE-FILE
                         READ D32 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN TRANSACTION-CODE-FILE
                         READ D34 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN OUTPUT-LOG-FILE
                         READ D35 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN MESSAGE-FILE
                         READ D36 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         READ D38 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN DEFAULT-ACCESS-FILE
                         READ D39 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN ACCESS-PROFILE-FILE
                         READ D40 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN EXTEL-CURRENCY-FILE
                         READ D42 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN STOCK-PRICE-FILE
                         READ D43 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN SEQ-BALANCE-FILE
                         READ D45 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB2                 WHEN TRANSACTION-FILE
BB2                      READ D46 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   READ D49 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   READ D50 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   READ D51 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      READ D68 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      READ D69 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     READ D70 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     READ D71 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN SKAN1-REPORT
 BB8                     READ D72 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN SKAN2-REPORT
 BB8                     READ D73 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     READ D74 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     READ D75 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     READ D76 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     READ D77 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    READ D78 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    READ D79 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    READ D80 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    READ D81 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    READ D82 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    READ D83 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    READ D84 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
MJ1                 WHEN RCF-FILE             
MJ1                      READ D86 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
DGD                 WHEN RCF-BACKUP-FILE
DGD                      READ D87 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    READ D88 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    READ D89 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    READ D90 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    READ D91 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    READ D92 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    READ D93 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   READ D94 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
  BB12              WHEN GAINLOSS-REPORT
  BB12                   READ D95 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     READ D98 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                    READ D101 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue         
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                   READ D107 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB21               WHEN TRACE-FILE
 BB21                   READ D108 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
 BB21               WHEN ERROR-LOG-FILE
 BB21                   READ D109 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB22                WHEN TAPER-RATE-FILE                  
BB22                    READ D112 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                    READ D153 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                    READ D154 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                    READ D163 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB27                WHEN PRICE-TYPES-FILE
BB27                    READ D167 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB27                WHEN PENDING-LOG-FILE
BB27                    READ D169 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
BB27                WHEN PENDING-ITEMS-FILE
BB27                    READ D170 NEXT INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       M-EXIT.
           EXIT.
      /
       N-READ SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         READ D3  INTO L-FILE-RECORD-AREA
                    WHEN FUND-FILE
                         READ D4  INTO L-FILE-RECORD-AREA
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      READ D5  INTO L-FILE-RECORD-AREA
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      READ D6  INTO L-FILE-RECORD-AREA
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         call "ParametersDAL" using L-FILE-ACTION
                                                    L-FILE-RECORD-AREA
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN USER-FILE
                         READ D9  INTO L-FILE-RECORD-AREA
                    WHEN STERLING-EXTEL-REPORT
                         READ D10 INTO L-FILE-RECORD-AREA
                    WHEN FOREIGN-EXTEL-REPORT
                         READ D11 INTO L-FILE-RECORD-AREA
                    WHEN OUTPUT-LISTING
                         READ D12 INTO L-FILE-RECORD-AREA
                    WHEN MASTER-LOG-FILE
                         READ D17 INTO L-FILE-RECORD-AREA
                    WHEN ERROR-REPORT-FILE
                         READ D18 INTO L-FILE-RECORD-AREA
                    WHEN REALISED-DATA-FILE
                         READ D19 INTO L-FILE-RECORD-AREA
                    WHEN UNREALISED-DATA-FILE
                         READ D20 INTO L-FILE-RECORD-AREA
                    WHEN REALISED-SCHEDULE-FILE
                         READ D21 INTO L-FILE-RECORD-AREA
                    WHEN UNREALISED-SCHEDULE-FILE
                         READ D22 INTO L-FILE-RECORD-AREA
                    WHEN CG01-REPORT-FILE
                         READ D23 INTO L-FILE-RECORD-AREA
                    WHEN CG02-REPORT-FILE
                         READ D24 INTO L-FILE-RECORD-AREA
                    WHEN CG03-REPORT-FILE
                         READ D25 INTO L-FILE-RECORD-AREA
                    WHEN YE-REC-REPORT-FILE
                         READ D26 INTO L-FILE-RECORD-AREA
                    WHEN YE-DEL-REPORT-FILE
                         READ D27 INTO L-FILE-RECORD-AREA
                    WHEN YE-CON-REPORT-FILE
                         READ D28 INTO L-FILE-RECORD-AREA
                    WHEN ERROR-DATA-FILE
                         READ D29 INTO L-FILE-RECORD-AREA
                    WHEN YE-ERR-REPORT-FILE
                         READ D30 INTO L-FILE-RECORD-AREA
                    WHEN PRINTER-FILE
                         READ D31 INTO L-FILE-RECORD-AREA
                    WHEN STOCK-TYPE-FILE
                         READ D32 INTO L-FILE-RECORD-AREA
                    WHEN YE-REC2-DATA-FILE
                         READ D33 INTO L-FILE-RECORD-AREA
                    WHEN TRANSACTION-CODE-FILE
                         READ D34 INTO L-FILE-RECORD-AREA
                    WHEN OUTPUT-LOG-FILE
                         READ D35 INTO L-FILE-RECORD-AREA
                    WHEN MESSAGE-FILE
                         READ D36 INTO L-FILE-RECORD-AREA
                    WHEN USER-FUND-FILE
                         call "UserFundDAL" using L-FILE-ACTION
                                                  L-FILE-RECORD-AREA
                                                  L-USER-NO
                         if L-FILE-RECORD-AREA = ""
                              move "10" to FILE-STATUS 
                         end-if
                    WHEN HELP-TEXT-FILE
                         READ D38 INTO L-FILE-RECORD-AREA
                    WHEN DEFAULT-ACCESS-FILE
                         READ D39 INTO L-FILE-RECORD-AREA
                    WHEN ACCESS-PROFILE-FILE
                         READ D40 INTO L-FILE-RECORD-AREA
                    WHEN EXTEL-PRICES-FILE
                         READ D41 INTO L-FILE-RECORD-AREA
                    WHEN EXTEL-CURRENCY-FILE
                         READ D42 INTO L-FILE-RECORD-AREA
                    WHEN STOCK-PRICE-FILE
                         READ D43 INTO L-FILE-RECORD-AREA
                    WHEN EXTEL-TRANSMISSION-FILE
                         READ D44 INTO L-FILE-RECORD-AREA
                    WHEN SEQ-BALANCE-FILE
                         READ D45 INTO L-FILE-RECORD-AREA
BB2                 WHEN TRANSACTION-FILE
BB2                      READ D46 INTO L-FILE-RECORD-AREA
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   READ D49 INTO L-FILE-RECORD-AREA
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   READ D50 INTO L-FILE-RECORD-AREA
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   READ D51 INTO L-FILE-RECORD-AREA
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      READ D68 INTO L-FILE-RECORD-AREA
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      READ D69 INTO L-FILE-RECORD-AREA
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     READ D70 INTO L-FILE-RECORD-AREA
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     READ D71 INTO L-FILE-RECORD-AREA
 BB8                WHEN SKAN1-REPORT
 BB8                     READ D72 INTO L-FILE-RECORD-AREA
 BB8                WHEN SKAN2-REPORT
 BB8                     READ D73 INTO L-FILE-RECORD-AREA
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     READ D74 INTO L-FILE-RECORD-AREA
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     READ D75 INTO L-FILE-RECORD-AREA
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     READ D76 INTO L-FILE-RECORD-AREA
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     READ D77 INTO L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    READ D78 INTO L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    READ D79 INTO L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    READ D80 INTO L-FILE-RECORD-AREA
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    READ D81 INTO L-FILE-RECORD-AREA
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    READ D82 INTO L-FILE-RECORD-AREA
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    READ D83 INTO L-FILE-RECORD-AREA
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    READ D84 INTO L-FILE-RECORD-AREA
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      READ D85 INTO L-FILE-RECORD-AREA
MJ1                 WHEN RCF-FILE
MJ1                      READ D86 INTO L-FILE-RECORD-AREA
DGD                 WHEN RCF-BACKUP-FILE
DGD                      READ D87 INTO L-FILE-RECORD-AREA
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    READ D88 INTO L-FILE-RECORD-AREA
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    READ D89 INTO L-FILE-RECORD-AREA
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    READ D90 INTO L-FILE-RECORD-AREA
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    READ D91 INTO L-FILE-RECORD-AREA
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    READ D92 INTO L-FILE-RECORD-AREA
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    READ D93 INTO L-FILE-RECORD-AREA
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   READ D94 INTO L-FILE-RECORD-AREA
  BB12              WHEN GAINLOSS-REPORT
  BB12                   READ D95 INTO L-FILE-RECORD-AREA
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     READ D98 INTO L-FILE-RECORD-AREA
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     READ D101 INTO L-FILE-RECORD-AREA
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    READ D107 INTO L-FILE-RECORD-AREA
 BB21               WHEN TRACE-FILE
 BB21                    READ D108 INTO L-FILE-RECORD-AREA
 BB21               WHEN ERROR-LOG-FILE
 BB21                    READ D109 INTO L-FILE-RECORD-AREA
BB22                WHEN TAPER-RATE-FILE                  
BB22                     READ D112 INTO L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     READ D153 INTO L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     READ D154 INTO L-FILE-RECORD-AREA
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     READ D163 INTO L-FILE-RECORD-AREA
BB27                WHEN PRICE-TYPES-FILE
BB27                     READ D167 INTO L-FILE-RECORD-AREA
BB27                WHEN PENDING-LOG-FILE
BB27                     READ D169 INTO L-FILE-RECORD-AREA
BB27                WHEN PENDING-ITEMS-FILE
BB27                     READ D170 INTO L-FILE-RECORD-AREA
                    WHEN OTHER
                          MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       N-EXIT.
           EXIT.
      /
       O-READ-WITH-LOCK SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         READ D3  INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN FUND-FILE
 BB3                     READ D4  INTO L-FILE-RECORD-AREA WITH KEPT LOCK
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      READ D5  INTO L-FILE-RECORD-AREA WITH LOCK
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      READ D6  INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
 BB3                     READ D9  INTO L-FILE-RECORD-AREA WITH KEPT LOCK
                    WHEN STERLING-EXTEL-REPORT
                         READ D10 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN FOREIGN-EXTEL-REPORT
                         READ D11 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN MASTER-LOG-FILE
                         READ D17 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN ERROR-REPORT-FILE
                         READ D18 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN REALISED-DATA-FILE
                         READ D19 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN UNREALISED-DATA-FILE
                         READ D20 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN REALISED-SCHEDULE-FILE
                         READ D21 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN UNREALISED-SCHEDULE-FILE
                         READ D22 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN CG01-REPORT-FILE
                         READ D23 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN CG02-REPORT-FILE
                         READ D24 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN CG03-REPORT-FILE
                         READ D25 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN YE-REC-REPORT-FILE
                         READ D26 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN YE-DEL-REPORT-FILE
                         READ D27 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN YE-CON-REPORT-FILE
                         READ D28 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN ERROR-DATA-FILE
                         READ D29 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN YE-ERR-REPORT-FILE
                         READ D30 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN PRINTER-FILE
                         READ D31 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN STOCK-TYPE-FILE
                         READ D32 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN YE-REC2-DATA-FILE
                         READ D33 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN TRANSACTION-CODE-FILE
                         READ D34 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN OUTPUT-LOG-FILE
                         READ D35 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN MESSAGE-FILE
                         READ D36 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         READ D38 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN DEFAULT-ACCESS-FILE
                         READ D39 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN ACCESS-PROFILE-FILE
                         READ D40 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN EXTEL-PRICES-FILE
                         READ D41 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN EXTEL-CURRENCY-FILE
                         READ D42 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN STOCK-PRICE-FILE
                         READ D43 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN EXTEL-TRANSMISSION-FILE
                         READ D44 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN SEQ-BALANCE-FILE
                         READ D45 INTO L-FILE-RECORD-AREA WITH LOCK
BB2                 WHEN TRANSACTION-FILE
BB2                      READ D46 INTO L-FILE-RECORD-AREA WITH LOCK
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   READ D49 INTO L-FILE-RECORD-AREA WITH LOCK
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   READ D50 INTO L-FILE-RECORD-AREA WITH LOCK
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   READ D51 INTO L-FILE-RECORD-AREA WITH LOCK
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      READ D68 INTO L-FILE-RECORD-AREA WITH LOCK
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      READ D69 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     READ D70 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     READ D71 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN SKAN1-REPORT
 BB8                     READ D72 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN SKAN2-REPORT
 BB8                     READ D73 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     READ D74 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     READ D75 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     READ D76 INTO L-FILE-RECORD-AREA WITH LOCK
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     READ D77 INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    READ D78 INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    READ D79 INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    READ D80 INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    READ D81 INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    READ D82 INTO L-FILE-RECORD-AREA WITH LOCK
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    READ D83 INTO L-FILE-RECORD-AREA WITH LOCK
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    READ D84 INTO L-FILE-RECORD-AREA WITH LOCK
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      READ D85 INTO L-FILE-RECORD-AREA WITH LOCK
MJ1                 WHEN RCF-FILE
MJ1                      READ D86 INTO L-FILE-RECORD-AREA WITH LOCK
DGD                 WHEN RCF-BACKUP-FILE
DGD                      READ D87 INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    READ D88 INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    READ D89 INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    READ D90 INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    READ D91 INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    READ D92 INTO L-FILE-RECORD-AREA WITH LOCK
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    READ D93 INTO L-FILE-RECORD-AREA WITH LOCK
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   READ D94 INTO L-FILE-RECORD-AREA WITH LOCK
  BB12              WHEN GAINLOSS-REPORT
  BB12                   READ D95 INTO L-FILE-RECORD-AREA WITH LOCK
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     READ D98 INTO L-FILE-RECORD-AREA WITH LOCK
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                    READ D101 INTO L-FILE-RECORD-AREA WITH LOCK
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                   READ D107 INTO L-FILE-RECORD-AREA WITH LOCK
 BB21               WHEN TRACE-FILE
 BB21                   READ D108 INTO L-FILE-RECORD-AREA WITH LOCK
 BB21               WHEN ERROR-LOG-FILE
 BB21                   READ D109 INTO L-FILE-RECORD-AREA WITH LOCK
BB22                WHEN TAPER-RATE-FILE                  
BB22                    READ D112 INTO L-FILE-RECORD-AREA WITH LOCK
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                    READ D153 INTO L-FILE-RECORD-AREA WITH LOCK
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                    READ D154 INTO L-FILE-RECORD-AREA WITH LOCK
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                    READ D163 INTO L-FILE-RECORD-AREA WITH LOCK
BB27                WHEN PRICE-TYPES-FILE
BB27                    READ D167 INTO L-FILE-RECORD-AREA WITH LOCK
BB27                WHEN PENDING-LOG-FILE
BB27                    READ D169 INTO L-FILE-RECORD-AREA WITH LOCK
BB27                WHEN PENDING-ITEMS-FILE
BB27                    READ D170 INTO L-FILE-RECORD-AREA WITH LOCK
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       O-EXIT.
           EXIT.
      /
       P-READ-ON-KEY2 SECTION.

           EVALUATE L-FILE-NAME
                    WHEN FUND-FILE
                         READ D4  INTO L-FILE-RECORD-AREA
                         KEY IS D4-KEY2
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       P-EXIT.
           EXIT.
      /
EJ1    PA-READ-ON-KEY3 SECTION.
 "
 "         EVALUATE L-FILE-NAME
 "                  WHEN FUND-FILE
 "                       READ D4  INTO L-FILE-RECORD-AREA
 "                       KEY IS D4-KEY3
 "                  WHEN OTHER
 "                       MOVE 'IF' TO FILE-STATUS
 "         END-EVALUATE.
 "     PA-EXIT.
 "         EXIT.
      /
       Q-WRITE SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         MOVE D3-KEY TO L-KEY
                         CALL 'CGTINVRT' USING L-KEY
                         MOVE L-KEY TO D3-INVERSE-KEY
                         WRITE D3-RECORD
                         MOVE D3-RECORD TO L-FILE-RECORD-AREA
                    WHEN FUND-FILE
                         MOVE D4-KEY TO L-KEY
                         CALL 'CGTINVRT' USING L-KEY
                         MOVE L-KEY TO D4-INVERSE-KEY
                         WRITE D4-RECORD
                         MOVE D4-RECORD TO L-FILE-RECORD-AREA
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      WRITE D5-RECORD FROM L-FILE-RECORD-AREA
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      WRITE D6-RECORD FROM L-FILE-RECORD-AREA
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN STERLING-EXTEL-REPORT
                         WRITE D10-RECORD FROM L-FILE-RECORD-AREA
                    WHEN FOREIGN-EXTEL-REPORT
                         WRITE D11-RECORD FROM L-FILE-RECORD-AREA
                    WHEN OUTPUT-LISTING
                         WRITE D12-RECORD FROM L-FILE-RECORD-AREA
                    WHEN MASTER-LOG-FILE
                         WRITE D17-RECORD FROM L-FILE-RECORD-AREA
                    WHEN ERROR-REPORT-FILE
                         WRITE D18-RECORD FROM L-FILE-RECORD-AREA
                    WHEN REALISED-DATA-FILE
                         MOVE  L-FILE-RECORD-AREA TO D19-RECORD
                         ADD   1                  TO W-D19-COUNT
                         MOVE  W-D19-COUNT        TO D19-SEQUENCE-NUMBER
      *                 set acquisition date century for use in key
                         if D19-ACQUISITION-DATE-YY > 44
                             move '19' to D19-ACQUISITION-DATE-CC
                         else
                             move '20' to D19-ACQUISITION-DATE-CC
                         end-if
                         if D19-TAPER-DATE-YY > 44
                             move '19' to D19-TAPER-DATE-CC
                         else
                             move '20' to D19-TAPER-DATE-CC
                         end-if
						 string CGTFILES-LINKAGE(1:11) 'write' delimited by size into timing-message-suffix
                         WRITE D19-RECORD
                    WHEN UNREALISED-DATA-FILE
                         MOVE  L-FILE-RECORD-AREA TO D20-RECORD
                         ADD   1                  TO W-D20-COUNT
                         MOVE  W-D20-COUNT        TO D20-SEQUENCE-NUMBER
      *                 set acquisition date century for use in key
                         if D20-ACQUISITION-DATE-YY > 44
                             move '19' to D20-ACQUISITION-DATE-CC
                         else
                             move '20' to D20-ACQUISITION-DATE-CC
                         end-if
                         if D20-TAPER-DATE-YY > 44
                             move '19' to D20-TAPER-DATE-CC
                         else
                             move '20' to D20-TAPER-DATE-CC
                         end-if
						 string CGTFILES-LINKAGE(1:11) 'write' delimited by size into timing-message-suffix
                         WRITE D20-RECORD
                    WHEN REALISED-SCHEDULE-FILE
                         WRITE D21-RECORD FROM L-FILE-RECORD-AREA
                    WHEN UNREALISED-SCHEDULE-FILE
                         WRITE D22-RECORD FROM L-FILE-RECORD-AREA
                    WHEN CG01-REPORT-FILE
                         WRITE D23-RECORD FROM L-FILE-RECORD-AREA
                    WHEN CG02-REPORT-FILE
                         WRITE D24-RECORD FROM L-FILE-RECORD-AREA
                    WHEN CG03-REPORT-FILE
                         WRITE D25-RECORD FROM L-FILE-RECORD-AREA
                    WHEN YE-REC-REPORT-FILE
                         WRITE D26-RECORD FROM L-FILE-RECORD-AREA
                    WHEN YE-DEL-REPORT-FILE
                         WRITE D27-RECORD FROM L-FILE-RECORD-AREA
                    WHEN YE-CON-REPORT-FILE
                         WRITE D28-RECORD FROM L-FILE-RECORD-AREA
                    WHEN ERROR-DATA-FILE
                         WRITE D29-RECORD FROM L-FILE-RECORD-AREA
                    WHEN YE-ERR-REPORT-FILE
                         WRITE D30-RECORD FROM L-FILE-RECORD-AREA
                    WHEN PRINTER-FILE
   BB4                   MOVE D31-KEY TO L-KEY
   BB4                   CALL 'CGTINVRT' USING L-KEY
   BB4                   MOVE L-KEY TO D31-INVERSE-KEY
   BB4                   WRITE D31-RECORD
   BB4                   MOVE D31-RECORD TO L-FILE-RECORD-AREA
                    WHEN STOCK-TYPE-FILE
                         WRITE D32-RECORD FROM L-FILE-RECORD-AREA
                    WHEN YE-REC2-DATA-FILE
                         WRITE D33-RECORD FROM L-FILE-RECORD-AREA
                    WHEN TRANSACTION-CODE-FILE
                         WRITE D34-RECORD FROM L-FILE-RECORD-AREA
                    WHEN OUTPUT-LOG-FILE
                         WRITE D35-RECORD FROM L-FILE-RECORD-AREA
                    WHEN MESSAGE-FILE
                         WRITE D36-RECORD FROM L-FILE-RECORD-AREA
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         WRITE D38-RECORD FROM L-FILE-RECORD-AREA
                    WHEN DEFAULT-ACCESS-FILE
                         WRITE D39-RECORD FROM L-FILE-RECORD-AREA
                    WHEN ACCESS-PROFILE-FILE
                         WRITE D40-RECORD FROM L-FILE-RECORD-AREA
                    WHEN EXTEL-PRICES-FILE
                         WRITE D41-RECORD FROM L-FILE-RECORD-AREA
                    WHEN EXTEL-CURRENCY-FILE
                         WRITE D42-RECORD FROM L-FILE-RECORD-AREA
                    WHEN STOCK-PRICE-FILE
                         WRITE D43-RECORD FROM L-FILE-RECORD-AREA
                    WHEN EXTEL-TRANSMISSION-FILE
                         WRITE D44-RECORD FROM L-FILE-RECORD-AREA
                    WHEN SEQ-BALANCE-FILE
BB7                      EVALUATE D45-1-RECORD-CODE
BB7                      WHEN '01'
DGD1                           MOVE   385 TO REC-LENGTH
BB7                      WHEN '02'
                               COMPUTE REC-LENGTH = 342 + (D45-2-NO-OF-COSTS-HELD-YTD * 80)
BB7                      WHEN '03'
BB7                            MOVE   319 TO REC-LENGTH
BB7                      WHEN  OTHER
BB7                            MOVE 16342 TO REC-LENGTH
BB7                      END-EVALUATE
                         WRITE D45-RECORD FROM L-FILE-RECORD-AREA
BB2                 WHEN TRANSACTION-FILE
BB2                      WRITE D46-RECORD FROM L-FILE-RECORD-AREA
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   WRITE D49-RECORD FROM L-FILE-RECORD-AREA
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   WRITE D50-RECORD FROM L-FILE-RECORD-AREA
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   WRITE D51-RECORD FROM L-FILE-RECORD-AREA
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      WRITE D68-RECORD FROM L-FILE-RECORD-AREA
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      WRITE D69-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     WRITE D70-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     WRITE D71-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN SKAN1-REPORT
 BB8                     WRITE D72-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN SKAN2-REPORT
 BB8                     WRITE D73-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     WRITE D74-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     WRITE D75-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     WRITE D76-RECORD FROM L-FILE-RECORD-AREA
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     WRITE D77-RECORD FROM L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    WRITE D78-RECORD FROM L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    WRITE D79-RECORD FROM L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    WRITE D80-RECORD FROM L-FILE-RECORD-AREA
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    MOVE  L-FILE-RECORD-AREA TO D81-RECORD
  BB9                    ADD   1                  TO W-D81-COUNT
  BB9                    MOVE  W-D81-COUNT        TO D81-SEQUENCE-NUMBER
      *                 set acquisition date century for use in key
                         if D81-ACQUISITION-DATE-YY > 44
                             move '19' to D81-ACQUISITION-DATE-CC
                         else
                             move '20' to D81-ACQUISITION-DATE-CC
                         end-if
                         if D81-TAPER-DATE-YY > 44
                             move '19' to D81-TAPER-DATE-CC
                         else
                             move '20' to D81-TAPER-DATE-CC
                         end-if
						 string CGTFILES-LINKAGE(1:11) 'write' delimited by size into timing-message-suffix
  BB9                    WRITE D81-RECORD
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    WRITE D82-RECORD FROM L-FILE-RECORD-AREA
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    WRITE D83-RECORD FROM L-FILE-RECORD-AREA
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    WRITE D84-RECORD FROM L-FILE-RECORD-AREA
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      WRITE D85-RECORD FROM L-FILE-RECORD-AREA
MJ1                 WHEN RCF-FILE
MJ1                      WRITE D86-RECORD FROM L-FILE-RECORD-AREA
DGD                 WHEN RCF-BACKUP-FILE
DGD                      WRITE D87-RECORD FROM L-FILE-RECORD-AREA
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    WRITE D88-RECORD FROM L-FILE-RECORD-AREA
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    WRITE D89-RECORD FROM L-FILE-RECORD-AREA
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    WRITE D90-RECORD FROM L-FILE-RECORD-AREA
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    WRITE D91-RECORD FROM L-FILE-RECORD-AREA
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    WRITE D92-RECORD FROM L-FILE-RECORD-AREA
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    WRITE D93-RECORD FROM L-FILE-RECORD-AREA
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   WRITE D94-RECORD FROM L-FILE-RECORD-AREA
  BB12              WHEN GAINLOSS-REPORT
  BB12                   WRITE D95-RECORD FROM L-FILE-RECORD-AREA
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     MOVE  L-FILE-RECORD-AREA TO D98-RECORD
BB17                     ADD   1                  TO W-D98-COUNT
BB17                     MOVE  W-D98-COUNT        TO D98-SEQUENCE-NUMBER
      *                 set acquisition date century for use in key
                         if D98-ACQUISITION-DATE-YY > 44
                             move '19' to D98-ACQUISITION-DATE-CC
                         else
                             move '20' to D98-ACQUISITION-DATE-CC
                         end-if
                         if D98-TAPER-DATE-YY > 44
                             move '19' to D98-TAPER-DATE-CC
                         else
                             move '20' to D98-TAPER-DATE-CC
                         end-if
						 string CGTFILES-LINKAGE(1:11) 'write' delimited by size into timing-message-suffix
BB17                     WRITE D98-RECORD
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     MOVE  L-FILE-RECORD-AREA TO D101-RECORD
BB17                     ADD   1                  TO W-D101-COUNT
BB17                     MOVE  W-D101-COUNT      TO D101-SEQUENCE-NUMBER
      *                 set acquisition date century for use in key
                         if D101-ACQUISITION-DATE-YY > 44
                             move '19' to D101-ACQUISITION-DATE-CC
                         else
                             move '20' to D101-ACQUISITION-DATE-CC
                         end-if
                         if D101-TAPER-DATE-YY > 44
                             move '19' to D101-TAPER-DATE-CC
                         else
                             move '20' to D101-TAPER-DATE-CC
                         end-if
						 string CGTFILES-LINKAGE(1:11) 'write' delimited by size into timing-message-suffix
BB17                     WRITE D101-RECORD
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    WRITE D107-RECORD FROM L-FILE-RECORD-AREA
 BB21                          AFTER ADVANCING 1 LINE
 BB21               WHEN TRACE-FILE
 BB21                    WRITE D108-RECORD FROM L-FILE-RECORD-AREA
 BB21                          AFTER ADVANCING 1 LINE
 BB21               WHEN ERROR-LOG-FILE
 BB21                    WRITE D109-RECORD FROM L-FILE-RECORD-AREA
 BB21                          AFTER ADVANCING 1 LINE
BB22                WHEN TAPER-RATE-FILE                  
BB22                     MOVE D112-DATE-EFFECTIVE TO L-KEY
BB22                     CALL 'CGTINVRT' USING L-KEY
BB22                     MOVE L-KEY TO D112-INVERSE-DATE
BB22                     WRITE D112-RECORD
BB22                     MOVE D112-RECORD TO L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     MOVE D153-KEY    TO    L-KEY
BB24                     CALL 'CGTINVRT'  USING L-KEY
BB24                     MOVE L-KEY       TO    D153-INVERSE-KEY
BB24                     WRITE D153-RECORD
BB24                     MOVE D153-RECORD TO    L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     WRITE D154-RECORD
BB24                     MOVE D154-RECORD TO    L-FILE-RECORD-AREA
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     MOVE D163-KEY    TO    L-KEY
BB26                     CALL 'CGTINVRT'  USING L-KEY
BB26                     MOVE L-KEY       TO    D163-INVERSE-KEY
BB26                     WRITE D163-RECORD
BB26                     MOVE D163-RECORD TO    L-FILE-RECORD-AREA
BB27                WHEN PRICE-TYPES-FILE
BB27                     MOVE D167-KEY    TO    L-KEY
BB27                     CALL 'CGTINVRT'  USING L-KEY
BB27                     MOVE L-KEY       TO    D167-INVERSE-KEY
BB27                     WRITE D167-RECORD
BB27                     MOVE D167-RECORD TO    L-FILE-RECORD-AREA
BB27                WHEN PENDING-LOG-FILE
BB27                     STRING D169-Key2A, D169-Key DELIMITED BY SIZE
BB27                                      INTO  L-KEY
BB27                     CALL 'CGTINVRT'  USING L-KEY
BB27                     MOVE L-KEY       TO    D169-INVERSE-KEY2
BB27                     WRITE D169-RECORD
BB27                     MOVE D169-RECORD TO    L-FILE-RECORD-AREA
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       Q-EXIT.
           EXIT.
      /
       R-REWRITE SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         REWRITE D3-RECORD  FROM L-FILE-RECORD-AREA
                    WHEN FUND-FILE
                         REWRITE D4-RECORD  FROM L-FILE-RECORD-AREA
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         REWRITE D9-RECORD  FROM L-FILE-RECORD-AREA
                    WHEN MASTER-LOG-FILE
                         REWRITE D17-RECORD FROM L-FILE-RECORD-AREA
                    WHEN REALISED-DATA-FILE
                         REWRITE D19-RECORD FROM L-FILE-RECORD-AREA
                    WHEN UNREALISED-DATA-FILE
                         REWRITE D20-RECORD FROM L-FILE-RECORD-AREA
                    WHEN PRINTER-FILE
                         REWRITE D31-RECORD FROM L-FILE-RECORD-AREA
                    WHEN STOCK-TYPE-FILE
                         REWRITE D32-RECORD FROM L-FILE-RECORD-AREA
                    WHEN TRANSACTION-CODE-FILE
                         REWRITE D34-RECORD FROM L-FILE-RECORD-AREA
                    WHEN OUTPUT-LOG-FILE
                         REWRITE D35-RECORD FROM L-FILE-RECORD-AREA
                    WHEN MESSAGE-FILE
                         REWRITE D36-RECORD FROM L-FILE-RECORD-AREA
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         REWRITE D38-RECORD FROM L-FILE-RECORD-AREA
                    WHEN DEFAULT-ACCESS-FILE
                         REWRITE D39-RECORD FROM L-FILE-RECORD-AREA
                    WHEN ACCESS-PROFILE-FILE
                         REWRITE D40-RECORD FROM L-FILE-RECORD-AREA
                    WHEN EXTEL-CURRENCY-FILE
                         REWRITE D42-RECORD FROM L-FILE-RECORD-AREA
                    WHEN STOCK-PRICE-FILE
                         REWRITE D43-RECORD FROM L-FILE-RECORD-AREA
                    WHEN SEQ-BALANCE-FILE
                         REWRITE D45-RECORD FROM L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    REWRITE D79-RECORD  FROM L-FILE-RECORD-AREA
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    REWRITE D80-RECORD  FROM L-FILE-RECORD-AREA
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    REWRITE D81-RECORD FROM L-FILE-RECORD-AREA
MJ1                 WHEN RCF-FILE             
MJ1                      REWRITE D86-RECORD  FROM L-FILE-RECORD-AREA
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     REWRITE D98-RECORD FROM L-FILE-RECORD-AREA
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     REWRITE D101-RECORD FROM L-FILE-RECORD-AREA
BB22                WHEN TAPER-RATE-FILE                  
BB22                     REWRITE D112-RECORD FROM L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     REWRITE D153-RECORD FROM L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     REWRITE D154-RECORD FROM L-FILE-RECORD-AREA
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     REWRITE D163-RECORD FROM L-FILE-RECORD-AREA
BB27                WHEN PRICE-TYPES-FILE
BB27                     REWRITE D167-RECORD FROM L-FILE-RECORD-AREA
BB27                WHEN PENDING-LOG-FILE
BB27                     REWRITE D169-RECORD FROM L-FILE-RECORD-AREA
BB27                WHEN PENDING-ITEMS-FILE
BB27                     REWRITE D170-RECORD FROM L-FILE-RECORD-AREA
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       R-EXIT.
           EXIT.
      /
       S-DELETE SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         DELETE D3
                    WHEN FUND-FILE
                         DELETE D4
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         DELETE D9
                    WHEN MASTER-LOG-FILE
                         DELETE D17
                    WHEN REALISED-DATA-FILE
                         DELETE D19
                    WHEN UNREALISED-DATA-FILE
                         DELETE D20
                    WHEN PRINTER-FILE
                         DELETE D31
                    WHEN STOCK-TYPE-FILE
                         DELETE D32
                    WHEN TRANSACTION-CODE-FILE
                         DELETE D34
                    WHEN OUTPUT-LOG-FILE
                         DELETE D35
                    WHEN MESSAGE-FILE
                         DELETE D36
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         DELETE D38
                    WHEN DEFAULT-ACCESS-FILE
                         DELETE D39
                    WHEN ACCESS-PROFILE-FILE
                         DELETE D40
                    WHEN EXTEL-CURRENCY-FILE
                         DELETE D42
                    WHEN STOCK-PRICE-FILE
                         DELETE D43
                    WHEN SEQ-BALANCE-FILE
                         DELETE D45
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    DELETE D79
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    DELETE D80
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    DELETE D81
MJ1                 WHEN RCF-FILE             
MJ1                      DELETE D86
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     DELETE D98
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     DELETE D101
BB22                WHEN TAPER-RATE-FILE                  
BB22                     DELETE D112
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     DELETE D153
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     DELETE D154
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     DELETE D163
BB27                WHEN PRICE-TYPES-FILE
BB27                     DELETE D167
BB27                WHEN PENDING-LOG-FILE
BB27                     DELETE D169
BB27                WHEN PENDING-ITEMS-FILE
BB27                     DELETE D170
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       S-EXIT.
           EXIT.
      /
       T-CLOSE SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         call "COUNTRYDAL" using L-FILE-ACTION
                                                 L-FILE-RECORD-AREA
                    WHEN GROUP-FILE
                         call "GroupDAL" using L-FILE-ACTION
                                               L-FILE-RECORD-AREA
                    WHEN STOCK-FILE
                         CLOSE D3
                    WHEN FUND-FILE
                         CLOSE D4
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      CLOSE D5
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      CLOSE D6
                    WHEN RPI-FILE
                         call "RPIDAL" using L-FILE-ACTION
                                             L-FILE-RECORD-AREA
                    WHEN PARAMETER-FILE
                         call "ParametersDAL" using L-FILE-ACTION
                                                    L-FILE-RECORD-AREA
                    WHEN USER-FILE
                         CLOSE D9
                    WHEN STERLING-EXTEL-REPORT
                         CLOSE D10
                    WHEN FOREIGN-EXTEL-REPORT
                         CLOSE D11
                    WHEN OUTPUT-LISTING
                         CLOSE D12
                    WHEN MASTER-LOG-FILE
                         CLOSE D17
                    WHEN ERROR-REPORT-FILE
                         CLOSE D18
                    WHEN REALISED-DATA-FILE
                         CLOSE D19
                    WHEN UNREALISED-DATA-FILE
                         CLOSE D20
                    WHEN REALISED-SCHEDULE-FILE
                         CLOSE D21
                    WHEN UNREALISED-SCHEDULE-FILE
                         CLOSE D22
                    WHEN CG01-REPORT-FILE
                         CLOSE D23
                    WHEN CG02-REPORT-FILE
                         CLOSE D24
                    WHEN CG03-REPORT-FILE
                         CLOSE D25
                    WHEN YE-REC-REPORT-FILE
                         CLOSE D26
                    WHEN YE-DEL-REPORT-FILE
                         CLOSE D27
                    WHEN YE-CON-REPORT-FILE
                         CLOSE D28
                    WHEN ERROR-DATA-FILE
                         CLOSE D29
                    WHEN YE-ERR-REPORT-FILE
                         CLOSE D30
                    WHEN PRINTER-FILE
                         CLOSE D31
                    WHEN STOCK-TYPE-FILE
                         CLOSE D32
                    WHEN YE-REC2-DATA-FILE
                         CLOSE D33
                    WHEN TRANSACTION-CODE-FILE
                         CLOSE D34
                    WHEN OUTPUT-LOG-FILE
                         CLOSE D35
                    WHEN MESSAGE-FILE
                         CLOSE D36
                    WHEN USER-FUND-FILE
                         call "UserFundDAL" using L-FILE-ACTION
                                                  L-FILE-RECORD-AREA
                                                  L-USER-NO
                    WHEN HELP-TEXT-FILE
                         CLOSE D38
                    WHEN DEFAULT-ACCESS-FILE
                         CLOSE D39
                    WHEN ACCESS-PROFILE-FILE
                         CLOSE D40
                    WHEN EXTEL-PRICES-FILE
                         CLOSE D41
                    WHEN EXTEL-CURRENCY-FILE
                         CLOSE D42
                    WHEN STOCK-PRICE-FILE
                         CLOSE D43
                    WHEN EXTEL-TRANSMISSION-FILE
                         CLOSE D44
                    WHEN SEQ-BALANCE-FILE
                         CLOSE D45
BB2                 WHEN TRANSACTION-FILE
BB2                      CLOSE D46
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   CLOSE D49
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   CLOSE D50
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   CLOSE D51
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      CLOSE D68
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      CLOSE D69
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     CLOSE D70
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     CLOSE D71
 BB8                WHEN SKAN1-REPORT
 BB8                     CLOSE D72
 BB8                WHEN SKAN2-REPORT
 BB8                     CLOSE D73
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     CLOSE D74
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     CLOSE D75
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     CLOSE D76
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     CLOSE D77
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    CLOSE D78
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    CLOSE D79
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    CLOSE D80
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    CLOSE D81
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    CLOSE D82
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    CLOSE D83
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    CLOSE D84
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      CLOSE D85 
MJ1                 WHEN RCF-FILE
MJ1                      CLOSE D86
DGD                 WHEN RCF-BACKUP-FILE
DGD                      CLOSE D87
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    CLOSE D88
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    CLOSE D89
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    CLOSE D90
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    CLOSE D91
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    CLOSE D92
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    CLOSE D93
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   CLOSE D94
  BB12              WHEN GAINLOSS-REPORT
  BB12                   CLOSE D95
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     CLOSE D98
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     CLOSE D101
 BB21               WHEN BATCH-RUN-LOG-FILE
dtv                      continue       
 BB21               WHEN BATCH-QUIT-RUN-FILE
 BB21                    CLOSE D107
 BB21               WHEN TRACE-FILE
 BB21                    CLOSE D108
 BB21               WHEN ERROR-LOG-FILE
 BB21                    CLOSE D109
BB22                WHEN TAPER-RATE-FILE                  
                         call "TaperRateDAL" using L-FILE-ACTION
                                                   L-FILE-RECORD-AREA
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     CLOSE D153
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     CLOSE D154
BB25                WHEN DISPOSALS-FROM-DB-FILE
BB25                     CLOSE D161
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     CLOSE D163
BB27                WHEN PRICE-TYPES-FILE
BB27                     CLOSE D167
BB27                WHEN PENDING-LOG-FILE
BB27                     CLOSE D169
BB27                WHEN PENDING-ITEMS-FILE
BB27                     CLOSE D170
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       T-EXIT.
           EXIT.
      /
       U-UNLOCK SECTION.

           EVALUATE L-FILE-NAME
                    WHEN COUNTRY-FILE
                         CONTINUE
                    WHEN GROUP-FILE
                         CONTINUE
                    WHEN STOCK-FILE
                         UNLOCK D3  RECORDS
                    WHEN FUND-FILE
                         UNLOCK D4  RECORDS
BB2                 WHEN CGTR04-REPORT-FILE
BB2                      UNLOCK D5  RECORDS
BB2                 WHEN CGTR05-REPORT-FILE
BB2                      UNLOCK D6  RECORDS
                    WHEN RPI-FILE
                         CONTINUE
                    WHEN PARAMETER-FILE
                         CONTINUE
                    WHEN USER-FILE
                         UNLOCK D9  RECORDS
                    WHEN STERLING-EXTEL-REPORT
                         UNLOCK D10 RECORDS
                    WHEN FOREIGN-EXTEL-REPORT
                         UNLOCK D11 RECORDS
                    WHEN MASTER-LOG-FILE
                         UNLOCK D17 RECORDS
                    WHEN ERROR-REPORT-FILE
                         UNLOCK D18 RECORDS
                    WHEN REALISED-DATA-FILE
                         UNLOCK D19 RECORDS
                    WHEN UNREALISED-DATA-FILE
                         UNLOCK D20 RECORDS
                    WHEN REALISED-SCHEDULE-FILE
                         UNLOCK D21 RECORDS
                    WHEN UNREALISED-SCHEDULE-FILE
                         UNLOCK D22 RECORDS
                    WHEN CG01-REPORT-FILE
                         UNLOCK D23 RECORDS
                    WHEN CG02-REPORT-FILE
                         UNLOCK D24 RECORDS
                    WHEN CG03-REPORT-FILE
                         UNLOCK D25 RECORDS
                    WHEN YE-REC-REPORT-FILE
                         UNLOCK D26 RECORDS
                    WHEN YE-DEL-REPORT-FILE
                         UNLOCK D27 RECORDS
                    WHEN YE-CON-REPORT-FILE
                         UNLOCK D28 RECORDS
                    WHEN ERROR-DATA-FILE
                         UNLOCK D29 RECORDS
                    WHEN YE-ERR-REPORT-FILE
                         UNLOCK D30 RECORDS
                    WHEN PRINTER-FILE
                         UNLOCK D31 RECORDS
                    WHEN STOCK-TYPE-FILE
                         UNLOCK D32 RECORDS
                    WHEN YE-REC2-DATA-FILE
                         UNLOCK D33 RECORDS
                    WHEN TRANSACTION-CODE-FILE
                         UNLOCK D34 RECORDS
                    WHEN OUTPUT-LOG-FILE
                         UNLOCK D35 RECORDS
                    WHEN MESSAGE-FILE
                         UNLOCK D36 RECORDS
                    WHEN USER-FUND-FILE
                         CONTINUE
                    WHEN HELP-TEXT-FILE
                         UNLOCK D38 RECORDS
                    WHEN DEFAULT-ACCESS-FILE
                         UNLOCK D39 RECORDS
                    WHEN ACCESS-PROFILE-FILE
                         UNLOCK D40 RECORDS
                    WHEN EXTEL-PRICES-FILE
                         UNLOCK D41 RECORDS
                    WHEN EXTEL-CURRENCY-FILE
                         UNLOCK D42 RECORDS
                    WHEN STOCK-PRICE-FILE
                         UNLOCK D43 RECORDS
                    WHEN EXTEL-TRANSMISSION-FILE
                         UNLOCK D44 RECORDS
                    WHEN SEQ-BALANCE-FILE
                         UNLOCK D45 RECORDS
BB2                 WHEN TRANSACTION-FILE
BB2                      UNLOCK D46 RECORDS
   BB4              WHEN YE-BAL-REPORT-FILE
   BB4                   UNLOCK D49 RECORDS
   BB4              WHEN STOCK-LOAD-REPORT
   BB4                   UNLOCK D50 RECORDS
   BB4              WHEN STOCK-LOAD-DATA-FILE
   BB4                   UNLOCK D51 RECORDS
MIK110              WHEN FUNDS-LOAD-DATA-FILE
BB8                      UNLOCK D68 RECORDS
MIK110              WHEN FUNDS-LOAD-REPORT
BB8                      UNLOCK D69 RECORDS
 BB8                WHEN PRICE-LOAD-DATA-FILE
 BB8                     UNLOCK D70 RECORDS
 BB8                WHEN PRICE-LOAD-REPORT
 BB8                     UNLOCK D71 RECORDS
 BB8                WHEN SKAN1-REPORT
 BB8                     UNLOCK D72 RECORDS
 BB8                WHEN SKAN2-REPORT
 BB8                     UNLOCK D73 RECORDS
 BB8                WHEN NEW-REALISED-REPORT
 BB8                     UNLOCK D74 RECORDS
 BB8                WHEN NEW-UNREALISED-REPORT
 BB8                     UNLOCK D75 RECORDS
 BB8                WHEN MGM1-REPORT-FILE
 BB8                     UNLOCK D76 RECORDS
 BB8                WHEN CAPITAL-REPORT-FILE
 BB8                     UNLOCK D77 RECORDS
  BB9               WHEN REPLACEMENT-RELIEF-REPORT
  BB9                    UNLOCK D78 RECORDS
  BB9               WHEN REPLACEMENT-ACQ-FILE
  BB9                    UNLOCK D79 RECORDS
  BB9               WHEN REPLACEMENT-DIS-FILE
  BB9                    UNLOCK D80 RECORDS
  BB9               WHEN NOTIONAL-SALE-DATA-FILE
  BB9                    UNLOCK D81 RECORDS
  BB9               WHEN NOTIONAL-SALE-SCHEDULE-FILE
  BB9                    UNLOCK D82 RECORDS
  BB9               WHEN BALANCE-LOAD-DATA-FILE
  BB9                    UNLOCK D83 RECORDS
  BB10              WHEN BALANCES-LOAD-REPORT
  BB9                    UNLOCK D84 RECORDS
EJ2                 WHEN OFFSHORE-INCOME-REPORT
EJ2                      UNLOCK D85 RECORDS
MJ1                 WHEN RCF-FILE
MJ1                      UNLOCK D86 RECORDS
DGD                 WHEN RCF-BACKUP-FILE
DGD                      UNLOCK D87 RECORDS
 BB11               WHEN GROUP-LOAD-REPORT
 BB11                    UNLOCK D88 RECORDS
 BB11               WHEN GROUP-LOAD-DATA-FILE
 BB11                    UNLOCK D89 RECORDS
 BB11               WHEN COUNTRY-LOAD-REPORT
 BB11                    UNLOCK D90 RECORDS
 BB11               WHEN COUNTRY-LOAD-DATA-FILE
 BB11                    UNLOCK D91 RECORDS
 BB11               WHEN RPI-LOAD-REPORT
 BB11                    UNLOCK D92 RECORDS
 BB11               WHEN RPI-LOAD-DATA-FILE
 BB11                    UNLOCK D93 RECORDS
  BB12              WHEN GAINLOSS-DATA-FILE
  BB12                   UNLOCK D94 RECORDS
  BB12              WHEN GAINLOSS-REPORT
  BB12                   UNLOCK D95 RECORDS
BB17                WHEN REALISED-TAX-DATA-FILE
BB17                     UNLOCK D98 RECORDS
BB17                WHEN UNREALISED-TAX-DATA-FILE
BB17                     UNLOCK D101 RECORDS
BB22                WHEN TAPER-RATE-FILE                  
BB22                     UNLOCK D112 RECORDS
BB24                WHEN PERIOD-END-CALENDAR-FILE
BB24                     UNLOCK D153 RECORDS
BB24                WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                     UNLOCK D154 RECORDS
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     UNLOCK D163 RECORDS
BB27                WHEN PRICE-TYPES-FILE
BB27                     UNLOCK D167 RECORDS
BB27                WHEN PENDING-LOG-FILE
BB27                     UNLOCK D169 RECORDS
BB27                WHEN PENDING-ITEMS-FILE
BB27                     UNLOCK D170 RECORDS
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       U-EXIT.
           EXIT.
      /
       V-START-NOT-LESS-THAN-KEY2 SECTION.

           EVALUATE L-FILE-NAME
BB5                 WHEN STOCK-FILE
BB5                      START D3 KEY NOT < D3-SPLIT-KEY
                    WHEN FUND-FILE
                         START D4 KEY NOT < D4-SUMMARY-FUND
BB22                WHEN TAPER-RATE-FILE                  
BB22                     START D112 KEY NOT < D112-INVERSE-DATE
                    WHEN REALISED-DATA-FILE
                         START D19 KEY NOT < D19-SPLIT-KEY-2
                    WHEN UNREALISED-DATA-FILE
                         START D20 KEY NOT < D20-SPLIT-KEY-2
                    WHEN NOTIONAL-SALE-DATA-FILE
                         START D81 KEY NOT < D81-SPLIT-KEY-2
                    WHEN REALISED-TAX-DATA-FILE
                         START D98 KEY NOT < D98-SPLIT-KEY-2
                    WHEN UNREALISED-TAX-DATA-FILE
                         START D101 KEY NOT < D101-SPLIT-KEY-2
BB26                WHEN INTER-CONNECTED-FUNDS-FILE
BB26                     START D163 KEY NOT < D163-OLAB-Fund-Code
BB27                WHEN PRICE-TYPES-FILE
BB27                     START D167 KEY NOT < D167-Sequence-Code
BB27                WHEN PENDING-LOG-FILE
BB27                     START D169 KEY NOT < D169-Key2
                    WHEN OTHER
                         MOVE 'IF' TO FILE-STATUS
           END-EVALUATE.
       V-EXIT.
           EXIT.
      /
EJ1    VA-START-NOT-LESS-THAN-KEY3 SECTION.
 "
 "         EVALUATE L-FILE-NAME
 "                  WHEN FUND-FILE
 "                       START D4 KEY NOT < D4-NEW-SUMMARY-FUND
 "                  WHEN OTHER
 "                       MOVE 'IF' TO FILE-STATUS
 "         END-EVALUATE.
 "     VA-EXIT.
 "         EXIT.
      /
       W-OPEN-INPUT-REPORT SECTION.
           OPEN INPUT D99.
           MOVE    1   TO W-PRINT-CHAR-SUB.
           MOVE SPACES TO W-PRINT-RECORD.
       W-EXIT.
           EXIT.
      /
       X-READ-REPORT SECTION.
           READ D99.

           EVALUATE D99-RECORD-BYTE
           WHEN X'0D'
                MOVE W-PRINT-RECORD TO L-FILE-RECORD-AREA
                MOVE    1   TO W-PRINT-CHAR-SUB
                MOVE SPACES TO W-PRINT-RECORD
           WHEN OTHER
BB14            IF W-STORE-ACTION NOT = SPACES
BB14            AND (D99-RECORD-BYTE = X'0A' OR X'0C')
BB14               CONTINUE
BB14            ELSE
                   MOVE D99-RECORD TO W-PRINT-CHAR(W-PRINT-CHAR-SUB)
                   ADD 1 TO W-PRINT-CHAR-SUB
BB14            END-IF
           END-EVALUATE.
       X-EXIT.
           EXIT.
           
       X4-CGTLOG SECTION.
      * to write a line in log file 
           ADD     1                   TO      WS-MESSAGE-NO
           MOVE    WS-MESSAGE-NO       TO      L-LOG-MESSAGE-NO
           MOVE    WS-PROGRAM-NAME     TO      L-LOG-PROGRAM
           MOVE    WRITE-RECORD        TO      L-LOG-ACTION
           CALL    'CGTLOG'            USING   CGTLOG-LINKAGE-AREA-1
                                               CGTLOG-LINKAGE-AREA-2.
           IF      L-LOG-MESSAGE-TYPE = 'Q'
               MOVE 'Q'                TO      WS-PROCESS-FLAG
               MOVE 'P'                TO      L-LOG-MESSAGE-TYPE
           END-IF.                    
       X4-EXIT.
           EXIT.
      /
       Y-CLOSE-REPORT SECTION.
           CLOSE D99.
       Y-EXIT.
           EXIT.

BB21   Z3-CLOSE-ALL-FILES SECTION.
BB21       CLOSE D1.
BB21       CLOSE D2.
BB21       CLOSE D3.
BB21       CLOSE D4.
BB21       CLOSE D5.
BB21       CLOSE D6.
BB21       CLOSE D7.
BB21       CLOSE D8.
BB21       CLOSE D10.
BB21       CLOSE D11.
BB21       CLOSE D12.
BB21       CLOSE D17.
BB21       CLOSE D18.
BB21       CLOSE D19.
BB21       CLOSE D20.
BB21       CLOSE D21.
BB21       CLOSE D22.
BB21       CLOSE D23.
BB21       CLOSE D24.
BB21       CLOSE D25.
BB21       CLOSE D26.
BB21       CLOSE D27.
BB21       CLOSE D28.
BB21       CLOSE D29.
BB21       CLOSE D30.
BB21       CLOSE D31.
BB21       CLOSE D32.
BB21       CLOSE D33.
BB21       CLOSE D34.
BB21       CLOSE D35.
BB21       CLOSE D36.
BB21       CLOSE D38.
BB21       CLOSE D39.
BB21       CLOSE D41.
BB21       CLOSE D42.
BB21       CLOSE D43.
BB21       CLOSE D44.
BB21       CLOSE D45.
BB21       CLOSE D46.
BB21       CLOSE D49.
BB21       CLOSE D50.
BB21       CLOSE D51.
BB21       CLOSE D68.
BB21       CLOSE D69.
BB21       CLOSE D70.
BB21       CLOSE D71.
BB21       CLOSE D72.
BB21       CLOSE D73.
BB21       CLOSE D74.
BB21       CLOSE D75.
BB21       CLOSE D76.
BB21       CLOSE D77.
BB21       CLOSE D78.
BB21       CLOSE D79.
BB21       CLOSE D80.
BB21       CLOSE D81.
BB21       CLOSE D82.
BB21       CLOSE D83.
BB21       CLOSE D84.
BB21       CLOSE D85 .
BB21       CLOSE D86.
BB21       CLOSE D87.
BB21       CLOSE D88.
BB21       CLOSE D89.
BB21       CLOSE D90.
BB21       CLOSE D91.
BB21       CLOSE D92.
BB21       CLOSE D93.
BB21       CLOSE D94.
BB21       CLOSE D95.
BB21       CLOSE D98.
BB21       CLOSE D101.
BB21       CLOSE D107.
BB21       CLOSE D108.
BB21       CLOSE D109.
 BB22      CLOSE D112.
           CLOSE D133.
BB27       CLOSE D153.
BB27       CLOSE D154.
BB27       CLOSE D163.
BB27       CLOSE D167.
BB27       CLOSE D169.
BB21   Z3-EXIT.
BB21       EXIT.

BB21   Z4-MAKE-FILE-NAME SECTION.
      *
      * Set full file path/name if action is open
BB21       EVALUATE L-FILE-ACTION
BB21       WHEN OPEN-INPUT
BB21       WHEN OPEN-OUTPUT
BB21       WHEN OPEN-I-O
BB21       WHEN OPEN-EXTEND
BB21       WHEN OPEN-INPUT-REPORT
BB21           EVALUATE L-FILE-NAME
BB21           WHEN COUNTRY-FILE
                    CONTINUE
BB21           WHEN GROUP-FILE
                    CONTINUE
BB21           WHEN STOCK-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D3-FILE         TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D3-FILE-NAME
BB21           WHEN FUND-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D4-FILE         TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D4-FILE-NAME
BB21           WHEN CGTR04-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D5-FILE         TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D5-FILE-NAME
BB21           WHEN CGTR05-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D6-FILE         TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D6-FILE-NAME
BB21           WHEN RPI-FILE
                    CONTINUE
BB21           WHEN PARAMETER-FILE
                    CONTINUE
BB21           WHEN USER-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D9-FILE         TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D9-FILE-NAME
BB21           WHEN STERLING-EXTEL-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D10-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D10-FILE-NAME
BB21           WHEN FOREIGN-EXTEL-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D11-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D11-FILE-NAME
BB21           WHEN OUTPUT-LISTING
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D12-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D12-FILE-NAME
BB21           WHEN MASTER-LOG-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D17-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D17-FILE-NAME
BB21           WHEN ERROR-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D18-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D18-FILE-NAME
BB21           WHEN REALISED-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D19-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D19-FILE-NAME
BB21           WHEN UNREALISED-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D20-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D20-FILE-NAME
BB21           WHEN REALISED-SCHEDULE-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D21-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D21-FILE-NAME
BB21           WHEN UNREALISED-SCHEDULE-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D22-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D22-FILE-NAME
BB21           WHEN CG01-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D23-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D23-FILE-NAME
BB21           WHEN CG02-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D24-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D24-FILE-NAME
BB21           WHEN CG03-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D25-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D25-FILE-NAME
BB21           WHEN YE-REC-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D26-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D26-FILE-NAME
BB21           WHEN YE-DEL-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D27-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D27-FILE-NAME
BB21           WHEN YE-CON-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D28-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D28-FILE-NAME
BB21           WHEN ERROR-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D29-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D29-FILE-NAME
BB21           WHEN YE-ERR-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D30-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D30-FILE-NAME
BB21           WHEN PRINTER-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D31-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D31-FILE-NAME
BB21           WHEN STOCK-TYPE-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D32-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D32-FILE-NAME
BB21           WHEN YE-REC2-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D33-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D33-FILE-NAME
BB21           WHEN TRANSACTION-CODE-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D34-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D34-FILE-NAME
BB21           WHEN OUTPUT-LOG-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D35-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D35-FILE-NAME
BB21           WHEN MESSAGE-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D36-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D36-FILE-NAME
BB21           WHEN USER-FUND-FILE
                    CONTINUE
BB21           WHEN HELP-TEXT-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D38-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D38-FILE-NAME
BB21           WHEN DEFAULT-ACCESS-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D39-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D39-FILE-NAME
BB21           WHEN ACCESS-PROFILE-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D40-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D40-FILE-NAME
BB21           WHEN EXTEL-PRICES-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D41-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D41-FILE-NAME
BB21           WHEN EXTEL-CURRENCY-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D42-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D42-FILE-NAME
BB21           WHEN STOCK-PRICE-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D43-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D43-FILE-NAME
BB21           WHEN EXTEL-TRANSMISSION-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D44-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D44-FILE-NAME
BB21           WHEN SEQ-BALANCE-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D45-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D45-FILE-NAME
BB21           WHEN TRANSACTION-FILE
BB21                MOVE D46-FILE TO D46-FILE-NAME
BB21           WHEN YE-BAL-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D49-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D49-FILE-NAME
BB21           WHEN STOCK-LOAD-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D50-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D50-FILE-NAME
BB21           WHEN STOCK-LOAD-DATA-FILE
BB21                MOVE D51-FILE TO D51-FILE-NAME
21           WHEN FUNDS-LOAD-DATA-FILE
BB21                MOVE D68-FILE TO D68-FILE-NAME
BB21           WHEN FUNDS-LOAD-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D69-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D69-FILE-NAME
BB21           WHEN PRICE-LOAD-DATA-FILE
BB21                MOVE D70-FILE TO D70-FILE-NAME
BB21           WHEN PRICE-LOAD-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D71-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D71-FILE-NAME
BB21           WHEN SKAN1-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D72-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D72-FILE-NAME
BB21           WHEN SKAN2-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D73-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D73-FILE-NAME
BB21           WHEN NEW-REALISED-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D74-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D74-FILE-NAME
BB21           WHEN NEW-UNREALISED-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D75-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D75-FILE-NAME
BB21           WHEN MGM1-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D76-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D76-FILE-NAME
BB21           WHEN CAPITAL-REPORT-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D77-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D77-FILE-NAME
BB21           WHEN REPLACEMENT-RELIEF-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D78-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D78-FILE-NAME
BB21           WHEN REPLACEMENT-ACQ-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D79-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D79-FILE-NAME
BB21           WHEN REPLACEMENT-DIS-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D80-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D80-FILE-NAME
BB21           WHEN NOTIONAL-SALE-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D81-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D81-FILE-NAME
BB21           WHEN NOTIONAL-SALE-SCHEDULE-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D82-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D82-FILE-NAME
BB21           WHEN BALANCE-LOAD-DATA-FILE
BB21                MOVE D83-FILE TO D83-FILE-NAME
BB21           WHEN BALANCES-LOAD-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D84-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D84-FILE-NAME
BB21           WHEN OFFSHORE-INCOME-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D85-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D85-FILE-NAME
BB21           WHEN RCF-FILE
BB21                MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D86-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D86-FILE-NAME
BB21           WHEN RCF-BACKUP-FILE
BB21                MOVE D87-FILE TO D87-FILE-NAME
BB21           WHEN GROUP-LOAD-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D88-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D88-FILE-NAME
BB21           WHEN GROUP-LOAD-DATA-FILE
BB21                MOVE D89-FILE TO D89-FILE-NAME
BB21           WHEN COUNTRY-LOAD-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D90-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D90-FILE-NAME
BB21           WHEN COUNTRY-LOAD-DATA-FILE
BB21                MOVE D91-FILE TO D91-FILE-NAME
BB21           WHEN RPI-LOAD-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D92-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D92-FILE-NAME
BB21           WHEN RPI-LOAD-DATA-FILE
BB21                MOVE D93-FILE TO D93-FILE-NAME
BB21           WHEN GAINLOSS-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D94-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D94-FILE-NAME
BB21           WHEN GAINLOSS-REPORT
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D95-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D95-FILE-NAME
               WHEN LOST-INDEXATION-REPORT
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D96-FILE        TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D96-FILE-NAME
               WHEN LOSU-INDEXATION-REPORT
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D97-FILE        TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D97-FILE-NAME
BB21           WHEN REALISED-TAX-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D98-FILE        TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D98-FILE-NAME
               WHEN REALISED-TAX-SCHEDULE-FILE
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D100-FILE       TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D100-FILE-NAME
BB21           WHEN UNREALISED-TAX-DATA-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D101-FILE       TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D101-FILE-NAME
               WHEN UNREALISED-TAX-SCHEDULE-FILE
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D102-FILE       TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D102-FILE-NAME
               WHEN REAL-H-O-GAINS-REPORT
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D103-FILE       TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D103-FILE-NAME
               WHEN UNREAL-H-O-GAINS-REPORT
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D104-FILE       TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D104-FILE-NAME
               WHEN ACQUISITION-EXPORT-FILE
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D105-FILE       TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D105-FILE-NAME
BB21           WHEN BATCH-RUN-LOG-FILE
dtv                 continue       
BB21           WHEN BATCH-QUIT-RUN-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D107-FILE       TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D107-FILE-NAME
BB21           WHEN TRACE-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D108-FILE       TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D108-FILE-NAME
BB21           WHEN ERROR-LOG-FILE
BB21                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB21                MOVE D109-FILE       TO EQTPATH-FILE-NAME
BB21                PERFORM X-CALL-EQTPATH
BB21                MOVE EQTPATH-PATH-FILE-NAME TO D109-FILE-NAME
               WHEN TAPER-REPORT-FILE
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D110-FILE       TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D110-FILE-NAME
               WHEN TAPER-EXPORT-FILE
                    MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
                    MOVE D111-FILE       TO EQTPATH-FILE-NAME
                    PERFORM X-CALL-EQTPATH
                    MOVE EQTPATH-PATH-FILE-NAME TO D111-FILE-NAME
 BB22          WHEN TAPER-RATE-FILE
 BB22               MOVE ADMIN-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
 BB22               MOVE D112-FILE       TO EQTPATH-FILE-NAME
 BB22               PERFORM X-CALL-EQTPATH
 BB22               MOVE EQTPATH-PATH-FILE-NAME TO D112-FILE-NAME
BB24           WHEN PERIOD-END-CALENDAR-FILE
BB24                MOVE ADMIN-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB24                MOVE D153-FILE        TO EQTPATH-FILE-NAME
BB24                PERFORM X-CALL-EQTPATH
BB24                MOVE EQTPATH-PATH-FILE-NAME TO D153-FILE-NAME
BB24           WHEN PERIOD-END-CALENDAR-DATES-FILE
BB24                MOVE ADMIN-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB24                MOVE D154-FILE        TO EQTPATH-FILE-NAME
BB24                PERFORM X-CALL-EQTPATH
BB24                MOVE EQTPATH-PATH-FILE-NAME TO D154-FILE-NAME
BB26           WHEN INTER-CONNECTED-FUNDS-FILE
BB26                MOVE ADMIN-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB26                MOVE D163-FILE        TO EQTPATH-FILE-NAME
BB26                PERFORM X-CALL-EQTPATH
BB26                MOVE EQTPATH-PATH-FILE-NAME TO D163-FILE-NAME
BB25           WHEN ALLOWANCES-FROM-DB-FILE
BB25                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB25                MOVE D159-FILE       TO EQTPATH-FILE-NAME
BB25                PERFORM X-CALL-EQTPATH
BB25                MOVE EQTPATH-PATH-FILE-NAME TO D159-FILE-NAME
BB25           WHEN LOSSES-FROM-DB-FILE
BB25                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB25                MOVE D160-FILE       TO EQTPATH-FILE-NAME
BB25                PERFORM X-CALL-EQTPATH
BB25                MOVE EQTPATH-PATH-FILE-NAME TO D160-FILE-NAME
BB25           WHEN DISPOSALS-FROM-DB-FILE
BB25                MOVE USER-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB25                MOVE D161-FILE       TO EQTPATH-FILE-NAME
BB25                PERFORM X-CALL-EQTPATH
BB25                MOVE EQTPATH-PATH-FILE-NAME TO D161-FILE-NAME
BB27           WHEN PRICE-TYPES-FILE
BB27                MOVE ADMIN-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB27                MOVE D167-FILE        TO EQTPATH-FILE-NAME
BB27                PERFORM X-CALL-EQTPATH
BB27                MOVE EQTPATH-PATH-FILE-NAME TO D167-FILE-NAME
BB27           WHEN PENDING-LOG-FILE
BB27                MOVE ADMIN-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB27                MOVE D169-FILE        TO EQTPATH-FILE-NAME
BB27                PERFORM X-CALL-EQTPATH
BB27                MOVE EQTPATH-PATH-FILE-NAME TO D169-FILE-NAME
BB27           WHEN PENDING-ITEMS-FILE
BB27                MOVE ADMIN-DATA-PATH  TO EQTPATH-PATH-ENV-VARIABLE
BB27                MOVE D170-FILE        TO EQTPATH-FILE-NAME
BB27                PERFORM X-CALL-EQTPATH
BB27                MOVE EQTPATH-PATH-FILE-NAME TO D170-FILE-NAME
BB21           END-EVALUATE
BB21       END-EVALUATE.
BB21   Z4-EXIT.
BB21       EXIT.

BB21      COPY EQTPATH.CPY.
BB21      COPY EQTDEBUG.CPY.