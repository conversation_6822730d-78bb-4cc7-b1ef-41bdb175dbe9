using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ElcgmioLinkage2 Data Structure

public class ElcgmioLinkage2
{
    private static int _size = 549;
    // [DEBUG] Class: ElcgmioLinkage2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LRecordArea, is_external=, is_static_class=False, static_prefix=
    private LRecordArea _LRecordArea = new LRecordArea();
    
    
    
    
    // [DEBUG] Field: Filler180, is_external=, is_static_class=False, static_prefix=
    private Filler180 _Filler180 = new Filler180();
    
    
    
    
    
    // Serialization methods
    public string GetElcgmioLinkage2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LRecordArea.GetLRecordAreaAsString());
        result.Append(_Filler180.GetFiller180AsString());
        
        return result.ToString();
    }
    
    public void SetElcgmioLinkage2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 271 <= data.Length)
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset, 271));
        }
        else
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset));
        }
        offset += 271;
        if (offset + 278 <= data.Length)
        {
            _Filler180.SetFiller180AsString(data.Substring(offset, 278));
        }
        else
        {
            _Filler180.SetFiller180AsString(data.Substring(offset));
        }
        offset += 278;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetElcgmioLinkage2AsString();
    }
    // Set<>String Override function
    public void SetElcgmioLinkage2(string value)
    {
        SetElcgmioLinkage2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public LRecordArea GetLRecordArea()
    {
        return _LRecordArea;
    }
    
    // Standard Setter
    public void SetLRecordArea(LRecordArea value)
    {
        _LRecordArea = value;
    }
    
    // Get<>AsString()
    public string GetLRecordAreaAsString()
    {
        return _LRecordArea != null ? _LRecordArea.GetLRecordAreaAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLRecordAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LRecordArea == null)
        {
            _LRecordArea = new LRecordArea();
        }
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    
    // Standard Getter
    public Filler180 GetFiller180()
    {
        return _Filler180;
    }
    
    // Standard Setter
    public void SetFiller180(Filler180 value)
    {
        _Filler180 = value;
    }
    
    // Get<>AsString()
    public string GetFiller180AsString()
    {
        return _Filler180 != null ? _Filler180.GetFiller180AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller180AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler180 == null)
        {
            _Filler180 = new Filler180();
        }
        _Filler180.SetFiller180AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLRecordArea(string value)
    {
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    // Nested Class: LRecordArea
    public class LRecordArea
    {
        private static int _size = 271;
        
        // Fields in the class
        
        
        // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
        private string _FixedPortion ="";
        
        
        
        
        // [DEBUG] Field: Filler179, is_external=, is_static_class=False, static_prefix=
        private string _Filler179 ="";
        
        
        
        
        // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
        private string[] _BalanceCosts = new string[200];
        
        
        
        
    public LRecordArea() {}
    
    public LRecordArea(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFixedPortion(data.Substring(offset, 270).Trim());
        offset += 270;
        SetFiller179(data.Substring(offset, 0).Trim());
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            string value = data.Substring(offset, 1);
            _BalanceCosts[i] = value.Trim();
            offset += 1;
        }
        
    }
    
    // Serialization methods
    public string GetLRecordAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_Filler179.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(1));
        }
        
        return result.ToString();
    }
    
    public void SetLRecordAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller179(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 1 > data.Length) break;
            string val = data.Substring(offset, 1);
            
            _BalanceCosts[i] = val.Trim();
            offset += 1;
        }
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public string GetFiller179()
    {
        return _Filler179;
    }
    
    // Standard Setter
    public void SetFiller179(string value)
    {
        _Filler179 = value;
    }
    
    // Get<>AsString()
    public string GetFiller179AsString()
    {
        return _Filler179.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller179AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler179 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(1);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller180(string value)
{
    _Filler180.SetFiller180AsString(value);
}
// Nested Class: Filler180
public class Filler180
{
    private static int _size = 278;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler181, is_external=, is_static_class=False, static_prefix=
    private string _Filler181 ="";
    
    
    
    
    // [DEBUG] Field: LFnUserNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnUserNo ="";
    
    
    
    
    // [DEBUG] Field: LFnGenerationNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnGenerationNo ="";
    
    
    
    
    // [DEBUG] Field: LFnYy, is_external=, is_static_class=False, static_prefix=
    private int _LFnYy =0;
    
    
    
    
    // [DEBUG] Field: Filler182, is_external=, is_static_class=False, static_prefix=
    private Filler180.Filler182 _Filler182 = new Filler180.Filler182();
    
    
    
    
public Filler180() {}

public Filler180(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller181(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnUserNo(data.Substring(offset, 4).Trim());
    offset += 4;
    SetLFnGenerationNo(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    _Filler182.SetFiller182AsString(data.Substring(offset, Filler182.GetSize()));
    offset += 270;
    
}

// Serialization methods
public string GetFiller180AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler181.PadRight(1));
    result.Append(_LFnUserNo.PadRight(4));
    result.Append(_LFnGenerationNo.PadRight(1));
    result.Append(_LFnYy.ToString().PadLeft(2, '0'));
    result.Append(_Filler182.GetFiller182AsString());
    
    return result.ToString();
}

public void SetFiller180AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller181(extracted);
    }
    offset += 1;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetLFnUserNo(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetLFnGenerationNo(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLFnYy(parsedInt);
    }
    offset += 2;
    if (offset + 270 <= data.Length)
    {
        _Filler182.SetFiller182AsString(data.Substring(offset, 270));
    }
    else
    {
        _Filler182.SetFiller182AsString(data.Substring(offset));
    }
    offset += 270;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller181()
{
    return _Filler181;
}

// Standard Setter
public void SetFiller181(string value)
{
    _Filler181 = value;
}

// Get<>AsString()
public string GetFiller181AsString()
{
    return _Filler181.PadRight(1);
}

// Set<>AsString()
public void SetFiller181AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler181 = value;
}

// Standard Getter
public string GetLFnUserNo()
{
    return _LFnUserNo;
}

// Standard Setter
public void SetLFnUserNo(string value)
{
    _LFnUserNo = value;
}

// Get<>AsString()
public string GetLFnUserNoAsString()
{
    return _LFnUserNo.PadRight(4);
}

// Set<>AsString()
public void SetLFnUserNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnUserNo = value;
}

// Standard Getter
public string GetLFnGenerationNo()
{
    return _LFnGenerationNo;
}

// Standard Setter
public void SetLFnGenerationNo(string value)
{
    _LFnGenerationNo = value;
}

// Get<>AsString()
public string GetLFnGenerationNoAsString()
{
    return _LFnGenerationNo.PadRight(1);
}

// Set<>AsString()
public void SetLFnGenerationNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnGenerationNo = value;
}

// Standard Getter
public int GetLFnYy()
{
    return _LFnYy;
}

// Standard Setter
public void SetLFnYy(int value)
{
    _LFnYy = value;
}

// Get<>AsString()
public string GetLFnYyAsString()
{
    return _LFnYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetLFnYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LFnYy = parsed;
}

// Standard Getter
public Filler182 GetFiller182()
{
    return _Filler182;
}

// Standard Setter
public void SetFiller182(Filler182 value)
{
    _Filler182 = value;
}

// Get<>AsString()
public string GetFiller182AsString()
{
    return _Filler182 != null ? _Filler182.GetFiller182AsString() : "";
}

// Set<>AsString()
public void SetFiller182AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler182 == null)
    {
        _Filler182 = new Filler182();
    }
    _Filler182.SetFiller182AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: Filler182
public class Filler182
{
    private static int _size = 270;
    
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion ="";
    
    
    
    
    // [DEBUG] Field: Filler183, is_external=, is_static_class=False, static_prefix=
    private string _Filler183 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts = new string[200];
    
    
    
    
public Filler182() {}

public Filler182(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFixedPortion(data.Substring(offset, 270).Trim());
    offset += 270;
    SetFiller183(data.Substring(offset, 0).Trim());
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        string value = data.Substring(offset, 0);
        _BalanceCosts[i] = value.Trim();
        offset += 0;
    }
    
}

// Serialization methods
public string GetFiller182AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_FixedPortion.PadRight(270));
    result.Append(_Filler183.PadRight(0));
    for (int i = 0; i < 200; i++)
    {
        result.Append(_BalanceCosts[i].PadRight(0));
    }
    
    return result.ToString();
}

public void SetFiller182AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 270 <= data.Length)
    {
        string extracted = data.Substring(offset, 270).Trim();
        SetFixedPortion(extracted);
    }
    offset += 270;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller183(extracted);
    }
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        if (offset + 0 > data.Length) break;
        string val = data.Substring(offset, 0);
        
        _BalanceCosts[i] = val.Trim();
        offset += 0;
    }
}

// Getter and Setter methods

// Standard Getter
public string GetFixedPortion()
{
    return _FixedPortion;
}

// Standard Setter
public void SetFixedPortion(string value)
{
    _FixedPortion = value;
}

// Get<>AsString()
public string GetFixedPortionAsString()
{
    return _FixedPortion.PadRight(270);
}

// Set<>AsString()
public void SetFixedPortionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _FixedPortion = value;
}

// Standard Getter
public string GetFiller183()
{
    return _Filler183;
}

// Standard Setter
public void SetFiller183(string value)
{
    _Filler183 = value;
}

// Get<>AsString()
public string GetFiller183AsString()
{
    return _Filler183.PadRight(0);
}

// Set<>AsString()
public void SetFiller183AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler183 = value;
}

// Array Accessors for BalanceCosts
public string GetBalanceCostsAt(int index)
{
    return _BalanceCosts[index];
}

public void SetBalanceCostsAt(int index, string value)
{
    _BalanceCosts[index] = value;
}

public string GetBalanceCostsAsStringAt(int index)
{
    return _BalanceCosts[index].PadRight(0);
}

public void SetBalanceCostsAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _BalanceCosts[index] = value;
}

// Flattened accessors (index 0)
public string GetBalanceCosts()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0]
    : default(string);
}

public void SetBalanceCosts(string value)
{
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    _BalanceCosts[0] = value;
}

public string GetBalanceCostsAsString()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0].ToString()
    : string.Empty;
}

public void SetBalanceCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    
    _BalanceCosts[0] = value;
}




public static int GetSize()
{
    return _size;
}

}
}

}}