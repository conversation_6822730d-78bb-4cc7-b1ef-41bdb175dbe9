using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdateDTO
{// DTO class representing LValidDate Data Structure

public class LValidDate
{
    private static int _size = 7;
    // [DEBUG] Class: LValidDate, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LDate, is_external=, is_static_class=False, static_prefix=
    private LDate lDate = new LDate();
    
    
    
    
    // [DEBUG] Field: LDateReturnCode, is_external=, is_static_class=False, static_prefix=
    private int lDateReturnCode =0;
    
    
    
    
    
    // Serialization methods
    public string GetLValidDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(lDate.GetLDateAsString());
        result.Append(lDateReturnCode.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetLValidDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            lDate.SetLDateAsString(data.Substring(offset, 6));
        }
        else
        {
            lDate.SetLDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLDateReturnCode(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetLValidDateAsString();
    }
    // Set<>String Override function
    public void SetLValidDate(string value)
    {
        SetLValidDateAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public LDate GetLDate()
    {
        return lDate;
    }
    
    // Standard Setter
    public void SetLDate(LDate value)
    {
        lDate = value;
    }
    
    // Get<>AsString()
    public string GetLDateAsString()
    {
        return lDate != null ? lDate.GetLDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (lDate == null)
        {
            lDate = new LDate();
        }
        lDate.SetLDateAsString(value);
    }
    
    // Standard Getter
    public int GetLDateReturnCode()
    {
        return lDateReturnCode;
    }
    
    // Standard Setter
    public void SetLDateReturnCode(int value)
    {
        lDateReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetLDateReturnCodeAsString()
    {
        return lDateReturnCode.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetLDateReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) lDateReturnCode = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLDate(string value)
    {
        lDate.SetLDateAsString(value);
    }
    // Nested Class: LDate
    public class LDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: LDd, is_external=, is_static_class=False, static_prefix=
        private int lDd =0;
        
        
        
        
        // [DEBUG] Field: LMm, is_external=, is_static_class=False, static_prefix=
        private int lMm =0;
        
        
        
        
        // [DEBUG] Field: LYy, is_external=, is_static_class=False, static_prefix=
        private int lYy =0;
        
        
        
        
    public LDate() {}
    
    public LDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetLDd(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetLMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetLYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetLDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(lDd.ToString().PadLeft(2, '0'));
        result.Append(lMm.ToString().PadLeft(2, '0'));
        result.Append(lYy.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetLDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLDd(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLMm(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLYy(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetLDd()
    {
        return lDd;
    }
    
    // Standard Setter
    public void SetLDd(int value)
    {
        lDd = value;
    }
    
    // Get<>AsString()
    public string GetLDdAsString()
    {
        return lDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetLDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) lDd = parsed;
    }
    
    // Standard Getter
    public int GetLMm()
    {
        return lMm;
    }
    
    // Standard Setter
    public void SetLMm(int value)
    {
        lMm = value;
    }
    
    // Get<>AsString()
    public string GetLMmAsString()
    {
        return lMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetLMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) lMm = parsed;
    }
    
    // Standard Getter
    public int GetLYy()
    {
        return lYy;
    }
    
    // Standard Setter
    public void SetLYy(int value)
    {
        lYy = value;
    }
    
    // Get<>AsString()
    public string GetLYyAsString()
    {
        return lYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetLYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) lYy = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
