using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing WtsTotals Data Structure

public class WtsTotals
{
    private static int _size = 400;
    // [DEBUG] Class: WtsTotals, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WtsSubgpICost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsSubgpICost =0M;
    
    
    
    
    // [DEBUG] Field: WtsSubgpCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsSubgpCost =0M;
    
    
    
    
    // [DEBUG] Field: WtsSubgpProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsSubgpProceeds =0M;
    
    
    
    
    // [DEBUG] Field: WtsSubgpCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsSubgpCgt =0M;
    
    
    
    
    // [DEBUG] Field: WtsSubgpProfit, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsSubgpProfit =0M;
    
    
    
    
    // [DEBUG] Field: WtsGpICost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsGpICost =0M;
    
    
    
    
    // [DEBUG] Field: WtsGpCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsGpCost =0M;
    
    
    
    
    // [DEBUG] Field: WtsGpProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsGpProceeds =0M;
    
    
    
    
    // [DEBUG] Field: WtsGpCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsGpCgt =0M;
    
    
    
    
    // [DEBUG] Field: WtsGpProfit, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsGpProfit =0M;
    
    
    
    
    // [DEBUG] Field: WtsCoICost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsCoICost =0M;
    
    
    
    
    // [DEBUG] Field: WtsCoCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsCoCost =0M;
    
    
    
    
    // [DEBUG] Field: WtsCoProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsCoProceeds =0M;
    
    
    
    
    // [DEBUG] Field: WtsCoCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsCoCgt =0M;
    
    
    
    
    // [DEBUG] Field: WtsCoProfit, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsCoProfit =0M;
    
    
    
    
    // [DEBUG] Field: WtsFdICost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsFdICost =0M;
    
    
    
    
    // [DEBUG] Field: WtsFdCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsFdCost =0M;
    
    
    
    
    // [DEBUG] Field: WtsFdProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsFdProceeds =0M;
    
    
    
    
    // [DEBUG] Field: WtsFdCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsFdCgt =0M;
    
    
    
    
    // [DEBUG] Field: WtsFdProfit, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsFdProfit =0M;
    
    
    
    
    // [DEBUG] Field: WtsAsICost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsAsICost =0M;
    
    
    
    
    // [DEBUG] Field: WtsAsCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsAsCost =0M;
    
    
    
    
    // [DEBUG] Field: WtsAsProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsAsProceeds =0M;
    
    
    
    
    // [DEBUG] Field: WtsAsCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsAsCgt =0M;
    
    
    
    
    // [DEBUG] Field: WtsAsProfit, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsAsProfit =0M;
    
    
    
    
    
    // Serialization methods
    public string GetWtsTotalsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtsSubgpICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsSubgpCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsSubgpProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsSubgpCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsSubgpProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsGpICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsGpCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsGpProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsGpCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsGpProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsCoICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsCoCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsCoProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsCoCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsCoProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsFdICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsFdCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsFdProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsFdCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsFdProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsAsICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsAsCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsAsProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsAsCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsAsProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWtsTotalsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsSubgpICost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsSubgpCost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsSubgpProceeds(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsSubgpCgt(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsSubgpProfit(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsGpICost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsGpCost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsGpProceeds(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsGpCgt(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsGpProfit(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsCoICost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsCoCost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsCoProceeds(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsCoCgt(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsCoProfit(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsFdICost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsFdCost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsFdProceeds(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsFdCgt(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsFdProfit(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsAsICost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsAsCost(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsAsProceeds(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsAsCgt(parsedDec);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsAsProfit(parsedDec);
        }
        offset += 16;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtsTotalsAsString();
    }
    // Set<>String Override function
    public void SetWtsTotals(string value)
    {
        SetWtsTotalsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public decimal GetWtsSubgpICost()
    {
        return _WtsSubgpICost;
    }
    
    // Standard Setter
    public void SetWtsSubgpICost(decimal value)
    {
        _WtsSubgpICost = value;
    }
    
    // Get<>AsString()
    public string GetWtsSubgpICostAsString()
    {
        return _WtsSubgpICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsSubgpICostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsSubgpICost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsSubgpCost()
    {
        return _WtsSubgpCost;
    }
    
    // Standard Setter
    public void SetWtsSubgpCost(decimal value)
    {
        _WtsSubgpCost = value;
    }
    
    // Get<>AsString()
    public string GetWtsSubgpCostAsString()
    {
        return _WtsSubgpCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsSubgpCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsSubgpCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsSubgpProceeds()
    {
        return _WtsSubgpProceeds;
    }
    
    // Standard Setter
    public void SetWtsSubgpProceeds(decimal value)
    {
        _WtsSubgpProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWtsSubgpProceedsAsString()
    {
        return _WtsSubgpProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsSubgpProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsSubgpProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsSubgpCgt()
    {
        return _WtsSubgpCgt;
    }
    
    // Standard Setter
    public void SetWtsSubgpCgt(decimal value)
    {
        _WtsSubgpCgt = value;
    }
    
    // Get<>AsString()
    public string GetWtsSubgpCgtAsString()
    {
        return _WtsSubgpCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsSubgpCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsSubgpCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsSubgpProfit()
    {
        return _WtsSubgpProfit;
    }
    
    // Standard Setter
    public void SetWtsSubgpProfit(decimal value)
    {
        _WtsSubgpProfit = value;
    }
    
    // Get<>AsString()
    public string GetWtsSubgpProfitAsString()
    {
        return _WtsSubgpProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsSubgpProfitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsSubgpProfit = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsGpICost()
    {
        return _WtsGpICost;
    }
    
    // Standard Setter
    public void SetWtsGpICost(decimal value)
    {
        _WtsGpICost = value;
    }
    
    // Get<>AsString()
    public string GetWtsGpICostAsString()
    {
        return _WtsGpICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsGpICostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsGpICost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsGpCost()
    {
        return _WtsGpCost;
    }
    
    // Standard Setter
    public void SetWtsGpCost(decimal value)
    {
        _WtsGpCost = value;
    }
    
    // Get<>AsString()
    public string GetWtsGpCostAsString()
    {
        return _WtsGpCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsGpCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsGpCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsGpProceeds()
    {
        return _WtsGpProceeds;
    }
    
    // Standard Setter
    public void SetWtsGpProceeds(decimal value)
    {
        _WtsGpProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWtsGpProceedsAsString()
    {
        return _WtsGpProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsGpProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsGpProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsGpCgt()
    {
        return _WtsGpCgt;
    }
    
    // Standard Setter
    public void SetWtsGpCgt(decimal value)
    {
        _WtsGpCgt = value;
    }
    
    // Get<>AsString()
    public string GetWtsGpCgtAsString()
    {
        return _WtsGpCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsGpCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsGpCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsGpProfit()
    {
        return _WtsGpProfit;
    }
    
    // Standard Setter
    public void SetWtsGpProfit(decimal value)
    {
        _WtsGpProfit = value;
    }
    
    // Get<>AsString()
    public string GetWtsGpProfitAsString()
    {
        return _WtsGpProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsGpProfitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsGpProfit = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsCoICost()
    {
        return _WtsCoICost;
    }
    
    // Standard Setter
    public void SetWtsCoICost(decimal value)
    {
        _WtsCoICost = value;
    }
    
    // Get<>AsString()
    public string GetWtsCoICostAsString()
    {
        return _WtsCoICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsCoICostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsCoICost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsCoCost()
    {
        return _WtsCoCost;
    }
    
    // Standard Setter
    public void SetWtsCoCost(decimal value)
    {
        _WtsCoCost = value;
    }
    
    // Get<>AsString()
    public string GetWtsCoCostAsString()
    {
        return _WtsCoCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsCoCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsCoCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsCoProceeds()
    {
        return _WtsCoProceeds;
    }
    
    // Standard Setter
    public void SetWtsCoProceeds(decimal value)
    {
        _WtsCoProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWtsCoProceedsAsString()
    {
        return _WtsCoProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsCoProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsCoProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsCoCgt()
    {
        return _WtsCoCgt;
    }
    
    // Standard Setter
    public void SetWtsCoCgt(decimal value)
    {
        _WtsCoCgt = value;
    }
    
    // Get<>AsString()
    public string GetWtsCoCgtAsString()
    {
        return _WtsCoCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsCoCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsCoCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsCoProfit()
    {
        return _WtsCoProfit;
    }
    
    // Standard Setter
    public void SetWtsCoProfit(decimal value)
    {
        _WtsCoProfit = value;
    }
    
    // Get<>AsString()
    public string GetWtsCoProfitAsString()
    {
        return _WtsCoProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsCoProfitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsCoProfit = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsFdICost()
    {
        return _WtsFdICost;
    }
    
    // Standard Setter
    public void SetWtsFdICost(decimal value)
    {
        _WtsFdICost = value;
    }
    
    // Get<>AsString()
    public string GetWtsFdICostAsString()
    {
        return _WtsFdICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsFdICostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsFdICost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsFdCost()
    {
        return _WtsFdCost;
    }
    
    // Standard Setter
    public void SetWtsFdCost(decimal value)
    {
        _WtsFdCost = value;
    }
    
    // Get<>AsString()
    public string GetWtsFdCostAsString()
    {
        return _WtsFdCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsFdCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsFdCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsFdProceeds()
    {
        return _WtsFdProceeds;
    }
    
    // Standard Setter
    public void SetWtsFdProceeds(decimal value)
    {
        _WtsFdProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWtsFdProceedsAsString()
    {
        return _WtsFdProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsFdProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsFdProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsFdCgt()
    {
        return _WtsFdCgt;
    }
    
    // Standard Setter
    public void SetWtsFdCgt(decimal value)
    {
        _WtsFdCgt = value;
    }
    
    // Get<>AsString()
    public string GetWtsFdCgtAsString()
    {
        return _WtsFdCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsFdCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsFdCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsFdProfit()
    {
        return _WtsFdProfit;
    }
    
    // Standard Setter
    public void SetWtsFdProfit(decimal value)
    {
        _WtsFdProfit = value;
    }
    
    // Get<>AsString()
    public string GetWtsFdProfitAsString()
    {
        return _WtsFdProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsFdProfitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsFdProfit = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsAsICost()
    {
        return _WtsAsICost;
    }
    
    // Standard Setter
    public void SetWtsAsICost(decimal value)
    {
        _WtsAsICost = value;
    }
    
    // Get<>AsString()
    public string GetWtsAsICostAsString()
    {
        return _WtsAsICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsAsICostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsAsICost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsAsCost()
    {
        return _WtsAsCost;
    }
    
    // Standard Setter
    public void SetWtsAsCost(decimal value)
    {
        _WtsAsCost = value;
    }
    
    // Get<>AsString()
    public string GetWtsAsCostAsString()
    {
        return _WtsAsCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsAsCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsAsCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsAsProceeds()
    {
        return _WtsAsProceeds;
    }
    
    // Standard Setter
    public void SetWtsAsProceeds(decimal value)
    {
        _WtsAsProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWtsAsProceedsAsString()
    {
        return _WtsAsProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsAsProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsAsProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsAsCgt()
    {
        return _WtsAsCgt;
    }
    
    // Standard Setter
    public void SetWtsAsCgt(decimal value)
    {
        _WtsAsCgt = value;
    }
    
    // Get<>AsString()
    public string GetWtsAsCgtAsString()
    {
        return _WtsAsCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsAsCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsAsCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsAsProfit()
    {
        return _WtsAsProfit;
    }
    
    // Standard Setter
    public void SetWtsAsProfit(decimal value)
    {
        _WtsAsProfit = value;
    }
    
    // Get<>AsString()
    public string GetWtsAsProfitAsString()
    {
        return _WtsAsProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsAsProfitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsAsProfit = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
