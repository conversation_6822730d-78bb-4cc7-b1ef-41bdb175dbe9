using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing CgtlogLinkageArea1 Data Structure

public class CgtlogLinkageArea1
{
    private static int _size = 21;
    // [DEBUG] Class: CgtlogLinkageArea1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LLogFileName, is_external=, is_static_class=False, static_prefix=
    private string _LLogFileName ="";
    
    
    
    
    // [DEBUG] Field: LLogAction, is_external=, is_static_class=False, static_prefix=
    private string _LLogAction ="";
    
    
    
    
    // [DEBUG] Field: LLogStatus, is_external=, is_static_class=False, static_prefix=
    private string _LLogStatus ="";
    
    
    
    
    // [DEBUG] Field: LLogProgram, is_external=, is_static_class=False, static_prefix=
    private string _LLogProgram ="";
    
    
    
    
    
    // Serialization methods
    public string GetCgtlogLinkageArea1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LLogFileName.PadRight(8));
        result.Append(_LLogAction.PadRight(3));
        result.Append(_LLogStatus.PadRight(2));
        result.Append(_LLogProgram.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetCgtlogLinkageArea1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetLLogFileName(extracted);
        }
        offset += 8;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetLLogAction(extracted);
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetLLogStatus(extracted);
        }
        offset += 2;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetLLogProgram(extracted);
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtlogLinkageArea1AsString();
    }
    // Set<>String Override function
    public void SetCgtlogLinkageArea1(string value)
    {
        SetCgtlogLinkageArea1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLLogFileName()
    {
        return _LLogFileName;
    }
    
    // Standard Setter
    public void SetLLogFileName(string value)
    {
        _LLogFileName = value;
    }
    
    // Get<>AsString()
    public string GetLLogFileNameAsString()
    {
        return _LLogFileName.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetLLogFileNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LLogFileName = value;
    }
    
    // Standard Getter
    public string GetLLogAction()
    {
        return _LLogAction;
    }
    
    // Standard Setter
    public void SetLLogAction(string value)
    {
        _LLogAction = value;
    }
    
    // Get<>AsString()
    public string GetLLogActionAsString()
    {
        return _LLogAction.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetLLogActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LLogAction = value;
    }
    
    // Standard Getter
    public string GetLLogStatus()
    {
        return _LLogStatus;
    }
    
    // Standard Setter
    public void SetLLogStatus(string value)
    {
        _LLogStatus = value;
    }
    
    // Get<>AsString()
    public string GetLLogStatusAsString()
    {
        return _LLogStatus.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetLLogStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LLogStatus = value;
    }
    
    // Standard Getter
    public string GetLLogProgram()
    {
        return _LLogProgram;
    }
    
    // Standard Setter
    public void SetLLogProgram(string value)
    {
        _LLogProgram = value;
    }
    
    // Get<>AsString()
    public string GetLLogProgramAsString()
    {
        return _LLogProgram.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetLLogProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LLogProgram = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}