using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing T000Totals Data Structure

public class T000Totals
{
    private static int _size = 284;
    // [DEBUG] Class: T000Totals, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: T001Control, is_external=, is_static_class=False, static_prefix=
    private string _T001Control ="";
    
    
    
    
    // [DEBUG] Field: T001StandardTotals, is_external=, is_static_class=False, static_prefix=
    private T001StandardTotals _T001StandardTotals = new T001StandardTotals();
    
    
    
    
    // [DEBUG] Field: T001ThreadneedleTotals, is_external=, is_static_class=False, static_prefix=
    private T001ThreadneedleTotals _T001ThreadneedleTotals = new T001ThreadneedleTotals();
    
    
    
    
    // [DEBUG] Field: T007Proceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _T007Proceeds =0;
    
    
    
    
    // [DEBUG] Field: T009Cgt, is_external=, is_static_class=False, static_prefix=
    private decimal _T009Cgt =0;
    
    
    
    
    // [DEBUG] Field: T010, is_external=, is_static_class=False, static_prefix=
    private string _T010 ="";
    
    
    
    
    // [DEBUG] Field: T011Profit, is_external=, is_static_class=False, static_prefix=
    private decimal _T011Profit =0;
    
    
    
    
    // [DEBUG] Field: T012, is_external=, is_static_class=False, static_prefix=
    private string _T012 ="";
    
    
    
    
    
    // Serialization methods
    public string GetT000TotalsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_T001Control.PadRight(1));
        result.Append(_T001StandardTotals.GetT001StandardTotalsAsString());
        result.Append(_T001ThreadneedleTotals.GetT001ThreadneedleTotalsAsString());
        result.Append(_T007Proceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_T009Cgt.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_T010.PadRight(1));
        result.Append(_T011Profit.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_T012.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetT000TotalsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetT001Control(extracted);
        }
        offset += 1;
        if (offset + 124 <= data.Length)
        {
            _T001StandardTotals.SetT001StandardTotalsAsString(data.Substring(offset, 124));
        }
        else
        {
            _T001StandardTotals.SetT001StandardTotalsAsString(data.Substring(offset));
        }
        offset += 124;
        if (offset + 115 <= data.Length)
        {
            _T001ThreadneedleTotals.SetT001ThreadneedleTotalsAsString(data.Substring(offset, 115));
        }
        else
        {
            _T001ThreadneedleTotals.SetT001ThreadneedleTotalsAsString(data.Substring(offset));
        }
        offset += 115;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetT007Proceeds(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetT009Cgt(parsedDec);
        }
        offset += 14;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetT010(extracted);
        }
        offset += 1;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetT011Profit(parsedDec);
        }
        offset += 14;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetT012(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetT000TotalsAsString();
    }
    // Set<>String Override function
    public void SetT000Totals(string value)
    {
        SetT000TotalsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetT001Control()
    {
        return _T001Control;
    }
    
    // Standard Setter
    public void SetT001Control(string value)
    {
        _T001Control = value;
    }
    
    // Get<>AsString()
    public string GetT001ControlAsString()
    {
        return _T001Control.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetT001ControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T001Control = value;
    }
    
    // Standard Getter
    public T001StandardTotals GetT001StandardTotals()
    {
        return _T001StandardTotals;
    }
    
    // Standard Setter
    public void SetT001StandardTotals(T001StandardTotals value)
    {
        _T001StandardTotals = value;
    }
    
    // Get<>AsString()
    public string GetT001StandardTotalsAsString()
    {
        return _T001StandardTotals != null ? _T001StandardTotals.GetT001StandardTotalsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetT001StandardTotalsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_T001StandardTotals == null)
        {
            _T001StandardTotals = new T001StandardTotals();
        }
        _T001StandardTotals.SetT001StandardTotalsAsString(value);
    }
    
    // Standard Getter
    public T001ThreadneedleTotals GetT001ThreadneedleTotals()
    {
        return _T001ThreadneedleTotals;
    }
    
    // Standard Setter
    public void SetT001ThreadneedleTotals(T001ThreadneedleTotals value)
    {
        _T001ThreadneedleTotals = value;
    }
    
    // Get<>AsString()
    public string GetT001ThreadneedleTotalsAsString()
    {
        return _T001ThreadneedleTotals != null ? _T001ThreadneedleTotals.GetT001ThreadneedleTotalsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetT001ThreadneedleTotalsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_T001ThreadneedleTotals == null)
        {
            _T001ThreadneedleTotals = new T001ThreadneedleTotals();
        }
        _T001ThreadneedleTotals.SetT001ThreadneedleTotalsAsString(value);
    }
    
    // Standard Getter
    public decimal GetT007Proceeds()
    {
        return _T007Proceeds;
    }
    
    // Standard Setter
    public void SetT007Proceeds(decimal value)
    {
        _T007Proceeds = value;
    }
    
    // Get<>AsString()
    public string GetT007ProceedsAsString()
    {
        return _T007Proceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetT007ProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _T007Proceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetT009Cgt()
    {
        return _T009Cgt;
    }
    
    // Standard Setter
    public void SetT009Cgt(decimal value)
    {
        _T009Cgt = value;
    }
    
    // Get<>AsString()
    public string GetT009CgtAsString()
    {
        return _T009Cgt.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetT009CgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _T009Cgt = parsed;
    }
    
    // Standard Getter
    public string GetT010()
    {
        return _T010;
    }
    
    // Standard Setter
    public void SetT010(string value)
    {
        _T010 = value;
    }
    
    // Get<>AsString()
    public string GetT010AsString()
    {
        return _T010.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetT010AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T010 = value;
    }
    
    // Standard Getter
    public decimal GetT011Profit()
    {
        return _T011Profit;
    }
    
    // Standard Setter
    public void SetT011Profit(decimal value)
    {
        _T011Profit = value;
    }
    
    // Get<>AsString()
    public string GetT011ProfitAsString()
    {
        return _T011Profit.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetT011ProfitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _T011Profit = parsed;
    }
    
    // Standard Getter
    public string GetT012()
    {
        return _T012;
    }
    
    // Standard Setter
    public void SetT012(string value)
    {
        _T012 = value;
    }
    
    // Get<>AsString()
    public string GetT012AsString()
    {
        return _T012.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetT012AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T012 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetT001StandardTotals(string value)
    {
        _T001StandardTotals.SetT001StandardTotalsAsString(value);
    }
    // Nested Class: T001StandardTotals
    public class T001StandardTotals
    {
        private static int _size = 124;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler88, is_external=, is_static_class=False, static_prefix=
        private string _Filler88 ="";
        
        
        
        
        // [DEBUG] Field: T002, is_external=, is_static_class=False, static_prefix=
        private string _T002 ="";
        
        
        
        
        // [DEBUG] Field: T003Text, is_external=, is_static_class=False, static_prefix=
        private string _T003Text ="";
        
        
        
        
        // [DEBUG] Field: T004, is_external=, is_static_class=False, static_prefix=
        private string _T004 ="";
        
        
        
        
        // [DEBUG] Field: T005TotalsText, is_external=, is_static_class=False, static_prefix=
        private string _T005TotalsText ="";
        
        
        
        
        // [DEBUG] Field: T006, is_external=, is_static_class=False, static_prefix=
        private string _T006 ="";
        
        
        
        
    public T001StandardTotals() {}
    
    public T001StandardTotals(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller88(data.Substring(offset, 1).Trim());
        offset += 1;
        SetT002(data.Substring(offset, 75).Trim());
        offset += 75;
        SetT003Text(data.Substring(offset, 40).Trim());
        offset += 40;
        SetT004(data.Substring(offset, 1).Trim());
        offset += 1;
        SetT005TotalsText(data.Substring(offset, 6).Trim());
        offset += 6;
        SetT006(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetT001StandardTotalsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler88.PadRight(1));
        result.Append(_T002.PadRight(75));
        result.Append(_T003Text.PadRight(40));
        result.Append(_T004.PadRight(1));
        result.Append(_T005TotalsText.PadRight(6));
        result.Append(_T006.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetT001StandardTotalsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller88(extracted);
        }
        offset += 1;
        if (offset + 75 <= data.Length)
        {
            string extracted = data.Substring(offset, 75).Trim();
            SetT002(extracted);
        }
        offset += 75;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetT003Text(extracted);
        }
        offset += 40;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetT004(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetT005TotalsText(extracted);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetT006(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller88()
    {
        return _Filler88;
    }
    
    // Standard Setter
    public void SetFiller88(string value)
    {
        _Filler88 = value;
    }
    
    // Get<>AsString()
    public string GetFiller88AsString()
    {
        return _Filler88.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller88AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler88 = value;
    }
    
    // Standard Getter
    public string GetT002()
    {
        return _T002;
    }
    
    // Standard Setter
    public void SetT002(string value)
    {
        _T002 = value;
    }
    
    // Get<>AsString()
    public string GetT002AsString()
    {
        return _T002.PadRight(75);
    }
    
    // Set<>AsString()
    public void SetT002AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T002 = value;
    }
    
    // Standard Getter
    public string GetT003Text()
    {
        return _T003Text;
    }
    
    // Standard Setter
    public void SetT003Text(string value)
    {
        _T003Text = value;
    }
    
    // Get<>AsString()
    public string GetT003TextAsString()
    {
        return _T003Text.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetT003TextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T003Text = value;
    }
    
    // Standard Getter
    public string GetT004()
    {
        return _T004;
    }
    
    // Standard Setter
    public void SetT004(string value)
    {
        _T004 = value;
    }
    
    // Get<>AsString()
    public string GetT004AsString()
    {
        return _T004.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetT004AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T004 = value;
    }
    
    // Standard Getter
    public string GetT005TotalsText()
    {
        return _T005TotalsText;
    }
    
    // Standard Setter
    public void SetT005TotalsText(string value)
    {
        _T005TotalsText = value;
    }
    
    // Get<>AsString()
    public string GetT005TotalsTextAsString()
    {
        return _T005TotalsText.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetT005TotalsTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T005TotalsText = value;
    }
    
    // Standard Getter
    public string GetT006()
    {
        return _T006;
    }
    
    // Standard Setter
    public void SetT006(string value)
    {
        _T006 = value;
    }
    
    // Get<>AsString()
    public string GetT006AsString()
    {
        return _T006.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetT006AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _T006 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetT001ThreadneedleTotals(string value)
{
    _T001ThreadneedleTotals.SetT001ThreadneedleTotalsAsString(value);
}
// Nested Class: T001ThreadneedleTotals
public class T001ThreadneedleTotals
{
    private static int _size = 115;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler89, is_external=, is_static_class=False, static_prefix=
    private string _Filler89 ="";
    
    
    
    
    // [DEBUG] Field: Th02, is_external=, is_static_class=False, static_prefix=
    private string _Th02 ="";
    
    
    
    
    // [DEBUG] Field: Th03Text, is_external=, is_static_class=False, static_prefix=
    private string _Th03Text ="";
    
    
    
    
    // [DEBUG] Field: Th04, is_external=, is_static_class=False, static_prefix=
    private string _Th04 ="";
    
    
    
    
    // [DEBUG] Field: Th05TotalsText, is_external=, is_static_class=False, static_prefix=
    private string _Th05TotalsText ="";
    
    
    
    
    // [DEBUG] Field: Th06, is_external=, is_static_class=False, static_prefix=
    private string _Th06 ="";
    
    
    
    
    // [DEBUG] Field: Th13Cost, is_external=, is_static_class=False, static_prefix=
    private decimal _Th13Cost =0;
    
    
    
    
    // [DEBUG] Field: Th13B, is_external=, is_static_class=False, static_prefix=
    private string _Th13B ="";
    
    
    
    
    // [DEBUG] Field: Th14, is_external=, is_static_class=False, static_prefix=
    private string _Th14 ="";
    
    
    
    
    // [DEBUG] Field: Th15IndexedCost, is_external=, is_static_class=False, static_prefix=
    private decimal _Th15IndexedCost =0;
    
    
    
    
    // [DEBUG] Field: Th16, is_external=, is_static_class=False, static_prefix=
    private string _Th16 ="";
    
    
    
    
public T001ThreadneedleTotals() {}

public T001ThreadneedleTotals(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller89(data.Substring(offset, 1).Trim());
    offset += 1;
    SetTh02(data.Substring(offset, 24).Trim());
    offset += 24;
    SetTh03Text(data.Substring(offset, 40).Trim());
    offset += 40;
    SetTh04(data.Substring(offset, 1).Trim());
    offset += 1;
    SetTh05TotalsText(data.Substring(offset, 6).Trim());
    offset += 6;
    SetTh06(data.Substring(offset, 1).Trim());
    offset += 1;
    SetTh13Cost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
    offset += 14;
    SetTh13B(data.Substring(offset, 1).Trim());
    offset += 1;
    SetTh14(data.Substring(offset, 13).Trim());
    offset += 13;
    SetTh15IndexedCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
    offset += 14;
    SetTh16(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetT001ThreadneedleTotalsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler89.PadRight(1));
    result.Append(_Th02.PadRight(24));
    result.Append(_Th03Text.PadRight(40));
    result.Append(_Th04.PadRight(1));
    result.Append(_Th05TotalsText.PadRight(6));
    result.Append(_Th06.PadRight(1));
    result.Append(_Th13Cost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Th13B.PadRight(1));
    result.Append(_Th14.PadRight(13));
    result.Append(_Th15IndexedCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Th16.PadRight(0));
    
    return result.ToString();
}

public void SetT001ThreadneedleTotalsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller89(extracted);
    }
    offset += 1;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetTh02(extracted);
    }
    offset += 24;
    if (offset + 40 <= data.Length)
    {
        string extracted = data.Substring(offset, 40).Trim();
        SetTh03Text(extracted);
    }
    offset += 40;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetTh04(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetTh05TotalsText(extracted);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetTh06(extracted);
    }
    offset += 1;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetTh13Cost(parsedDec);
    }
    offset += 14;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetTh13B(extracted);
    }
    offset += 1;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetTh14(extracted);
    }
    offset += 13;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetTh15IndexedCost(parsedDec);
    }
    offset += 14;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetTh16(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller89()
{
    return _Filler89;
}

// Standard Setter
public void SetFiller89(string value)
{
    _Filler89 = value;
}

// Get<>AsString()
public string GetFiller89AsString()
{
    return _Filler89.PadRight(1);
}

// Set<>AsString()
public void SetFiller89AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler89 = value;
}

// Standard Getter
public string GetTh02()
{
    return _Th02;
}

// Standard Setter
public void SetTh02(string value)
{
    _Th02 = value;
}

// Get<>AsString()
public string GetTh02AsString()
{
    return _Th02.PadRight(24);
}

// Set<>AsString()
public void SetTh02AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th02 = value;
}

// Standard Getter
public string GetTh03Text()
{
    return _Th03Text;
}

// Standard Setter
public void SetTh03Text(string value)
{
    _Th03Text = value;
}

// Get<>AsString()
public string GetTh03TextAsString()
{
    return _Th03Text.PadRight(40);
}

// Set<>AsString()
public void SetTh03TextAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th03Text = value;
}

// Standard Getter
public string GetTh04()
{
    return _Th04;
}

// Standard Setter
public void SetTh04(string value)
{
    _Th04 = value;
}

// Get<>AsString()
public string GetTh04AsString()
{
    return _Th04.PadRight(1);
}

// Set<>AsString()
public void SetTh04AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th04 = value;
}

// Standard Getter
public string GetTh05TotalsText()
{
    return _Th05TotalsText;
}

// Standard Setter
public void SetTh05TotalsText(string value)
{
    _Th05TotalsText = value;
}

// Get<>AsString()
public string GetTh05TotalsTextAsString()
{
    return _Th05TotalsText.PadRight(6);
}

// Set<>AsString()
public void SetTh05TotalsTextAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th05TotalsText = value;
}

// Standard Getter
public string GetTh06()
{
    return _Th06;
}

// Standard Setter
public void SetTh06(string value)
{
    _Th06 = value;
}

// Get<>AsString()
public string GetTh06AsString()
{
    return _Th06.PadRight(1);
}

// Set<>AsString()
public void SetTh06AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th06 = value;
}

// Standard Getter
public decimal GetTh13Cost()
{
    return _Th13Cost;
}

// Standard Setter
public void SetTh13Cost(decimal value)
{
    _Th13Cost = value;
}

// Get<>AsString()
public string GetTh13CostAsString()
{
    return _Th13Cost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetTh13CostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _Th13Cost = parsed;
}

// Standard Getter
public string GetTh13B()
{
    return _Th13B;
}

// Standard Setter
public void SetTh13B(string value)
{
    _Th13B = value;
}

// Get<>AsString()
public string GetTh13BAsString()
{
    return _Th13B.PadRight(1);
}

// Set<>AsString()
public void SetTh13BAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th13B = value;
}

// Standard Getter
public string GetTh14()
{
    return _Th14;
}

// Standard Setter
public void SetTh14(string value)
{
    _Th14 = value;
}

// Get<>AsString()
public string GetTh14AsString()
{
    return _Th14.PadRight(13);
}

// Set<>AsString()
public void SetTh14AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th14 = value;
}

// Standard Getter
public decimal GetTh15IndexedCost()
{
    return _Th15IndexedCost;
}

// Standard Setter
public void SetTh15IndexedCost(decimal value)
{
    _Th15IndexedCost = value;
}

// Get<>AsString()
public string GetTh15IndexedCostAsString()
{
    return _Th15IndexedCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetTh15IndexedCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _Th15IndexedCost = parsed;
}

// Standard Getter
public string GetTh16()
{
    return _Th16;
}

// Standard Setter
public void SetTh16(string value)
{
    _Th16 = value;
}

// Get<>AsString()
public string GetTh16AsString()
{
    return _Th16.PadRight(0);
}

// Set<>AsString()
public void SetTh16AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Th16 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}
