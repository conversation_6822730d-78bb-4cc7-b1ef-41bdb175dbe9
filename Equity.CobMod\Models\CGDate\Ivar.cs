using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdateDTO
{// <Section> Class for Ivar
public class Ivar
{
public Ivar() {}

// Fields in the class


// [DEBUG] Field: LValidDate, is_external=, is_static_class=False, static_prefix=
private LValidDate lValidDate = new LValidDate();




// Getter and Setter methods

// Standard Getter
public LValidDate GetLValidDate()
{
    return lValidDate;
}

// Standard Setter
public void SetLValidDate(LValidDate value)
{
    lValidDate = value;
}

// Get<>AsString()
public string GetLValidDateAsString()
{
    return lValidDate != null ? lValidDate.GetLValidDateAsString() : "";
}

// Set<>AsString()
public void SetLValidDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (lValidDate == null)
    {
        lValidDate = new LValidDate();
    }
    lValidDate.SetLValidDateAsString(value);
}



}}
