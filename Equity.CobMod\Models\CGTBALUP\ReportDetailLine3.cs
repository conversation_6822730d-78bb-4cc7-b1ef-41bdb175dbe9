using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportDetailLine3 Data Structure

public class ReportDetailLine3
{
    private static int _size = 125;
    // [DEBUG] Class: ReportDetailLine3, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler161, is_external=, is_static_class=False, static_prefix=
    private string _Filler161 ="";
    
    
    
    
    // [DEBUG] Field: Filler162, is_external=, is_static_class=False, static_prefix=
    private string _Filler162 ="";
    
    
    
    
    // [DEBUG] Field: ReportLit, is_external=, is_static_class=False, static_prefix=
    private string[] _ReportLit = new string[4];
    
    
    
    
    
    // Serialization methods
    public string GetReportDetailLine3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler161.PadRight(6));
        result.Append(_Filler162.PadRight(11));
        for (int i = 0; i < 4; i++)
        {
            result.Append(_ReportLit[i].PadRight(27));
        }
        
        return result.ToString();
    }
    
    public void SetReportDetailLine3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetFiller161(extracted);
        }
        offset += 6;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetFiller162(extracted);
        }
        offset += 11;
        for (int i = 0; i < 4; i++)
        {
            if (offset + 27 > data.Length) break;
            string val = data.Substring(offset, 27);
            
            _ReportLit[i] = val.Trim();
            offset += 27;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportDetailLine3AsString();
    }
    // Set<>String Override function
    public void SetReportDetailLine3(string value)
    {
        SetReportDetailLine3AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller161()
    {
        return _Filler161;
    }
    
    // Standard Setter
    public void SetFiller161(string value)
    {
        _Filler161 = value;
    }
    
    // Get<>AsString()
    public string GetFiller161AsString()
    {
        return _Filler161.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetFiller161AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler161 = value;
    }
    
    // Standard Getter
    public string GetFiller162()
    {
        return _Filler162;
    }
    
    // Standard Setter
    public void SetFiller162(string value)
    {
        _Filler162 = value;
    }
    
    // Get<>AsString()
    public string GetFiller162AsString()
    {
        return _Filler162.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetFiller162AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler162 = value;
    }
    
    // Array Accessors for ReportLit
    public string GetReportLitAt(int index)
    {
        return _ReportLit[index];
    }
    
    public void SetReportLitAt(int index, string value)
    {
        _ReportLit[index] = value;
    }
    
    public string GetReportLitAsStringAt(int index)
    {
        return _ReportLit[index].PadRight(27);
    }
    
    public void SetReportLitAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _ReportLit[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetReportLit()
    {
        return _ReportLit != null && _ReportLit.Length > 0
        ? _ReportLit[0]
        : default(string);
    }
    
    public void SetReportLit(string value)
    {
        if (_ReportLit == null || _ReportLit.Length == 0)
        _ReportLit = new string[1];
        _ReportLit[0] = value;
    }
    
    public string GetReportLitAsString()
    {
        return _ReportLit != null && _ReportLit.Length > 0
        ? _ReportLit[0].ToString()
        : string.Empty;
    }
    
    public void SetReportLitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_ReportLit == null || _ReportLit.Length == 0)
        _ReportLit = new string[1];
        
        _ReportLit[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}