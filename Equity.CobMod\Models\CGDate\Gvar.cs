using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdateDTO
{// <Section> Class for Gvar
public class Gvar
{
public Gvar() {}

// Fields in the class


// [DEBUG] Field: WsProramId, is_external=, is_static_class=False, static_prefix=
private string wsProramId ="CGTDATE";




// [DEBUG] Field: Ws2MonthTable, is_external=, is_static_class=False, static_prefix=
private Ws2MonthTable ws2MonthTable = new Ws2MonthTable();




// [DEBUG] Field: Ws3WorkArea, is_external=, is_static_class=False, static_prefix=
private Ws3WorkArea ws3WorkArea = new Ws3WorkArea();




// [DEBUG] Field: Ws4DisplayScreen, is_external=, is_static_class=False, static_prefix=
private Ws4DisplayScreen ws4DisplayScreen = new Ws4DisplayScreen();




// Getter and Setter methods

// Standard Getter
public string GetWsProramId()
{
    return wsProramId;
}

// Standard Setter
public void SetWsProramId(string value)
{
    wsProramId = value;
}

// Get<>AsString()
public string GetWsProramIdAsString()
{
    return wsProramId.PadRight(15);
}

// Set<>AsString()
public void SetWsProramIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    wsProramId = value;
}

// Standard Getter
public Ws2MonthTable GetWs2MonthTable()
{
    return ws2MonthTable;
}

// Standard Setter
public void SetWs2MonthTable(Ws2MonthTable value)
{
    ws2MonthTable = value;
}

// Get<>AsString()
public string GetWs2MonthTableAsString()
{
    return ws2MonthTable != null ? ws2MonthTable.GetWs2MonthTableAsString() : "";
}

// Set<>AsString()
public void SetWs2MonthTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (ws2MonthTable == null)
    {
        ws2MonthTable = new Ws2MonthTable();
    }
    ws2MonthTable.SetWs2MonthTableAsString(value);
}

// Standard Getter
public Ws3WorkArea GetWs3WorkArea()
{
    return ws3WorkArea;
}

// Standard Setter
public void SetWs3WorkArea(Ws3WorkArea value)
{
    ws3WorkArea = value;
}

// Get<>AsString()
public string GetWs3WorkAreaAsString()
{
    return ws3WorkArea != null ? ws3WorkArea.GetWs3WorkAreaAsString() : "";
}

// Set<>AsString()
public void SetWs3WorkAreaAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (ws3WorkArea == null)
    {
        ws3WorkArea = new Ws3WorkArea();
    }
    ws3WorkArea.SetWs3WorkAreaAsString(value);
}

// Standard Getter
public Ws4DisplayScreen GetWs4DisplayScreen()
{
    return ws4DisplayScreen;
}

// Standard Setter
public void SetWs4DisplayScreen(Ws4DisplayScreen value)
{
    ws4DisplayScreen = value;
}

// Get<>AsString()
public string GetWs4DisplayScreenAsString()
{
    return ws4DisplayScreen != null ? ws4DisplayScreen.GetWs4DisplayScreenAsString() : "";
}

// Set<>AsString()
public void SetWs4DisplayScreenAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (ws4DisplayScreen == null)
    {
        ws4DisplayScreen = new Ws4DisplayScreen();
    }
    ws4DisplayScreen.SetWs4DisplayScreenAsString(value);
}



}}
