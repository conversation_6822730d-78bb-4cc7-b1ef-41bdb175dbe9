using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdateDTO
{// DTO class representing Ws3WorkArea Data Structure

public class Ws3WorkArea
{
    private static int _size = 5;
    // [DEBUG] Class: Ws3WorkArea, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Ws3TempYear, is_external=, is_static_class=False, static_prefix=
    private int ws3TempYear =0;
    
    
    
    
    // [DEBUG] Field: Ws3Rem, is_external=, is_static_class=False, static_prefix=
    private int ws3Rem =0;
    
    
    
    
    
    // Serialization methods
    public string GetWs3WorkAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(ws3TempYear.ToString().PadLeft(4, '0'));
        result.Append(ws3Rem.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetWs3WorkAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWs3TempYear(parsedInt);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWs3Rem(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWs3WorkAreaAsString();
    }
    // Set<>String Override function
    public void SetWs3WorkArea(string value)
    {
        SetWs3WorkAreaAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWs3TempYear()
    {
        return ws3TempYear;
    }
    
    // Standard Setter
    public void SetWs3TempYear(int value)
    {
        ws3TempYear = value;
    }
    
    // Get<>AsString()
    public string GetWs3TempYearAsString()
    {
        return ws3TempYear.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWs3TempYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) ws3TempYear = parsed;
    }
    
    // Standard Getter
    public int GetWs3Rem()
    {
        return ws3Rem;
    }
    
    // Standard Setter
    public void SetWs3Rem(int value)
    {
        ws3Rem = value;
    }
    
    // Get<>AsString()
    public string GetWs3RemAsString()
    {
        return ws3Rem.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWs3RemAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) ws3Rem = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
