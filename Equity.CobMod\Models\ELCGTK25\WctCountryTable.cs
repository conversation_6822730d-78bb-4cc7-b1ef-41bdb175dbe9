using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing WctCountryTable Data Structure

public class WctCountryTable
{
    private static int _size = 43000;
    // [DEBUG] Class: WctCountryTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WctEntry, is_external=, is_static_class=False, static_prefix=
    private WctEntry[] _WctEntry = new WctEntry[1000];
    
    public void InitializeWctEntryArray()
    {
        for (int i = 0; i < 1000; i++)
        {
            _WctEntry[i] = new WctEntry();
        }
    }
    
    
    
    
    // Serialization methods
    public string GetWctCountryTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 1000; i++)
        {
            result.Append(_WctEntry[i].GetWctEntryAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWctCountryTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 1000; i++)
        {
            if (offset + 43 > data.Length) break;
            string val = data.Substring(offset, 43);
            
            _WctEntry[i].SetWctEntryAsString(val);
            offset += 43;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWctCountryTableAsString();
    }
    // Set<>String Override function
    public void SetWctCountryTable(string value)
    {
        SetWctCountryTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for WctEntry
    public WctEntry GetWctEntryAt(int index)
    {
        return _WctEntry[index];
    }
    
    public void SetWctEntryAt(int index, WctEntry value)
    {
        _WctEntry[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WctEntry GetWctEntry()
    {
        return _WctEntry != null && _WctEntry.Length > 0
        ? _WctEntry[0]
        : new WctEntry();
    }
    
    public void SetWctEntry(WctEntry value)
    {
        if (_WctEntry == null || _WctEntry.Length == 0)
        _WctEntry = new WctEntry[1];
        _WctEntry[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWctEntry(string value)
    {
        if (!string.IsNullOrEmpty(value) && value.Length < WctEntry.GetSize() * _WctEntry.Length)
        {
            value = value.PadRight(WctEntry.GetSize() * _WctEntry.Length);
        }
        
        int offset = 0;
        for (int i = 0; i < _WctEntry.Length; i++)
        {
            if (offset + WctEntry.GetSize() > value.Length) break;
            string chunk = value.Substring(offset, WctEntry.GetSize());
            _WctEntry[i].SetWctEntryAsString(chunk);
            offset += WctEntry.GetSize();
        }
    }
    // Nested Class: WctEntry
    public class WctEntry
    {
        private static int _size = 43;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WctCountry, is_external=, is_static_class=False, static_prefix=
        private string _WctCountry ="";
        
        
        
        
        // [DEBUG] Field: WctCountryName, is_external=, is_static_class=False, static_prefix=
        private string _WctCountryName ="";
        
        
        
        
    public WctEntry() {}
    
    public WctEntry(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWctCountry(data.Substring(offset, 3).Trim());
        offset += 3;
        SetWctCountryName(data.Substring(offset, 40).Trim());
        offset += 40;
        
    }
    
    // Serialization methods
    public string GetWctEntryAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WctCountry.PadRight(3));
        result.Append(_WctCountryName.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetWctEntryAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWctCountry(extracted);
        }
        offset += 3;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWctCountryName(extracted);
        }
        offset += 40;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWctCountry()
    {
        return _WctCountry;
    }
    
    // Standard Setter
    public void SetWctCountry(string value)
    {
        _WctCountry = value;
    }
    
    // Get<>AsString()
    public string GetWctCountryAsString()
    {
        return _WctCountry.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWctCountryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WctCountry = value;
    }
    
    // Standard Getter
    public string GetWctCountryName()
    {
        return _WctCountryName;
    }
    
    // Standard Setter
    public void SetWctCountryName(string value)
    {
        _WctCountryName = value;
    }
    
    // Get<>AsString()
    public string GetWctCountryNameAsString()
    {
        return _WctCountryName.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWctCountryNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WctCountryName = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
