using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdateDTO
{// DTO class representing Ws4DisplayScreen Data Structure

public class Ws4DisplayScreen
{
    private static int _size = 60;
    // [DEBUG] Class: Ws4DisplayScreen, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Ws41, is_external=, is_static_class=False, static_prefix=
    private string ws41 =" ** ERROR - ";
    
    
    
    
    // [DEBUG] Field: Ws42, is_external=, is_static_class=False, static_prefix=
    private string ws42 ="INVALID DATE - ";
    
    
    
    
    // [DEBUG] Field: Ws4ErrMess, is_external=, is_static_class=False, static_prefix=
    private string ws4ErrMess ="";
    
    
    
    
    // [DEBUG] Field: Ws4Accept, is_external=, is_static_class=False, static_prefix=
    private string ws4Accept ="";
    
    
    
    
    
    // Serialization methods
    public string GetWs4DisplayScreenAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(ws41.PadRight(12));
        result.Append(ws42.PadRight(15));
        result.Append(ws4ErrMess.PadRight(32));
        result.Append(ws4Accept.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWs4DisplayScreenAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetWs41(extracted);
        }
        offset += 12;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetWs42(extracted);
        }
        offset += 15;
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetWs4ErrMess(extracted);
        }
        offset += 32;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWs4Accept(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWs4DisplayScreenAsString();
    }
    // Set<>String Override function
    public void SetWs4DisplayScreen(string value)
    {
        SetWs4DisplayScreenAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWs41()
    {
        return ws41;
    }
    
    // Standard Setter
    public void SetWs41(string value)
    {
        ws41 = value;
    }
    
    // Get<>AsString()
    public string GetWs41AsString()
    {
        return ws41.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetWs41AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        ws41 = value;
    }
    
    // Standard Getter
    public string GetWs42()
    {
        return ws42;
    }
    
    // Standard Setter
    public void SetWs42(string value)
    {
        ws42 = value;
    }
    
    // Get<>AsString()
    public string GetWs42AsString()
    {
        return ws42.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetWs42AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        ws42 = value;
    }
    
    // Standard Getter
    public string GetWs4ErrMess()
    {
        return ws4ErrMess;
    }
    
    // Standard Setter
    public void SetWs4ErrMess(string value)
    {
        ws4ErrMess = value;
    }
    
    // Get<>AsString()
    public string GetWs4ErrMessAsString()
    {
        return ws4ErrMess.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetWs4ErrMessAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        ws4ErrMess = value;
    }
    
    // Standard Getter
    public string GetWs4Accept()
    {
        return ws4Accept;
    }
    
    // Standard Setter
    public void SetWs4Accept(string value)
    {
        ws4Accept = value;
    }
    
    // Get<>AsString()
    public string GetWs4AcceptAsString()
    {
        return ws4Accept.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWs4AcceptAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        ws4Accept = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
