using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing F000Footing Data Structure

public class F000Footing
{
    private static int _size = 180;
    // [DEBUG] Class: F000Footing, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: F001Control, is_external=, is_static_class=False, static_prefix=
    private string _F001Control ="4";
    
    
    
    
    // [DEBUG] Field: F002, is_external=, is_static_class=False, static_prefix=
    private string _F002 =" ";
    
    
    
    
    // [DEBUG] Field: F003Text, is_external=, is_static_class=False, static_prefix=
    private string _F003Text ="*INDEXATION ALLOWANCE CLAIMED LIMITED TO GROSS GAIN";
    
    
    
    
    // [DEBUG] Field: F004, is_external=, is_static_class=False, static_prefix=
    private string _F004 =" ";
    
    
    
    
    
    // Serialization methods
    public string GetF000FootingAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_F001Control.PadRight(1));
        result.Append(_F002.PadRight(119));
        result.Append(_F003Text.PadRight(51));
        result.Append(_F004.PadRight(9));
        
        return result.ToString();
    }
    
    public void SetF000FootingAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetF001Control(extracted);
        }
        offset += 1;
        if (offset + 119 <= data.Length)
        {
            string extracted = data.Substring(offset, 119).Trim();
            SetF002(extracted);
        }
        offset += 119;
        if (offset + 51 <= data.Length)
        {
            string extracted = data.Substring(offset, 51).Trim();
            SetF003Text(extracted);
        }
        offset += 51;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            SetF004(extracted);
        }
        offset += 9;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetF000FootingAsString();
    }
    // Set<>String Override function
    public void SetF000Footing(string value)
    {
        SetF000FootingAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetF001Control()
    {
        return _F001Control;
    }
    
    // Standard Setter
    public void SetF001Control(string value)
    {
        _F001Control = value;
    }
    
    // Get<>AsString()
    public string GetF001ControlAsString()
    {
        return _F001Control.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetF001ControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _F001Control = value;
    }
    
    // Standard Getter
    public string GetF002()
    {
        return _F002;
    }
    
    // Standard Setter
    public void SetF002(string value)
    {
        _F002 = value;
    }
    
    // Get<>AsString()
    public string GetF002AsString()
    {
        return _F002.PadRight(119);
    }
    
    // Set<>AsString()
    public void SetF002AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _F002 = value;
    }
    
    // Standard Getter
    public string GetF003Text()
    {
        return _F003Text;
    }
    
    // Standard Setter
    public void SetF003Text(string value)
    {
        _F003Text = value;
    }
    
    // Get<>AsString()
    public string GetF003TextAsString()
    {
        return _F003Text.PadRight(51);
    }
    
    // Set<>AsString()
    public void SetF003TextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _F003Text = value;
    }
    
    // Standard Getter
    public string GetF004()
    {
        return _F004;
    }
    
    // Standard Setter
    public void SetF004(string value)
    {
        _F004 = value;
    }
    
    // Get<>AsString()
    public string GetF004AsString()
    {
        return _F004.PadRight(9);
    }
    
    // Set<>AsString()
    public void SetF004AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _F004 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
