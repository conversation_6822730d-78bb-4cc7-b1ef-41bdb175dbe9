using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.CgtfilesDTO;


namespace Equity.CobMod.FIleHandlers
{
    // Cgtfiles Class Definition

    //Cgtfiles Class Constructor
    public class Cgtfiles
    {
        // Declare Cgtfiles Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
        }

        public void SetStart(object input)
        {
            //sice Setstart is not implimented i have created a place holder method 
        }

        public void SetKey(object input)
        {
            //sice Setstart is not implimented i have created a place holder method 
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// Cgtfilescontrol
        /// </summary>
        /// <remarks>
        /// Additional information about Cgtfilescontrol
        /// </remarks>
        public void Cgtfilescontrol(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // PERFORM   A-INITIALISE.
            gvar.SetAInitialise(true);
            ApplyInitialize(fvar, gvar, ivar); // PERFORM A-INITIALISE

            // PERFORM   Z5-RETRIEVE-CACHED-SCHED-DATA
            gvar.SetZ5RetrieveCachedSchedData(true);
            ApplyRetrieveCachedSchedData(fvar, gvar, ivar); // PERFORM Z5-RETRIEVE-CACHED-SCHED-DATA

            if (gvar.GetRecordRetrieved())
            {
                // GO   TO   CGTFILES-CONTROL-EXIT
                gvar.SetCgtfilesControlExit(true);
                return;
            }

            // EVALUATE   L-FILE-ACTION
            string fileAction = ivar.GetCgtfilesLinkage().GetLFileAction();
            switch (fileAction)
            {
                case "OPEN-INPUT":
                    // PERFORM   B-OPEN-INPUT
                    ApplyOpenInput(fvar, gvar, ivar); // PERFORM B-OPEN-INPUT
                    break;
                case "OPEN-OUTPUT":
                    // PERFORM   C-OPEN-OUTPUT
                    ApplyOpenOutput(fvar, gvar, ivar); // PERFORM C-OPEN-OUTPUT
                    break;
                case "OPEN-I-O":
                    // PERFORM   D-OPEN-I-O
                    ApplyOpenIO(fvar, gvar, ivar); // PERFORM D-OPEN-I-O
                    break;
                case "OPEN-EXTEND":
                    // PERFORM   E-OPEN-EXTEND
                    ApplyOpenExtend(fvar, gvar, ivar); // PERFORM E-OPEN-EXTEND
                    break;
                case "START-EQUAL":
                    // PERFORM   F-START-EQUAL
                    ApplyStartEqual(fvar, gvar, ivar); // PERFORM F-START-EQUAL
                    break;
                case "START-NOT-LESS-THAN":
                    // PERFORM   G-START-NOT-LESS-THAN
                    ApplyStartNotLessThan(fvar, gvar, ivar); // PERFORM G-START-NOT-LESS-THAN
                    break;
                case "START-GREATER-THAN":
                    // PERFORM   H-START-GREATER-THAN
                    ApplyStartGreaterThan(fvar, gvar, ivar); // PERFORM H-START-GREATER-THAN
                    break;
                case "START-GT-INVERSE-KEY":
                    // PERFORM   I-START-GT-INVERSE-KEY
                    ApplyStartGtInverseKey(fvar, gvar, ivar); // PERFORM I-START-GT-INVERSE-KEY
                    break;
                case "START-LESS-THAN":
                    // PERFORM   J-START-LESS-THAN
                    ApplyStartLessThan(fvar, gvar, ivar); // PERFORM J-START-LESS-THAN
                    break;
                case "START-NOT-GREATER-THAN":
                    // PERFORM   K-START-NOT-GREATER-THAN
                    ApplyStartNotGreaterThan(fvar, gvar, ivar); // PERFORM K-START-NOT-GREATER-THAN
                    break;
                case "READ-NEXT":
                    // PERFORM   L-READ-NEXT
                    ApplyReadNext(fvar, gvar, ivar); // PERFORM L-READ-NEXT
                    break;
                case "READ-NEXT-WITH-LOCK":
                    // PERFORM   M-READ-NEXT-WITH-LOCK
                    ApplyReadNextWithLock(fvar, gvar, ivar); // PERFORM M-READ-NEXT-WITH-LOCK
                    break;
                case "READ-RECORD":
                    // PERFORM   N-READ
                    ApplyRead(fvar, gvar, ivar); // PERFORM N-READ
                    break;
                case "READ-WITH-LOCK":
                    // PERFORM   O-READ-WITH-LOCK
                    ApplyReadWithLock(fvar, gvar, ivar); // PERFORM O-READ-WITH-LOCK
                    break;
                case "READ-ON-KEY2":
                    // PERFORM   P-READ-ON-KEY2
                    ApplyReadOnKey2(fvar, gvar, ivar); // PERFORM P-READ-ON-KEY2
                    break;
                case "READ-ON-KEY3":
                    // PERFORM   PA-READ-ON-KEY3
                    ApplyReadOnKey3(fvar, gvar, ivar); // PERFORM PA-READ-ON-KEY3
                    break;
                case "WRITE-RECORD":
                    // PERFORM   Q-WRITE
                    ApplyWrite(fvar, gvar, ivar); // PERFORM Q-WRITE
                    break;
                case "REWRITE-RECORD":
                    // PERFORM   R-REWRITE
                    ApplyRewrite(fvar, gvar, ivar); // PERFORM R-REWRITE
                    break;
                case "DELETE-RECORD":
                    // PERFORM   S-DELETE
                    ApplyDelete(fvar, gvar, ivar); // PERFORM S-DELETE
                    break;
                case "CLOSE-FILE":
                    // PERFORM   T-CLOSE
                    ApplyClose(fvar, gvar, ivar); // PERFORM T-CLOSE
                    break;
                case "CLOSE-ALL-FILES":
                    // PERFORM   Z3-CLOSE-ALL-FILES
                    ApplyCloseAllFiles(fvar, gvar, ivar); // PERFORM Z3-CLOSE-ALL-FILES
                    break;
                case "UNLOCK-RECORD":
                    // PERFORM   U-UNLOCK
                    ApplyUnlock(fvar, gvar, ivar); // PERFORM U-UNLOCK
                    break;
                case "START-NOT-LESS-THAN-KEY2":
                    // PERFORM   V-START-NOT-LESS-THAN-KEY2
                    ApplyStartNotLessThanKey2(fvar, gvar, ivar); // PERFORM V-START-NOT-LESS-THAN-KEY2
                    break;
                case "START-NOT-LESS-THAN-KEY3":
                    // PERFORM   VA-START-NOT-LESS-THAN-KEY3
                    ApplyStartNotLessThanKey3(fvar, gvar, ivar); // PERFORM VA-START-NOT-LESS-THAN-KEY3
                    break;
                case "OPEN-INPUT-REPORT":
                    // PERFORM   W-OPEN-INPUT-REPORT
                    ApplyOpenInputReport(fvar, gvar, ivar); // PERFORM W-OPEN-INPUT-REPORT
                    fvar.SetD99RecordAsString("");
                    gvar.SetFileStatusAsString("00");
                    // PERFORM   X-READ-REPORT
                    ApplyReadReport(fvar, gvar, ivar); // PERFORM X-READ-REPORT
                    while (fvar.GetD99Record() != "" && gvar.GetFileStatus() != "10")
                    {
                        // PERFORM   X-READ-REPORT
                        ApplyReadReport(fvar, gvar, ivar); // PERFORM X-READ-REPORT
                    }
                    break;
                case "CLOSE-REPORT":
                    // PERFORM   Y-CLOSE-REPORT
                    ApplyCloseReport(fvar, gvar, ivar); // PERFORM Y-CLOSE-REPORT
                    break;
                default:
                    // MOVE   'IA'   TO   FILE-STATUS
                    gvar.SetFileStatusAsString("IA");
                    break;
            }

            if (gvar.GetWStoreAction() != "")
            {
                // MOVE   W-STORE-ACTION   TO   L-FILE-ACTION
                ivar.GetCgtfilesLinkage().SetLFileAction(gvar.GetWStoreAction());
                // MOVE   SPACES           TO   W-STORE-ACTION
                gvar.SetWStoreAction("");
            }

            // MOVE   FILE-STATUS   TO   L-FILE-RETURN-CODE.
            ivar.GetCgtfilesLinkage().SetLFileReturnCode(gvar.GetFileStatus());

            // PERFORM   Z6-STORE-CACHED-SCHED-DATA.
            gvar.SetZ6StoreCachedSchedData(true);
            ApplyStoreCachedSchedData(fvar, gvar, ivar); // PERFORM Z6-STORE-CACHED-SCHED-DATA
        }
        /// <summary>
        /// cgtfilesControlExit
        /// </summary>
        /// <remarks>
        /// COBOL paragraph name: cgtfilesControlExit
        /// </remarks>
        public void Exit(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EXIT PROGRAM in COBOL is equivalent to returning from the method in C#
            // Since this is the last statement in the program, we can simply return
            return;
        }
        /// <summary>
        /// z5RetrieveCachedSchedData
        /// </summary>
        /// <remarks>
        /// Converts COBOL paragraph z5RetrieveCachedSchedData to C# method Z5retrievecachedscheddata
        /// </remarks>
        public void Z5retrievecachedscheddata(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE   ZERO   TO   W-SCHED-SUB
            gvar.SetWSchedSub(gvar.GetZero());

            // SET    RECORD-NOT-RETRIEVED   TO   TRUE
            gvar.SetRecordNotRetrieved(true);

            // EVALUATE   L-FILE-ACTION
            if (ivar.GetCgtfilesLinkage().GetLFileAction() == Ivar.READ_NEXT ||
                ivar.GetCgtfilesLinkage().GetLFileAction() == Ivar.READ_NEXT_WITH_LOCK ||
                ivar.GetCgtfilesLinkage().GetLFileAction() == Ivar.READ_RECORD ||
                ivar.GetCgtfilesLinkage().GetLFileAction() == Ivar.READ_WITH_LOCK)
            {
                // EVALUATE   L-FILE-NAME
                if (ivar.GetCgtfilesLinkage().GetLFileName() == Ivar.REALISED_DATA_FILE)
                {
                    // SET   SUB-REALISED            TO   TRUE
                    gvar.SetSubRealised(true);
                }
                else if (ivar.GetCgtfilesLinkage().GetLFileName() == Ivar.UNREALISED_DATA_FILE)
                {
                    // SET   SUB-UNREALISED          TO   TRUE
                    gvar.SetSubUnrealised(true);
                }
                else if (ivar.GetCgtfilesLinkage().GetLFileName() == Ivar.NOTIONAL_SALE_DATA_FILE)
                {
                    // SET   SUB-DEEMED-DISP         TO   TRUE
                    gvar.SetSubDeemedDisp(true);
                }
                else if (ivar.GetCgtfilesLinkage().GetLFileName() == Ivar.REALISED_TAX_DATA_FILE)
                {
                    // SET   SUB-REALISED-TAX        TO   TRUE
                    gvar.SetSubRealisedTax(true);
                }
                else if (ivar.GetCgtfilesLinkage().GetLFileName() == Ivar.UNREALISED_TAX_DATA_FILE)
                {
                    // SET   SUB-UNREALISED-TAX      TO   TRUE
                    gvar.SetSubUnrealisedTax(true);
                }

                // IF   W-SCHED-SUB   <>   ZERO
                if (gvar.GetWSchedSub() != gvar.GetZero())
                {
                    // IF   RECORD-CACHED ( W-SCHED-SUB )
                    if (gvar.GetRecordCached(gvar.GetWSchedSub()))
                    {
                        // MOVE   S-RECORD ( W-SCHED-SUB )          TO   L-FILE-RECORD-AREA
                        ivar.SetLFileRecordAreaAsString(gvar.GetFiller334().GetSRecord(gvar.GetWSchedSub()).ToString());

                        // SET   RECORD-NOT-CACHED ( W-SCHED-SUB ) TO   TRUE
                        gvar.SetRecordNotCached(gvar.GetWSchedSub(), true);

                        // MOVE   '00'   TO   L-FILE-RETURN-CODE
                        ivar.GetCgtfilesLinkage().SetLFileReturnCode("00");

                        // SET    RECORD-RETRIEVED               TO   TRUE
                        gvar.SetRecordRetrieved(true);
                    }
                }
            }
        }
        /// <summary>
        /// z6StoreCachedSchedData
        /// </summary>
        /// <remarks>
        /// This method processes schedule data caching based on file type and action.
        /// </remarks>
        public void Z6storecachedscheddata(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE ZERO TO W-SCHED-SUB
            gvar.SetWSchedSub(0);

            // EVALUATE L-FILE-NAME
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case string fileName when fileName == Ivar.REALISED_DATA_FILE:
                    // SET SUB-REALISED TO TRUE
                    gvar.SetSubRealised(true);
                    // MOVE D19-RECORD TO S-RECORD (SUB-CURRENT-RECORD)
                    gvar.GetFiller334().SetSRecord(fvar.GetD19Record());
                    break;
                case string fileName when fileName == Ivar.UNREALISED_DATA_FILE:
                    // SET SUB-UNREALISED TO TRUE
                    gvar.SetSubUnrealised(true);
                    // MOVE D20-RECORD TO S-RECORD (SUB-CURRENT-RECORD)
                    gvar.GetFiller334().SetSRecord(fvar.GetD20Record());
                    break;
                case string fileName when fileName == Ivar.NOTIONAL_SALE_DATA_FILE:
                    // SET SUB-DEEMED-DISP TO TRUE
                    gvar.SetSubDeemedDisp(true);
                    // MOVE D81-RECORD TO S-RECORD (SUB-CURRENT-RECORD)
                    gvar.GetFiller334().SetSRecord(fvar.GetD81Record());
                    break;
                case string fileName when fileName == Ivar.REALISED_TAX_DATA_FILE:
                    // SET SUB-REALISED-TAX TO TRUE
                    gvar.SetSubRealisedTax(true);
                    // MOVE D98-RECORD TO S-RECORD (SUB-CURRENT-RECORD)
                    gvar.GetFiller334().SetSRecord(fvar.GetD98Record());
                    break;
                case string fileName when fileName == Ivar.UNREALISED_TAX_DATA_FILE:
                    // SET SUB-UNREALISED-TAX TO TRUE
                    gvar.SetSubUnrealisedTax(true);
                    // MOVE D101-RECORD TO S-RECORD (SUB-CURRENT-RECORD)
                    gvar.GetFiller334().SetSRecord(fvar.GetD101Record());
                    break;
            }

            // IF W-SCHED-SUB IS NOT ZERO
            if (gvar.GetWSchedSub() != 0)
            {
                // SET RECORD-NOT-CACHED (W-SCHED-SUB) TO TRUE
                gvar.SetRecordNotCachedAt(gvar.GetWSchedSub(), true);

                // EVALUATE L-FILE-ACTION
                switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                {
                    case string action when action == Ivar.READ_NEXT ||
                                           action == Ivar.READ_NEXT_WITH_LOCK ||
                                           action == Ivar.READ_RECORD ||
                                           action == Ivar.READ_WITH_LOCK:
                        if (gvar.GetFileStatus().GetStatus1() == 0)
                        {
                            if (((gvar.GetFiller334().GetSCoAcLkAt(gvar.GetSUB_CURRENT_RECORD()) !=
                                 gvar.GetFiller334().GetSCoAcLkAt(gvar.GetWSchedSub()))
                                ||
                                (gvar.GetFiller334().GetSSedolSortAt(gvar.GetSUB_CURRENT_RECORD()) !=
                                 gvar.GetFiller334().GetSSedolSortAt(gvar.GetWSchedSub())))
                        &&
                        (gvar.GetFiller334().GetSRecordTypeAt(gvar.GetSUB_CURRENT_RECORD()) == "2"))
                    {
                                // STRING 'SCHEDULE DATA TYPE 1 RECORD MISSING FOR '
                                string logMessage = $"SCHEDULE DATA TYPE 1 RECORD MISSING FOR {gvar.GetFiller334().GetSCoAcLkAt(gvar.GetSUB_CURRENT_RECORD())} {gvar.GetFiller334().GetSSedolSortAt(gvar.GetSUB_CURRENT_RECORD())}";
                                // PERFORM X4-CGTLOG
                                // X4Cgtlog(fvar, gvar, ivar); // Not implemented
                                // MOVE S-RECORD (SUB-CURRENT-RECORD) TO S-RECORD (W-SCHED-SUB)
                                gvar.GetFiller334().SetSRecordAt(gvar.GetWSchedSub(),
                                    gvar.GetFiller334().GetSRecordAt(gvar.GetSUB_CURRENT_RECORD()));
                                // MOVE S-SEDOL-SORT (SUB-CURRENT-RECORD) TO S-SEDOL-NUMBER (W-SCHED-SUB)
                                gvar.GetFiller334().SetSSedolNumber(gvar.GetFiller334().GetSSedolSortAt(gvar.GetSUB_CURRENT_RECORD()));
                                // MOVE '1' TO S-RECORD-TYPE (W-SCHED-SUB)
                                gvar.GetFiller334().SetSRecordTypeAt(gvar.GetWSchedSub(), "1");
                                // MOVE '* Schedule data  *' TO S-ISSUERS-NAME (W-SCHED-SUB)
                                gvar.GetFiller334().SetSIssuersNameAt(gvar.GetWSchedSub(), "* Schedule data  *");
                                // MOVE '* header missing *' TO S-STOCK-DESCRIPTION (W-SCHED-SUB)
                                gvar.GetFiller334().SetSStockDescriptionAt(gvar.GetWSchedSub(), "* header missing *");
                                // MOVE SPACES TO S-LAST-TRANCHE-CONTRACT-NO (W-SCHED-SUB)
                                gvar.GetFiller334().SetSLastTrancheContractNoAt(gvar.GetWSchedSub(), " ");
                                // MOVE '1' TO S-SEDOL-RECORD-COUNT (W-SCHED-SUB)
                                gvar.GetFiller334().SetSSedolRecordCountAt(gvar.GetWSchedSub(), "1");
                                // MOVE '1' TO S-TRANCHE-COUNT (W-SCHED-SUB)
                                gvar.GetFiller334().SetSTrancheCountAt(gvar.GetWSchedSub(), "1");
                                // MOVE 'P' TO S-PRINT-FLAG (W-SCHED-SUB)
                                gvar.GetFiller334().SetSPrintFlagAt(gvar.GetWSchedSub(), "P");
                                // MOVE ' ' TO S-HOLDING-FLAG (W-SCHED-SUB)
                                gvar.GetFiller334().SetSHoldingFlagAt(gvar.GetWSchedSub(), " ");
                                // MOVE ' ' TO S-REIT-SECURITY-TYPE (W-SCHED-SUB)
                                gvar.GetFiller334().SetSReitSecurityTypeAt(gvar.GetWSchedSub(), " ");
                                // MOVE '0' TO S-QUOTED-INDICATOR (W-SCHED-SUB)
                                gvar.GetFiller334().SetSQuotedIndicatorAt(gvar.GetWSchedSub(), "0");
                                // MOVE ZERO TO S-PROFIT-LOSS (W-SCHED-SUB), S-LOST-INDEXATION (W-SCHED-SUB), S-OFFSHORE-INCOME-GAIN (W-SCHED-SUB)
                                gvar.GetFiller334().SetSProfitLossAt(gvar.GetWSchedSub(), "0");
                                gvar.GetFiller334().SetSLostIndexationAt(gvar.GetWSchedSub(), "0");
                                gvar.GetFiller334().SetSOffshoreIncomeGainAt(gvar.GetWSchedSub(), "0");

                                // EVALUATE L-FILE-NAME
                                switch (ivar.GetCgtfilesLinkage().GetLFileName())
                                {
                                    case string fileName when fileName == Ivar.REALISED_DATA_FILE:
                                        // MOVE S-RECORD (W-SCHED-SUB) TO D19-RECORD
                                        fvar.SetD19Record(gvar.GetFiller334().GetSRecordAt(gvar.GetWSchedSub()));
                                        break;
                                    case string fileName when fileName == Ivar.UNREALISED_DATA_FILE:
                                        // MOVE S-RECORD (W-SCHED-SUB) TO D20-RECORD
                                        fvar.SetD20Record(gvar.GetFiller334().GetSRecordAt(gvar.GetWSchedSub()));
                                        break;
                                    case string fileName when fileName == Ivar.NOTIONAL_SALE_DATA_FILE:
                                        // MOVE S-RECORD (W-SCHED-SUB) TO D81-RECORD
                                        fvar.SetD81Record(gvar.GetFiller334().GetSRecordAt(gvar.GetWSchedSub()));
                                        break;
                                    case string fileName when fileName == Ivar.REALISED_TAX_DATA_FILE:
                                        // MOVE S-RECORD (W-SCHED-SUB) TO D98-RECORD
                                        fvar.SetD98Record(gvar.GetFiller334().GetSRecordAt(gvar.GetWSchedSub()));
                                        break;
                                    case string fileName when fileName == Ivar.UNREALISED_TAX_DATA_FILE:
                                        // MOVE S-RECORD (W-SCHED-SUB) TO D101-RECORD
                                        fvar.SetD101Record(gvar.GetFiller334().GetSRecordAt(gvar.GetWSchedSub()));
                                        break;
                                }

                                // SET RECORD-CACHED (W-SCHED-SUB) TO TRUE
                                gvar.SetRecordCachedAt(gvar.GetWSchedSub(), true);
                            }
                        }
                        break;
                }
            }

            // MOVE S-RECORD (SUB-CURRENT-RECORD) TO S-RECORD (W-SCHED-SUB)
            gvar.GetFiller334().SetSRecordAt(gvar.GetWSchedSub(),
                gvar.GetFiller334().GetSRecordAt(gvar.GetSUB_CURRENT_RECORD()));
        }
        /// <summary>
        /// Initializes file processing by setting up file names and managing file actions
        /// based on the file type.
        /// </summary>
        /// <remarks>
        /// Converted from COBOL paragraph: aInitialise
        /// This method handles file initialization for numerous file types,
        /// setting up file names and managing file actions accordingly.
        /// </remarks>
        public void Ainitialise(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE '00' TO L-FILE-RETURN-CODE
            ivar.GetCgtfilesLinkage().SetLFileReturnCode("00");

            // FILE-STATUS.
            gvar.SetFileStatus();

            // MOVE SPACES TO W-STORE-ACTION
            gvar.SetWStoreAction(" ");

            // EVALUATE L-FILE-NAME
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case var fileName when fileName == Ivar.GetCGTR04_REPORT_FILE():
                    // MOVE L-USER-NO TO D5-USER-NO
                    gvar.GetD5File().SetD5UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D5-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD5FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD5FileName());
                    break;

                case var fileName when fileName == Ivar.GetCGTR05_REPORT_FILE():
                    // MOVE L-USER-NO TO D6-USER-NO
                    gvar.GetD6File().SetD6UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D6-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD6FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD6FileName());
                    break;

                case var fileName when fileName == Ivar.STERLING_EXTEL_REPORT:
                    // MOVE L-USER-NO TO D10-USER-NO
                    gvar.GetD10File().SetD10UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D10-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD10FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD10FileName());
                    break;

                case var fileName when fileName == Ivar.FOREIGN_EXTEL_REPORT:
                    // MOVE L-USER-NO TO D11-USER-NO
                    gvar.GetD11File().SetD11UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D11-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD11FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD11FileName());
                    break;

                case var fileName when fileName == ivar.GetCgtdate2LinkageDate1().GetOutputListing():
                    // MOVE L-USER-NO TO D12-USER-NO
                    gvar.GetD12File().SetD12UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetErrorReportFile():
                    // MOVE L-USER-NO TO D18-USER-NO
                    gvar.GetD18File().SetD18UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D18-REPORT-NO
                    gvar.GetD18File().SetD18ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D18-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD18FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD18FileName());
                    break;

                case var fileName when fileName == Ivar.REALISED_DATA_FILE:
                    // MOVE L-USER-NO TO D19-USER-NO
                    gvar.GetD19File().SetD19UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D19-REPORT-NO
                    gvar.GetD19File().SetD19ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.UNREALISED_DATA_FILE:
                    // MOVE L-USER-NO TO D20-USER-NO
                    gvar.GetD20File().SetD20UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D20-REPORT-NO
                    gvar.GetD20File().SetD20ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.REALISED_SCHEDULE_FILE:
                    // MOVE L-USER-NO TO D21-USER-NO
                    gvar.GetD21File().SetD21UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D21-REPORT-NO
                    gvar.GetD21File().SetD21ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D21-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD21FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD21FileName());
                    break;

                case var fileName when fileName == Ivar.UNREALISED_SCHEDULE_FILE:
                    // MOVE L-USER-NO TO D22-USER-NO
                    gvar.GetD22File().SetD22UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D22-REPORT-NO
                    gvar.GetD22File().SetD22ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D22-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD22FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD22FileName());
                    break;

                case var fileName when fileName == Ivar.GetCG01_REPORT_FILE():
                    // MOVE L-USER-NO TO D23-USER-NO
                    gvar.GetD23File().SetD23UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D23-REPORT-NO
                    gvar.GetD23File().SetD23ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D23-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD23FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD23FileName());
                    break;

                case var fileName when fileName == Ivar.GetCG02_REPORT_FILE():
                    // MOVE L-USER-NO TO D24-USER-NO
                    gvar.GetD24File().SetD24UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D24-REPORT-NO
                    gvar.GetD24File().SetD24ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D24-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD24FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD24FileName());
                    break;

                case var fileName when fileName == Ivar.GetCG03_REPORT_FILE():
                    // MOVE L-USER-NO TO D25-USER-NO
                    gvar.GetD25File().SetD25UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D25-REPORT-NO
                    gvar.GetD25File().SetD25ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D25-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD25FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD25FileName());
                    break;

                case var fileName when fileName == Ivar.YE_REC_REPORT_FILE:
                    // MOVE L-USER-NO TO D26-USER-NO
                    gvar.GetD26File().SetD26UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D26-REPORT-NO
                    gvar.GetD26File().SetD26ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D26-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD26FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD26FileName());
                    break;

                case var fileName when fileName == Ivar.YE_DEL_REPORT_FILE:
                    // MOVE L-USER-NO TO D27-USER-NO
                    gvar.GetD27File().SetD27UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D27-REPORT-NO
                    gvar.GetD27File().SetD27ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D27-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD27FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD27FileName());
                    break;

                case var fileName when fileName == Ivar.YE_CON_REPORT_FILE:
                    // MOVE L-USER-NO TO D28-USER-NO
                    gvar.GetD28File().SetD28UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D28-REPORT-NO
                    gvar.GetD28File().SetD28ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D28-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD28FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD28FileName());
                    break;

                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetErrorDataFile():
                    // MOVE L-USER-NO TO D29-USER-NO
                    gvar.GetD29File().SetD29UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D29-REPORT-NO
                    gvar.GetD29File().SetD29ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.YE_ERR_REPORT_FILE:
                    // MOVE L-USER-NO TO D30-USER-NO
                    gvar.GetD30File().SetD30UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D30-REPORT-NO
                    gvar.GetD30File().SetD30ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D30-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD30FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD30FileName());
                    break;

                case var fileName when fileName == Ivar.GetYE_REC2_DATA_FILE():
                    // MOVE L-USER-NO TO D33-USER-NO
                    gvar.GetD33File().SetD33UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D33-REPORT-NO
                    gvar.GetD33File().SetD33ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.USER_FUND_FILE:

                    break;

                case var fileName when fileName == Ivar.SEQ_BALANCE_FILE:
                    // MOVE L-USER-NO TO D45-USER-NO
                    gvar.GetD45File().SetD45UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.TRANSACTION_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D46-FILE
                    gvar.SetD46FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.YE_BAL_REPORT_FILE:
                    // MOVE L-USER-NO TO D49-USER-NO
                    gvar.GetD49File().SetD49UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D49-REPORT-NO
                    gvar.GetD49File().SetD49ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D49-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD49FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD49FileName());
                    break;

                case var fileName when fileName == Ivar.STOCK_LOAD_REPORT:
                    // MOVE L-USER-NO TO D50-USER-NO
                    gvar.GetD50File().SetD50UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D50-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD50FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD50FileName());
                    break;

                case var fileName when fileName == Ivar.STOCK_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D51-FILE
                    gvar.SetD51FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.FUNDS_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D68-FILE
                    gvar.SetD68FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.FUNDS_LOAD_REPORT:
                    // MOVE L-USER-NO TO D69-USER-NO
                    gvar.GetD69File().SetD69UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D69-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD69FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD69FileName());
                    break;

                case var fileName when fileName == Ivar.PRICE_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D70-FILE
                    gvar.SetD70FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.PRICE_LOAD_REPORT:
                    // MOVE L-USER-NO TO D71-USER-NO
                    gvar.GetD71File().SetD71UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D71-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD71FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD71FileName());
                    break;

                case var fileName when fileName == Ivar.GetSKAN1_REPORT():
                    // MOVE L-USER-NO TO D72-USER-NO
                    gvar.GetD72File().SetD72UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D72-REPORT-NO
                    gvar.GetD72File().SetD72ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D72-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD72FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD72FileName());
                    break;

                case var fileName when fileName == Ivar.GetSKAN2_REPORT():
                    // MOVE L-USER-NO TO D73-USER-NO
                    gvar.GetD73File().SetD73UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D73-REPORT-NO
                    gvar.GetD73File().SetD73ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D73-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD73FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD73FileName());
                    break;

                case var fileName when fileName == Ivar.NEW_REALISED_REPORT:
                    // MOVE L-USER-NO TO D74-USER-NO
                    gvar.GetD74File().SetD74UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D74-REPORT-NO
                    gvar.GetD74File().SetD74ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D74-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD74FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD74FileName());
                    break;

                case var fileName when fileName == Ivar.NEW_UNREALISED_REPORT:
                    // MOVE L-USER-NO TO D75-USER-NO
                    gvar.GetD75File().SetD75UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D75-REPORT-NO
                    gvar.GetD75File().SetD75ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D75-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD75FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD75FileName());
                    break;

                case var fileName when fileName == Ivar.GetMGM1_REPORT_FILE():
                    // MOVE L-USER-NO TO D76-USER-NO
                    gvar.GetD76File().SetD76UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D76-REPORT-NO
                    gvar.GetD76File().SetD76ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D76-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD76FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD76FileName());
                    break;

                case var fileName when fileName == Ivar.CAPITAL_REPORT_FILE:
                    // MOVE L-USER-NO TO D77-USER-NO
                    gvar.GetD77File().SetD77UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D77-REPORT-NO
                    gvar.GetD77File().SetD77ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D77-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD77FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD77FileName());
                    break;

                case var fileName when fileName == Ivar.REPLACEMENT_RELIEF_REPORT:
                    // MOVE L-USER-NO TO D78-USER-NO
                    gvar.GetD78File().SetD78UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D78-REPORT-NO
                    gvar.GetD78File().SetD78ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D78-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD78FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD78FileName());
                    break;

                case var fileName when fileName == Ivar.NOTIONAL_SALE_DATA_FILE:
                    // MOVE L-USER-NO TO D81-USER-NO
                    gvar.GetD81File().SetD81UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D81-REPORT-NO
                    gvar.GetD81File().SetD81ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // W-REPORT-NAME (This appears to be a comment or placeholder in original COBOL)
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.NOTIONAL_SALE_SCHEDULE_FILE:
                    // MOVE L-USER-NO TO D82-USER-NO
                    gvar.GetD82File().SetD82UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D82-REPORT-NO
                    gvar.GetD82File().SetD82ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D82-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD82FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD82FileName());
                    break;

                case var fileName when fileName == Ivar.BALANCE_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D83-FILE
                    gvar.SetD83FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.BALANCES_LOAD_REPORT:
                    // MOVE L-USER-NO TO D84-USER-NO
                    gvar.GetD84File().SetD84UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D84-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD84FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD84FileName());
                    break;

                case var fileName when fileName == Ivar.OFFSHORE_INCOME_REPORT:
                    // MOVE L-USER-NO TO D85-USER-NO
                    gvar.GetD85File().SetD85UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D85-REPORT-NO
                    gvar.GetD85File().SetD85ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D85-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD85FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD85FileName());
                    break;

                case var fileName when fileName == Ivar.RCF_BACKUP_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D87-FILE
                    gvar.SetD87FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.GROUP_LOAD_REPORT:
                    // MOVE L-USER-NO TO D88-USER-NO
                    gvar.GetD88File().SetD88UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D88-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD88FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD88FileName());
                    break;

                case var fileName when fileName == Ivar.GROUP_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D89-FILE
                    gvar.SetD89FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.COUNTRY_LOAD_REPORT:
                    // MOVE L-USER-NO TO D90-USER-NO
                    gvar.GetD90File().SetD90UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D90-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD90FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD90FileName());
                    break;

                case var fileName when fileName == Ivar.COUNTRY_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D91-FILE
                    gvar.SetD91FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.RPI_LOAD_REPORT:
                    // MOVE L-USER-NO TO D92-USER-NO
                    gvar.GetD92File().SetD92UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D92-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD92FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD92FileName());
                    break;

                case var fileName when fileName == Ivar.RPI_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D93-FILE
                    gvar.SetD93FileAsString(ivar.GetLFileRecordArea());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.GAINLOSS_DATA_FILE:
                    // MOVE L-USER-NO TO D94-USER-NO
                    gvar.GetD94File().SetD94UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D94-REPORT-NO
                    gvar.GetD94File().SetD94ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D94-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD94FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD94FileName());
                    break;

                case var fileName when fileName == Ivar.GAINLOSS_REPORT:
                    // MOVE L-USER-NO TO D95-USER-NO
                    gvar.GetD95File().SetD95UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D95-REPORT-NO
                    gvar.GetD95File().SetD95ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D95-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD95FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD95FileName());
                    break;

                case var fileName when fileName == Ivar.LOST_INDEXATION_REPORT:
                    // MOVE L-USER-NO TO D96-USER-NO
                    gvar.GetD96File().SetD96UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D96-REPORT-NO
                    gvar.GetD96File().SetD96ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D96-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD96FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD96FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.LOSU_INDEXATION_REPORT:
                    // MOVE L-USER-NO TO D97-USER-NO
                    gvar.GetD97File().SetD97UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D97-REPORT-NO
                    gvar.GetD97File().SetD97ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D97-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD97FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD97FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.REALISED_TAX_DATA_FILE:
                    // MOVE L-USER-NO TO D98-USER-NO
                    gvar.GetD98File().SetD98UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D98-REPORT-NO
                    gvar.GetD98File().SetD98ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.REALISED_TAX_SCHEDULE_FILE:
                    // MOVE L-USER-NO TO D100-USER-NO
                    gvar.GetD100File().SetD100UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D100-REPORT-NO
                    gvar.GetD100File().SetD100ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D100-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD100FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD100FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.UNREALISED_TAX_DATA_FILE:
                    // MOVE L-USER-NO TO D101-USER-NO
                    gvar.GetD101File().SetD101UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D101-REPORT-NO
                    gvar.GetD101File().SetD101ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.UNREALISED_TAX_SCHEDULE_FILE:
                    // MOVE L-USER-NO TO D102-USER-NO
                    gvar.GetD102File().SetD102UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D102-REPORT-NO
                    gvar.GetD102File().SetD102ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D102-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD102FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD102FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.REAL_H_O_GAINS_REPORT:
                    // MOVE L-USER-NO TO D103-USER-NO
                    gvar.GetD103File().SetD103UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D103-REPORT-NO
                    gvar.GetD103File().SetD103ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D103-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD103FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD103FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.UNREAL_H_O_GAINS_REPORT:
                    // MOVE L-USER-NO TO D104-USER-NO
                    gvar.GetD104File().SetD104UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D104-REPORT-NO
                    gvar.GetD104File().SetD104ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D104-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD104FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD104FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.ACQUISITION_EXPORT_FILE:
                    // MOVE L-USER-NO TO D105-USER-NO
                    gvar.GetD105File().SetD105UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D105-REPORT-NO
                    gvar.GetD105File().SetD105ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D105-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD105FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD105FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetBatchRunLogFile():
                    // continue
                   
                    break;

                case var fileName when fileName == Ivar.BATCH_QUIT_RUN_FILE:
                    // MOVE L-USER-NO TO D107-USER-NO
                    gvar.GetD107File().SetD107UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.TRACE_FILE:
                    // MOVE L-USER-NO TO D108-USER-NO
                    gvar.GetD108File().SetD108UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetErrorLogFile():
                    // MOVE L-USER-NO TO D109-USER-NO
                    gvar.GetD109File().SetD109UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.TAPER_REPORT_FILE:
                    // MOVE L-USER-NO TO D110-USER-NO
                    gvar.GetD110File().SetD110UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D110-REPORT-NO
                    gvar.GetD110File().SetD110ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D110-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD110FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD110FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.TAPER_EXPORT_FILE:
                    // MOVE L-USER-NO TO D111-USER-NO
                    gvar.GetD111File().SetD111UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // MOVE L-REPORT-NO TO D111-REPORT-NO
                    gvar.GetD111File().SetD111ReportNo(ivar.GetCgtfilesLinkage().GetLReportNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D111-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD111FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD111FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.INTER_CONNECTED_FUNDS_EXPORT:
                    // MOVE L-USER-NO TO D164-USER-NO
                    gvar.GetD164File().SetD164UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D164-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD164FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD164FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.INTER_CONNECTED_FUNDS_REPORT:
                    // MOVE L-USER-NO TO D165-USER-NO
                    gvar.GetD165File().SetD165UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D165-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD165FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD165FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.ALLOWANCES_FROM_DB_FILE:
                    // MOVE L-USER-NO TO D159-USER-NO
                    gvar.GetD159File().SetD159UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D159-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD159FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD159FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.LOSSES_FROM_DB_FILE:
                    // MOVE L-USER-NO TO D160-USER-NO
                    gvar.GetD160File().SetD160UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D160-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD160FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD160FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                case var fileName when fileName == Ivar.DISPOSALS_FROM_DB_FILE:
                    // MOVE L-USER-NO TO D161-USER-NO
                    gvar.GetD161File().SetD161UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;

                case var fileName when fileName == Ivar.DAILY_TRANSACTION_EXPORT_FILE:
                    // MOVE L-USER-NO TO D171-USER-NO
                    gvar.GetD171File().SetD171UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                                              // MOVE D171-FILE-NAME TO W-REPORT W-REPORT-NAME
                    gvar.SetWReport(gvar.GetD171FileName());
                    gvar.GetGvar().GetWReport().SetName(gvar.GetD171FileName());

                    // EVALUATE L-FILE-ACTION
                    switch (ivar.GetCgtfilesLinkage().GetLFileAction())
                    {
                        case var fileAction when fileAction == ivar.GetCgtdate2LinkageDate1().GetOpenInput():
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE OPEN-INPUT-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(ivar.GetCgtdate2LinkageDate1().GetOpenInputReport());
                            break;

                        case var fileAction when fileAction == Ivar.CLOSE_FILE:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE CLOSE-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_REPORT);
                            break;

                        case var fileAction when fileAction == Ivar.READ_NEXT:
                            // MOVE L-FILE-ACTION TO W-STORE-ACTION
                            gvar.SetWStoreAction(ivar.GetCgtfilesLinkage().GetLFileAction());
                            // MOVE READ-REPORT TO L-FILE-ACTION
                            ivar.GetCgtfilesLinkage().SetLFileAction(Ivar.READ_REPORT);
                            break;
                    }
                    // END-EVALUATE
                    gvar.SetEndEvaluate();
                    break;

                default:
                    // WHEN OTHER
                    // PERFORM Z4-MAKE-FILE-NAME
                    gvar.SetZ4MakeFileName(); // PERFORM Z4-MAKE-FILE-NAME
                    break;
            }
            // END-EVALUATE
            gvar.SetEndEvaluate();

            // EVALUATE L-FILE-NAME
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case var fileName when fileName == Ivar.COUNTRY_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D1-RECORD
                    fvar.SetD1RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.GROUP_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D2-RECORD
                    fvar.SetD2RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.STOCK_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D3-RECORD
                    fvar.SetD3RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.FUND_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D4-RECORD
                    fvar.SetD4RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.GetCGTR04_REPORT_FILE():
                    // MOVE L-FILE-RECORD-AREA TO D5-RECORD
                    fvar.SetD5RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.GetCGTR05_REPORT_FILE():
                    // MOVE L-FILE-RECORD-AREA TO D6-RECORD
                    fvar.SetD6RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.RPI_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D7-RECORD
                    fvar.SetD7RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == ivar.GetCgtdate2LinkageDate1().GetParameterFile():
                    // MOVE L-FILE-RECORD-AREA TO D8-RECORD
                    fvar.SetD8RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.USER_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D9-RECORD
                    fvar.SetD9RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.STERLING_EXTEL_REPORT:
                    // MOVE L-FILE-RECORD-AREA TO D10-RECORD
                    fvar.SetD10RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.FOREIGN_EXTEL_REPORT:
                    // MOVE L-FILE-RECORD-AREA TO D11-RECORD
                    fvar.SetD11RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == ivar.GetCgtdate2LinkageDate1().GetOutputListing():
                    // MOVE L-FILE-RECORD-AREA TO D12-RECORD
                    fvar.SetD12RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetMasterLogFile():
                    // MOVE L-FILE-RECORD-AREA TO D17-RECORD
                    fvar.SetD17RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.REALISED_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D19-RECORD
                    fvar.SetD19RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.UNREALISED_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D20-RECORD
                    fvar.SetD20RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.PRINTER_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D31-RECORD
                    fvar.SetD31RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.STOCK_TYPE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D32-RECORD
                    fvar.SetD32RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.TRANSACTION_CODE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D34-RECORD
                    fvar.SetD34RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == ivar.GetCgtdate2LinkageDate1().GetOutputLogFile():
                    // MOVE L-FILE-RECORD-AREA TO D35-RECORD
                    fvar.SetD35RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetMessageFile():
                    // MOVE L-FILE-RECORD-AREA TO D36-RECORD
                    fvar.SetD36RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.USER_FUND_FILE:
                    // continue

                    break;

                case var fileName when fileName == Ivar.HELP_TEXT_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D38-RECORD
                    fvar.SetD38RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.DEFAULT_ACCESS_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D39-RECORD
                    fvar.SetD39RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.ACCESS_PROFILE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D40-RECORD
                    fvar.SetD40RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.EXTEL_CURRENCY_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D42-RECORD
                    fvar.SetD42RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.STOCK_PRICE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D43-RECORD
                    fvar.SetD43RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.SEQ_BALANCE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D45-BAL-ACQ-DISP-RECORD
                    fvar.SetD45BalAcqDispRecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.TRANSACTION_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D46-RECORD
                    fvar.SetD46RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.REPLACEMENT_ACQ_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D79-RECORD
                    fvar.SetD79RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.REPLACEMENT_DIS_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D80-RECORD
                    fvar.SetD80RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.NOTIONAL_SALE_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D81-RECORD
                    fvar.SetD81RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.RCF_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D86-RECORD
                    fvar.SetD86RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.GROUP_LOAD_REPORT:
                    // MOVE L-FILE-RECORD-AREA TO D88-RECORD
                    fvar.SetD88RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.GROUP_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D89-RECORD
                    fvar.SetD89RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.COUNTRY_LOAD_REPORT:
                    // MOVE L-FILE-RECORD-AREA TO D90-RECORD
                    fvar.SetD90RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.COUNTRY_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D91-RECORD
                    fvar.SetD91RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.RPI_LOAD_REPORT:
                    // MOVE L-FILE-RECORD-AREA TO D92-RECORD
                    fvar.SetD92RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.RPI_LOAD_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D93-RECORD
                    fvar.SetD93RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.GAINLOSS_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D94-RECORD
                    fvar.SetD94RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.GAINLOSS_REPORT:
                    // MOVE L-FILE-RECORD-AREA TO D95-RECORD
                    fvar.SetD95RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.REALISED_TAX_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D98-RECORD
                    fvar.SetD98RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.UNREALISED_TAX_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D101-RECORD
                    fvar.SetD101RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.TAPER_RATE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D112-RECORD
                    fvar.SetD112RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.PERIOD_END_CALENDAR_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D153-RECORD
                    fvar.SetD153RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.PERIOD_END_CALENDAR_DATES_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D154-RECORD
                    fvar.SetD154RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.INTER_CONNECTED_FUNDS_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D163-RECORD
                    fvar.SetD163RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.PRICE_TYPES_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D167-RECORD
                    fvar.SetD167RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetPendingLogFile():
                    // MOVE L-FILE-RECORD-AREA TO D169-RECORD
                    fvar.SetD169RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;

                case var fileName when fileName == Ivar.PENDING_ITEMS_FILE:
                    // MOVE L-FILE-RECORD-AREA TO D170-RECORD
                    fvar.SetD170RecordAsString(ivar.GetLFileRecordAreaAsString());
                    break;
            }
            // END-EVALUATE
            gvar.SetEndEvaluate();
        }
        /// <summary>
        /// bOpenInput
        /// </summary>
        /// <remarks>
        /// Opens input files based on L-File-Name.
        /// </remarks>
        public void Bopeninput(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    new CountryDAL().ProcessFile(gvar.GetCgtfilesLinkage().GetLFileAction(), ivar.GetLFileRecordAreaAsString());
                    break;
                case "GROUP-FILE":
                    // new GroupDAL().Run(gvar.GetCgtfilesLinkage().GetLFileAction(), ivar.GetLFileRecordArea()); // GroupDAL has different method signature
                    break;
                case "STOCK-FILE":
                    gvar.SetD3FileAsString("Opened");
                    break;
                case "FUND-FILE":
                    gvar.SetD4FileAsString("Opened");
                    break;
                case "CGTR04-REPORT-FILE":
                    gvar.SetD5FileAsString("Opened");
                    break;
                case "CGTR05-REPORT-FILE":
                    gvar.SetD6FileAsString("Opened");
                    break;
                case "RPI-FILE":
                    string recordArea = ivar.GetLFileRecordAreaAsString();
                    new RPIDAL().ProcessFile(gvar.GetCgtfilesLinkage().GetLFileAction(), ref recordArea);
                    ivar.SetLFileRecordAreaAsString(recordArea);
                    break;
                case "PARAMETER-FILE":
                    string paramRecordArea = ivar.GetLFileRecordAreaAsString();
                    new ParametersDAL().Run(gvar.GetCgtfilesLinkage().GetLFileAction(), ref paramRecordArea);
                    ivar.SetLFileRecordAreaAsString(paramRecordArea);
                    break;
                case "USER-FILE":
                    gvar.SetD9FileAsString("Opened");
                    break;
                case "STERLING-EXTEL-REPORT":
                    gvar.SetD10FileAsString("Opened");
                    break;
                case "FOREIGN-EXTEL-REPORT":
                    gvar.SetD11FileAsString("Opened");
                    break;
                case "MASTER-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetMasterLogFile("Opened"); // SetMasterLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "ERROR-REPORT-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetErrorReportFile("Opened"); // SetErrorReportFile method not implemented in CgtlogLinkageArea1
                    break;
                case "REALISED-DATA-FILE":
                    gvar.SetD19FileAsString("Opened");
                    break;
                case "UNREALISED-DATA-FILE":
                    gvar.SetD20FileAsString("Opened");
                    break;
                case "REALISED-SCHEDULE-FILE":
                    gvar.SetD21FileAsString("Opened");
                    break;
                case "UNREALISED-SCHEDULE-FILE":
                    gvar.SetD22FileAsString("Opened");
                    break;
                case "CG01-REPORT-FILE":
                    gvar.SetD23FileAsString("Opened");
                    break;
                case "CG02-REPORT-FILE":
                    gvar.SetD24FileAsString("Opened");
                    break;
                case "CG03-REPORT-FILE":
                    gvar.SetD25FileAsString("Opened");
                    break;
                case "YE-REC-REPORT-FILE":
                    gvar.SetD26FileAsString("Opened");
                    break;
                case "YE-DEL-REPORT-FILE":
                    gvar.SetD27FileAsString("Opened");
                    break;
                case "YE-CON-REPORT-FILE":
                    gvar.SetD28FileAsString("Opened");
                    break;
                case "ERROR-DATA-FILE":
                    gvar.GetCgtlogLinkageArea1().SetErrorDataFile("Opened");
                    break;
                case "YE-ERR-REPORT-FILE":
                    gvar.SetD30FileAsString("Opened");
                    break;
                case "PRINTER-FILE":
                    gvar.SetD31FileAsString("Opened");
                    break;
                case "STOCK-TYPE-FILE":
                    gvar.SetD32FileAsString("Opened");
                    break;
                case "YE-REC2-DATA-FILE":
                    gvar.SetD33FileAsString("Opened");
                    break;
                case "TRANSACTION-CODE-FILE":
                    gvar.SetD34FileAsString("Opened");
                    break;
                case "OUTPUT-LOG-FILE":
                    gvar.SetD35FileAsString("Opened");
                    break;
                case "MESSAGE-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetMessageFile("Opened"); // SetMessageFile method not implemented in CgtlogLinkageArea1
                    break;
                case "USER-FUND-FILE":
                    // Create a new instance of the external program
                    var userFundRecordArea = ivar.GetLFileRecordAreaAsString();
                    new UserFundsDAL().Execute(ivar.GetCgtfilesLinkage().GetLFileAction(), ref userFundRecordArea, ivar.GetCommonLinkage().GetLUserNo());
                    ivar.SetLFileRecordAreaAsString(userFundRecordArea);
                    break;
                case "HELP-TEXT-FILE":
                    gvar.SetD38FileAsString("Opened");
                    break;
                case "DEFAULT-ACCESS-FILE":
                    gvar.SetD39FileAsString("Opened");
                    break;
                case "ACCESS-PROFILE-FILE":
                    gvar.SetD40FileAsString("Opened");
                    break;
                case "EXTEL-PRICES-FILE":
                    gvar.SetD41FileAsString("Opened");
                    break;
                case "EXTEL-CURRENCY-FILE":
                    gvar.SetD42FileAsString("Opened");
                    break;
                case "STOCK-PRICE-FILE":
                    gvar.SetD43FileAsString("Opened");
                    break;
                case "EXTEL-TRANSMISSION-FILE":
                    gvar.SetD44FileAsString("Opened");
                    break;
                case "SEQ-BALANCE-FILE":
                    gvar.SetD45FileAsString("Opened");
                    break;
                case "TRANSACTION-FILE":
                    gvar.SetD46FileAsString("Opened");
                    break;
                case "YE-BAL-REPORT-FILE":
                    gvar.SetD49FileAsString("Opened");
                    break;
                case "STOCK-LOAD-REPORT":
                    gvar.SetD50FileAsString("Opened");
                    break;
                case "STOCK-LOAD-DATA-FILE":
                    gvar.SetD51FileAsString("Opened");
                    break;
                case "FUNDS-LOAD-DATA-FILE":
                    gvar.SetD68FileAsString("Opened");
                    break;
                case "FUNDS-LOAD-REPORT":
                    gvar.SetD69FileAsString("Opened");
                    break;
                case "PRICE-LOAD-DATA-FILE":
                    gvar.SetD70FileAsString("Opened");
                    break;
                case "PRICE-LOAD-REPORT":
                    gvar.SetD71FileAsString("Opened");
                    break;
                case "SKAN1-REPORT":
                    gvar.SetD72FileAsString("Opened");
                    break;
                case "SKAN2-REPORT":
                    gvar.SetD73FileAsString("Opened");
                    break;
                case "NEW-REALISED-REPORT":
                    gvar.SetD74FileAsString("Opened");
                    break;
                case "NEW-UNREALISED-REPORT":
                    gvar.SetD75FileAsString("Opened");
                    break;
                case "MGM1-REPORT-FILE":
                    gvar.SetD76FileAsString("Opened");
                    break;
                case "CAPITAL-REPORT-FILE":
                    gvar.SetD77FileAsString("Opened");
                    break;
                case "REPLACEMENT-RELIEF-REPORT":
                    gvar.SetD78FileAsString("Opened");
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    gvar.SetD79FileAsString("Opened");
                    break;
                case "REPLACEMENT-DIS-FILE":
                    gvar.SetD80FileAsString("Opened");
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    gvar.SetD81FileAsString("Opened");
                    break;
                case "NOTIONAL-SALE-SCHEDULE-FILE":
                    gvar.SetD82FileAsString("Opened");
                    break;
                case "BALANCE-LOAD-DATA-FILE":
                    gvar.SetD83FileAsString("Opened");
                    break;
                case "BALANCES-LOAD-REPORT":
                    gvar.SetD84FileAsString("Opened");
                    break;
                case "OFFSHORE-INCOME-REPORT":
                    gvar.SetD85FileAsString("Opened");
                    break;
                case "RCF-FILE":
                    gvar.SetD86FileAsString("Opened");
                    break;
                case "RCF-BACKUP-FILE":
                    gvar.SetD87FileAsString("Opened");
                    break;
                case "GROUP-LOAD-REPORT":
                    gvar.SetD88FileAsString("Opened");
                    break;
                case "GROUP-LOAD-DATA-FILE":
                    gvar.SetD89FileAsString("Opened");
                    break;
                case "COUNTRY-LOAD-REPORT":
                    gvar.SetD90FileAsString("Opened");
                    break;
                case "COUNTRY-LOAD-DATA-FILE":
                    gvar.SetD91FileAsString("Opened");
                    break;
                case "RPI-LOAD-REPORT":
                    gvar.SetD92FileAsString("Opened");
                    break;
                case "RPI-LOAD-DATA-FILE":
                    gvar.SetD93FileAsString("Opened");
                    break;
                case "GAINLOSS-DATA-FILE":
                    gvar.SetD94FileAsString("Opened");
                    break;
                case "GAINLOSS-REPORT":
                    gvar.SetD95FileAsString("Opened");
                    break;
                case "REALISED-TAX-DATA-FILE":
                    gvar.SetD98FileAsString("Opened");
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    gvar.SetD101FileAsString("Opened");
                    break;
                case "BATCH-RUN-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetBatchRunLogFile("Opened"); // SetBatchRunLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "BATCH-QUIT-RUN-FILE":
                    gvar.SetD107FileAsString("Opened");
                    break;
                case "TRACE-FILE":
                    gvar.SetD108FileAsString("Opened");
                    break;
                case "ERROR-LOG-FILE":
                    gvar.SetD109FileAsString("Opened");
                    break;
                case "TAPER-RATE-FILE":
                    // new TaperRateDAL().Run(gvar.GetCgtfilesLinkage().GetLFileAction(), ivar.GetLFileRecordArea(), ivar.GetCommonLinkage().GetLUserNo()); // TaperRateDAL has different method signature
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    gvar.SetD153FileAsString("Opened");
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    gvar.SetD154FileAsString("Opened");
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    gvar.SetD163FileAsString("Opened");
                    break;
                case "DISPOSALS-FROM-DB-FILE":
                    gvar.SetD161FileAsString("Opened");
                    // gvar.SetWD161CharSub(1); // SetWD161CharSub method not implemented
                    gvar.SetWd161RecordAsString(" ");
                    break;
                case "PRICE-TYPES-FILE":
                    gvar.SetD167FileAsString("Opened");
                    break;
                case "PENDING-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetPendingLogFile("Opened"); // SetPendingLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "PENDING-ITEMS-FILE":
                    gvar.SetD170FileAsString("Opened");
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// cOpenOutput
        /// </summary>
        /// <remarks>
        /// This method implements the COBOL paragraph cOpenOutput.
        /// It opens various output files based on the value of L-FILE-NAME.
        /// </remarks>
        public void Copenoutput(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // No action required
                    break;
                case "GROUP-FILE":
                    // No action required
                    break;
                case "STOCK-FILE":
                    gvar.SetD3FileAsString("OUTPUT");
                    break;
                case "FUND-FILE":
                    gvar.SetD4FileAsString("OUTPUT");
                    break;
                case "CGTR04-REPORT-FILE":
                    gvar.SetD5FileAsString("OUTPUT");
                    break;
                case "CGTR05-REPORT-FILE":
                    gvar.SetD6FileAsString("OUTPUT");
                    break;
                case "RPI-FILE":
                    // No action required
                    break;
                case "PARAMETER-FILE":
                    // No action required
                    break;
                case "USER-FILE":
                    gvar.SetD9FileAsString("OUTPUT");
                    break;
                case "STERLING-EXTEL-REPORT":
                    gvar.SetD10FileAsString("OUTPUT");
                    break;
                case "FOREIGN-EXTEL-REPORT":
                    gvar.SetD11FileAsString("OUTPUT");
                    break;
                case "OUTPUT-LISTING":
                    gvar.SetD12FileAsString("OUTPUT");
                    break;
                case "MASTER-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetMasterLogFile("OUTPUT"); // SetMasterLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "ERROR-REPORT-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetErrorReportFile("OUTPUT"); // SetErrorReportFile method not implemented in CgtlogLinkageArea1
                    break;
                case "REALISED-DATA-FILE":
                    gvar.SetD19FileAsString("OUTPUT");
                    gvar.GetWScheduleCounters().SetWd19Count(0);
                    break;
                case "UNREALISED-DATA-FILE":
                    gvar.SetD20FileAsString("OUTPUT");
                    gvar.GetWScheduleCounters().SetWd20Count(0);
                    break;
                case "REALISED-SCHEDULE-FILE":
                    gvar.SetD21FileAsString("OUTPUT");
                    break;
                case "UNREALISED-SCHEDULE-FILE":
                    gvar.SetD22FileAsString("OUTPUT");
                    break;
                case "CG01-REPORT-FILE":
                    gvar.SetD23FileAsString("OUTPUT");
                    break;
                case "CG02-REPORT-FILE":
                    gvar.SetD24FileAsString("OUTPUT");
                    break;
                case "CG03-REPORT-FILE":
                    gvar.SetD25FileAsString("OUTPUT");
                    break;
                case "YE-REC-REPORT-FILE":
                    gvar.SetD26FileAsString("OUTPUT");
                    break;
                case "YE-DEL-REPORT-FILE":
                    gvar.SetD27FileAsString("OUTPUT");
                    break;
                case "YE-CON-REPORT-FILE":
                    gvar.SetD28FileAsString("OUTPUT");
                    break;
                case "ERROR-DATA-FILE":
                    gvar.GetCgtlogLinkageArea1().SetErrorDataFile("OUTPUT");
                    break;
                case "YE-ERR-REPORT-FILE":
                    gvar.SetD30FileAsString("OUTPUT");
                    break;
                case "PRINTER-FILE":
                    gvar.SetD31FileAsString("OUTPUT");
                    break;
                case "STOCK-TYPE-FILE":
                    gvar.SetD32FileAsString("OUTPUT");
                    break;
                case "TRANSACTION-CODE-FILE":
                    gvar.SetD34FileAsString("OUTPUT");
                    break;
                case "TRANSACTION-FILE":
                    gvar.SetD46FileAsString("OUTPUT");
                    break;
                case "SEQ-BALANCE-FILE":
                    gvar.SetD45FileAsString("OUTPUT");
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    gvar.SetD81FileAsString("OUTPUT");
                    gvar.GetWScheduleCounters().SetWd81Count(0);
                    break;
                case "NOTIONAL-SALE-SCHEDULE-FILE":
                    gvar.SetD82FileAsString("OUTPUT");
                    break;
                case "BALANCE-LOAD-DATA-FILE":
                    gvar.SetD83FileAsString("OUTPUT");
                    break;
                case "BALANCES-LOAD-REPORT":
                    gvar.SetD84FileAsString("OUTPUT");
                    break;
                case "PRICE-LOAD-DATA-FILE":
                    gvar.SetD70FileAsString("OUTPUT");
                    break;
                case "PRICE-LOAD-REPORT":
                    gvar.SetD71FileAsString("OUTPUT");
                    break;
                case "FUNDS-LOAD-DATA-FILE":
                    gvar.SetD68FileAsString("OUTPUT");
                    break;
                case "FUNDS-LOAD-REPORT":
                    gvar.SetD69FileAsString("OUTPUT");
                    break;
                case "OUTPUT-LOG-FILE":
                    gvar.SetD35FileAsString("OUTPUT");
                    break;
                case "MESSAGE-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetMessageFile("OUTPUT"); // SetMessageFile method not implemented in CgtlogLinkageArea1
                    break;
                case "HELP-TEXT-FILE":
                    gvar.SetD38FileAsString("OUTPUT");
                    break;
                case "DEFAULT-ACCESS-FILE":
                    gvar.SetD39FileAsString("OUTPUT");
                    break;
                case "ACCESS-PROFILE-FILE":
                    gvar.SetD40FileAsString("OUTPUT");
                    break;
                case "EXTEL-PRICES-FILE":
                    gvar.SetD41FileAsString("OUTPUT");
                    break;
                case "EXTEL-CURRENCY-FILE":
                    gvar.SetD42FileAsString("OUTPUT");
                    break;
                case "STOCK-PRICE-FILE":
                    gvar.SetD43FileAsString("OUTPUT");
                    break;
                case "EXTEL-TRANSMISSION-FILE":
                    gvar.SetD44FileAsString("OUTPUT");
                    break;
                case "CAPITAL-REPORT-FILE":
                    gvar.SetD77FileAsString("OUTPUT");
                    break;
                case "REPLACEMENT-RELIEF-REPORT":
                    gvar.SetD78FileAsString("OUTPUT");
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    gvar.SetD79FileAsString("OUTPUT");
                    break;
                case "REPLACEMENT-DIS-FILE":
                    gvar.SetD80FileAsString("OUTPUT");
                    break;
                case "REALISED-TAX-DATA-FILE":
                    gvar.SetD98FileAsString("OUTPUT");
                    gvar.GetWScheduleCounters().SetWd98Count(0);
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    gvar.SetD101FileAsString("OUTPUT");
                    gvar.GetWScheduleCounters().SetWd101Count(0);
                    break;
                case "BATCH-RUN-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetBatchRunLogFile("OUTPUT"); // SetBatchRunLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "BATCH-QUIT-RUN-FILE":
                    gvar.SetD107FileAsString("OUTPUT");
                    break;
                case "ERROR-LOG-FILE":
                    gvar.SetD109FileAsString("OUTPUT");
                    break;
                case "TAPER-RATE-FILE":
                    gvar.SetD112FileAsString("OUTPUT");
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    gvar.SetD163FileAsString("OUTPUT");
                    break;
                case "PRICE-TYPES-FILE":
                    gvar.SetD167FileAsString("OUTPUT");
                    break;
                case "PENDING-LOG-FILE":
                    gvar.SetD169FileAsString("OUTPUT");
                    break;
                case "PENDING-ITEMS-FILE":
                    gvar.SetD170FileAsString("OUTPUT");
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    gvar.SetD153FileAsString("OUTPUT");
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    gvar.SetD154FileAsString("OUTPUT");
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// dOpenIO
        /// </summary>
        /// <remarks>
        /// This method is responsible for opening various files based on the L-FILE-NAME.
        /// </remarks>
        public void Dopenio(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case var fileName when fileName == Ivar.COUNTRY_FILE:
                    // CONTINUE
                    break;
                case var fileName when fileName == Ivar.GROUP_FILE:
                    // CONTINUE
                    break;
                case var fileName when fileName == Ivar.STOCK_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD3(gvar.GetIO()); // SetD3 method not implemented
                    break;
                case var fileName when fileName == Ivar.FUND_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD4(gvar.GetIO()); // SetD4 method not implemented
                    break;
                case var fileName when fileName == Ivar.RPI_FILE:
                    // CONTINUE
                    break;
                case var fileName when fileName == Ivar.PARAMETER_FILE:
                    // Create a new instance of the external program
                    ParametersDAL parametersDAL = new ParametersDAL();
                    // Call the Run method with ONLY the parameters specified in the USING clause
                    string paramRecordArea2 = fvar.GetLFileRecordAreaAsString();
                    parametersDAL.Run(ivar.GetCgtdate2LinkageDate1().GetParameterFile(), ref paramRecordArea2);
                    fvar.SetLFileRecordAreaAsString(paramRecordArea2);
                    break;
                case var fileName when fileName == Ivar.USER_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD9(gvar.GetIO()); // SetD9 method not implemented
                    break;
                case var fileName when fileName == Ivar.OUTPUT_LISTING:
                    gvar.SetIO("I-O");
                    // gvar.SetD12(gvar.GetIO()); // SetD12 method not implemented
                    break;
                case var fileName when fileName == gvar.GetMASTER_LOG_FILE():
                    gvar.SetIO("I-O");
                    // gvar.SetD17(gvar.GetIO()); // SetD17 method not implemented
                    break;
                case var fileName when fileName == Ivar.REALISED_DATA_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD19(gvar.GetIO()); // SetD19 method not implemented
                    gvar.SetZero(0);
                    gvar.GetWScheduleCounters().SetWd19Count(gvar.GetZero());
                    break;
                case var fileName when fileName == Ivar.UNREALISED_DATA_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD20(gvar.GetIO()); // SetD20 method not implemented
                    gvar.SetZero(0);
                    gvar.GetWScheduleCounters().SetWd20Count(gvar.GetZero());
                    break;
                case var fileName when fileName == Ivar.PRINTER_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD31(gvar.GetIO()); // SetD31 method not implemented
                    break;
                case var fileName when fileName == Ivar.STOCK_TYPE_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD32(gvar.GetIO()); // SetD32 method not implemented
                    break;
                case var fileName when fileName == Ivar.TRANSACTION_CODE_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD34(gvar.GetIO()); // SetD34 method not implemented
                    break;
                case var fileName when fileName == Ivar.OUTPUT_LOG_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD35(gvar.GetIO()); // SetD35 method not implemented
                    break;
                case var fileName when fileName == Ivar.MESSAGE_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD36(gvar.GetIO()); // SetD36 method not implemented
                    break;
                case var fileName when fileName == Ivar.USER_FUND_FILE:
                    // Create a new instance of the external program
                    var userFundRecordArea2 = fvar.GetLFileRecordAreaAsString();
                    new UserFundsDAL().Execute(ivar.GetCgtfilesLinkage().GetLFileAction(), ref userFundRecordArea2, ivar.GetCommonLinkage().GetLUserNo());
                    fvar.SetLFileRecordAreaAsString(userFundRecordArea2);
                    break;
                case var fileName when fileName == Ivar.HELP_TEXT_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD38(gvar.GetIO()); // SetD38 method not implemented
                    break;
                case var fileName when fileName == Ivar.DEFAULT_ACCESS_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD39(gvar.GetIO()); // SetD39 method not implemented
                    break;
                case var fileName when fileName == Ivar.ACCESS_PROFILE_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD40(gvar.GetIO()); // SetD40 method not implemented
                    break;
                case var fileName when fileName == Ivar.EXTEL_CURRENCY_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD42(gvar.GetIO()); // SetD42 method not implemented
                    break;
                case var fileName when fileName == Ivar.STOCK_PRICE_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD43(gvar.GetIO()); // SetD43 method not implemented
                    break;
                case var fileName when fileName == Ivar.SEQ_BALANCE_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD45(gvar.GetIO()); // SetD45 method not implemented
                    break;
                case var fileName when fileName == Ivar.REPLACEMENT_ACQ_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD79(gvar.GetIO()); // SetD79 method not implemented
                    break;
                case var fileName when fileName == Ivar.REPLACEMENT_DIS_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD80(gvar.GetIO()); // SetD80 method not implemented
                    break;
                case var fileName when fileName == Ivar.NOTIONAL_SALE_DATA_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD81(gvar.GetIO()); // SetD81 method not implemented
                    gvar.SetZero(0);
                    gvar.GetWScheduleCounters().SetWd81Count(gvar.GetZero());
                    break;
                case var fileName when fileName == Ivar.RCF_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD86(gvar.GetIO()); // SetD86 method not implemented
                    break;
                case var fileName when fileName == Ivar.REALISED_TAX_DATA_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD98(gvar.GetIO()); // SetD98 method not implemented
                    gvar.SetZero(0);
                    gvar.GetWScheduleCounters().SetWd98Count(gvar.GetZero());
                    break;
                case var fileName when fileName == Ivar.UNREALISED_TAX_DATA_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD101(gvar.GetIO()); // SetD101 method not implemented
                    gvar.SetZero(0);
                    gvar.GetWScheduleCounters().SetWd101Count(gvar.GetZero());
                    break;
                case var fileName when fileName == Ivar.TAPER_RATE_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD112(gvar.GetIO()); // SetD112 method not implemented
                    break;
                case var fileName when fileName == Ivar.PERIOD_END_CALENDAR_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD153(gvar.GetIO()); // SetD153 method not implemented
                    break;
                case var fileName when fileName == Ivar.PERIOD_END_CALENDAR_DATES_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD154(gvar.GetIO()); // SetD154 method not implemented
                    break;
                case var fileName when fileName == Ivar.INTER_CONNECTED_FUNDS_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD163(gvar.GetIO()); // SetD163 method not implemented
                    break;
                case var fileName when fileName == Ivar.PRICE_TYPES_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD167(gvar.GetIO()); // SetD167 method not implemented
                    break;
                case var fileName when fileName == Ivar.PENDING_LOG_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD169(gvar.GetIO()); // SetD169 method not implemented
                    break;
                case var fileName when fileName == Ivar.PENDING_ITEMS_FILE:
                    gvar.SetIO("I-O");
                    // gvar.SetD170(gvar.GetIO()); // SetD170 method not implemented
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// eOpenExtend
        /// </summary>
        /// <remarks>
        /// This method implements the COBOL paragraph eOpenExtend.
        /// It evaluates L-FILE-NAME and opens the corresponding file in extend mode.
        /// </remarks>
        public void Eopenextend(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case var fileName when fileName == ivar.GetCGTR04_REPORT_FILE():
                    // gvar.SetD5("OPEN EXTEND D5"); // SetD5 method not implemented
                    // OPEN EXTEND D5
                    break;
                case var fileName when fileName == ivar.GetCGTR05_REPORT_FILE():
                    // gvar.SetD6("OPEN EXTEND D6"); // SetD6 method not implemented
                    // OPEN EXTEND D6
                    break;
                case var fileName when fileName == Ivar.STERLING_EXTEL_REPORT:
                    // gvar.SetD10("OPEN EXTEND D10"); // SetD10 method not implemented
                    // OPEN EXTEND D10
                    break;
                case var fileName when fileName == Ivar.FOREIGN_EXTEL_REPORT:
                    // gvar.SetD11("OPEN EXTEND D11"); // SetD11 method not implemented
                    // OPEN EXTEND D11
                    break;
                case var fileName when fileName == Ivar.ERROR_REPORT_FILE:
                    // gvar.SetD18("OPEN EXTEND D18"); // SetD18 method not implemented
                    // OPEN EXTEND D18
                    break;
                case var fileName when fileName == Ivar.REALISED_SCHEDULE_FILE:
                    // gvar.SetD21("OPEN EXTEND D21"); // SetD21 method not implemented
                    // OPEN EXTEND D21
                    break;
                case var fileName when fileName == Ivar.UNREALISED_SCHEDULE_FILE:
                    // gvar.SetD22("OPEN EXTEND D22"); // SetD22 method not implemented
                    // OPEN EXTEND D22
                    break;
                case var fileName when fileName == ivar.GetCG01_REPORT_FILE():
                    // gvar.SetD23("OPEN EXTEND D23"); // SetD23 method not implemented
                    // OPEN EXTEND D23
                    break;
                case var fileName when fileName == ivar.GetCG02_REPORT_FILE():
                    // gvar.SetD24("OPEN EXTEND D24"); // SetD24 method not implemented
                    // OPEN EXTEND D24
                    break;
                case var fileName when fileName == ivar.GetCG03_REPORT_FILE():
                    // gvar.SetD25("OPEN EXTEND D25"); // SetD25 method not implemented
                    // OPEN EXTEND D25
                    break;
                case var fileName when fileName == Ivar.YE_REC_REPORT_FILE:
                    // gvar.SetD26("OPEN EXTEND D26"); // SetD26 method not implemented
                    // OPEN EXTEND D26
                    break;
                case var fileName when fileName == Ivar.YE_DEL_REPORT_FILE:
                    // gvar.SetD27("OPEN EXTEND D27"); // SetD27 method not implemented
                    // OPEN EXTEND D27
                    break;
                case var fileName when fileName == Ivar.YE_CON_REPORT_FILE:
                    // gvar.SetD28("OPEN EXTEND D28"); // SetD28 method not implemented
                    // OPEN EXTEND D28
                    break;
                case var fileName when fileName == Ivar.ERROR_DATA_FILE:
                    // gvar.SetD29("OPEN EXTEND D29"); // SetD29 method not implemented
                    // OPEN EXTEND D29
                    break;
                case var fileName when fileName == Ivar.YE_ERR_REPORT_FILE:
                    // gvar.SetD30("OPEN EXTEND D30"); // SetD30 method not implemented
                    // OPEN EXTEND D30
                    break;
                case var fileName when fileName == ivar.GetYE_REC2_DATA_FILE():
                    // gvar.SetD33("OPEN EXTEND D33"); // SetD33 method not implemented
                    // OPEN EXTEND D33
                    break;
                case var fileName when fileName == Ivar.EXTEL_PRICES_FILE:
                    // gvar.SetD41("OPEN EXTEND D41"); // SetD41 method not implemented
                    // OPEN EXTEND D41
                    break;
                case var fileName when fileName == Ivar.EXTEL_TRANSMISSION_FILE:
                    // gvar.SetD44("OPEN EXTEND D44"); // SetD44 method not implemented
                    // OPEN EXTEND D44
                    break;
                case var fileName when fileName == Ivar.TRANSACTION_FILE:
                    // gvar.SetD46("OPEN EXTEND D46"); // SetD46 method not implemented
                    // OPEN EXTEND D46
                    break;
                case var fileName when fileName == Ivar.YE_BAL_REPORT_FILE:
                    // gvar.SetD49("OPEN EXTEND D49"); // SetD49 method not implemented
                    // OPEN EXTEND D49
                    break;
                case var fileName when fileName == Ivar.STOCK_LOAD_REPORT:
                    // gvar.SetD50("OPEN EXTEND D50"); // SetD50 method not implemented
                    // OPEN EXTEND D50
                    break;
                case var fileName when fileName == Ivar.STOCK_LOAD_DATA_FILE:
                    // gvar.SetD51("OPEN EXTEND D51"); // SetD51 method not implemented
                    // OPEN EXTEND D51
                    break;
                case var fileName when fileName == Ivar.FUNDS_LOAD_DATA_FILE:
                    // gvar.SetD68("OPEN EXTEND D68"); // SetD68 method not implemented
                    // OPEN EXTEND D68
                    break;
                case var fileName when fileName == Ivar.FUNDS_LOAD_REPORT:
                    // gvar.SetD69("OPEN EXTEND D69"); // SetD69 method not implemented
                    // OPEN EXTEND D69
                    break;
                case var fileName when fileName == Ivar.PRICE_LOAD_DATA_FILE:
                    // gvar.SetD70("OPEN EXTEND D70"); // SetD70 method not implemented
                    // OPEN EXTEND D70
                    break;
                case var fileName when fileName == Ivar.PRICE_LOAD_REPORT:
                    // gvar.SetD71("OPEN EXTEND D71"); // SetD71 method not implemented
                    // OPEN EXTEND D71
                    break;
                case var fileName when fileName == ivar.GetSKAN1_REPORT():
                    // gvar.SetD72("OPEN EXTEND D72"); // SetD72 method not implemented
                    // OPEN EXTEND D72
                    break;
                case var fileName when fileName == ivar.GetSKAN2_REPORT():
                    // gvar.SetD73("OPEN EXTEND D73"); // SetD73 method not implemented
                    // OPEN EXTEND D73
                    break;
                case var fileName when fileName == Ivar.NEW_REALISED_REPORT:
                    // gvar.SetD74("OPEN EXTEND D74"); // SetD74 method not implemented
                    // OPEN EXTEND D74
                    break;
                case var fileName when fileName == Ivar.NEW_UNREALISED_REPORT:
                    // gvar.SetD75("OPEN EXTEND D75"); // SetD75 method not implemented
                    // OPEN EXTEND D75
                    break;
                case var fileName when fileName == ivar.GetMGM1_REPORT_FILE():
                    // gvar.SetD76("OPEN EXTEND D76"); // SetD76 method not implemented
                    // OPEN EXTEND D76
                    break;
                case var fileName when fileName == Ivar.CAPITAL_REPORT_FILE:
                    // gvar.SetD77("OPEN EXTEND D77"); // SetD77 method not implemented
                    // OPEN EXTEND D77
                    break;
                case var fileName when fileName == Ivar.REPLACEMENT_RELIEF_REPORT:
                    // gvar.SetD78("OPEN EXTEND D78"); // SetD78 method not implemented
                    // OPEN EXTEND D78
                    break;
                case var fileName when fileName == Ivar.NOTIONAL_SALE_SCHEDULE_FILE:
                    // gvar.SetD82("OPEN EXTEND D82"); // SetD82 method not implemented
                    // OPEN EXTEND D82
                    break;
                case var fileName when fileName == Ivar.BALANCE_LOAD_DATA_FILE:
                    // gvar.SetD83("OPEN EXTEND D83"); // SetD83 method not implemented
                    // OPEN EXTEND D83
                    break;
                case var fileName when fileName == Ivar.BALANCES_LOAD_REPORT:
                    // gvar.SetD84("OPEN EXTEND D84"); // SetD84 method not implemented
                    // OPEN EXTEND D84
                    break;
                case var fileName when fileName == Ivar.OFFSHORE_INCOME_REPORT:
                    // gvar.SetD85("OPEN EXTEND D85"); // SetD85 method not implemented
                    // OPEN EXTEND D85
                    break;
                case var fileName when fileName == Ivar.RCF_BACKUP_FILE:
                    // gvar.SetD87("OPEN EXTEND D87"); // SetD87 method not implemented
                    // OPEN EXTEND D87
                    break;
                case var fileName when fileName == Ivar.GROUP_LOAD_REPORT:
                    // gvar.SetD88("OPEN EXTEND D88"); // SetD88 method not implemented
                    // OPEN EXTEND D88
                    break;
                case var fileName when fileName == Ivar.GROUP_LOAD_DATA_FILE:
                    // gvar.SetD89("OPEN EXTEND D89"); // SetD89 method not implemented
                    // OPEN EXTEND D89
                    break;
                case var fileName when fileName == Ivar.COUNTRY_LOAD_REPORT:
                    // gvar.SetD90("OPEN EXTEND D90"); // SetD90 method not implemented
                    // OPEN EXTEND D90
                    break;
                case var fileName when fileName == Ivar.COUNTRY_LOAD_DATA_FILE:
                    // gvar.SetD91("OPEN EXTEND D91"); // SetD91 method not implemented
                    // OPEN EXTEND D91
                    break;
                case var fileName when fileName == Ivar.RPI_LOAD_REPORT:
                    // gvar.SetD92("OPEN EXTEND D92"); // SetD92 method not implemented
                    // OPEN EXTEND D92
                    break;
                case var fileName when fileName == Ivar.RPI_LOAD_DATA_FILE:
                    // gvar.SetD93("OPEN EXTEND D93"); // SetD93 method not implemented
                    // OPEN EXTEND D93
                    break;
                case var fileName when fileName == Ivar.GAINLOSS_REPORT:
                    // gvar.SetD95("OPEN EXTEND D95"); // SetD95 method not implemented
                    // OPEN EXTEND D95
                    break;
                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetBatchRunLogFile():
                    // continue
                    break;
                case var fileName when fileName == Ivar.BATCH_QUIT_RUN_FILE:
                    // gvar.SetD107("OPEN EXTEND D107"); // SetD107 method not implemented
                    // OPEN EXTEND D107
                    break;
                case var fileName when fileName == Ivar.TRACE_FILE:
                    // gvar.SetD108("OPEN EXTEND D108"); // SetD108 method not implemented
                    // OPEN EXTEND D108
                    break;
                case var fileName when fileName == gvar.GetCgtlogLinkageArea1().GetErrorLogFile():
                    // gvar.SetD109("OPEN EXTEND D109"); // SetD109 method not implemented
                    // OPEN EXTEND D109
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    // MOVE 'IF' TO FILE-STATUS
                    break;
            }
        }
        /// <summary>
        /// fStartEqual
        /// </summary>
        /// <remarks>
        /// This method implements the COBOL paragraph fStartEqual.
        /// It evaluates L-FILE-NAME and performs actions based on its value.
        /// </remarks>
        public void Fstartequal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EVALUATE L-FILE-NAME
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // CONTINUE
                    break;
                case "GROUP-FILE":
                    // CONTINUE
                    break;
                case "STOCK-FILE":
                    SetStart(true);
                    // gvar.SetD3(gvar.GetD3()); // SetD3 method not implemented
                    // fvar.SetD3RecordAsString(fvar.GetD3Record().GetD3Key().ToString()); // GetD3Key method not implemented in D3Record
                    break;
                case "FUND-FILE":
                    SetStart(true);
                    // gvar.SetD4(gvar.GetD4()); // SetD4 method not implemented
                    // fvar.SetD4Record(fvar.GetD4Record().SetD4Key(gvar.GetD4Key())); // SetD4Key method not implemented in D4Record, GetD4Key method not implemented in Gvar
                    break;
                case "RPI-FILE":
                    // CONTINUE
                    break;
                case "PARAMETER-FILE":
                    // CONTINUE
                    break;
                case "USER-FILE":
                    SetStart(true);
                    // gvar.SetD9(gvar.GetD9()); // SetD9 method not implemented
                    // fvar.SetD9Record(fvar.GetD9Record().SetD9Key(gvar.GetD9Key())); // SetD9Key method not implemented in D9Record, GetD9Key method not implemented in Gvar
                    break;
                case "MASTER-LOG-FILE":
                    SetStart(true);
                    // gvar.SetD17(gvar.GetD17()); // SetD17 method not implemented
                    // fvar.SetD17Record(fvar.GetD17Record().SetD17Key(gvar.GetD17Key())); // SetD17Key method not implemented in D17Record, GetD17Key method not implemented in Gvar
                    break;
                case "REALISED-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD19(gvar.GetD19()); // SetD19 method not implemented
                    gvar.SetD19SplitKey(gvar.GetD19SplitKey());
                    break;
                case "UNREALISED-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD20(gvar.GetD20()); // SetD20 method not implemented
                    gvar.SetD20SplitKey(gvar.GetD20SplitKey());
                    break;
                case "PRINTER-FILE":
                    SetStart(true);
                    // gvar.SetD31(gvar.GetD31()); // SetD31 method not implemented
                    // fvar.SetD31Record(fvar.GetD31Record().SetD31Key(gvar.GetD31Key())); // SetD31Key method not implemented in D31Record, GetD31Key method not implemented in Gvar
                    break;
                case "STOCK-TYPE-FILE":
                    SetStart(true);
                    // gvar.SetD32(gvar.GetD32()); // SetD32 method not implemented
                    // fvar.SetD32Record(fvar.GetD32Record().SetD32Key(gvar.GetD32Key())); // SetD32Key method not implemented in D32Record, GetD32Key method not implemented in Gvar
                    break;
                case "TRANSACTION-CODE-FILE":
                    SetStart(true);
                    // gvar.SetD34(gvar.GetD34()); // SetD34 method not implemented
                    // fvar.SetD34Record(fvar.GetD34Record().SetD34Key(gvar.GetD34Key())); // SetD34Key method not implemented in D34Record, GetD34Key method not implemented in Gvar
                    break;
                case "OUTPUT-LOG-FILE":
                    SetStart(true);
                    // gvar.SetD35(gvar.GetD35()); // SetD35 method not implemented
                    // fvar.SetD35Record(fvar.GetD35Record().SetD35Key(gvar.GetD35Key())); // SetD35Key method not implemented in D35Record, GetD35Key method not implemented in Gvar
                    break;
                case "MESSAGE-FILE":
                    SetStart(true);
                    // gvar.SetD36(gvar.GetD36()); // SetD36 method not implemented
                    // fvar.SetD36Record(fvar.GetD36Record().SetD36Key(gvar.GetD36Key())); // SetD36Key method not implemented in D36Record, GetD36Key method not implemented in Gvar
                    break;
                case "USER-FUND-FILE":
                    // CONTINUE
                    break;
                case "HELP-TEXT-FILE":
                    SetStart(true);
                    // gvar.SetD38(gvar.GetD38()); // SetD38 method not implemented
                    // fvar.SetD38Record(fvar.GetD38Record().SetD38Key(gvar.GetD38Key())); // SetD38Key method not implemented in D38Record, GetD38Key method not implemented in Gvar
                    break;
                case "DEFAULT-ACCESS-FILE":
                    SetStart(true);
                    // gvar.SetD39(gvar.GetD39()); // SetD39 method not implemented
                    // fvar.SetD39Record(fvar.GetD39Record().SetD39Key(gvar.GetD39Key())); // SetD39Key method not implemented in D39Record, GetD39Key method not implemented in Gvar
                    break;
                case "ACCESS-PROFILE-FILE":
                    SetStart(true);
                    // gvar.SetD40(gvar.GetD40()); // SetD40 method not implemented
                    // fvar.SetD40Record(fvar.GetD40Record().SetD40Key(gvar.GetD40Key())); // SetD40Key method not implemented in D40Record, GetD40Key method not implemented in Gvar
                    break;
                case "EXTEL-CURRENCY-FILE":
                    SetStart(true);
                    // gvar.SetD42(gvar.GetD42()); // SetD42 method not implemented
                    // fvar.SetD42Record(fvar.GetD42Record().SetD42Key(gvar.GetD42Key())); // SetD42Key method not implemented in D42Record, GetD42Key method not implemented in Gvar
                    break;
                case "STOCK-PRICE-FILE":
                    SetStart(true);
                    // gvar.SetD43(gvar.GetD43()); // SetD43 method not implemented
                    // fvar.SetD43Record(fvar.GetD43Record().SetD43Key(gvar.GetD43Key())); // SetD43Key method not implemented in D43Record, GetD43Key method not implemented in Gvar
                    break;
                case "SEQ-BALANCE-FILE":
                    SetStart(true);
                    // gvar.SetD45(gvar.GetD45()); // SetD45 method not implemented
                    // fvar.SetD45Record(fvar.GetD45Record().SetD45Key(gvar.GetD45Key())); // SetD45Key method not implemented in D45Record, GetD45Key method not implemented in Gvar
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    SetStart(true);
                    // gvar.SetD79(gvar.GetD79()); // SetD79 method not implemented
                    // fvar.SetD79Record(fvar.GetD79Record().SetD79Key(gvar.GetD79Key())); // SetD79Key method not implemented in D79Record, GetD79Key method not implemented in Gvar
                    break;
                case "REPLACEMENT-DIS-FILE":
                    SetStart(true);
                    // gvar.SetD80(gvar.GetD80()); // SetD80 method not implemented
                    // fvar.SetD80Record(fvar.GetD80Record().SetD80Key(gvar.GetD80Key())); // SetD80Key method not implemented in D80Record, GetD80Key method not implemented in Gvar
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD81(gvar.GetD81()); // SetD81 method not implemented
                    gvar.SetD81SplitKey(gvar.GetD81SplitKey());
                    break;
                case "RCF-FILE":
                    SetStart(true);
                    // gvar.SetD86(gvar.GetD86()); // SetD86 method not implemented
                    // fvar.SetD86Record(fvar.GetD86Record().SetD86Key(gvar.GetD86Key())); // SetD86Key method not implemented in D86Record, GetD86Key method not implemented in Gvar
                    break;
                case "REALISED-TAX-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD98(gvar.GetD98()); // SetD98 method not implemented
                    gvar.SetD98SplitKey(gvar.GetD98SplitKey());
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD101(gvar.GetD101()); // SetD101 method not implemented
                    gvar.SetD101SplitKey(gvar.GetD101SplitKey());
                    break;
                case "TAPER-RATE-FILE":
                    SetStart(true);
                    // gvar.SetD112(gvar.GetD112()); // SetD112 method not implemented
                    // fvar.SetD112Record(fvar.GetD112Record().SetD112Key(gvar.GetD112Key())); // SetD112Key method not implemented in D112Record, GetD112Key method not implemented in Gvar
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    SetStart(true);
                    // gvar.SetD153(gvar.GetD153()); // SetD153 method not implemented
                    // fvar.SetD153Record(fvar.GetD153Record().SetD153Key(gvar.GetD153Key())); // SetD153Key method not implemented in D153Record, GetD153Key method not implemented in Gvar
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    SetStart(true);
                    // gvar.SetD154(gvar.GetD154()); // SetD154 method not implemented
                    // fvar.SetD154Record(fvar.GetD154Record().SetD154Key(gvar.GetD154Key())); // SetD154Key method not implemented in D154Record, GetD154Key method not implemented in Gvar
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    SetStart(true);
                    // gvar.SetD163(gvar.GetD163()); // SetD163 method not implemented
                    // fvar.SetD163Record(fvar.GetD163Record().SetD163Key(gvar.GetD163Key())); // SetD163Key method not implemented in D163Record, GetD163Key method not implemented in Gvar
                    break;
                case "PRICE-TYPES-FILE":
                    SetStart(true);
                    // gvar.SetD167(gvar.GetD167()); // SetD167 method not implemented
                    // fvar.SetD167Record(fvar.GetD167Record().SetD167Key(gvar.GetD167Key())); // SetD167Key method not implemented in D167Record, GetD167Key method not implemented in Gvar
                    break;
                case "PENDING-LOG-FILE":
                    SetStart(true);
                    // gvar.SetD169(gvar.GetD169()); // SetD169 method not implemented
                    // fvar.SetD169Record(fvar.GetD169Record().SetD169Key(gvar.GetD169Key())); // SetD169Key method not implemented in D169Record, GetD169Key method not implemented in Gvar
                    break;
                case "PENDING-ITEMS-FILE":
                    SetStart(true);
                    // gvar.SetD170(gvar.GetD170()); // SetD170 method not implemented
                    // fvar.SetD170Record(fvar.GetD170Record().SetD170Key(gvar.GetD170Key())); // SetD170Key method not implemented in D170Record, GetD170Key method not implemented in Gvar
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// gStartNotLessThan
        /// </summary>
        /// <remarks>
        /// Converted from COBOL paragraph gStartNotLessThan
        /// </remarks>
        public void Gstartnotlessthan(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // CONTINUE
                    break;
                case "GROUP-FILE":
                    // CONTINUE
                    break;
                case "STOCK-FILE":
                    SetStart(gvar.GetD3File());
                   // SetKey(gvar.GetD3Key()); // GetD3Key method not implemented in Gvar
                    // Not implemented: START D3 KEY NOT < D3-KEY
                    break;
                case "FUND-FILE":
                    SetStart(gvar.GetD4File());
                   // SetKey(gvar.GetD4Key()); // GetD4Key method not implemented in Gvar
                    // Not implemented: START D4 KEY NOT < D4-KEY
                    break;
                case "RPI-FILE":
                    // CONTINUE
                    break;
                case "PARAMETER-FILE":
                    // CONTINUE
                    break;
                case "USER-FILE":
                    SetStart(gvar.GetD9File());
                   // SetKey(fvar.GetD9Record().GetD9Key()); // GetD9Key method not implemented in D9Record
                    // Not implemented: START D9 KEY NOT < D9-KEY
                    break;
                case "MASTER-LOG-FILE":
                    SetStart(gvar.GetD17File());
                   // SetKey(fvar.GetD17Record().GetD17Key()); // GetD17Key method not implemented in D17Record
                    // Not implemented: START D17 KEY NOT < D17-KEY
                    break;
                case "REALISED-DATA-FILE":
                    SetStart(gvar.GetD19File());
                   SetKey(gvar.GetD19SplitKey());
                    // Not implemented: START D19 KEY NOT < D19-SPLIT-KEY
                    break;
                case "UNREALISED-DATA-FILE":
                    SetStart(gvar.GetD20File());
                   SetKey(gvar.GetD20SplitKey());
                    // Not implemented: START D20 KEY NOT < D20-SPLIT-KEY
                    break;
                case "PRINTER-FILE":
                    SetStart(gvar.GetD31File());
                   // SetKey(fvar.GetD31Record().GetD31Key()); // GetD31Key method not implemented in D31Record
                    // Not implemented: START D31 KEY NOT < D31-KEY
                    break;
                case "STOCK-TYPE-FILE":
                    SetStart(gvar.GetD32File());
                   SetKey(fvar.GetD32Record().GetD32Key());
                    // Not implemented: START D32 KEY NOT < D32-KEY
                    break;
                case "TRANSACTION-CODE-FILE":
                    SetStart(gvar.GetD34File());
                   SetKey(fvar.GetD34Record().GetD34Key());
                    // Not implemented: START D34 KEY NOT < D34-KEY
                    break;
                case "OUTPUT-LOG-FILE":
                    SetStart(gvar.GetD35File());
                   SetKey(fvar.GetD35Record().GetD35Key());
                    // Not implemented: START D35 KEY NOT < D35-KEY
                    break;
                case "MESSAGE-FILE":
                    SetStart(gvar.GetD36File());
                   SetKey(fvar.GetD36Record().GetD36Key());
                    // Not implemented: START D36 KEY NOT < D36-KEY
                    break;
                case "USER-FUND-FILE":
                    // CONTINUE
                    break;
                case "HELP-TEXT-FILE":
                    SetStart(gvar.GetD38File());
                   SetKey(fvar.GetD38Record().GetD38Key());
                    // Not implemented: START D38 KEY NOT < D38-KEY
                    break;
                case "DEFAULT-ACCESS-FILE":
                    SetStart(gvar.GetD39File());
                   SetKey(fvar.GetD39Record().GetD39Key());
                    // Not implemented: START D39 KEY NOT < D39-KEY
                    break;
                case "ACCESS-PROFILE-FILE":
                    SetStart(gvar.GetD40File());
                   SetKey(fvar.GetD40Record().GetD40Key());
                    // Not implemented: START D40 KEY NOT < D40-KEY
                    break;
                case "EXTEL-CURRENCY-FILE":
                    SetStart(gvar.GetD42File());
                   SetKey(fvar.GetD42Record().GetD42Key());
                    // Not implemented: START D42 KEY NOT < D42-KEY
                    break;
                case "STOCK-PRICE-FILE":
                    SetStart(gvar.GetD43File());
                   SetKey(fvar.GetD43Record().GetD43Key());
                    // Not implemented: START D43 KEY NOT < D43-KEY
                    break;
                case "SEQ-BALANCE-FILE":
                    SetStart(gvar.GetD45File());
                   SetKey(fvar.GetD45Record().GetD45Key());
                    // Not implemented: START D45 KEY NOT < D45-KEY
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    SetStart(gvar.GetD79File());
                   SetKey(fvar.GetD79Record().GetD79Key());
                    // Not implemented: START D79 KEY NOT < D79-KEY
                    break;
                case "REPLACEMENT-DIS-FILE":
                    SetStart(gvar.GetD80File());
                   SetKey(fvar.GetD80Record().GetD80Key());
                    // Not implemented: START D80 KEY NOT < D80-KEY
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    SetStart(gvar.GetD81File());
                   SetKey(gvar.GetD81SplitKey());
                    // Not implemented: START D81 KEY NOT < D81-SPLIT-KEY
                    break;
                case "RCF-FILE":
                    SetStart(gvar.GetD86File());
                   SetKey(fvar.GetD86Record().GetD86Key());
                    // Not implemented: START D86 KEY NOT < D86-KEY
                    break;
                case "REALISED-TAX-DATA-FILE":
                    SetStart(gvar.GetD98File());
                   SetKey(gvar.GetD98SplitKey());
                    // Not implemented: START D98 KEY NOT < D98-SPLIT-KEY
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    SetStart(gvar.GetD101File());
                   SetKey(gvar.GetD101SplitKey());
                    // Not implemented: START D101 KEY NOT < D101-SPLIT-KEY
                    break;
                case "TAPER-RATE-FILE":
                    SetStart(gvar.GetD112File());
                   SetKey(fvar.GetD112Record().GetD112Key());
                    // Not implemented: START D112 KEY NOT < D112-KEY
                    break;
                case "ASSET-USAGE-CALENDAR-FILE":
                    // Create a new instance of the external program
                    AssetUsageCalendarDAL assetUsageCalendarDAL = new AssetUsageCalendarDAL();
                    // Call the Run method with ONLY the parameters specified in the USING clause
                    assetUsageCalendarDAL.Run(gvar.GetAssetusagecalendardal(), ivar.GetLFileRecordArea());
                    if (ivar.GetLFileRecordArea() == "")
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    SetStart(gvar.GetD153File());
                   SetKey(fvar.GetD153Record().GetD153Key());
                    // Not implemented: START D153 KEY NOT < D153-KEY
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    SetStart(gvar.GetD154File());
                   SetKey(fvar.GetD154Record().GetD154Key());
                    // Not implemented: START D154 KEY NOT < D154-KEY
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    SetStart(gvar.GetD163File());
                   SetKey(fvar.GetD163Record().GetD163Key());
                    // Not implemented: START D163 KEY NOT < D163-KEY
                    break;
                case "PRICE-TYPES-FILE":
                    SetStart(gvar.GetD167File());
                   SetKey(fvar.GetD167Record().GetD167Key());
                    // Not implemented: START D167 KEY NOT < D167-KEY
                    break;
                case "PENDING-LOG-FILE":
                    SetStart(gvar.GetD169File());
                   SetKey(fvar.GetD169Record().GetD169Key());
                    // Not implemented: START D169 KEY NOT < D169-KEY
                    break;
                case "PENDING-ITEMS-FILE":
                    SetStart(gvar.GetD170File());
                   SetKey(fvar.GetD170Record().GetD170Key());
                    // Not implemented: START D170 KEY NOT < D170-KEY
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// hStartGreaterThan
        /// </summary>
        /// <remarks>
        /// This method implements the COBOL paragraph hStartGreaterThan.
        /// </remarks>
        public void Hstartgreaterthan(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // CONTINUE
                    break;
                case "GROUP-FILE":
                    // CONTINUE
                    break;
                case "STOCK-FILE":
                    SetStart(gvar.GetD3File());
                    // fvar.SetD3Key(fvar.GetD3Record().GetD3Key()); // SetD3Key method not implemented in Fvar
                    break;
                case "FUND-FILE":
                    SetStart(gvar.GetD4File());
                    // fvar.SetD4Key(fvar.GetD4Record().GetD4Key()); // SetD4Key method not implemented in Fvar
                    break;
                case "RPI-FILE":
                    // CONTINUE
                    break;
                case "PARAMETER-FILE":
                    // CONTINUE
                    break;
                case "USER-FILE":
                    SetStart(gvar.GetD9File());
                    // fvar.SetD9Key(fvar.GetD9Record().GetD9Key()); // SetD9Key method not implemented in Fvar
                    break;
                case "MASTER-LOG-FILE":
                    SetStart(gvar.GetD17File());
                    // fvar.SetD17Key(fvar.GetD17Record().GetD17Key()); // SetD17Key method not implemented in Fvar
                    break;
                case "REALISED-DATA-FILE":
                    SetStart(gvar.GetD19File());
                    // gvar.SetD19SplitKey(gvar.GetD19SplitKey()); // SetD19SplitKey method not implemented in Gvar
                    break;
                case "UNREALISED-DATA-FILE":
                    SetStart(gvar.GetD20File());
                    // gvar.SetD20SplitKey(gvar.GetD20SplitKey()); // SetD20SplitKey method not implemented in Gvar
                    break;
                case "PRINTER-FILE":
                    SetStart(gvar.GetD31File());
                    // fvar.SetD31Key(fvar.GetD31Record().GetD31Key()); // SetD31Key method not implemented in Fvar
                    break;
                case "STOCK-TYPE-FILE":
                    SetStart(gvar.GetD32File());
                    // fvar.SetD32Key(fvar.GetD32Record().GetD32Key()); // SetD32Key method not implemented in Fvar
                    break;
                case "TRANSACTION-CODE-FILE":
                    SetStart(gvar.GetD34File());
                    // fvar.SetD34Key(fvar.GetD34Record().GetD34Key()); // SetD34Key method not implemented in Fvar
                    break;
                case "OUTPUT-LOG-FILE":
                    SetStart(gvar.GetD35File());
                    // fvar.SetD35Key(fvar.GetD35Record().GetD35Key()); // SetD35Key method not implemented in Fvar
                    break;
                case "MESSAGE-FILE":
                    SetStart(gvar.GetD36File());
                    // fvar.SetD36Key(fvar.GetD36Record().GetD36Key()); // SetD36Key method not implemented in Fvar
                    break;
                case "USER-FUND-FILE":
                    // CONTINUE
                    break;
                case "HELP-TEXT-FILE":
                    SetStart(gvar.GetD38File());
                    // fvar.SetD38Key(fvar.GetD38Record().GetD38Key()); // SetD38Key method not implemented in Fvar
                    break;
                case "DEFAULT-ACCESS-FILE":
                    SetStart(gvar.GetD39File());
                    // fvar.SetD39Key(fvar.GetD39Record().GetD39Key()); // SetD39Key method not implemented in Fvar
                    break;
                case "ACCESS-PROFILE-FILE":
                    SetStart(gvar.GetD40File());
                    // fvar.SetD40Key(fvar.GetD40Record().GetD40Key()); // SetD40Key method not implemented in Fvar
                    break;
                case "EXTEL-CURRENCY-FILE":
                    SetStart(gvar.GetD42File());
                    // fvar.SetD42Key(fvar.GetD42Record().GetD42Key()); // SetD42Key method not implemented in Fvar
                    break;
                case "STOCK-PRICE-FILE":
                    SetStart(gvar.GetD43File());
                    // fvar.SetD43Key(fvar.GetD43Record().GetD43Key()); // SetD43Key method not implemented in Fvar
                    break;
                case "SEQ-BALANCE-FILE":
                    SetStart(gvar.GetD45File());
                    // fvar.SetD45Key(fvar.GetD45Record().GetD45Key()); // SetD45Key method not implemented in Fvar
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    SetStart(gvar.GetD79File());
                    // fvar.SetD79Key(fvar.GetD79Record().GetD79Key()); // SetD79Key method not implemented in Fvar
                    break;
                case "REPLACEMENT-DIS-FILE":
                    SetStart(gvar.GetD80File());
                    // fvar.SetD80Key(fvar.GetD80Record().GetD80Key()); // SetD80Key method not implemented in Fvar
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    SetStart(gvar.GetD81File());
                    // gvar.SetD81SplitKey(gvar.GetD81SplitKey()); // SetD81SplitKey method not implemented in Gvar
                    break;
                case "RCF-FILE":
                    SetStart(gvar.GetD86File());
                    // fvar.SetD86Key(fvar.GetD86Record().GetD86Key()); // SetD86Key method not implemented in Fvar
                    break;
                case "REALISED-TAX-DATA-FILE":
                    SetStart(gvar.GetD98File());
                    // gvar.SetD98SplitKey(gvar.GetD98SplitKey()); // SetD98SplitKey method not implemented in Gvar
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    SetStart(gvar.GetD101File());
                    // gvar.SetD101SplitKey(gvar.GetD101SplitKey()); // SetD101SplitKey method not implemented in Gvar
                    break;
                case "TAPER-RATE-FILE":
                    SetStart(gvar.GetD112File());
                    // fvar.SetD112Key(fvar.GetD112Record().GetD112Key()); // SetD112Key method not implemented in Fvar
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    SetStart(gvar.GetD153File());
                    // fvar.SetD153Key(fvar.GetD153Record().GetD153Key()); // SetD153Key method not implemented in Fvar
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    SetStart(gvar.GetD154File());
                    // fvar.SetD154Key(fvar.GetD154Record().GetD154Key()); // SetD154Key method not implemented in Fvar
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    SetStart(gvar.GetD163File());
                    // fvar.SetD163Key(fvar.GetD163Record().GetD163Key()); // SetD163Key method not implemented in Fvar
                    break;
                case "PRICE-TYPES-FILE":
                    SetStart(gvar.GetD167File());
                    // fvar.SetD167Key(fvar.GetD167Record().GetD167Key()); // SetD167Key method not implemented in Fvar
                    break;
                case "PENDING-LOG-FILE":
                    SetStart(gvar.GetD169File());
                    // fvar.SetD169Key(fvar.GetD169Record().GetD169Key()); // SetD169Key method not implemented in Fvar
                    break;
                case "PENDING-ITEMS-FILE":
                    SetStart(gvar.GetD170File());
                    // fvar.SetD170Key(fvar.GetD170Record().GetD170Key()); // SetD170Key method not implemented in Fvar
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// iStartGtInverseKey
        /// </summary>
        /// <remarks>
        /// COBOL paragraph iStartGtInverseKey converted to C# method
        /// </remarks>
        public void Istartgtinversekey(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EVALUATE L-FILE-NAME
            string lFileName = ivar.GetCgtfilesLinkage().GetLFileName();
            if (lFileName == Ivar.COUNTRY_FILE)
            {
                // CONTINUE
            }
            else if (lFileName == Ivar.GROUP_FILE)
            {
                // CONTINUE
            }
            else if (lFileName == Ivar.STOCK_FILE)
            {
                // START D3 KEY > D3-INVERSE-KEY
                // gvar.GetD3().Start(fvar.GetD3Record().GetD3InverseKey(), FileStatus.GreaterThan); // File operation not implemented
            }
            else if (lFileName == Ivar.FUND_FILE)
            {
                // START D4 KEY > D4-INVERSE-KEY
                // gvar.GetD4().Start(fvar.GetD4Record().GetD4InverseKey(), FileStatus.GreaterThan); // File operation not implemented
            }
            else if (lFileName == Ivar.USER_FILE)
            {
                // START D9 KEY > D9-INVERSE-KEY
                // gvar.GetD9().Start(fvar.GetD9Record().GetD9InverseKey(), FileStatus.GreaterThan); // File operation not implemented
            }
            else if (lFileName == Ivar.PRINTER_FILE)
            {
                // START D31 KEY > D31-INVERSE-KEY
                // gvar.GetD31().Start(fvar.GetD31Record().GetD31InverseKey(), FileStatus.GreaterThan); // File operation not implemented
            }
            else if (lFileName == Ivar.USER_FUND_FILE)
            {
                // CONTINUE
            }
            else if (lFileName == Ivar.PERIOD_END_CALENDAR_FILE)
            {
                // START D153 KEY > D153-INVERSE-KEY
                // gvar.GetD153().Start(fvar.GetD153Record().GetD153InverseKey(), FileStatus.GreaterThan); // File operation not implemented
            }
            else if (lFileName == Ivar.INTER_CONNECTED_FUNDS_FILE)
            {
                // START D163 KEY > D163-INVERSE-KEY
                // gvar.GetD163().Start(fvar.GetD163Record().GetD163InverseKey(), FileStatus.GreaterThan); // File operation not implemented
            }
            else if (lFileName == Ivar.PRICE_TYPES_FILE)
            {
                // START D167 KEY > D167-INVERSE-KEY
                // gvar.GetD167().Start(fvar.GetD167Record().GetD167InverseKey(), FileStatus.GreaterThan); // File operation not implemented
            }
            else if (lFileName == gvar.GetCgtlogLinkageArea1().GetPendingLogFile())
            {
                // START D169 KEY > D169-INVERSE-KEY2
                // gvar.GetD169().Start(fvar.GetD169Record().GetD169InverseKey2(), FileStatus.GreaterThan); // File operation not implemented
            }
            else
            {
                // MOVE 'IF' TO FILE-STATUS
                gvar.SetFileStatusAsString("IF");
            }
        }
        /// <summary>
        /// jStartLessThan
        /// </summary>
        /// <remarks>
        /// Conversion of COBOL paragraph jStartLessThan to C# method Jstartlessthan.
        /// </remarks>
        public void Jstartlessthan(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // gvar.SetContinue(true); // SetContinue method not implemented in Gvar
                    break;
                case "GROUP-FILE":
                    // gvar.SetContinue(true); // SetContinue method not implemented in Gvar
                    break;
                case "STOCK-FILE":
                    SetStart(true);
                    // gvar.SetD3(1); // SetD3 method not implemented
                    // fvar.SetD3Record(fvar.GetD3Record().SetD3Key(fvar.GetD3Record().GetD3Key())); // SetD3Record/GetD3Record methods not implemented correctly
                    break;
                case "FUND-FILE":
                    SetStart(true);
                    // gvar.SetD4(1); // SetD4 method not implemented
                    // fvar.SetD4Record(fvar.GetD4Record().SetD4Key(fvar.GetD4Record().GetD4Key())); // SetD4Record/GetD4Record methods not implemented correctly
                    break;
                case "RPI-FILE":
                    // gvar.SetContinue(true); // SetContinue method not implemented in Gvar
                    break;
                case "PARAMETER-FILE":
                    // gvar.SetContinue(true); // SetContinue method not implemented in Gvar
                    break;
                case "USER-FILE":
                    SetStart(true);
                    // gvar.SetD9(1); // SetD9 method not implemented
                    // fvar.SetD9Record(fvar.GetD9Record().SetD9Key(fvar.GetD9Record().GetD9Key())); // SetD9Record/GetD9Record methods not implemented correctly
                    break;
                case "MASTER-LOG-FILE":
                    SetStart(true);
                    // gvar.SetD17(1); // SetD17 method not implemented
                    // fvar.SetD17Record(fvar.GetD17Record().SetD17Key(fvar.GetD17Record().GetD17Key())); // SetD17Record/GetD17Record methods not implemented correctly
                    break;
                case "REALISED-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD19(1); // SetD19 method not implemented
                    // gvar.SetD19SplitKey(gvar.GetD19SplitKey()); // SetD19SplitKey method not implemented in Gvar
                    break;
                case "UNREALISED-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD20(1); // SetD20 method not implemented
                    // gvar.SetD20SplitKey(gvar.GetD20SplitKey()); // SetD20SplitKey method not implemented in Gvar
                    break;
                case "PRINTER-FILE":
                    SetStart(true);
                    // gvar.SetD31(1); // SetD31 method not implemented
                    // fvar.SetD31Record(fvar.GetD31Record().SetD31Key(fvar.GetD31Record().GetD31Key())); // SetD31Record/GetD31Record methods not implemented correctly
                    break;
                case "STOCK-TYPE-FILE":
                    SetStart(true);
                    // gvar.SetD32(1); // SetD32 method not implemented
                    // fvar.SetD32Record(fvar.GetD32Record().SetD32Key(fvar.GetD32Record().GetD32Key())); // SetD32Record/GetD32Record methods not implemented correctly
                    break;
                case "TRANSACTION-CODE-FILE":
                    SetStart(true);
                    // gvar.SetD34(1); // SetD34 method not implemented
                    // fvar.SetD34Record(fvar.GetD34Record().SetD34Key(fvar.GetD34Record().GetD34Key())); // SetD34Record/GetD34Record methods not implemented correctly
                    break;
                case "OUTPUT-LOG-FILE":
                    SetStart(true);
                    // gvar.SetD35(1); // SetD35 method not implemented
                    // fvar.SetD35Record(fvar.GetD35Record().SetD35Key(fvar.GetD35Record().GetD35Key())); // SetD35Record/GetD35Record methods not implemented correctly
                    break;
                case "MESSAGE-FILE":
                    SetStart(true);
                    // gvar.SetD36(1); // SetD36 method not implemented
                    // fvar.SetD36Record(fvar.GetD36Record().SetD36Key(fvar.GetD36Record().GetD36Key())); // SetD36Record/GetD36Record methods not implemented correctly
                    break;
                case "USER-FUND-FILE":
                    // gvar.SetContinue(true); // SetContinue method not implemented in Gvar
                    break;
                case "HELP-TEXT-FILE":
                    SetStart(true);
                    // gvar.SetD38(1); // SetD38 method not implemented
                    fvar.SetD38Record(fvar.GetD38Record().SetD38Key(fvar.GetD38Record().GetD38Key()));
                    break;
                case "DEFAULT-ACCESS-FILE":
                    SetStart(true);
                    // gvar.SetD39(1); // SetD39 method not implemented
                    fvar.SetD39Record(fvar.GetD39Record().SetD39Key(fvar.GetD39Record().GetD39Key()));
                    break;
                case "ACCESS-PROFILE-FILE":
                    SetStart(true);
                    // gvar.SetD40(1); // SetD40 method not implemented
                    fvar.SetD40Record(fvar.GetD40Record().SetD40Key(fvar.GetD40Record().GetD40Key()));
                    break;
                case "EXTEL-CURRENCY-FILE":
                    SetStart(true);
                    // gvar.SetD42(1); // SetD42 method not implemented
                    fvar.SetD42Record(fvar.GetD42Record().SetD42Key(fvar.GetD42Record().GetD42Key()));
                    break;
                case "STOCK-PRICE-FILE":
                    SetStart(true);
                    // gvar.SetD43(1); // SetD43 method not implemented
                    fvar.SetD43Record(fvar.GetD43Record().SetD43Key(fvar.GetD43Record().GetD43Key()));
                    break;
                case "SEQ-BALANCE-FILE":
                    SetStart(true);
                    // gvar.SetD45(1); // SetD45 method not implemented
                    fvar.SetD45Record(fvar.GetD45Record().SetD45Key(fvar.GetD45Record().GetD45Key()));
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    SetStart(true);
                    // gvar.SetD79(1); // SetD79 method not implemented
                    fvar.SetD79Record(fvar.GetD79Record().SetD79Key(fvar.GetD79Record().GetD79Key()));
                    break;
                case "REPLACEMENT-DIS-FILE":
                    SetStart(true);
                    // gvar.SetD80(1); // SetD80 method not implemented
                    fvar.SetD80Record(fvar.GetD80Record().SetD80Key(fvar.GetD80Record().GetD80Key()));
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD81(1); // SetD81 method not implemented
                    gvar.SetD81SplitKey(gvar.GetD81SplitKey());
                    break;
                case "RCF-FILE":
                    SetStart(true);
                    // gvar.SetD86(1); // SetD86 method not implemented
                    fvar.SetD86Record(fvar.GetD86Record().SetD86Key(fvar.GetD86Record().GetD86Key()));
                    break;
                case "REALISED-TAX-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD98(1); // SetD98 method not implemented
                    gvar.SetD98SplitKey(gvar.GetD98SplitKey());
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    SetStart(true);
                    // gvar.SetD101(1); // SetD101 method not implemented
                    gvar.SetD101SplitKey(gvar.GetD101SplitKey());
                    break;
                case "TAPER-RATE-FILE":
                    SetStart(true);
                    // gvar.SetD112(1); // SetD112 method not implemented
                    fvar.SetD112Record(fvar.GetD112Record().SetD112Key(fvar.GetD112Record().GetD112Key()));
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    SetStart(true);
                    // gvar.SetD153(1); // SetD153 method not implemented
                    fvar.SetD153Record(fvar.GetD153Record().SetD153Key(fvar.GetD153Record().GetD153Key()));
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    SetStart(true);
                    // gvar.SetD154(1); // SetD154 method not implemented
                    fvar.SetD154Record(fvar.GetD154Record().SetD154Key(fvar.GetD154Record().GetD154Key()));
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    SetStart(true);
                    // gvar.SetD163(1); // SetD163 method not implemented
                    fvar.SetD163Record(fvar.GetD163Record().SetD163Key(fvar.GetD163Record().GetD163Key()));
                    break;
                case "PRICE-TYPES-FILE":
                    SetStart(true);
                    // gvar.SetD167(1); // SetD167 method not implemented
                    fvar.SetD167Record(fvar.GetD167Record().SetD167Key(fvar.GetD167Record().GetD167Key()));
                    break;
                case "PENDING-LOG-FILE":
                    SetStart(true);
                    // gvar.SetD169(1); // SetD169 method not implemented
                    fvar.SetD169Record(fvar.GetD169Record().SetD169Key(fvar.GetD169Record().GetD169Key()));
                    break;
                case "PENDING-ITEMS-FILE":
                    SetStart(true);
                    // gvar.SetD170(1); // SetD170 method not implemented
                    fvar.SetD170Record(fvar.GetD170Record().SetD170Key(fvar.GetD170Record().GetD170Key()));
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// kStartNotGreaterThan
        /// </summary>
        /// <remarks>
        /// Converted from COBOL paragraph kStartNotGreaterThan
        /// </remarks>
        public void Kstartnotgreaterthan(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // CONTINUE
                    break;
                case "GROUP-FILE":
                    // CONTINUE
                    break;
                case "STOCK-FILE":
                    SetStart(gvar.GetD3File());
                    fvar.SetD3Record(fvar.GetD3Record().GetD3Key());
                    break;
                case "FUND-FILE":
                    SetStart(gvar.GetD4File());
                    fvar.SetD4Record(fvar.GetD4Record().GetD4Key());
                    break;
                case "RPI-FILE":
                    // CONTINUE
                    break;
                case "PARAMETER-FILE":
                    // CONTINUE
                    break;
                case "USER-FILE":
                    SetStart(gvar.GetD9File());
                    fvar.SetD9Record(fvar.GetD9Record().GetD9Key());
                    break;
                case "MASTER-LOG-FILE":
                    SetStart(gvar.GetD17File());
                    fvar.SetD17Record(fvar.GetD17Record().GetD17Key());
                    break;
                case "REALISED-DATA-FILE":
                    SetStart(gvar.GetD19File());
                    gvar.SetD19SplitKey(gvar.GetD19SplitKey());
                    break;
                case "UNREALISED-DATA-FILE":
                    SetStart(gvar.GetD20File());
                    gvar.SetD20SplitKey(gvar.GetD20SplitKey());
                    break;
                case "PRINTER-FILE":
                    SetStart(gvar.GetD31File());
                    fvar.SetD31Record(fvar.GetD31Record().GetD31Key());
                    break;
                case "STOCK-TYPE-FILE":
                    SetStart(gvar.GetD32File());
                    fvar.SetD32Record(fvar.GetD32Record().GetD32Key());
                    break;
                case "TRANSACTION-CODE-FILE":
                    SetStart(gvar.GetD34File());
                    fvar.SetD34Record(fvar.GetD34Record().GetD34Key());
                    break;
                case "OUTPUT-LOG-FILE":
                    SetStart(gvar.GetD35File());
                    fvar.SetD35Record(fvar.GetD35Record().GetD35Key());
                    break;
                case "MESSAGE-FILE":
                    SetStart(gvar.GetD36File());
                    fvar.SetD36Record(fvar.GetD36Record().GetD36Key());
                    break;
                case "USER-FUND-FILE":
                    // CONTINUE
                    break;
                case "HELP-TEXT-FILE":
                    SetStart(gvar.GetD38File());
                    fvar.SetD38Record(fvar.GetD38Record().GetD38Key());
                    break;
                case "DEFAULT-ACCESS-FILE":
                    SetStart(gvar.GetD39File());
                    fvar.SetD39Record(fvar.GetD39Record().GetD39Key());
                    break;
                case "ACCESS-PROFILE-FILE":
                    SetStart(gvar.GetD40File());
                    fvar.SetD40Record(fvar.GetD40Record().GetD40Key());
                    break;
                case "EXTEL-CURRENCY-FILE":
                    SetStart(gvar.GetD42File());
                    fvar.SetD42Record(fvar.GetD42Record().GetD42Key());
                    break;
                case "STOCK-PRICE-FILE":
                    SetStart(gvar.GetD43File());
                    fvar.SetD43Record(fvar.GetD43Record().GetD43Key());
                    break;
                case "SEQ-BALANCE-FILE":
                    SetStart(gvar.GetD45File());
                    fvar.SetD45Record(fvar.GetD45Record().GetD45Key());
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    SetStart(gvar.GetD79File());
                    fvar.SetD79Record(fvar.GetD79Record().GetD79Key());
                    break;
                case "REPLACEMENT-DIS-FILE":
                    SetStart(gvar.GetD80File());
                    fvar.SetD80Record(fvar.GetD80Record().GetD80Key());
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    SetStart(gvar.GetD81File());
                    gvar.SetD81SplitKey(gvar.GetD81SplitKey());
                    break;
                case "RCF-FILE":
                    SetStart(gvar.GetD86File());
                    fvar.SetD86Record(fvar.GetD86Record().GetD86Key());
                    break;
                case "REALISED-TAX-DATA-FILE":
                    SetStart(gvar.GetD98File());
                    gvar.SetD98SplitKey(gvar.GetD98SplitKey());
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    SetStart(gvar.GetD101File());
                    gvar.SetD101SplitKey(gvar.GetD101SplitKey());
                    break;
                case "TAPER-RATE-FILE":
                    SetStart(gvar.GetD112File());
                    fvar.SetD112Record(fvar.GetD112Record().GetD112Key());
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    SetStart(gvar.GetD153File());
                    fvar.SetD153Record(fvar.GetD153Record().GetD153Key());
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    SetStart(gvar.GetD154File());
                    fvar.SetD154Record(fvar.GetD154Record().GetD154Key());
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    SetStart(gvar.GetD163File());
                    fvar.SetD163Record(fvar.GetD163Record().GetD163Key());
                    break;
                case "PRICE-TYPES-FILE":
                    SetStart(gvar.GetD167File());
                    fvar.SetD167Record(fvar.GetD167Record().GetD167Key());
                    break;
                case "PENDING-LOG-FILE":
                    SetStart(gvar.GetD169File());
                    fvar.SetD169Record(fvar.GetD169Record().GetD169Key());
                    break;
                case "PENDING-ITEMS-FILE":
                    SetStart(gvar.GetD170File());
                    fvar.SetD170Record(fvar.GetD170Record().GetD170Key());
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// lReadNext
        /// </summary>
        /// <remarks>
        /// Reads the next record from a file based on L-FILE-NAME.
        /// </remarks>
        public void Lreadnext(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    var countryRecordArea = ivar.GetLFileRecordAreaAsString();
                    new CountryDAL().Run(ivar.GetCgtfilesLinkage().GetLFileAction(), ref countryRecordArea);
                    ivar.SetLFileRecordAreaAsString(countryRecordArea);
                    if (string.IsNullOrEmpty(ivar.GetLFileRecordAreaAsString()))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;

                case "GROUP-FILE":
                    // GroupDAL groupDal = new GroupDAL();
                    // groupDal.Run(gvar.GetLFileAction(), ivar.GetLFileRecordArea()); // GroupDAL has different method signature
                    if (string.IsNullOrEmpty(ivar.GetLFileRecordAreaAsString()))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;

                case "STOCK-FILE":
                    // gvar.SetD3(gvar.GetD3() + 1); // SetD3 method not implemented
                    // gvar.SetInto(gvar.GetD3File()); // SetInto method not implemented
                    break;

                case "FUND-FILE":
                    // gvar.SetD4(gvar.GetD4() + 1); // SetD4 method not implemented
                    // gvar.SetInto(gvar.GetD4File()); // SetInto method not implemented
                    break;

                case "CGTR04-REPORT-FILE":
                    // gvar.SetD5(gvar.GetD5() + 1); // SetD5 method not implemented
                    // gvar.SetInto(gvar.GetD5File()); // SetInto method not implemented
                    break;

                case "CGTR05-REPORT-FILE":
                    // gvar.SetD6(gvar.GetD6() + 1); // SetD6 method not implemented
                    // gvar.SetInto(gvar.GetD6File()); // SetInto method not implemented
                    break;

                case "RPI-FILE":
                    RPIDAL rpiDal = new RPIDAL();
                    string rpiRecordArea = ivar.GetLFileRecordAreaAsString();
                    rpiDal.ProcessFile(ivar.GetCgtfilesLinkage().GetLFileAction(), ref rpiRecordArea);
                    ivar.SetLFileRecordAreaAsString(rpiRecordArea);
                    if (string.IsNullOrEmpty(ivar.GetLFileRecordAreaAsString()))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;

                case "PARAMETER-FILE":
                    var paramRecordArea3 = ivar.GetLFileRecordAreaAsString();
                    new ParametersDAL().Run(ivar.GetCgtfilesLinkage().GetLFileAction(), ref paramRecordArea3);
                    ivar.SetLFileRecordAreaAsString(paramRecordArea3);
                    if (string.IsNullOrEmpty(ivar.GetLFileRecordAreaAsString()))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;

                case "USER-FILE":
                    // gvar.SetD9(gvar.GetD9() + 1); // SetD9 method not implemented
                    // gvar.SetInto(gvar.GetD9File()); // SetInto method not implemented
                    break;

                case "STERLING-EXTEL-REPORT":
                    // gvar.SetD10(gvar.GetD10() + 1); // SetD10 method not implemented
                    // gvar.SetInto(gvar.GetD10File()); // SetInto method not implemented
                    break;

                case "FOREIGN-EXTEL-REPORT":
                    // gvar.SetD11(gvar.GetD11() + 1); // SetD11 method not implemented
                    // gvar.SetInto(gvar.GetD11File()); // SetInto method not implemented
                    break;

                case "MASTER-LOG-FILE":
                    // gvar.SetD17(gvar.GetD17() + 1); // SetD17 method not implemented
                    // gvar.SetInto(gvar.GetD17File()); // SetInto method not implemented
                    break;

                case "ERROR-REPORT-FILE":
                    // gvar.SetD18(gvar.GetD18() + 1); // SetD18 method not implemented
                    // gvar.SetInto(gvar.GetD18File()); // SetInto method not implemented
                    break;

                case "REALISED-DATA-FILE":
                    // gvar.SetD19(gvar.GetD19() + 1); // SetD19 method not implemented
                    // gvar.SetInto(gvar.GetD19File()); // SetInto method not implemented
                    break;

                case "UNREALISED-DATA-FILE":
                    // gvar.SetD20(gvar.GetD20() + 1); // SetD20 method not implemented
                    // gvar.SetInto(gvar.GetD20File()); // SetInto method not implemented
                    break;

                case "REALISED-SCHEDULE-FILE":
                    // gvar.SetD21(gvar.GetD21() + 1); // SetD21 method not implemented
                    // gvar.SetInto(gvar.GetD21File()); // SetInto method not implemented
                    break;

                case "UNREALISED-SCHEDULE-FILE":
                    // gvar.SetD22(gvar.GetD22() + 1); // SetD22 method not implemented
                    // gvar.SetInto(gvar.GetD22File()); // SetInto method not implemented
                    break;

                case "CG01-REPORT-FILE":
                    // gvar.SetD23(gvar.GetD23() + 1); // SetD23 method not implemented
                    // gvar.SetInto(gvar.GetD23File()); // SetInto method not implemented
                    break;

                case "CG02-REPORT-FILE":
                    // gvar.SetD24(gvar.GetD24() + 1); // SetD24 method not implemented
                    // gvar.SetInto(gvar.GetD24File()); // SetInto method not implemented
                    break;

                case "CG03-REPORT-FILE":
                    // gvar.SetD25(gvar.GetD25() + 1); // SetD25 method not implemented
                    // gvar.SetInto(gvar.GetD25File()); // SetInto method not implemented
                    break;

                case "YE-REC-REPORT-FILE":
                    // gvar.SetD26(gvar.GetD26() + 1); // SetD26 method not implemented
                    // gvar.SetInto(gvar.GetD26File()); // SetInto method not implemented
                    break;

                case "YE-DEL-REPORT-FILE":
                    // gvar.SetD27(gvar.GetD27() + 1); // SetD27 method not implemented
                    // gvar.SetInto(gvar.GetD27File()); // SetInto method not implemented
                    break;

                case "YE-CON-REPORT-FILE":
                    // gvar.SetD28(gvar.GetD28() + 1); // SetD28 method not implemented
                    // gvar.SetInto(gvar.GetD28File()); // SetInto method not implemented
                    break;

                case "ERROR-DATA-FILE":
                    // gvar.SetD29(gvar.GetD29() + 1); // SetD29 method not implemented
                    // gvar.SetInto(gvar.GetD29File()); // SetInto method not implemented
                    break;

                case "YE-ERR-REPORT-FILE":
                    // gvar.SetD30(gvar.GetD30() + 1); // SetD30 method not implemented
                    // gvar.SetInto(gvar.GetD30File()); // SetInto method not implemented
                    break;

                case "PRINTER-FILE":
                    // gvar.SetD31(gvar.GetD31() + 1); // SetD31 method not implemented
                    // gvar.SetInto(gvar.GetD31File()); // SetInto method not implemented
                    break;

                case "STOCK-TYPE-FILE":
                    // gvar.SetD32(gvar.GetD32() + 1); // SetD32 method not implemented
                    // gvar.SetInto(gvar.GetD32File()); // SetInto method not implemented
                    break;

                case "YE-REC2-DATA-FILE":
                    // gvar.SetD33(gvar.GetD33() + 1); // SetD33 method not implemented
                    // gvar.SetInto(gvar.GetD33File()); // SetInto method not implemented
                    break;

                case "TRANSACTION-CODE-FILE":
                    // gvar.SetD34(gvar.GetD34() + 1); // SetD34 method not implemented
                    // gvar.SetInto(gvar.GetD34File()); // SetInto method not implemented
                    break;

                case "OUTPUT-LOG-FILE":
                    // gvar.SetD35(gvar.GetD35() + 1); // SetD35 method not implemented
                    // gvar.SetInto(gvar.GetD35File()); // SetInto method not implemented
                    break;

                case "MESSAGE-FILE":
                    // gvar.SetD36(gvar.GetD36() + 1); // SetD36 method not implemented
                    // gvar.SetInto(gvar.GetD36File()); // SetInto method not implemented
                    break;

                case "USER-FUND-FILE":
                    UserFundsDAL userFundsDal = new UserFundsDAL();
                    string userFundRecordArea3 = ivar.GetLFileRecordAreaAsString();
                    userFundsDal.Execute(ivar.GetCgtfilesLinkage().GetLFileAction(), ref userFundRecordArea3, ivar.GetCommonLinkage().GetLUserNo());
                    ivar.SetLFileRecordAreaAsString(userFundRecordArea3);
                    if (string.IsNullOrEmpty(userFundRecordArea3))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;

                case "HELP-TEXT-FILE":
                    // gvar.SetD38(gvar.GetD38() + 1); // SetD38 method not implemented
                    // gvar.SetInto(gvar.GetD38File()); // SetInto method not implemented
                    break;

                case "DEFAULT-ACCESS-FILE":
                    // gvar.SetD39(gvar.GetD39() + 1); // SetD39 method not implemented
                    // gvar.SetInto(gvar.GetD39File()); // SetInto method not implemented
                    break;

                case "ACCESS-PROFILE-FILE":
                    // gvar.SetD40(gvar.GetD40() + 1); // SetD40 method not implemented
                    // gvar.SetInto(gvar.GetD40File()); // SetInto method not implemented
                    break;

                case "EXTEL-PRICES-FILE":
                    // gvar.SetD41(gvar.GetD41() + 1); // SetD41 method not implemented
                    // gvar.SetInto(gvar.GetD41File()); // SetInto method not implemented
                    break;

                case "EXTEL-CURRENCY-FILE":
                    // gvar.SetD42(gvar.GetD42() + 1); // SetD42 method not implemented
                    // gvar.SetInto(gvar.GetD42File()); // SetInto method not implemented
                    break;

                case "STOCK-PRICE-FILE":
                    // gvar.SetD43(gvar.GetD43() + 1); // SetD43 method not implemented
                    // gvar.SetInto(gvar.GetD43File()); // SetInto method not implemented
                    break;

                case "EXTEL-TRANSMISSION-FILE":
                    // gvar.SetD44(gvar.GetD44() + 1); // SetD44 method not implemented
                    // gvar.SetInto(gvar.GetD44File()); // SetInto method not implemented
                    break;

                case "SEQ-BALANCE-FILE":
                    // gvar.SetD45(gvar.GetD45() + 1); // SetD45 method not implemented
                    // gvar.SetInto(gvar.GetD45File()); // SetInto method not implemented
                    break;

                case "TRANSACTION-FILE":
                    // gvar.SetD46(gvar.GetD46() + 1); // SetD46 method not implemented
                    // gvar.SetInto(gvar.GetD46File()); // SetInto method not implemented
                    break;

                case "YE-BAL-REPORT-FILE":
                    // gvar.SetD49(gvar.GetD49() + 1); // SetD49 method not implemented
                    // gvar.SetInto(gvar.GetD49File()); // SetInto method not implemented
                    break;

                case "STOCK-LOAD-REPORT":
                    // gvar.SetD50(gvar.GetD50() + 1); // SetD50 method not implemented
                    // gvar.SetInto(gvar.GetD50File()); // SetInto method not implemented
                    break;

                case "STOCK-LOAD-DATA-FILE":
                    // gvar.SetD51(gvar.GetD51() + 1); // SetD51 method not implemented
                    // gvar.SetInto(gvar.GetD51File()); // SetInto method not implemented
                    break;

                case "FUNDS-LOAD-DATA-FILE":
                    // gvar.SetD68(gvar.GetD68() + 1); // SetD68 method not implemented
                    // gvar.SetInto(gvar.GetD68File()); // SetInto method not implemented in Gvar
                    break;

                case "FUNDS-LOAD-REPORT":
                    // gvar.SetD69(gvar.GetD69() + 1); // SetD69 method not implemented
                    // gvar.SetInto(gvar.GetD69File()); // SetInto method not implemented in Gvar
                    break;

                case "PRICE-LOAD-DATA-FILE":
                    // gvar.SetD70(gvar.GetD70() + 1); // SetD70 method not implemented
                    // gvar.SetInto(gvar.GetD70File()); // SetInto method not implemented in Gvar
                    break;

                case "PRICE-LOAD-REPORT":
                    // gvar.SetD71(gvar.GetD71() + 1); // SetD71 method not implemented
                    // gvar.SetInto(gvar.GetD71File()); // SetInto method not implemented in Gvar
                    break;

                case "SKAN1-REPORT":
                    // gvar.SetD72(gvar.GetD72() + 1); // SetD72 method not implemented
                    // gvar.SetInto(gvar.GetD72File()); // SetInto method not implemented in Gvar
                    break;

                case "SKAN2-REPORT":
                    // gvar.SetD73(gvar.GetD73() + 1); // SetD73 method not implemented
                    // gvar.SetInto(gvar.GetD73()); // SetInto method not implemented
                    break;

                case "NEW-REALISED-REPORT":
                    // gvar.SetD74(gvar.GetD74() + 1); // SetD74 method not implemented
                    // gvar.SetInto(gvar.GetD74()); // SetInto method not implemented
                    break;

                case "NEW-UNREALISED-REPORT":
                    // gvar.SetD75(gvar.GetD75() + 1); // SetD75 method not implemented
                    // gvar.SetInto(gvar.GetD75()); // SetInto method not implemented
                    break;

                case "MGM1-REPORT-FILE":
                    // gvar.SetD76(gvar.GetD76() + 1); // SetD76 method not implemented
                    // gvar.SetInto(gvar.GetD76()); // SetInto method not implemented
                    break;

                case "CAPITAL-REPORT-FILE":
                    // gvar.SetD77(gvar.GetD77() + 1); // SetD77 method not implemented
                    // gvar.SetInto(gvar.GetD77()); // SetInto method not implemented
                    break;

                case "REPLACEMENT-RELIEF-REPORT":
                    // gvar.SetD78(gvar.GetD78() + 1); // SetD78 method not implemented
                    // gvar.SetInto(gvar.GetD78()); // SetInto method not implemented
                    break;

                case "REPLACEMENT-ACQ-FILE":
                    // gvar.SetD79(gvar.GetD79() + 1); // SetD79 method not implemented
                    // gvar.SetInto(gvar.GetD79()); // SetInto method not implemented
                    break;

                case "REPLACEMENT-DIS-FILE":
                    // gvar.SetD80(gvar.GetD80() + 1); // SetD80 method not implemented
                    // gvar.SetInto(gvar.GetD80()); // SetInto method not implemented
                    break;

                case "NOTIONAL-SALE-DATA-FILE":
                    // gvar.SetD81(gvar.GetD81() + 1); // SetD81 method not implemented
                    // gvar.SetInto(gvar.GetD81()); // SetInto method not implemented
                    break;

                case "NOTIONAL-SALE-SCHEDULE-FILE":
                    // gvar.SetD82(gvar.GetD82() + 1); // SetD82 method not implemented
                    // gvar.SetInto(gvar.GetD82()); // SetInto method not implemented
                    break;

                case "BALANCE-LOAD-DATA-FILE":
                    // gvar.SetD83(gvar.GetD83() + 1); // SetD83 method not implemented
                    // gvar.SetInto(gvar.GetD83()); // SetInto method not implemented
                    break;

                case "BALANCES-LOAD-REPORT":
                    // gvar.SetD84(gvar.GetD84() + 1); // SetD84 method not implemented
                    // gvar.SetInto(gvar.GetD84()); // SetInto method not implemented
                    break;

                case "OFFSHORE-INCOME-REPORT":
                    // gvar.SetD85(gvar.GetD85() + 1); // SetD85 method not implemented
                    // gvar.SetInto(gvar.GetD85()); // SetInto method not implemented
                    break;

                case "RCF-FILE":
                    // gvar.SetD86(gvar.GetD86() + 1); // SetD86 method not implemented
                    // gvar.SetInto(gvar.GetD86()); // SetInto method not implemented
                    break;

                case "RCF-BACKUP-FILE":
                    // gvar.SetD87(gvar.GetD87() + 1); // SetD87 method not implemented
                    // gvar.SetInto(gvar.GetD87()); // SetInto method not implemented
                    break;

                case "GROUP-LOAD-REPORT":
                    // gvar.SetD88(gvar.GetD88() + 1); // SetD88 method not implemented
                    // gvar.SetInto(gvar.GetD88()); // SetInto method not implemented
                    break;

                case "GROUP-LOAD-DATA-FILE":
                    // gvar.SetD89(gvar.GetD89() + 1); // SetD89 method not implemented
                    // gvar.SetInto(gvar.GetD89()); // SetInto method not implemented
                    break;

                case "COUNTRY-LOAD-REPORT":
                    // gvar.SetD90(gvar.GetD90() + 1); // SetD90 method not implemented
                    // gvar.SetInto(gvar.GetD90()); // SetInto method not implemented
                    break;

                case "COUNTRY-LOAD-DATA-FILE":
                    // gvar.SetD91(gvar.GetD91() + 1); // SetD91 method not implemented
                    // gvar.SetInto(gvar.GetD91()); // SetInto method not implemented
                    break;

                case "RPI-LOAD-REPORT":
                    // gvar.SetD92(gvar.GetD92() + 1); // SetD92 method not implemented
                    // gvar.SetInto(gvar.GetD92()); // SetInto method not implemented
                    break;

                case "RPI-LOAD-DATA-FILE":
                    // gvar.SetD93(gvar.GetD93() + 1); // SetD93 method not implemented
                    // gvar.SetInto(gvar.GetD93()); // SetInto method not implemented
                    break;

                case "GAINLOSS-DATA-FILE":
                    // gvar.SetD94(gvar.GetD94() + 1); // SetD94 method not implemented
                    // gvar.SetInto(gvar.GetD94()); // SetInto method not implemented
                    break;

                case "GAINLOSS-REPORT":
                    // gvar.SetD95(gvar.GetD95() + 1); // SetD95 method not implemented
                    // gvar.SetInto(gvar.GetD95()); // SetInto method not implemented
                    break;

                case "REALISED-TAX-DATA-FILE":
                    // gvar.SetD98(gvar.GetD98() + 1); // SetD98 method not implemented
                    // gvar.SetInto(gvar.GetD98()); // SetInto method not implemented
                    break;

                case "UNREALISED-TAX-DATA-FILE":
                    // gvar.SetD101(gvar.GetD101() + 1); // SetD101 method not implemented
                    // gvar.SetInto(gvar.GetD101()); // SetInto method not implemented
                    break;

                case "BATCH-RUN-LOG-FILE":
                    // gvar.SetContinue(gvar.GetContinue()); // SetContinue and GetContinue methods not implemented in Gvar
                    break;

                case "BATCH-QUIT-RUN-FILE":
                    // gvar.SetD107(gvar.GetD107() + 1); // SetD107 method not implemented
                    // gvar.SetInto(gvar.GetD107File()); // SetInto method not implemented
                    break;

                case "TRACE-FILE":
                    // gvar.SetD108(gvar.GetD108() + 1); // SetD108 method not implemented
                    // gvar.SetInto(gvar.GetD108File()); // SetInto method not implemented
                    break;

                case "ERROR-LOG-FILE":
                    // gvar.SetD109(gvar.GetD109() + 1); // SetD109 method not implemented
                    // gvar.SetInto(gvar.GetD109File()); // SetInto method not implemented
                    break;

                case "TAPER-RATE-FILE":
                    // TaperRateDAL taperRateDal = new TaperRateDAL();
                    // taperRateDal.Run(gvar.GetLFileAction(), ivar.GetLFileRecordArea()); // TaperRateDAL has different method signature
                    if (string.IsNullOrEmpty(ivar.GetLFileRecordAreaAsString()))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;

                case "ASSET-USAGE-CALENDAR-FILE":
                    AssetUsageCalendarDAL assetUsageCalendarDal = new AssetUsageCalendarDAL();
                    // assetUsageCalendarDal.Run(gvar.GetLFileAction(), ivar.GetLFileRecordArea()); // GetLFileAction method not implemented in Gvar
                    if (string.IsNullOrEmpty(ivar.GetLFileRecordArea()))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;

                case "PERIOD-END-CALENDAR-FILE":
                    // gvar.SetD153(gvar.GetD153() + 1); // SetD153 method not implemented
                    // gvar.SetInto(gvar.GetD153File()); // SetInto method not implemented
                    break;

                case "PERIOD-END-CALENDAR-DATES-FILE":
                    // gvar.SetD154(gvar.GetD154() + 1); // SetD154 method not implemented
                    // gvar.SetInto(gvar.GetD154File()); // SetInto method not implemented
                    break;

                case "INTER-CONNECTED-FUNDS-FILE":
                    // gvar.SetD163(gvar.GetD163() + 1); // SetD163 method not implemented
                    // gvar.SetInto(gvar.GetD163File()); // SetInto method not implemented
                    break;

                case "DISPOSALS-FROM-DB-FILE":
                    fvar.SetD161RecordAsString("0");
                    gvar.SetFileStatusAsString("0");
                    int wD161CharSub = 1;
                    byte[] wD161Record = new byte[0];
                    while (gvar.GetFileStatus() != "10" && gvar.GetFileStatus() != "IF")
                    {
                        // gvar.SetD161(gvar.GetD161() + 1); // SetD161 method not implemented
                        // gvar.SetInto(gvar.GetD161File()); // SetInto method not implemented
                        byte[] d161RecordByte = fvar.GetD161Record().GetD161RecordByte();
                        switch (d161RecordByte[0])
                        {
                            case 13:
                                wD161Record = wD161Record.Concat(fvar.GetD161Record().GetWD161Record()).ToArray();
                                wD161CharSub = 1;
                                ivar.SetLFileRecordAreaAsString(wD161Record.ToString());
                                break;
                            default:
                                if (d161RecordByte[0] == 10 || d161RecordByte[0] == 12)
                                {
                                    wD161CharSub = 1;
                                }
                                else
                                {
                                    wD161Record = wD161Record.Concat(fvar.GetD161Record().GetWD161Char(wD161CharSub)).ToArray();
                                    wD161CharSub += 1;
                                }
                                break;
                        }
                    }
                    break;

                case "PRICE-TYPES-FILE":
                    // gvar.SetD167(gvar.GetD167() + 1); // SetD167 method not implemented
                    // gvar.SetInto(gvar.GetD167File()); // SetInto method not implemented
                    break;

                case "PENDING-LOG-FILE":
                    // gvar.SetD169(gvar.GetD169() + 1); // SetD169 method not implemented
                    // gvar.SetInto(gvar.GetD169File()); // SetInto method not implemented
                    break;

                case "PENDING-ITEMS-FILE":
                    // gvar.SetD170(gvar.GetD170() + 1); // SetD170 method not implemented
                    // gvar.SetInto(gvar.GetD170File()); // SetInto method not implemented
                    break;

                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// mReadNextWithLock
        /// </summary>
        /// <remarks>
        /// This method reads the next record from a file based on the L-FILE-NAME.
        /// </remarks>
        public void Mreadnextwithlock(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // CONTINUE
                    break;
                case "GROUP-FILE":
                    // CONTINUE
                    break;
                case "STOCK-FILE":
                    // gvar.SetD3(gvar.GetD3().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD3 method not implemented
                    break;
                case "FUND-FILE":
                    // gvar.SetD4(gvar.GetD4().ReadNextInto(gvar.GetLFileRecordArea()).WithKeptLock()); // SetD4 method not implemented
                    break;
                case "CGTR04-REPORT-FILE":
                    // gvar.SetD5(gvar.GetD5().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD5 method not implemented
                    break;
                case "CGTR05-REPORT-FILE":
                    // gvar.SetD6(gvar.GetD6().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD6 method not implemented
                    break;
                case "RPI-FILE":
                    // CONTINUE
                    break;
                case "PARAMETER-FILE":
                    // CONTINUE
                    break;
                case "USER-FILE":
                    // gvar.SetD9(gvar.GetD9().ReadNextInto(gvar.GetLFileRecordArea()).WithKeptLock()); // SetD9 method not implemented
                    break;
                case "MASTER-LOG-FILE":
                    // gvar.SetD17(gvar.GetD17().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD17 method not implemented
                    break;
                case "REALISED-DATA-FILE":
                    // gvar.SetD19(gvar.GetD19().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD19 method not implemented
                    break;
                case "UNREALISED-DATA-FILE":
                    // gvar.SetD20(gvar.GetD20().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD20 method not implemented
                    break;
                case "PRINTER-FILE":
                    // gvar.SetD31(gvar.GetD31().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD31 method not implemented
                    break;
                case "STOCK-TYPE-FILE":
                    // gvar.SetD32(gvar.GetD32().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD32 method not implemented
                    break;
                case "TRANSACTION-CODE-FILE":
                    // gvar.SetD34(gvar.GetD34().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD34 method not implemented
                    break;
                case "OUTPUT-LOG-FILE":
                    // gvar.SetD35(gvar.GetD35().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD35 method not implemented
                    break;
                case "MESSAGE-FILE":
                    // gvar.SetD36(gvar.GetD36().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD36 method not implemented
                    break;
                case "USER-FUND-FILE":
                    // CONTINUE
                    break;
                case "HELP-TEXT-FILE":
                    // gvar.SetD38(gvar.GetD38().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD38 method not implemented
                    break;
                case "DEFAULT-ACCESS-FILE":
                    // gvar.SetD39(gvar.GetD39().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD39 method not implemented
                    break;
                case "ACCESS-PROFILE-FILE":
                    // gvar.SetD40(gvar.GetD40().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD40 method not implemented
                    break;
                case "EXTEL-CURRENCY-FILE":
                    // gvar.SetD42(gvar.GetD42().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD42 method not implemented
                    break;
                case "STOCK-PRICE-FILE":
                    // gvar.SetD43(gvar.GetD43().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD43 method not implemented
                    break;
                case "SEQ-BALANCE-FILE":
                    // gvar.SetD45(gvar.GetD45().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD45 method not implemented
                    break;
                case "TRANSACTION-FILE":
                    // gvar.SetD46(gvar.GetD46().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD46 method not implemented
                    break;
                case "YE-BAL-REPORT-FILE":
                    // gvar.SetD49(gvar.GetD49().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD49 method not implemented
                    break;
                case "STOCK-LOAD-REPORT":
                    // gvar.SetD50(gvar.GetD50().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD50 method not implemented
                    break;
                case "STOCK-LOAD-DATA-FILE":
                    // gvar.SetD51(gvar.GetD51().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD51 method not implemented
                    break;
                case "FUNDS-LOAD-DATA-FILE":
                    // gvar.SetD68(gvar.GetD68().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD68 method not implemented
                    break;
                case "FUNDS-LOAD-REPORT":
                    // gvar.SetD69(gvar.GetD69().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD69 method not implemented
                    break;
                case "PRICE-LOAD-DATA-FILE":
                    // gvar.SetD70(gvar.GetD70().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD70 method not implemented
                    break;
                case "PRICE-LOAD-REPORT":
                    // gvar.SetD71(gvar.GetD71().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD71 method not implemented
                    break;
                case "SKAN1-REPORT":
                    // gvar.SetD72(gvar.GetD72().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD72 method not implemented
                    break;
                case "SKAN2-REPORT":
                    // gvar.SetD73(gvar.GetD73().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD73 method not implemented
                    break;
                case "NEW-REALISED-REPORT":
                    // gvar.SetD74(gvar.GetD74().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD74 method not implemented
                    break;
                case "NEW-UNREALISED-REPORT":
                    // gvar.SetD75(gvar.GetD75().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD75 method not implemented
                    break;
                case "MGM1-REPORT-FILE":
                    // gvar.SetD76(gvar.GetD76().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD76 method not implemented
                    break;
                case "CAPITAL-REPORT-FILE":
                    // gvar.SetD77(gvar.GetD77().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD77 method not implemented
                    break;
                case "REPLACEMENT-RELIEF-REPORT":
                    // gvar.SetD78(gvar.GetD78().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD78 method not implemented
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    // gvar.SetD79(gvar.GetD79().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD79 method not implemented
                    break;
                case "REPLACEMENT-DIS-FILE":
                    // gvar.SetD80(gvar.GetD80().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD80 method not implemented
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    // gvar.SetD81(gvar.GetD81().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD81 method not implemented
                    break;
                case "NOTIONAL-SALE-SCHEDULE-FILE":
                    // gvar.SetD82(gvar.GetD82().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD82 method not implemented
                    break;
                case "BALANCE-LOAD-DATA-FILE":
                    // gvar.SetD83(gvar.GetD83().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD83 method not implemented
                    break;
                case "BALANCES-LOAD-REPORT":
                    // gvar.SetD84(gvar.GetD84().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD84 method not implemented
                    break;
                case "RCF-FILE":
                    // gvar.SetD86(gvar.GetD86().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD86 method not implemented
                    break;
                case "RCF-BACKUP-FILE":
                    // gvar.SetD87(gvar.GetD87().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD87 method not implemented
                    break;
                case "GROUP-LOAD-REPORT":
                    // gvar.SetD88(gvar.GetD88().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD88 method not implemented
                    break;
                case "GROUP-LOAD-DATA-FILE":
                    // gvar.SetD89(gvar.GetD89().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD89 method not implemented
                    break;
                case "COUNTRY-LOAD-REPORT":
                    // gvar.SetD90(gvar.GetD90().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD90 method not implemented
                    break;
                case "COUNTRY-LOAD-DATA-FILE":
                    // gvar.SetD91(gvar.GetD91().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD91 method not implemented
                    break;
                case "RPI-LOAD-REPORT":
                    // gvar.SetD92(gvar.GetD92().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD92 method not implemented
                    break;
                case "RPI-LOAD-DATA-FILE":
                    // gvar.SetD93(gvar.GetD93().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD93 method not implemented
                    break;
                case "GAINLOSS-DATA-FILE":
                    // gvar.SetD94(gvar.GetD94().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD94 method not implemented
                    break;
                case "GAINLOSS-REPORT":
                    // gvar.SetD95(gvar.GetD95().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD95 method not implemented
                    break;
                case "REALISED-TAX-DATA-FILE":
                    // gvar.SetD98(gvar.GetD98().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD98 method not implemented
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    // gvar.SetD101(gvar.GetD101().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD101 method not implemented
                    break;
                case "BATCH-RUN-LOG-FILE":
                    // CONTINUE
                    break;
                case "BATCH-QUIT-RUN-FILE":
                    // gvar.SetD107(gvar.GetD107().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD107 method not implemented
                    break;
                case "TRACE-FILE":
                    // gvar.SetD108(gvar.GetD108().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD108 method not implemented
                    break;
                case "ERROR-LOG-FILE":
                    // gvar.SetD109(gvar.GetD109().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD109 method not implemented
                    break;
                case "TAPER-RATE-FILE":
                    // gvar.SetD112(gvar.GetD112().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD112 method not implemented
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    // gvar.SetD153(gvar.GetD153().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD153 method not implemented
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    // gvar.SetD154(gvar.GetD154().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD154 method not implemented
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    // gvar.SetD163(gvar.GetD163().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD163 method not implemented
                    break;
                case "PRICE-TYPES-FILE":
                    // gvar.SetD167(gvar.GetD167().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD167 method not implemented
                    break;
                case "PENDING-LOG-FILE":
                    // gvar.SetD169(gvar.GetD169().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD169 method not implemented
                    break;
                case "PENDING-ITEMS-FILE":
                    // gvar.SetD170(gvar.GetD170().ReadNextInto(gvar.GetLFileRecordArea()).WithLock()); // SetD170 method not implemented
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// nRead
        /// </summary>
        /// <remarks>
        /// This method reads data from various files based on the L-FILE-NAME.
        /// </remarks>
        public void Nread(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    // CONTINUE (No action required)
                    break;
                case "GROUP-FILE":
                    // CONTINUE (No action required)
                    break;
                case "STOCK-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD3FileAsString());
                    break;
                case "FUND-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD4FileAsString());
                    break;
                case "CGTR04-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD5FileAsString());
                    break;
                case "CGTR05-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD6FileAsString());
                    break;
                case "RPI-FILE":
                    // CONTINUE (No action required)
                    break;
                case "PARAMETER-FILE":
                    // ParametersDAL parametersDAL = new ParametersDAL();
                    // parametersDAL.Run(ivar.GetLFileAction(), ivar.GetLFileRecordArea());
                    if (string.IsNullOrEmpty(ivar.GetLFileRecordAreaAsString()))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;
                case "USER-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD9FileAsString());
                    break;
                case "STERLING-EXTEL-REPORT":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD10FileAsString());
                    break;
                case "FOREIGN-EXTEL-REPORT":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD11FileAsString());
                    break;
                case "OUTPUT-LISTING":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD12FileAsString());
                    break;
                case "MASTER-LOG-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD17FileAsString());
                    break;
                case "ERROR-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD18FileAsString());
                    break;
                case "REALISED-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD19FileAsString());
                    break;
                case "UNREALISED-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD20FileAsString());
                    break;
                case "REALISED-SCHEDULE-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD21FileAsString());
                    break;
                case "UNREALISED-SCHEDULE-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD22FileAsString());
                    break;
                case "CG01-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD23FileAsString());
                    break;
                case "CG02-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD24FileAsString());
                    break;
                case "CG03-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD25FileAsString());
                    break;
                case "YE-REC-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD26FileAsString());
                    break;
                case "YE-DEL-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD27FileAsString());
                    break;
                case "YE-CON-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD28FileAsString());
                    break;
                case "ERROR-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD29FileAsString());
                    break;
                case "YE-ERR-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD30FileAsString());
                    break;
                case "PRINTER-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD31FileAsString());
                    break;
                case "STOCK-TYPE-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD32FileAsString());
                    break;
                case "YE-REC2-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD33FileAsString());
                    break;
                case "TRANSACTION-CODE-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD34FileAsString());
                    break;
                case "OUTPUT-LOG-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD35FileAsString());
                    break;
                case "MESSAGE-FILE":
                    ivar.SetLFileRecordAreaAsString(gvar.GetD36FileAsString());
                    break;
                case "USER-FUND-FILE":
                    var userFundRecordArea4 = ivar.GetLFileRecordAreaAsString();
                    new UserFundsDAL().Execute(ivar.GetCgtfilesLinkage().GetLFileAction(), ref userFundRecordArea4, ivar.GetCommonLinkage().GetLUserNo());
                    ivar.SetLFileRecordAreaAsString(userFundRecordArea4);
                    if (string.IsNullOrEmpty(userFundRecordArea4))
                    {
                        gvar.SetFileStatusAsString("10");
                    }
                    break;
                case "HELP-TEXT-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD38FileAsString());
                    break;
                case "DEFAULT-ACCESS-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD39FileAsString());
                    break;
                case "ACCESS-PROFILE-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD40FileAsString());
                    break;
                case "EXTEL-PRICES-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD41FileAsString());
                    break;
                case "EXTEL-CURRENCY-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD42FileAsString());
                    break;
                case "STOCK-PRICE-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD43FileAsString());
                    break;
                case "EXTEL-TRANSMISSION-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD44FileAsString());
                    break;
                case "SEQ-BALANCE-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD45FileAsString());
                    break;
                case "TRANSACTION-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD46FileAsString());
                    break;
                case "YE-BAL-REPORT-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD49FileAsString());
                    break;
                case "STOCK-LOAD-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD50FileAsString());
                    break;
                case "STOCK-LOAD-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD51FileAsString());
                    break;
                case "FUNDS-LOAD-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD68FileAsString());
                    break;
                case "FUNDS-LOAD-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD69FileAsString());
                    break;
                case "PRICE-LOAD-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD70FileAsString());
                    break;
                case "PRICE-LOAD-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD71FileAsString());
                    break;
                case "SKAN1-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD72FileAsString());
                    break;
                case "SKAN2-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD73FileAsString());
                    break;
                case "NEW-REALISED-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD74FileAsString());
                    break;
                case "NEW-UNREALISED-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD75FileAsString());
                    break;
                case "MGM1-REPORT-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD76FileAsString());
                    break;
                case "CAPITAL-REPORT-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD77FileAsString());
                    break;
                case "REPLACEMENT-RELIEF-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD78FileAsString());
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD79FileAsString());
                    break;
                case "REPLACEMENT-DIS-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD80FileAsString());
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD81FileAsString());
                    break;
                case "NOTIONAL-SALE-SCHEDULE-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD82FileAsString());
                    break;
                case "BALANCE-LOAD-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD83FileAsString());
                    break;
                case "BALANCES-LOAD-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD84FileAsString());
                    break;
                case "OFFSHORE-INCOME-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD85FileAsString());
                    break;
                case "RCF-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD86FileAsString());
                    break;
                case "RCF-BACKUP-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD87FileAsString());
                    break;
                case "GROUP-LOAD-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD88FileAsString());
                    break;
                case "GROUP-LOAD-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD89FileAsString());
                    break;
                case "COUNTRY-LOAD-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD90FileAsString());
                    break;
                case "COUNTRY-LOAD-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD91FileAsString());
                    break;
                case "RPI-LOAD-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD92FileAsString());
                    break;
                case "RPI-LOAD-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD93FileAsString());
                    break;
                case "GAINLOSS-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD94FileAsString());
                    break;
                case "GAINLOSS-REPORT":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD95FileAsString());
                    break;
                case "REALISED-TAX-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD98FileAsString());
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD101FileAsString());
                    break;
                case "BATCH-RUN-LOG-FILE":
                    // CONTINUE (No action required)
                    break;
                case "BATCH-QUIT-RUN-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD107FileAsString());
                    break;
                case "TRACE-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD108FileAsString());
                    break;
                case "ERROR-LOG-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD109FileAsString());
                    break;
                case "TAPER-RATE-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD112FileAsString());
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD153FileAsString());
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD154FileAsString());
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD163FileAsString());
                    break;
                case "PRICE-TYPES-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD167FileAsString());
                    break;
                case "PENDING-LOG-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD169FileAsString());
                    break;
                case "PENDING-ITEMS-FILE":
                    // No action required
                    ivar.SetLFileRecordAreaAsString(gvar.GetD170FileAsString());
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// oReadWithLock
        /// </summary>
        /// <remarks>
        /// This method implements the oReadWithLock COBOL paragraph.
        /// It reads data from various files based on the value of L-FILE-NAME.
        /// </remarks>
        public void Oreadwithlock(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                case "GROUP-FILE":
                case "RPI-FILE":
                case "USER-FILE":
                case "PARAMETER-FILE":
                case "BATCH-QUIT-RUN-FILE":
                    // gvar.SetContinue("Continue"); // SetContinue method not implemented in Gvar
                    break;
                case "STOCK-FILE":
                    // gvar.SetInto("D3"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD3File(), 1); // ReadFile expects int fileHandle, not D3File object
                    break;
                case "FUND-FILE":
                    // gvar.SetInto("D4"); // SetInto method not implemented in Gvar
                    // ReadFileWithKeptLock(fvar, gvar, ivar, gvar.GetD4File(), 1); // ReadFileWithKeptLock expects int fileHandle, not D4File object
                    break;
                case "CGTR04-REPORT-FILE":
                    // gvar.SetInto("D5"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD5File(), 0); // ReadFile expects int fileHandle, not D5File object
                    break;
                case "CGTR05-REPORT-FILE":
                    // gvar.SetInto("D6"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD6File(), 0); // ReadFile expects int fileHandle, not D6File object
                    break;
                case "USER-FUND-FILE":
                case "RPI-FILE":
                    break;
                case "STERLING-EXTEL-REPORT":
                    // gvar.SetInto("D10"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD10File(), 0); // ReadFile expects int fileHandle, not D10File object
                    break;
                case "FOREIGN-EXTEL-REPORT":
                    // gvar.SetInto("D11"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD11File(), 0); // ReadFile expects int fileHandle, not D11File object
                    break;
                case "MASTER-LOG-FILE":
                    // gvar.SetInto("D17"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD17File(), 0); // ReadFile expects int fileHandle, not D17File object
                    break;
                case "ERROR-REPORT-FILE":
                    // gvar.SetInto("D18"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD18File(), 0); // ReadFile expects int fileHandle, not D18File object
                    break;
                case "REALISED-DATA-FILE":
                    // gvar.SetInto("D19"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD19File(), 0); // ReadFile expects int fileHandle, not D19File object
                    break;
                case "UNREALISED-DATA-FILE":
                    // gvar.SetInto("D20"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD20File(), 0); // ReadFile expects int fileHandle, not D20File object
                    break;
                case "REALISED-SCHEDULE-FILE":
                    // gvar.SetInto("D21"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD21File(), 0); // ReadFile expects int fileHandle, not D21File object
                    break;
                case "UNREALISED-SCHEDULE-FILE":
                    // gvar.SetInto("D22"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD22File(), 0); // ReadFile expects int fileHandle, not D22File object
                    break;
                case "CG01-REPORT-FILE":
                    // gvar.SetInto("D23"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD23File(), 0); // ReadFile expects int fileHandle, not D23File object
                    break;
                case "CG02-REPORT-FILE":
                    // gvar.SetInto("D24"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD24File(), 0); // ReadFile expects int fileHandle, not D24File object
                    break;
                case "CG03-REPORT-FILE":
                    // gvar.SetInto("D25"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD25File(), 0); // ReadFile expects int fileHandle, not D25File object
                    break;
                case "YE-REC-REPORT-FILE":
                    // gvar.SetInto("D26"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD26File(), 0); // ReadFile expects int fileHandle, not D26File object
                    break;
                case "YE-DEL-REPORT-FILE":
                    // gvar.SetInto("D27"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD27File(), 0); // ReadFile expects int fileHandle, not D27File object
                    break;
                case "YE-CON-REPORT-FILE":
                    // gvar.SetInto("D28"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD28File(), 0); // ReadFile expects int fileHandle, not D28File object
                    break;
                case "ERROR-DATA-FILE":
                    // gvar.SetInto("D29"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD29File(), 0); // ReadFile expects int fileHandle, not D29File object
                    break;
                case "YE-ERR-REPORT-FILE":
                    // gvar.SetInto("D30"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD30File(), 0); // ReadFile expects int fileHandle, not D30File object
                    break;
                case "PRINTER-FILE":
                    // gvar.SetInto("D31"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD31File(), 0); // ReadFile expects int fileHandle, not D31File object
                    break;
                case "STOCK-TYPE-FILE":
                    // gvar.SetInto("D32"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD32File(), 0); // ReadFile expects int fileHandle, not D32File object
                    break;
                case "YE-REC2-DATA-FILE":
                    // gvar.SetInto("D33"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD33File(), 0); // ReadFile expects int fileHandle, not D33File object
                    break;
                case "TRANSACTION-CODE-FILE":
                    // gvar.SetInto("D34"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD34File(), 0); // ReadFile expects int fileHandle, not D34File object
                    break;
                case "OUTPUT-LOG-FILE":
                    // gvar.SetInto("D35"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD35File(), 0); // ReadFile expects int fileHandle, not D35File object
                    break;
                case "MESSAGE-FILE":
                    // gvar.SetInto("D36"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD36File(), 0); // ReadFile expects int fileHandle, not D36File object
                    break;
                case "HELP-TEXT-FILE":
                    // gvar.SetInto("D38"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD38File(), 0); // ReadFile expects int fileHandle, not D38File object
                    break;
                case "DEFAULT-ACCESS-FILE":
                    // gvar.SetInto("D39"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD39File(), 0); // ReadFile expects int fileHandle, not D39File object
                    break;
                case "ACCESS-PROFILE-FILE":
                    // gvar.SetInto("D40"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD40File(), 0); // ReadFile expects int fileHandle, not D40File object
                    break;
                case "EXTEL-PRICES-FILE":
                    // gvar.SetInto("D41"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD41File(), 0); // ReadFile expects int fileHandle, not D41File object
                    break;
                case "EXTEL-CURRENCY-FILE":
                    // gvar.SetInto("D42"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD42File(), 0); // ReadFile expects int fileHandle, not D42File object
                    break;
                case "STOCK-PRICE-FILE":
                    // gvar.SetInto("D43"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD43File(), 0); // ReadFile expects int fileHandle, not D43File object
                    break;
                case "EXTEL-TRANSMISSION-FILE":
                    // gvar.SetInto("D44"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD44File(), 0); // ReadFile expects int fileHandle, not D44File object
                    break;
                case "SEQ-BALANCE-FILE":
                    // gvar.SetInto("D45"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD45File(), 0); // ReadFile expects int fileHandle, not D45File object
                    break;
                case "TRANSACTION-FILE":
                    // gvar.SetInto("D46"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD46File(), 0); // ReadFile expects int fileHandle, not D46File object
                    break;
                case "BATCH-RUN-LOG-FILE":
                    // gvar.SetInto("D109"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD109File(), 0); // ReadFile expects int fileHandle, not D109File object
                    break;
                case "BATCH-QUIT-RUN-FILE":
                    // gvar.SetInto("D107"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD107File(), 0); // ReadFile expects int fileHandle, not D107File object
                    break;
                case "TRACE-FILE":
                    // gvar.SetInto("D108"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD108File(), 0); // ReadFile expects int fileHandle, not D108File object
                    break;
                case "ERROR-LOG-FILE":
                    // gvar.SetInto("D85"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD85File(), 0); // ReadFile expects int fileHandle, not D85File object
                    break;
                case "TAPER-RATE-FILE":
                    // gvar.SetInto("D112"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD112File(), 0); // ReadFile expects int fileHandle, not D112File object
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    // gvar.SetInto("D153"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD153File(), 0); // ReadFile expects int fileHandle, not D153File object
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    // gvar.SetInto("D154"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD154File(), 0); // ReadFile expects int fileHandle, not D154File object
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    // gvar.SetInto("D163"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD163File(), 0); // ReadFile expects int fileHandle, not D163File object
                    break;
                case "PRICE-TYPES-FILE":
                    // gvar.SetInto("D167"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD167File(), 0); // ReadFile expects int fileHandle, not D167File object
                    break;
                case "PENDING-LOG-FILE":
                    // gvar.SetInto("D169"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD169File(), 0); // ReadFile expects int fileHandle, not D169File object
                    break;
                case "PENDING-ITEMS-FILE":
                    // gvar.SetInto("D170"); // SetInto method not implemented in Gvar
                    // ReadFile(fvar, gvar, ivar, gvar.GetD170File(), 0); // ReadFile expects int fileHandle, not D170File object
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }

        public void ReadFile(Fvar fvar, Gvar gvar, Ivar ivar, int fileHandle, int lockType)
        {
            // implement read file logic here
        }

        public void ReadFileWithKeptLock(Fvar fvar, Gvar gvar, Ivar ivar, int fileHandle, int lockType)
        {
            // implement read file with kept lock logic here
        }
        /// <summary>
        /// COBOL paragraph: pReadOnKey2
        /// </summary>
        /// <remarks>
        /// This method reads a record from a file based on the file name and key.
        /// </remarks>
        public void Preadonkey2(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EVALUATE L-FILE-NAME
            if (ivar.GetCgtfilesLinkage().GetLFileName().Equals(Ivar.FUND_FILE))
            {
                // READ D4 INTO L-FILE-RECORD-AREA KEY IS D4-KEY2
                // Assuming D4 is a file and it has a method to read a record by key
                // For simplicity, let's assume we have a method ReadRecordByKey
                // that takes the key and record area as parameters
                fvar.GetD4().ReadRecordByKey(fvar.GetD4Record().GetD4Key2(), ivar.GetLFileRecordArea());
            }
            else
            {
                // MOVE 'IF' TO FILE-STATUS
                gvar.SetFileStatusAsString("IF");
            }
        }
        /// <summary>
        /// COBOL paragraph: paReadOnKey3
        /// </summary>
        /// <remarks>
        /// This method implements the logic from the COBOL paragraph paReadOnKey3.
        /// It evaluates the L-FILE-NAME and performs a READ operation on D4 based on the file name.
        /// </remarks>
        public void Pareadonkey3(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EVALUATE L-FILE-NAME
            if (ivar.GetCgtfilesLinkage().GetLFileName().Equals(Ivar.FUND_FILE))
            {
                // READ D4 INTO L-FILE-RECORD-AREA KEY IS D4-KEY3
                // Assuming a method to read D4 into L-FILE-RECORD-AREA with KEY D4-KEY3
                // For simplicity, direct assignment is used here; actual implementation may vary
                ivar.SetLFileRecordAreaAsString(fvar.GetD4Record().GetD4Key3AsString());
            }
            else
            {
                // MOVE 'IF' TO FILE-STATUS
                gvar.SetFileStatusAsString("IF");
            }
        }
        /// <summary>
        /// qWrite
        /// </summary>
        /// <remarks>
        /// This method implements the COBOL paragraph qWrite.
        /// It writes records to various files based on the L-FILE-NAME.
        /// </remarks>
        public void Qwrite(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    break;
                case "GROUP-FILE":
                    break;
                case "STOCK-FILE":
                    // fvar.SetLKey(fvar.GetD3Key()); // SetLKey method not implemented in Fvar
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(fvar.GetLKey()); // ExternalProgram class not implemented
                    // fvar.SetD3InverseKey(fvar.GetLKey()); // SetD3InverseKey method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD3RecordAsString());
                    break;
                case "FUND-FILE":
                    // fvar.SetLKey(fvar.GetD4Key()); // SetLKey method not implemented in Fvar
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(fvar.GetLKey()); // ExternalProgram class not implemented
                    // fvar.SetD4InverseKey(fvar.GetLKey()); // SetD4InverseKey method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD4RecordAsString());
                    break;
                case "CGTR04-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD5RecordAsString());
                    break;
                case "CGTR05-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD6RecordAsString());
                    break;
                case "RPI-FILE":
                    break;
                case "PARAMETER-FILE":
                    break;
                case "STERLING-EXTEL-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD10RecordAsString());
                    break;
                case "FOREIGN-EXTEL-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD11RecordAsString());
                    break;
                case "OUTPUT-LISTING":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD12RecordAsString());
                    break;
                case "MASTER-LOG-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD17RecordAsString());
                    break;
                case "ERROR-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD18RecordAsString());
                    break;
                case "REALISED-DATA-FILE":
                    fvar.SetD19RecordAsString(ivar.GetLFileRecordAreaAsString());
                    gvar.GetWScheduleCounters().SetWd19Count(gvar.GetWScheduleCounters().GetWd19Count() + 1);
                    // fvar.SetD19SequenceNumber(gvar.GetWScheduleCounters().GetWd19Count()); // SetD19SequenceNumber method not implemented in Fvar
                    if (fvar.GetD19Record().GetD19AcquisitionDateYy() > 44)
                        fvar.GetD19Record().SetD19AcquisitionDateCc("19");
                    else
                        fvar.GetD19Record().SetD19AcquisitionDateCc("20");
                    if (fvar.GetD19Record().GetD19TaperDateYy() > 44)
                        fvar.GetD19Record().SetD19TaperDateCc("19");
                    else
                        fvar.GetD19Record().SetD19TaperDateCc("20");
                    // STRING CGTFILES-LINKAGE ( 1 : 11 ) 'write' DELIMITED BY SIZE INTO timing-message-suffix
                    gvar.SetTimingLinkage(new Gvar.TimingLinkage { TimingMessageSuffix = "write" });
                    break;
                case "UNREALISED-DATA-FILE":
                    fvar.SetD20RecordAsString(ivar.GetLFileRecordAreaAsString());
                    gvar.GetWScheduleCounters().SetWd20Count(gvar.GetWScheduleCounters().GetWd20Count() + 1);
                    // fvar.SetD20SequenceNumber(gvar.GetWScheduleCounters().GetWd20Count()); // SetD20SequenceNumber method not implemented in Fvar
                    if (fvar.GetD20Record().GetD20AcquisitionDateYy() > 44)
                        fvar.GetD20Record().SetD20AcquisitionDateCc("19");
                    else
                        fvar.GetD20Record().SetD20AcquisitionDateCc("20");
                    if (fvar.GetD20Record().GetD20TaperDateYy() > 44)
                        fvar.GetD20Record().SetD20TaperDateCc("19");
                    else
                        fvar.GetD20Record().SetD20TaperDateCc("20");
                    // STRING CGTFILES-LINKAGE ( 1 : 11 ) 'write' DELIMITED BY SIZE INTO timing-message-suffix
                    gvar.SetTimingLinkage(new Gvar.TimingLinkage { TimingMessageSuffix = "write" });
                    break;
                case "REALISED-SCHEDULE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD21RecordAsString());
                    break;
                case "UNREALISED-SCHEDULE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD22RecordAsString());
                    break;
                case "CG01-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD23RecordAsString());
                    break;
                case "CG02-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD24RecordAsString());
                    break;
                case "CG03-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD25RecordAsString());
                    break;
                case "YE-REC-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD26RecordAsString());
                    break;
                case "YE-DEL-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD27RecordAsString());
                    break;
                case "YE-CON-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD28RecordAsString());
                    break;
                case "ERROR-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD29RecordAsString());
                    break;
                case "YE-ERR-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD30RecordAsString());
                    break;
                case "PRINTER-FILE":
                    // fvar.SetLKey(fvar.GetD31Key()); // SetLKey method not implemented in Fvar
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(fvar.GetLKey()); // ExternalProgram class not implemented
                    // fvar.SetD31InverseKey(fvar.GetLKey()); // SetD31InverseKey method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD31RecordAsString());
                    break;
                case "STOCK-TYPE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD32RecordAsString());
                    break;
                case "YE-REC2-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD33RecordAsString());
                    break;
                case "TRANSACTION-CODE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD34RecordAsString());
                    break;
                case "OUTPUT-LOG-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD35RecordAsString());
                    break;
                case "MESSAGE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD36RecordAsString());
                    break;
                case "USER-FUND-FILE":
                    break;
                case "HELP-TEXT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD38RecordAsString());
                    break;
                case "DEFAULT-ACCESS-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD39RecordAsString());
                    break;
                case "ACCESS-PROFILE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD40RecordAsString());
                    break;
                case "EXTEL-PRICES-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD41RecordAsString());
                    break;
                case "EXTEL-CURRENCY-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD42RecordAsString());
                    break;
                case "STOCK-PRICE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD43RecordAsString());
                    break;
                case "EXTEL-TRANSMISSION-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD44RecordAsString());
                    break;
                case "SEQ-BALANCE-FILE":
                    switch (fvar.GetD45Record().GetD451RecordCode())
                    {
                        case "01":
                            gvar.SetRecLength(385);
                            break;
                        case "02":
                            gvar.SetRecLength(342 + (fvar.GetD452NoOfCostsHeldYtd() * 80));
                            break;
                        case "03":
                            gvar.SetRecLength(319);
                            break;
                        default:
                            gvar.SetRecLength(16342);
                            break;
                    }
                    ivar.SetLFileRecordAreaAsString(fvar.GetD45RecordAsString());
                    break;
                case "TRANSACTION-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD46RecordAsString());
                    break;
                case "YE-BAL-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD49RecordAsString());
                    break;
                case "STOCK-LOAD-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD50RecordAsString());
                    break;
                case "STOCK-LOAD-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD51RecordAsString());
                    break;
                case "FUNDS-LOAD-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD68RecordAsString());
                    break;
                case "FUNDS-LOAD-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD69RecordAsString());
                    break;
                case "PRICE-LOAD-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD70RecordAsString());
                    break;
                case "PRICE-LOAD-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD71RecordAsString());
                    break;
                case "SKAN1-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD72RecordAsString());
                    break;
                case "SKAN2-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD73RecordAsString());
                    break;
                case "NEW-REALISED-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD74RecordAsString());
                    break;
                case "NEW-UNREALISED-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD75RecordAsString());
                    break;
                case "MGM1-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD76RecordAsString());
                    break;
                case "CAPITAL-REPORT-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD77RecordAsString());
                    break;
                case "REPLACEMENT-RELIEF-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD78RecordAsString());
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD79RecordAsString());
                    break;
                case "REPLACEMENT-DIS-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD80RecordAsString());
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    fvar.SetD81Record(fvar.GetLFileRecordArea());
                    gvar.GetWScheduleCounters().SetWd81Count(gvar.GetWScheduleCounters().GetWd81Count() + 1);
                    // fvar.SetD81SequenceNumber(gvar.GetWScheduleCounters().GetWd81Count()); // SetD81SequenceNumber method not implemented in Fvar
                    if (fvar.GetD81Record().GetD81AcquisitionDateYy() > 44)
                        fvar.GetD81Record().SetD81AcquisitionDateCc("19");
                    else
                        fvar.GetD81Record().SetD81AcquisitionDateCc("20");
                    if (fvar.GetD81Record().GetD81TaperDateYy() > 44)
                        fvar.GetD81Record().SetD81TaperDateCc("19");
                    else
                        fvar.GetD81Record().SetD81TaperDateCc("20");
                    // STRING CGTFILES-LINKAGE ( 1 : 11 ) 'write' DELIMITED BY SIZE INTO timing-message-suffix
                    gvar.SetTimingLinkage(new Gvar.TimingLinkage { TimingMessageSuffix = "write" });
                    break;
                case "NOTIONAL-SALE-SCHEDULE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD82RecordAsString());
                    break;
                case "BALANCE-LOAD-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD83RecordAsString());
                    break;
                case "BALANCES-LOAD-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD84RecordAsString());
                    break;
                case "OFFSHORE-INCOME-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD85RecordAsString());
                    break;
                case "RCF-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD86RecordAsString());
                    break;
                case "RCF-BACKUP-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD87RecordAsString());
                    break;
                case "GROUP-LOAD-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD88RecordAsString());
                    break;
                case "GROUP-LOAD-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD89RecordAsString());
                    break;
                case "COUNTRY-LOAD-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD90RecordAsString());
                    break;
                case "COUNTRY-LOAD-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD91RecordAsString());
                    break;
                case "RPI-LOAD-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD92RecordAsString());
                    break;
                case "RPI-LOAD-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD93RecordAsString());
                    break;
                case "GAINLOSS-DATA-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD94RecordAsString());
                    break;
                case "GAINLOSS-REPORT":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD95RecordAsString());
                    break;
                case "REALISED-TAX-DATA-FILE":
                    fvar.SetD98Record(fvar.GetLFileRecordArea());
                    gvar.GetWScheduleCounters().SetWd98Count(gvar.GetWScheduleCounters().GetWd98Count() + 1);
                    // fvar.SetD98SequenceNumber(gvar.GetWScheduleCounters().GetWd98Count()); // SetD98SequenceNumber method not implemented in Fvar
                    if (fvar.GetD98Record().GetD98AcquisitionDateYy() > 44)
                        fvar.GetD98Record().SetD98AcquisitionDateCc("19");
                    else
                        fvar.GetD98Record().SetD98AcquisitionDateCc("20");
                    if (fvar.GetD98Record().GetD98TaperDateYy() > 44)
                        fvar.GetD98Record().SetD98TaperDateCc("19");
                    else
                        fvar.GetD98Record().SetD98TaperDateCc("20");
                    // STRING CGTFILES-LINKAGE ( 1 : 11 ) 'write' DELIMITED BY SIZE INTO timing-message-suffix
                    gvar.SetTimingLinkage(new Gvar.TimingLinkage { TimingMessageSuffix = "write" });
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    fvar.SetD101Record(fvar.GetLFileRecordArea());
                    gvar.GetWScheduleCounters().SetWd101Count(gvar.GetWScheduleCounters().GetWd101Count() + 1);
                    // fvar.SetD101SequenceNumber(gvar.GetWScheduleCounters().GetWd101Count()); // SetD101SequenceNumber method not implemented in Fvar
                    if (fvar.GetD101Record().GetD101AcquisitionDateYy() > 44)
                        fvar.GetD101Record().SetD101AcquisitionDateCc("19");
                    else
                        fvar.GetD101Record().SetD101AcquisitionDateCc("20");
                    if (fvar.GetD101Record().GetD101TaperDateYy() > 44)
                        fvar.GetD101Record().SetD101TaperDateCc("19");
                    else
                        fvar.GetD101Record().SetD101TaperDateCc("20");
                    // STRING CGTFILES-LINKAGE ( 1 : 11 ) 'write' DELIMITED BY SIZE INTO timing-message-suffix
                    gvar.SetTimingLinkage(new Gvar.TimingLinkage { TimingMessageSuffix = "write" });
                    break;
                case "BATCH-RUN-LOG-FILE":
                    break;
                case "BATCH-QUIT-RUN-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD107RecordAsString());
                    gvar.SetAdvancing(1);
                    break;
                case "TRACE-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD108RecordAsString());
                    gvar.SetAdvancing(1);
                    break;
                case "ERROR-LOG-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD109RecordAsString());
                    gvar.SetAdvancing(1);
                    break;
                case "TAPER-RATE-FILE":
                    // fvar.SetLKey(fvar.GetD112DateEffective()); // SetLKey method not implemented in Fvar
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(fvar.GetLKey()); // ExternalProgram class not implemented
                    // fvar.SetD112InverseDate(fvar.GetLKey()); // SetD112InverseDate method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD112RecordAsString());
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    // fvar.SetLKey(fvar.GetD153Key()); // SetLKey method not implemented in Fvar
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(fvar.GetLKey()); // ExternalProgram class not implemented
                    // fvar.SetD153InverseKey(fvar.GetLKey()); // SetD153InverseKey method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD153RecordAsString());
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    ivar.SetLFileRecordAreaAsString(fvar.GetD154RecordAsString());
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    // fvar.SetLKey(fvar.GetD163Key()); // SetLKey method not implemented in Fvar
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(fvar.GetLKey()); // ExternalProgram class not implemented
                    // fvar.SetD163InverseKey(fvar.GetLKey()); // SetD163InverseKey method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD163RecordAsString());
                    break;
                case "PRICE-TYPES-FILE":
                    // fvar.SetLKey(fvar.GetD167Key()); // SetLKey method not implemented in Fvar
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(fvar.GetLKey()); // ExternalProgram class not implemented
                    // fvar.SetD167InverseKey(fvar.GetLKey()); // SetD167InverseKey method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD167RecordAsString());
                    break;
                case "PENDING-LOG-FILE":
                    // STRING D169-Key2A ,  D169-Key   DELIMITED   BY   SIZE INTO    L-KEY
                    string lKey = fvar.GetD169Record().GetD169Key2a() + fvar.GetD169Record().GetD169Key();
                    // CALL 'CGTINVRT' USING L-KEY
                    // new ExternalProgram().Run(lKey); // ExternalProgram class not implemented
                    // fvar.SetD169InverseKey2(lKey); // SetD169InverseKey2 method not implemented in Fvar
                    ivar.SetLFileRecordAreaAsString(fvar.GetD169RecordAsString());
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// rRewrite
        /// </summary>
        /// <remarks>
        /// Rewrite records based on L-FILE-NAME.
        /// </remarks>
        public void Rrewrite(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Evaluate L-FILE-NAME
            string lFileName = ivar.GetCgtfilesLinkage().GetLFileName();

            switch (lFileName)
            {
                case "COUNTRY-FILE":
                case "GROUP-FILE":
                case "RPI-FILE":
                case "PARAMETER-FILE":
                case "USER-FILE":
                case "PENDING-LOG-FILE":
                case "MESSAGE-FILE":
                case "USER-FUND-FILE":
                case "HELP-TEXT-FILE":
                    // Continue, no action needed
                    break;
                case "STOCK-FILE":
                    fvar.SetD3RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D3-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "FUND-FILE":
                    fvar.SetD4RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D4-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "MASTER-LOG-FILE":
                    fvar.SetD17RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D17-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "REALISED-DATA-FILE":
                    fvar.SetD19RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D19-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "UNREALISED-DATA-FILE":
                    fvar.SetD20RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D20-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "PRINTER-FILE":
                    fvar.SetD31RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D31-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "STOCK-TYPE-FILE":
                    fvar.SetD32RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D32-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "TRANSACTION-CODE-FILE":
                    fvar.SetD34RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D34-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "OUTPUT-LOG-FILE":
                    fvar.SetD35RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D35-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "MESSAGE-FILE":
                    fvar.SetD36RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D36-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "USER-FILE":
                    fvar.SetD9RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D9-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    fvar.SetD163RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D163-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "PRICE-TYPES-FILE":
                    fvar.SetD167RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D167-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "PENDING-LOG-FILE":
                    fvar.SetD169RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D169-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "PENDING-ITEMS-FILE":
                    fvar.SetD170RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D170-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "RCF-FILE":
                    fvar.SetD86RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D86-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "REALISED-TAX-DATA-FILE":
                    fvar.SetD98RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D98-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    fvar.SetD101RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D101-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "TAPER-RATE-FILE":
                    fvar.SetD112RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D112-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    fvar.SetD153RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D153-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    fvar.SetD154RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D154-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "SEQ-BALANCE-FILE":
                    fvar.SetD45RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D45-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    fvar.SetD79RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D79-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "REPLACEMENT-DIS-FILE":
                    fvar.SetD80RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D80-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    fvar.SetD81RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D81-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "STOCK-PRICE-FILE":
                    fvar.SetD43RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D43-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "EXTEL-CURRENCY-FILE":
                    fvar.SetD42RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D42-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "ACCESS-PROFILE-FILE":
                    fvar.SetD40RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D40-RECORD FROM L-FILE-RECORD-AREA
                    break;
                case "DEFAULT-ACCESS-FILE":
                    fvar.SetD39RecordAsString(ivar.GetLFileRecordAreaAsString());
                    // REWRITE D39-RECORD FROM L-FILE-RECORD-AREA
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// sDelete
        /// </summary>
        /// <remarks>
        /// This method implements the sDelete COBOL paragraph.
        /// It evaluates L-FILE-NAME and performs corresponding delete operations.
        /// </remarks>
        public void Sdelete(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EVALUATE L-FILE-NAME
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                case "GROUP-FILE":
                case "RPI-FILE":
                case "PARAMETER-FILE":
                case "USER-FUND-FILE":
                    // CONTINUE
                    break;
                case "STOCK-FILE":
                    // gvar.SetD3(true); // SetD3 method not implemented
                    break;
                case "FUND-FILE":
                    // gvar.SetD4(true); // SetD4 method not implemented
                    break;
                case "USER-FILE":
                    // gvar.SetD9(true); // SetD9 method not implemented
                    break;
                case "MASTER-LOG-FILE":
                    // gvar.SetD17(true); // SetD17 method not implemented
                    break;
                case "REALISED-DATA-FILE":
                    // gvar.SetD19(true); // SetD19 method not implemented
                    break;
                case "UNREALISED-DATA-FILE":
                    // gvar.SetD20(true); // SetD20 method not implemented
                    break;
                case "PRINTER-FILE":
                    // gvar.SetD31(true); // SetD31 method not implemented
                    break;
                case "STOCK-TYPE-FILE":
                    // gvar.SetD32(true); // SetD32 method not implemented
                    break;
                case "TRANSACTION-CODE-FILE":
                    // gvar.SetD34(true); // SetD34 method not implemented
                    break;
                case "OUTPUT-LOG-FILE":
                    // gvar.SetD35(true); // SetD35 method not implemented
                    break;
                case "MESSAGE-FILE":
                    // gvar.SetD36(true); // SetD36 method not implemented
                    break;
                case "HELP-TEXT-FILE":
                    // gvar.SetD38(true); // SetD38 method not implemented
                    break;
                case "DEFAULT-ACCESS-FILE":
                    // gvar.SetD39(true); // SetD39 method not implemented
                    break;
                case "ACCESS-PROFILE-FILE":
                    // gvar.SetD40(true); // SetD40 method not implemented
                    break;
                case "EXTEL-CURRENCY-FILE":
                    // gvar.SetD42(true); // SetD42 method not implemented
                    break;
                case "STOCK-PRICE-FILE":
                    // gvar.SetD43(true); // SetD43 method not implemented
                    break;
                case "SEQ-BALANCE-FILE":
                    // gvar.SetD45(true); // SetD45 method not implemented
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    // gvar.SetD79(true); // SetD79 method not implemented
                    break;
                case "REPLACEMENT-DIS-FILE":
                    // gvar.SetD80(true); // SetD80 method not implemented
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    // gvar.SetD81(true); // SetD81 method not implemented
                    break;
                case "RCF-FILE":
                    // gvar.SetD86(true); // SetD86 method not implemented
                    break;
                case "REALISED-TAX-DATA-FILE":
                    // gvar.SetD98(true); // SetD98 method not implemented
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    // gvar.SetD101(true); // SetD101 method not implemented
                    break;
                case "TAPER-RATE-FILE":
                    // gvar.SetD112(true); // SetD112 method not implemented
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    // gvar.SetD153(true); // SetD153 method not implemented
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    // gvar.SetD154(true); // SetD154 method not implemented
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    // gvar.SetD163(true); // SetD163 method not implemented
                    break;
                case "PRICE-TYPES-FILE":
                    // gvar.SetD167(true); // SetD167 method not implemented
                    break;
                case "PENDING-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetPendingLogFile(true); // SetPendingLogFile method not implemented in CgtlogLinkageArea1
                    // gvar.SetD169(true); // SetD169 method not implemented
                    break;
                case "PENDING-ITEMS-FILE":
                    // gvar.SetD170(true); // SetD170 method not implemented
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// tClose
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: tClose
        /// </remarks>
        public void Tclose(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case "COUNTRY-FILE":
                    new CountryDAL().ProcessFile(ivar.GetCgtfilesLinkage().GetLFileAction(), ivar.GetLFileRecordAreaAsString());
                    break;
                case "GROUP-FILE":
                    // new GroupDAL().Run(gvar.GetLFileAction(), ivar.GetLFileRecordArea()); // GroupDAL has different method signature
                    break;
                case "STOCK-FILE":
                    // gvar.SetD3(1); // SetD3 method not implemented
                    break;
                case "FUND-FILE":
                    // gvar.SetD4(1); // SetD4 method not implemented
                    break;
                case "CGTR04-REPORT-FILE":
                    // gvar.SetD5(1); // SetD5 method not implemented
                    break;
                case "CGTR05-REPORT-FILE":
                    // gvar.SetD6(1); // SetD6 method not implemented
                    break;
                case "RPI-FILE":
                    string rpiCloseRecordArea = ivar.GetLFileRecordAreaAsString();
                    new RPIDAL().ProcessFile(ivar.GetCgtfilesLinkage().GetLFileAction(), ref rpiCloseRecordArea);
                    ivar.SetLFileRecordAreaAsString(rpiCloseRecordArea);
                    break;
                case "PARAMETER-FILE":
                    string paramCloseRecordArea = ivar.GetLFileRecordAreaAsString();
                    new ParametersDAL().Run(ivar.GetCgtfilesLinkage().GetLFileAction(), ref paramCloseRecordArea);
                    ivar.SetLFileRecordAreaAsString(paramCloseRecordArea);
                    break;
                case "USER-FILE":
                    // gvar.SetD9(1); // SetD9 method not implemented
                    break;
                case "STERLING-EXTEL-REPORT":
                    // gvar.SetD10(1); // SetD10 method not implemented
                    break;
                case "FOREIGN-EXTEL-REPORT":
                    // gvar.SetD11(1); // SetD11 method not implemented
                    break;
                case "OUTPUT-LISTING":
                    // gvar.SetD12(1); // SetD12 method not implemented
                    break;
                case "MASTER-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetMasterLogFile(1); // SetMasterLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "ERROR-REPORT-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetErrorReportFile(1); // SetErrorReportFile method not implemented in CgtlogLinkageArea1
                    break;
                case "REALISED-DATA-FILE":
                    // gvar.SetD19(1); // SetD19 method not implemented
                    break;
                case "UNREALISED-DATA-FILE":
                    // gvar.SetD20(1); // SetD20 method not implemented
                    break;
                case "REALISED-SCHEDULE-FILE":
                    // gvar.SetD21(1); // SetD21 method not implemented
                    break;
                case "UNREALISED-SCHEDULE-FILE":
                    // gvar.SetD22(1); // SetD22 method not implemented
                    break;
                case "CG01-REPORT-FILE":
                    // gvar.SetD23(1); // SetD23 method not implemented
                    break;
                case "CG02-REPORT-FILE":
                    // gvar.SetD24(1); // SetD24 method not implemented
                    break;
                case "CG03-REPORT-FILE":
                    // gvar.SetD25(1); // SetD25 method not implemented
                    break;
                case "YE-REC-REPORT-FILE":
                    // gvar.SetD26(1); // SetD26 method not implemented
                    break;
                case "YE-DEL-REPORT-FILE":
                    // gvar.SetD27(1); // SetD27 method not implemented
                    break;
                case "YE-CON-REPORT-FILE":
                    // gvar.SetD28(1); // SetD28 method not implemented
                    break;
                case "ERROR-DATA-FILE":
                    // gvar.SetD29(1); // SetD29 method not implemented
                    break;
                case "YE-ERR-REPORT-FILE":
                    // gvar.SetD30(1); // SetD30 method not implemented
                    break;
                case "PRINTER-FILE":
                    // gvar.SetD31(1); // SetD31 method not implemented
                    break;
                case "STOCK-TYPE-FILE":
                    // gvar.SetD32(1); // SetD32 method not implemented
                    break;
                case "YE-REC2-DATA-FILE":
                    // gvar.SetD33(1); // SetD33 method not implemented
                    break;
                case "TRANSACTION-CODE-FILE":
                    // gvar.SetD34(1); // SetD34 method not implemented
                    break;
                case "OUTPUT-LOG-FILE":
                    // gvar.SetD35(1); // SetD35 method not implemented
                    break;
                case "MESSAGE-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetMessageFile(1); // SetMessageFile method not implemented in CgtlogLinkageArea1
                    break;
                case "TAPER-RATE-FILE":
                    // new TaperRateDAL().Run(gvar.GetLFileAction(), ivar.GetLFileRecordArea()); // TaperRateDAL has different method signature
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    // gvar.SetD153(1); // SetD153 method not implemented
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    // gvar.SetD154(1); // SetD154 method not implemented
                    break;
                case "DISPOSALS-FROM-DB-FILE":
                    // gvar.SetD161(1); // SetD161 method not implemented
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    // gvar.SetD163(1); // SetD163 method not implemented
                    break;
                case "PRICE-TYPES-FILE":
                    // gvar.SetD167(1); // SetD167 method not implemented
                    break;
                case "PENDING-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetPendingLogFile(1); // SetPendingLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "PENDING-ITEMS-FILE":
                    // gvar.SetD170(1); // SetD170 method not implemented
                    break;
                case "BATCH-RUN-LOG-FILE":
                    // gvar.GetCgtlogLinkageArea1().SetBatchRunLogFile(1); // SetBatchRunLogFile method not implemented in CgtlogLinkageArea1
                    break;
                case "BATCH-QUIT-RUN-FILE":
                    // gvar.SetD107(1); // SetD107 method not implemented
                    break;
                case "TRACE-FILE":
                    // gvar.SetD108(1); // SetD108 method not implemented
                    break;
                case "ERROR-LOG-FILE":
                    // gvar.SetD109(1); // SetD109 method not implemented
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// uUnlock
        /// </summary>
        /// <remarks>
        /// This method unlocks various files based on the value of L-FILE-NAME.
        /// </remarks>
        public void Uunlock(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // switch (gvar.GetWhen()) // GetWhen method not implemented in Gvar
            switch ("") // Placeholder to maintain structure
            {
                case "COUNTRY-FILE":
                    // No action required for COUNTRY-FILE
                    break;
                case "GROUP-FILE":
                    // No action required for GROUP-FILE
                    break;
                case "STOCK-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D3"); // UNLOCK operation not implemented
                    // UNLOCK D3 RECORDS
                    break;
                case "FUND-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D4"); // UNLOCK operation not implemented
                    // UNLOCK D4 RECORDS
                    break;
                case "CGTR04-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D5"); // UNLOCK operation not implemented
                    // UNLOCK D5 RECORDS
                    break;
                case "CGTR05-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D6"); // UNLOCK operation not implemented
                    // UNLOCK D6 RECORDS
                    break;
                case "RPI-FILE":
                    // No action required for RPI-FILE
                    break;
                case "PARAMETER-FILE":
                    // No action required for PARAMETER-FILE
                    break;
                case "USER-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D9"); // UNLOCK operation not implemented
                    // UNLOCK D9 RECORDS
                    break;
                case "STERLING-EXTEL-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D10"); // UNLOCK operation not implemented
                    // UNLOCK D10 RECORDS
                    break;
                case "FOREIGN-EXTEL-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D11"); // UNLOCK operation not implemented
                    // UNLOCK D11 RECORDS
                    break;
                case "MASTER-LOG-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D17"); // UNLOCK operation not implemented
                    // UNLOCK D17 RECORDS
                    break;
                case "ERROR-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D18"); // UNLOCK operation not implemented
                    // UNLOCK D18 RECORDS
                    break;
                case "REALISED-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D19"); // UNLOCK operation not implemented
                    // UNLOCK D19 RECORDS
                    break;
                case "UNREALISED-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D20"); // UNLOCK operation not implemented
                    // UNLOCK D20 RECORDS
                    break;
                case "REALISED-SCHEDULE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D21"); // UNLOCK operation not implemented
                    // UNLOCK D21 RECORDS
                    break;
                case "UNREALISED-SCHEDULE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D22"); // UNLOCK operation not implemented
                    // UNLOCK D22 RECORDS
                    break;
                case "CG01-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D23"); // UNLOCK operation not implemented
                    // UNLOCK D23 RECORDS
                    break;
                case "CG02-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D24"); // UNLOCK operation not implemented
                    // UNLOCK D24 RECORDS
                    break;
                case "CG03-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D25"); // UNLOCK operation not implemented
                    // UNLOCK D25 RECORDS
                    break;
                case "YE-REC-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D26"); // UNLOCK operation not implemented
                    // UNLOCK D26 RECORDS
                    break;
                case "YE-DEL-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D27"); // UNLOCK operation not implemented
                    // UNLOCK D27 RECORDS
                    break;
                case "YE-CON-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D28"); // UNLOCK operation not implemented
                    // UNLOCK D28 RECORDS
                    break;
                case "ERROR-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D29"); // UNLOCK operation not implemented
                    // UNLOCK D29 RECORDS
                    break;
                case "YE-ERR-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D30"); // UNLOCK operation not implemented
                    // UNLOCK D30 RECORDS
                    break;
                case "PRINTER-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D31"); // UNLOCK operation not implemented
                    // UNLOCK D31 RECORDS
                    break;
                case "STOCK-TYPE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D32"); // UNLOCK operation not implemented
                    // UNLOCK D32 RECORDS
                    break;
                case "YE-REC2-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D33"); // UNLOCK operation not implemented
                    // UNLOCK D33 RECORDS
                    break;
                case "TRANSACTION-CODE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D34"); // UNLOCK operation not implemented
                    // UNLOCK D34 RECORDS
                    break;
                case "OUTPUT-LOG-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D35"); // UNLOCK operation not implemented
                    // UNLOCK D35 RECORDS
                    break;
                case "MESSAGE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D36"); // UNLOCK operation not implemented
                    // UNLOCK D36 RECORDS
                    break;
                case "USER-FUND-FILE":
                    // No action required for USER-FUND-FILE
                    break;
                case "HELP-TEXT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D38"); // UNLOCK operation not implemented
                    // UNLOCK D38 RECORDS
                    break;
                case "DEFAULT-ACCESS-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D39"); // UNLOCK operation not implemented
                    // UNLOCK D39 RECORDS
                    break;
                case "ACCESS-PROFILE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D40"); // UNLOCK operation not implemented
                    // UNLOCK D40 RECORDS
                    break;
                case "EXTEL-PRICES-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D41"); // UNLOCK operation not implemented
                    // UNLOCK D41 RECORDS
                    break;
                case "EXTEL-CURRENCY-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D42"); // UNLOCK operation not implemented
                    // UNLOCK D42 RECORDS
                    break;
                case "STOCK-PRICE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D43"); // UNLOCK operation not implemented
                    // UNLOCK D43 RECORDS
                    break;
                case "EXTEL-TRANSMISSION-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D44"); // UNLOCK operation not implemented
                    // UNLOCK D44 RECORDS
                    break;
                case "SEQ-BALANCE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D45"); // UNLOCK operation not implemented
                    // UNLOCK D45 RECORDS
                    break;
                case "TRANSACTION-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D46"); // UNLOCK operation not implemented
                    // UNLOCK D46 RECORDS
                    break;
                case "YE-BAL-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("49"); // UNLOCK operation not implemented
                    // UNLOCK D49 RECORDS
                    break;
                case "STOCK-LOAD-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D50"); // UNLOCK operation not implemented
                    // UNLOCK D50 RECORDS
                    break;
                case "STOCK-LOAD-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D51"); // UNLOCK operation not implemented
                    // UNLOCK D51 RECORDS
                    break;
                case "FUNDS-LOAD-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D68"); // UNLOCK operation not implemented
                    // UNLOCK D68 RECORDS
                    break;
                case "FUNDS-LOAD-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D69"); // UNLOCK operation not implemented
                    // UNLOCK D69 RECORDS
                    break;
                case "PRICE-LOAD-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D70"); // UNLOCK operation not implemented
                    // UNLOCK D70 RECORDS
                    break;
                case "PRICE-LOAD-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D71"); // UNLOCK operation not implemented
                    // UNLOCK D71 RECORDS
                    break;
                case "SKAN1-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D72"); // UNLOCK operation not implemented
                    // UNLOCK D72 RECORDS
                    break;
                case "SKAN2-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D73"); // UNLOCK operation not implemented
                    // UNLOCK D73 RECORDS
                    break;
                case "NEW-REALISED-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D74"); // UNLOCK operation not implemented
                    // UNLOCK D74 RECORDS
                    break;
                case "NEW-UNREALISED-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D75"); // UNLOCK operation not implemented
                    // UNLOCK D75 RECORDS
                    break;
                case "MGM1-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D76"); // UNLOCK operation not implemented
                    // UNLOCK D76 RECORDS
                    break;
                case "CAPITAL-REPORT-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D77"); // UNLOCK operation not implemented
                    // UNLOCK D77 RECORDS
                    break;
                case "REPLACEMENT-RELIEF-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D78"); // UNLOCK operation not implemented
                    // UNLOCK D78 RECORDS
                    break;
                case "REPLACEMENT-ACQ-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D79"); // UNLOCK operation not implemented
                    // UNLOCK D79 RECORDS
                    break;
                case "REPLACEMENT-DIS-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D80"); // UNLOCK operation not implemented
                    // UNLOCK D80 RECORDS
                    break;
                case "NOTIONAL-SALE-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D81"); // UNLOCK operation not implemented
                    // UNLOCK D81 RECORDS
                    break;
                case "NOTIONAL-SALE-SCHEDULE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D82"); // UNLOCK operation not implemented
                    // UNLOCK D82 RECORDS
                    break;
                case "BALANCE-LOAD-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D83"); // UNLOCK operation not implemented
                    // UNLOCK D83 RECORDS
                    break;
                case "BALANCES-LOAD-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D84"); // UNLOCK operation not implemented
                    // UNLOCK D84 RECORDS
                    break;
                case "OFFSHORE-INCOME-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D85"); // UNLOCK operation not implemented
                    // UNLOCK D85 RECORDS
                    break;
                case "RCF-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D86"); // UNLOCK operation not implemented
                    // UNLOCK D86 RECORDS
                    break;
                case "RCF-BACKUP-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D87"); // UNLOCK operation not implemented
                    // UNLOCK D87 RECORDS
                    break;
                case "GROUP-LOAD-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D88"); // UNLOCK operation not implemented
                    // UNLOCK D88 RECORDS
                    break;
                case "GROUP-LOAD-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D89"); // UNLOCK operation not implemented
                    // UNLOCK D89 RECORDS
                    break;
                case "COUNTRY-LOAD-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D90"); // UNLOCK operation not implemented
                    // UNLOCK D90 RECORDS
                    break;
                case "COUNTRY-LOAD-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D91"); // UNLOCK operation not implemented
                    // UNLOCK D91 RECORDS
                    break;
                case "RPI-LOAD-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D92"); // UNLOCK operation not implemented
                    // UNLOCK D92 RECORDS
                    break;
                case "RPI-LOAD-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D93"); // UNLOCK operation not implemented
                    // UNLOCK D93 RECORDS
                    break;
                case "GAINLOSS-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D94"); // UNLOCK operation not implemented
                    // UNLOCK D94 RECORDS
                    break;
                case "GAINLOSS-REPORT":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D95"); // UNLOCK operation not implemented
                    // UNLOCK D95 RECORDS
                    break;
                case "REALISED-TAX-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D98"); // UNLOCK operation not implemented
                    // UNLOCK D98 RECORDS
                    break;
                case "UNREALISED-TAX-DATA-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D101"); // UNLOCK operation not implemented
                    // UNLOCK D101 RECORDS
                    break;
                case "TAPER-RATE-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D112"); // UNLOCK operation not implemented
                    // UNLOCK D112 RECORDS
                    break;
                case "PERIOD-END-CALENDAR-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D153"); // UNLOCK operation not implemented
                    // UNLOCK D153 RECORDS
                    break;
                case "PERIOD-END-CALENDAR-DATES-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D154"); // UNLOCK operation not implemented
                    // UNLOCK D154 RECORDS
                    break;
                case "INTER-CONNECTED-FUNDS-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D163"); // UNLOCK operation not implemented
                    // UNLOCK D163 RECORDS
                    break;
                case "PRICE-TYPES-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D167"); // UNLOCK operation not implemented
                    // UNLOCK D167 RECORDS
                    break;
                case "PENDING-LOG-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D169"); // UNLOCK operation not implemented
                    // UNLOCK D169 RECORDS
                    break;
                case "PENDING-ITEMS-FILE":
                    // gvar.SetUnlock("UNLOCK"); // UNLOCK operation not implemented
                    // gvar.SetRecords("D170"); // UNLOCK operation not implemented
                    // UNLOCK D170 RECORDS
                    break;
                default:
                    gvar.SetFileStatusAsString("IF");
                    break;
            }
        }
        /// <summary>
        /// vStartNotLessThanKey2
        /// </summary>
        /// <remarks>
        /// This method implements the COBOL paragraph vStartNotLessThanKey2.
        /// It evaluates the L-FILE-NAME and performs a START operation on the corresponding file.
        /// </remarks>
        public void Vstartnotlessthankey2(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EVALUATE L-FILE-NAME
            string lFileName = ivar.GetCgtfilesLinkage().GetLFileName();
            if (lFileName == Ivar.STOCK_FILE)
            {
                // START D3 KEY NOT < D3-SPLIT-KEY
                // gvar.GetD3().StartKeyNotLessThan(gvar.GetD3SplitKey()); // File operation not implemented
            }
            else if (lFileName == Ivar.FUND_FILE)
            {
                // START D4 KEY NOT < D4-SUMMARY-FUND
                // gvar.GetD4().StartKeyNotLessThan(fvar.GetD4Record().GetD4SummaryFund()); // File operation not implemented
            }
            else if (lFileName == Ivar.TAPER_RATE_FILE)
            {
                // START D112 KEY NOT < D112-INVERSE-DATE
                // gvar.GetD112().StartKeyNotLessThan(fvar.GetD112Record().GetD112InverseDate()); // File operation not implemented
            }
            else if (lFileName == Ivar.REALISED_DATA_FILE)
            {
                // START D19 KEY NOT < D19-SPLIT-KEY-2
                // gvar.GetD19().StartKeyNotLessThan(gvar.GetD19SplitKey2()); // File operation not implemented
            }
            else if (lFileName == Ivar.UNREALISED_DATA_FILE)
            {
                // START D20 KEY NOT < D20-SPLIT-KEY-2
                // gvar.GetD20().StartKeyNotLessThan(gvar.GetD20SplitKey2()); // File operation not implemented
            }
            else if (lFileName == Ivar.NOTIONAL_SALE_DATA_FILE)
            {
                // START D81 KEY NOT < D81-SPLIT-KEY-2
                // gvar.GetD81().StartKeyNotLessThan(gvar.GetD81SplitKey2()); // File operation not implemented
            }
            else if (lFileName == Ivar.REALISED_TAX_DATA_FILE)
            {
                // START D98 KEY NOT < D98-SPLIT-KEY-2
                // gvar.GetD98().StartKeyNotLessThan(gvar.GetD98SplitKey2()); // File operation not implemented
            }
            else if (lFileName == Ivar.UNREALISED_TAX_DATA_FILE)
            {
                // START D101 KEY NOT < D101-SPLIT-KEY-2
                // gvar.GetD101().StartKeyNotLessThan(gvar.GetD101SplitKey2()); // File operation not implemented
            }
            else if (lFileName == Ivar.INTER_CONNECTED_FUNDS_FILE)
            {
                // START D163 KEY NOT < D163-OLAB-FUND-CODE
                // gvar.GetD163().StartKeyNotLessThan(fvar.GetD163Record().GetD163OlabFundCode()); // File operation not implemented
            }
            else if (lFileName == Ivar.PRICE_TYPES_FILE)
            {
                // START D167 KEY NOT < D167-SEQUENCE-CODE
                // gvar.GetD167().StartKeyNotLessThan(fvar.GetD167Record().GetD167SequenceCode()); // File operation not implemented
            }
            else if (lFileName == Ivar.PENDING_LOG_FILE)
            {
                // START D169 KEY NOT < D169-KEY2
                // gvar.GetD169().StartKeyNotLessThan(gvar.GetD169Key2()); // File operation not implemented
            }
            else
            {
                // MOVE 'IF' TO FILE-STATUS
                gvar.SetFileStatusAsString("IF");
            }
        }
        /// <summary>
        /// vaStartNotLessThanKey3
        /// </summary>
        /// <remarks>
        /// COBOL paragraph vaStartNotLessThanKey3 converted to C# method Vastartnotlessthankey3
        /// </remarks>
        public void Vastartnotlessthankey3(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // EVALUATE L-FILE-NAME
            if (ivar.GetCgtfilesLinkage().GetLFileName() == Ivar.FUND_FILE)
            {
                // START D4 KEY NOT < D4-NEW-SUMMARY-FUND
                // Note: Direct translation of COBOL START statement is complex and may require additional logic or library functions.
                // For simplicity, assume that the START statement is handled by an external method or library function.
                // Here, we just set a variable to indicate that the START statement was executed.
                SetStart("Executed");
            }
            else
            {
                // MOVE 'IF' TO FILE-STATUS
                gvar.SetFileStatusAsString("IF");
            }
        }
        /// <summary>
        /// wOpenInputReport
        /// </summary>
        /// <remarks>
        /// This method implements the logic from the COBOL paragraph wOpenInputReport.
        /// </remarks>
        public void Wopeninputreport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // OPEN   INPUT   D99.
            // gvar.SetD99("OPEN INPUT"); // SetD99 method not implemented in Gvar

            // MOVE      1     TO   W-PRINT-CHAR-SUB.
            gvar.SetWPrintCharSub(1);

            // MOVE   SPACES   TO   W-PRINT-RECORD.
            gvar.SetWPrintRecordAsString(" ");
        }
        /// <summary>
        /// xReadReport
        /// </summary>
        /// <remarks>
        /// COBOL paragraph xReadReport converted to C# method Xreadreport
        /// </remarks>
        public void Xreadreport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // READ D99.
            fvar.SetD99RecordAsString("");

            // EVALUATE D99-RECORD-BYTE
            byte d99RecordByte = fvar.GetD99Record().GetD99RecordByte();
            if (d99RecordByte == 0x0D)
            {
                // WHEN X'0D'
                // MOVE W-PRINT-RECORD TO L-FILE-RECORD-AREA
                if (ivar != null)
                {
                    ivar.SetLFileRecordAreaAsString(gvar.GetWPrintRecord().ToString());
                }

                // MOVE 1 TO W-PRINT-CHAR-SUB
                gvar.SetWPrintCharSub(1);

                // MOVE SPACES TO W-PRINT-RECORD
                gvar.SetWPrintRecordAsString("");
            }
            else
            {
                // WHEN OTHER
                if (!(gvar.GetWStoreAction() != " " && (d99RecordByte == 0x0A || d99RecordByte == 0x0C)))
                {
                    // IF W-STORE-ACTION NOT = SPACES AND (D99-RECORD-BYTE = X'0A' OR X'0C')
                    // CONTINUE
                    // ELSE
                    // MOVE D99-RECORD TO W-PRINT-CHAR(W-PRINT-CHAR-SUB)
                    int wPrintCharSub = gvar.GetWPrintCharSub();
                    gvar.GetWPrintRecord().SetWPrintCharAt(wPrintCharSub, fvar.GetD99Record());

                    // ADD 1 TO W-PRINT-CHAR-SUB
                    gvar.SetWPrintCharSub(wPrintCharSub + 1);
                    // END-IF
                }
            }
            // END-EVALUATE
        }
        /// <summary>
        /// x4Cgtlog
        /// </summary>
        /// <remarks>
        /// COBOL paragraph x4Cgtlog converted to C# method X4cgtlog
        /// </remarks>
        public void X4cgtlog(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // ADD       1                     TO        WS-MESSAGE-NO
            int wsMessageNo = gvar.GetWsMessageNo() + 1;
            gvar.SetWsMessageNo(wsMessageNo);

            // MOVE      WS-MESSAGE-NO         TO        L-LOG-MESSAGE-NO
            gvar.GetCgtlogLinkageArea2().SetLLogMessageNo(wsMessageNo.ToString());

            // MOVE      WS-PROGRAM-NAME       TO        L-LOG-PROGRAM
            gvar.GetCgtlogLinkageArea1().SetLLogProgram(gvar.GetWsProcessFlag().GetWsProgramName());

            // MOVE      WRITE-RECORD          TO        L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Ivar.WRITE_RECORD);

            // CALL      'CGTLOG'              USING     CGTLOG-LINKAGE-AREA-1
            //          CGTLOG-LINKAGE-AREA-2.
            // Cgtlog cgtlog = new Cgtlog(); // External COBOL program call not implemented
            // cgtlog.Run(gvar.GetCgtlogLinkageArea1(), gvar.GetCgtlogLinkageArea2());

            // IF        L-LOG-MESSAGE-TYPE   =   'Q'
            if (gvar.GetCgtlogLinkageArea2().GetLLogMessageType() == "Q")
            {
                // MOVE   'Q'                  TO        WS-PROCESS-FLAG
                gvar.SetWsProcessFlag("Q");

                // MOVE   'P'                  TO        L-LOG-MESSAGE-TYPE
                gvar.GetCgtlogLinkageArea2().SetLLogMessageType("P");
            }
        }
        /// <summary>
        /// Closes the report.
        /// </summary>
        /// <remarks>
        /// Corresponds to COBOL paragraph: yCloseReport
        /// </remarks>
        public void Yclosereport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // CLOSE D99 in COBOL translates to closing the file/stream represented by gvar.GetD99File()
            // Since C# does not have a direct equivalent of COBOL's CLOSE statement for streams,
            // we'll assume that gvar.GetD99File() is a stream or a file that needs to be closed.
            // if (gvar.GetD99File() != null) // GetD99File method not implemented in Gvar
            // {
            //     ((IDisposable)gvar.GetD99File()).Dispose();
            //     // gvar.SetD99(null); // SetD99 method not implemented
            // }
        }
        /// <summary>
        /// Closes all files.
        /// </summary>
        /// <remarks>
        /// This method corresponds to the COBOL paragraph 'z3CloseAllFiles'.
        /// </remarks>
        public void Z3closeallfiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // CLOSE D1.
            // Note: In COBOL, CLOSE statement does not have a direct equivalent in C#.
            //       This is typically handled through file streams or other I/O mechanisms.
            //       For simplicity, we assume that the files are being closed through some external mechanism.

            // Simulating CLOSE statements for demonstration purposes
            // gvar.SetD1(null); // SetD1 method not implemented
            // gvar.SetD2(null); // SetD2 method not implemented
            // gvar.SetD3(null); // SetD3 method not implemented
            // gvar.SetD4(null); // SetD4 method not implemented
            // gvar.SetD5(null); // SetD5 method not implemented
            // gvar.SetD6(null); // SetD6 method not implemented
            // gvar.SetD7(null); // SetD7 method not implemented
            // gvar.SetD8(null); // SetD8 method not implemented
            // gvar.SetD10(null); // SetD10 method not implemented
            // gvar.SetD11(null); // SetD11 method not implemented
            // gvar.SetD12(null); // SetD12 method not implemented
            // gvar.SetD17(null); // SetD17 method not implemented
            // gvar.SetD18(null); // SetD18 method not implemented
            // gvar.SetD19(null); // SetD19 method not implemented
            // gvar.SetD20(null); // SetD20 method not implemented
            // gvar.SetD21(null); // SetD21 method not implemented
            // gvar.SetD22(null); // SetD22 method not implemented
            // gvar.SetD23(null); // SetD23 method not implemented
            // gvar.SetD24(null); // SetD24 method not implemented
            // gvar.SetD25(null); // SetD25 method not implemented
            // gvar.SetD26(null); // SetD26 method not implemented
            // gvar.SetD27(null); // SetD27 method not implemented
            // gvar.SetD28(null); // SetD28 method not implemented
            // gvar.SetD29(null); // SetD29 method not implemented
            // gvar.SetD30(null); // SetD30 method not implemented
            // gvar.SetD31(null); // SetD31 method not implemented
            // gvar.SetD32(null); // SetD32 method not implemented
            // gvar.SetD33(null); // SetD33 method not implemented
            // gvar.SetD34(null); // SetD34 method not implemented
            // gvar.SetD35(null); // SetD35 method not implemented
            // gvar.SetD36(null); // SetD36 method not implemented
            // gvar.SetD38(null); // SetD38 method not implemented
            // gvar.SetD39(null); // SetD39 method not implemented
            // gvar.SetD41(null); // SetD41 method not implemented
            // gvar.SetD42(null); // SetD42 method not implemented
            // gvar.SetD43(null); // SetD43 method not implemented
            // gvar.SetD44(null); // SetD44 method not implemented
            // gvar.SetD45(null); // SetD45 method not implemented
            // gvar.SetD46(null); // SetD46 method not implemented
            // gvar.SetD49(null); // SetD49 method not implemented
            // gvar.SetD50(null); // SetD50 method not implemented
            // gvar.SetD51(null); // SetD51 method not implemented
            // gvar.SetD68(null); // SetD68 method not implemented
            // gvar.SetD69(null); // SetD69 method not implemented
            // gvar.SetD70(null); // SetD70 method not implemented
            // gvar.SetD71(null); // SetD71 method not implemented
            // gvar.SetD72(null); // SetD72 method not implemented
            // gvar.SetD73(null); // SetD73 method not implemented
            // gvar.SetD74(null); // SetD74 method not implemented
            // gvar.SetD75(null); // SetD75 method not implemented
            // gvar.SetD76(null); // SetD76 method not implemented
            // gvar.SetD77(null); // SetD77 method not implemented
            // gvar.SetD78(null); // SetD78 method not implemented
            // gvar.SetD79(null); // SetD79 method not implemented
            // gvar.SetD80(null); // SetD80 method not implemented
            // gvar.SetD81(null); // SetD81 method not implemented
            // gvar.SetD82(null); // SetD82 method not implemented
            // gvar.SetD83(null); // SetD83 method not implemented
            // gvar.SetD84(null); // SetD84 method not implemented
            // gvar.SetD85(null); // SetD85 method not implemented
            // gvar.SetD86(null); // SetD86 method not implemented
            // gvar.SetD87(null); // SetD87 method not implemented
            // gvar.SetD88(null); // SetD88 method not implemented
            // gvar.SetD89(null); // SetD89 method not implemented
            // gvar.SetD90(null); // SetD90 method not implemented
            // gvar.SetD91(null); // SetD91 method not implemented
            // gvar.SetD92(null); // SetD92 method not implemented
            // gvar.SetD93(null); // SetD93 method not implemented
            // gvar.SetD94(null); // SetD94 method not implemented
            // gvar.SetD95(null); // SetD95 method not implemented
            // gvar.SetD98(null); // SetD98 method not implemented
            // gvar.SetD101(null); // SetD101 method not implemented
            // gvar.SetD107(null); // SetD107 method not implemented
            // gvar.SetD108(null); // SetD108 method not implemented
            // gvar.SetD109(null); // SetD109 method not implemented
            // gvar.SetD112(null); // SetD112 method not implemented
            // gvar.SetD133(null); // SetD133 method not implemented
            // gvar.SetD153(null); // SetD153 method not implemented
            // gvar.SetD154(null); // SetD154 method not implemented
            // gvar.SetD163(null); // SetD163 method not implemented
            // gvar.SetD167(null); // SetD167 method not implemented
            // gvar.SetD169(null); // SetD169 method not implemented
        }
        /// <summary>
        /// z4MakeFileName
        /// </summary>
        /// <remarks>
        /// COBOL paragraph z4MakeFileName converted to C# method Z4makefilename
        /// </remarks>
        public void Z4makefilename(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileAction())
            {
                case "OPEN-INPUT":
                case "OPEN-OUTPUT":
                case "OPEN-I-O":
                case "OPEN-EXTEND":
                case "OPEN-INPUT-REPORT":
                    switch (ivar.GetCgtfilesLinkage().GetLFileName())
                    {
                        case "COUNTRY-FILE":
                            // CONTINUE
                            break;
                        case "GROUP-FILE":
                            // CONTINUE
                            break;
                        case "STOCK-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetADMIN_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD3File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD3FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "FUND-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetADMIN_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD4File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD4FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "CGTR04-REPORT-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD5File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD5FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "CGTR05-REPORT-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD6File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD6FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "RPI-FILE":
                            // CONTINUE
                            break;
                        case "PARAMETER-FILE":
                            // CONTINUE
                            break;
                        case "USER-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetADMIN_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD9File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD9FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "STERLING-EXTEL-REPORT":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD10File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD10FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "FOREIGN-EXTEL-REPORT":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD11File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD11FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "OUTPUT-LISTING":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD12File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD12FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "MASTER-LOG-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetADMIN_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD17File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD17FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "ERROR-REPORT-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD18File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD18FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "REALISED-DATA-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD19File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD19FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "UNREALISED-DATA-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD20File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD20FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "REALISED-SCHEDULE-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD21File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD21FileName(gvar.GetEqtpathPathFileName());
                            break;
                        case "UNREALISED-SCHEDULE-FILE":
                            // gvar.SetEqtpathPathEnvVariable(gvar.GetUSER_DATA_PATH()); // Methods not implemented in Gvar
                            // gvar.SetEqtpathFileName(gvar.GetD22File());
                            // XCallEqtpath(gvar); // PERFORM X-CALL-EQTPATH - External COBOL program call not implemented
                            // gvar.SetD22FileName(gvar.GetEqtpathPathFileName());
                            break;
                            // ... rest of the cases
                    }
                    break;
            }
        }
        /// <summary>
        /// xCallEqtpath
        /// </summary>
        /// <remarks>
        /// Calls the EQTPATH external program using the EQTPATH-LINKAGE parameter.
        /// </remarks>
        public void Xcalleqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program EQTPATH
            // Eqtpath eqtpath = new Eqtpath(); // External COBOL program not implemented
            // Call the Run method with the EQTPATH-LINKAGE parameter
            // eqtpath.Run(gvar.GetEqtpathLinkage());
        }
        /// <summary>
        /// xCallEqtdebug
        /// </summary>
        /// <remarks>
        /// COBOL paragraph xCallEqtdebug converted to C# method Xcalleqtdebug
        /// </remarks>
        public void Xcalleqtdebug(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program EQTDEBUG
            // Eqtdebug eqtdebug = new Eqtdebug(); // External COBOL program not implemented
            // Call the Run method with ONLY the parameters specified in the USING clause
            // eqtdebug.Run(gvar.GetEqtdebugLinkage());
        }

    }
}
