using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing E000Detail Data Structure

public class E000Detail
{
    private static int _size = 181;
    // [DEBUG] Class: E000Detail, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: E000Control, is_external=, is_static_class=False, static_prefix=
    private string _E000Control ="";
    
    
    
    
    // [DEBUG] Field: E000, is_external=, is_static_class=False, static_prefix=
    private string _E000 ="";
    
    
    
    
    // [DEBUG] Field: E001Text, is_external=, is_static_class=False, static_prefix=
    private string _E001Text ="";
    
    
    
    
    // [DEBUG] Field: Filler86, is_external=, is_static_class=False, static_prefix=
    private string _Filler86 ="";
    
    
    
    
    // [DEBUG] Field: E002Rest, is_external=, is_static_class=False, static_prefix=
    private string _E002Rest ="";
    
    
    
    
    
    // Serialization methods
    public string GetE000DetailAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_E000Control.PadRight(1));
        result.Append(_E000.PadRight(1));
        result.Append(_E001Text.PadRight(18));
        result.Append(_Filler86.PadRight(1));
        result.Append(_E002Rest.PadRight(160));
        
        return result.ToString();
    }
    
    public void SetE000DetailAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetE000Control(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetE000(extracted);
        }
        offset += 1;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetE001Text(extracted);
        }
        offset += 18;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller86(extracted);
        }
        offset += 1;
        if (offset + 160 <= data.Length)
        {
            string extracted = data.Substring(offset, 160).Trim();
            SetE002Rest(extracted);
        }
        offset += 160;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetE000DetailAsString();
    }
    // Set<>String Override function
    public void SetE000Detail(string value)
    {
        SetE000DetailAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetE000Control()
    {
        return _E000Control;
    }
    
    // Standard Setter
    public void SetE000Control(string value)
    {
        _E000Control = value;
    }
    
    // Get<>AsString()
    public string GetE000ControlAsString()
    {
        return _E000Control.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetE000ControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _E000Control = value;
    }
    
    // Standard Getter
    public string GetE000()
    {
        return _E000;
    }
    
    // Standard Setter
    public void SetE000(string value)
    {
        _E000 = value;
    }
    
    // Get<>AsString()
    public string GetE000AsString()
    {
        return _E000.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetE000AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _E000 = value;
    }
    
    // Standard Getter
    public string GetE001Text()
    {
        return _E001Text;
    }
    
    // Standard Setter
    public void SetE001Text(string value)
    {
        _E001Text = value;
    }
    
    // Get<>AsString()
    public string GetE001TextAsString()
    {
        return _E001Text.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetE001TextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _E001Text = value;
    }
    
    // Standard Getter
    public string GetFiller86()
    {
        return _Filler86;
    }
    
    // Standard Setter
    public void SetFiller86(string value)
    {
        _Filler86 = value;
    }
    
    // Get<>AsString()
    public string GetFiller86AsString()
    {
        return _Filler86.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller86AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler86 = value;
    }
    
    // Standard Getter
    public string GetE002Rest()
    {
        return _E002Rest;
    }
    
    // Standard Setter
    public void SetE002Rest(string value)
    {
        _E002Rest = value;
    }
    
    // Get<>AsString()
    public string GetE002RestAsString()
    {
        return _E002Rest.PadRight(160);
    }
    
    // Set<>AsString()
    public void SetE002RestAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _E002Rest = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
