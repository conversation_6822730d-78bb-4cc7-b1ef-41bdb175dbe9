using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing H000Heading Data Structure

public class H000Heading
{
    private static int _size = 181;
    // [DEBUG] Class: H000Heading, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: H001Control, is_external=, is_static_class=False, static_prefix=
    private string _H001Control ="";
    
    
    
    
    // [DEBUG] Field: Filler82, is_external=, is_static_class=False, static_prefix=
    private string _Filler82 ="";
    
    
    
    
    // [DEBUG] Field: H002, is_external=, is_static_class=False, static_prefix=
    private string _H002 ="";
    
    
    
    
    // [DEBUG] Field: Filler83, is_external=, is_static_class=False, static_prefix=
    private string _Filler83 ="";
    
    
    
    
    // [DEBUG] Field: H003Fund, is_external=, is_static_class=False, static_prefix=
    private string _H003Fund ="";
    
    
    
    
    // [DEBUG] Field: H004, is_external=, is_static_class=False, static_prefix=
    private string _H004 ="";
    
    
    
    
    // [DEBUG] Field: H005Cal, is_external=, is_static_class=False, static_prefix=
    private string _H005Cal ="";
    
    
    
    
    // [DEBUG] Field: H006, is_external=, is_static_class=False, static_prefix=
    private string _H006 ="";
    
    
    
    
    // [DEBUG] Field: H007Country, is_external=, is_static_class=False, static_prefix=
    private string _H007Country ="";
    
    
    
    
    // [DEBUG] Field: H008, is_external=, is_static_class=False, static_prefix=
    private string _H008 ="";
    
    
    
    
    // [DEBUG] Field: H009Group, is_external=, is_static_class=False, static_prefix=
    private string _H009Group ="";
    
    
    
    
    // [DEBUG] Field: H010, is_external=, is_static_class=False, static_prefix=
    private string _H010 ="";
    
    
    
    
    // [DEBUG] Field: H011Dates, is_external=, is_static_class=False, static_prefix=
    private string _H011Dates ="";
    
    
    
    
    // [DEBUG] Field: H012, is_external=, is_static_class=False, static_prefix=
    private string _H012 ="";
    
    
    
    
    // [DEBUG] Field: H013PageNumber, is_external=, is_static_class=False, static_prefix=
    private decimal _H013PageNumber =0;
    
    
    
    
    // [DEBUG] Field: H014, is_external=, is_static_class=False, static_prefix=
    private string _H014 ="";
    
    
    
    
    
    // Serialization methods
    public string GetH000HeadingAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_H001Control.PadRight(1));
        result.Append(_Filler82.PadRight(1));
        result.Append(_H002.PadRight(50));
        result.Append(_Filler83.PadRight(21));
        result.Append(_H003Fund.PadRight(60));
        result.Append(_H004.PadRight(3));
        result.Append(_H005Cal.PadRight(6));
        result.Append(_H006.PadRight(3));
        result.Append(_H007Country.PadRight(3));
        result.Append(_H008.PadRight(4));
        result.Append(_H009Group.PadRight(2));
        result.Append(_H010.PadRight(2));
        result.Append(_H011Dates.PadRight(17));
        result.Append(_H012.PadRight(2));
        result.Append(_H013PageNumber.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_H014.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetH000HeadingAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetH001Control(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller82(extracted);
        }
        offset += 1;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetH002(extracted);
        }
        offset += 50;
        if (offset + 21 <= data.Length)
        {
            string extracted = data.Substring(offset, 21).Trim();
            SetFiller83(extracted);
        }
        offset += 21;
        if (offset + 60 <= data.Length)
        {
            string extracted = data.Substring(offset, 60).Trim();
            SetH003Fund(extracted);
        }
        offset += 60;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetH004(extracted);
        }
        offset += 3;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetH005Cal(extracted);
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetH006(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetH007Country(extracted);
        }
        offset += 3;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetH008(extracted);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetH009Group(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetH010(extracted);
        }
        offset += 2;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            SetH011Dates(extracted);
        }
        offset += 17;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetH012(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetH013PageNumber(parsedDec);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetH014(extracted);
        }
        offset += 2;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetH000HeadingAsString();
    }
    // Set<>String Override function
    public void SetH000Heading(string value)
    {
        SetH000HeadingAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetH001Control()
    {
        return _H001Control;
    }
    
    // Standard Setter
    public void SetH001Control(string value)
    {
        _H001Control = value;
    }
    
    // Get<>AsString()
    public string GetH001ControlAsString()
    {
        return _H001Control.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetH001ControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H001Control = value;
    }
    
    // Standard Getter
    public string GetFiller82()
    {
        return _Filler82;
    }
    
    // Standard Setter
    public void SetFiller82(string value)
    {
        _Filler82 = value;
    }
    
    // Get<>AsString()
    public string GetFiller82AsString()
    {
        return _Filler82.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller82AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler82 = value;
    }
    
    // Standard Getter
    public string GetH002()
    {
        return _H002;
    }
    
    // Standard Setter
    public void SetH002(string value)
    {
        _H002 = value;
    }
    
    // Get<>AsString()
    public string GetH002AsString()
    {
        return _H002.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetH002AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H002 = value;
    }
    
    // Standard Getter
    public string GetFiller83()
    {
        return _Filler83;
    }
    
    // Standard Setter
    public void SetFiller83(string value)
    {
        _Filler83 = value;
    }
    
    // Get<>AsString()
    public string GetFiller83AsString()
    {
        return _Filler83.PadRight(21);
    }
    
    // Set<>AsString()
    public void SetFiller83AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler83 = value;
    }
    
    // Standard Getter
    public string GetH003Fund()
    {
        return _H003Fund;
    }
    
    // Standard Setter
    public void SetH003Fund(string value)
    {
        _H003Fund = value;
    }
    
    // Get<>AsString()
    public string GetH003FundAsString()
    {
        return _H003Fund.PadRight(60);
    }
    
    // Set<>AsString()
    public void SetH003FundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H003Fund = value;
    }
    
    // Standard Getter
    public string GetH004()
    {
        return _H004;
    }
    
    // Standard Setter
    public void SetH004(string value)
    {
        _H004 = value;
    }
    
    // Get<>AsString()
    public string GetH004AsString()
    {
        return _H004.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetH004AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H004 = value;
    }
    
    // Standard Getter
    public string GetH005Cal()
    {
        return _H005Cal;
    }
    
    // Standard Setter
    public void SetH005Cal(string value)
    {
        _H005Cal = value;
    }
    
    // Get<>AsString()
    public string GetH005CalAsString()
    {
        return _H005Cal.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetH005CalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H005Cal = value;
    }
    
    // Standard Getter
    public string GetH006()
    {
        return _H006;
    }
    
    // Standard Setter
    public void SetH006(string value)
    {
        _H006 = value;
    }
    
    // Get<>AsString()
    public string GetH006AsString()
    {
        return _H006.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetH006AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H006 = value;
    }
    
    // Standard Getter
    public string GetH007Country()
    {
        return _H007Country;
    }
    
    // Standard Setter
    public void SetH007Country(string value)
    {
        _H007Country = value;
    }
    
    // Get<>AsString()
    public string GetH007CountryAsString()
    {
        return _H007Country.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetH007CountryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H007Country = value;
    }
    
    // Standard Getter
    public string GetH008()
    {
        return _H008;
    }
    
    // Standard Setter
    public void SetH008(string value)
    {
        _H008 = value;
    }
    
    // Get<>AsString()
    public string GetH008AsString()
    {
        return _H008.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetH008AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H008 = value;
    }
    
    // Standard Getter
    public string GetH009Group()
    {
        return _H009Group;
    }
    
    // Standard Setter
    public void SetH009Group(string value)
    {
        _H009Group = value;
    }
    
    // Get<>AsString()
    public string GetH009GroupAsString()
    {
        return _H009Group.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetH009GroupAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H009Group = value;
    }
    
    // Standard Getter
    public string GetH010()
    {
        return _H010;
    }
    
    // Standard Setter
    public void SetH010(string value)
    {
        _H010 = value;
    }
    
    // Get<>AsString()
    public string GetH010AsString()
    {
        return _H010.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetH010AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H010 = value;
    }
    
    // Standard Getter
    public string GetH011Dates()
    {
        return _H011Dates;
    }
    
    // Standard Setter
    public void SetH011Dates(string value)
    {
        _H011Dates = value;
    }
    
    // Get<>AsString()
    public string GetH011DatesAsString()
    {
        return _H011Dates.PadRight(17);
    }
    
    // Set<>AsString()
    public void SetH011DatesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H011Dates = value;
    }
    
    // Standard Getter
    public string GetH012()
    {
        return _H012;
    }
    
    // Standard Setter
    public void SetH012(string value)
    {
        _H012 = value;
    }
    
    // Get<>AsString()
    public string GetH012AsString()
    {
        return _H012.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetH012AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H012 = value;
    }
    
    // Standard Getter
    public decimal GetH013PageNumber()
    {
        return _H013PageNumber;
    }
    
    // Standard Setter
    public void SetH013PageNumber(decimal value)
    {
        _H013PageNumber = value;
    }
    
    // Get<>AsString()
    public string GetH013PageNumberAsString()
    {
        return _H013PageNumber.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetH013PageNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _H013PageNumber = parsed;
    }
    
    // Standard Getter
    public string GetH014()
    {
        return _H014;
    }
    
    // Standard Setter
    public void SetH014(string value)
    {
        _H014 = value;
    }
    
    // Get<>AsString()
    public string GetH014AsString()
    {
        return _H014.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetH014AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _H014 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
