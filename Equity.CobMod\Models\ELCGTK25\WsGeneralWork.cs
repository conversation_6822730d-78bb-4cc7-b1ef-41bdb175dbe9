using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing WsGeneralWork Data Structure

public class WsGeneralWork
{
    private static int _size = 121;
    // [DEBUG] Class: WsGeneralWork, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private string _Filler1 ="WS=GENERAL======";
    
    
    
    
    // [DEBUG] Field: WsVector, is_external=, is_static_class=False, static_prefix=
    private string _WsVector =" ";
    
    
    
    
    // [DEBUG] Field: WsReturnCode, is_external=, is_static_class=False, static_prefix=
    private int _WsReturnCode =0;
    
    
    
    
    // [DEBUG] Field: WsNumber, is_external=, is_static_class=False, static_prefix=
    private int _WsNumber =0;
    
    
    
    
    // [DEBUG] Field: WsFileFlags, is_external=, is_static_class=False, static_prefix=
    private WsFileFlags _WsFileFlags = new WsFileFlags();
    
    
    
    
    // [DEBUG] Field: WsPgmNames, is_external=, is_static_class=False, static_prefix=
    private WsPgmNames _WsPgmNames = new WsPgmNames();
    
    
    
    
    // [DEBUG] Field: WsFileNames, is_external=, is_static_class=False, static_prefix=
    private WsFileNames _WsFileNames = new WsFileNames();
    
    
    
    
    // [DEBUG] Field: WsProgDetails, is_external=, is_static_class=False, static_prefix=
    private WsProgDetails _WsProgDetails = new WsProgDetails();
    
    
    
    
    // [DEBUG] Field: WsHoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsHoldingFlag ="N";
    
    
    
    
    // [DEBUG] Field: WsBondFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsBondFlag ="N";
    
    
    
    
    // [DEBUG] Field: WsCloseD8Flag, is_external=, is_static_class=False, static_prefix=
    private string _WsCloseD8Flag ="";
    
    
    // 88-level condition checks for WsCloseD8Flag
    public bool IsCloseD8Needed()
    {
        if (this._WsCloseD8Flag == "'Y'") return true;
        return false;
    }
    public bool IsCloseD8NotNeeded()
    {
        if (this._WsCloseD8Flag == "'N'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsTaperDate, is_external=, is_static_class=False, static_prefix=
    private string _WsTaperDate ="";
    
    
    
    
    // [DEBUG] Field: WsTaperDatePrinted, is_external=, is_static_class=False, static_prefix=
    private string _WsTaperDatePrinted ="";
    
    
    // 88-level condition checks for WsTaperDatePrinted
    public bool IsTaperDatePrinted()
    {
        if (this._WsTaperDatePrinted == "'Y'") return true;
        return false;
    }
    public bool IsTaperDateNotPrinted()
    {
        if (this._WsTaperDatePrinted == "'N'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsTaperDateActionFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsTaperDateActionFlag ="";
    
    
    // 88-level condition checks for WsTaperDateActionFlag
    public bool IsSetTaperDate()
    {
        if (this._WsTaperDateActionFlag == "'1'") return true;
        return false;
    }
    public bool IsPrintTaperDate()
    {
        if (this._WsTaperDateActionFlag == "'2'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetWsGeneralWorkAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler1.PadRight(16));
        result.Append(_WsVector.PadRight(1));
        result.Append(_WsReturnCode.ToString().PadLeft(2, '0'));
        result.Append(_WsNumber.ToString().PadLeft(2, '0'));
        result.Append(_WsFileFlags.GetWsFileFlagsAsString());
        result.Append(_WsPgmNames.GetWsPgmNamesAsString());
        result.Append(_WsFileNames.GetWsFileNamesAsString());
        result.Append(_WsProgDetails.GetWsProgDetailsAsString());
        result.Append(_WsHoldingFlag.PadRight(1));
        result.Append(_WsBondFlag.PadRight(1));
        result.Append(_WsCloseD8Flag.PadRight(1));
        result.Append(_WsTaperDate.PadRight(7));
        result.Append(_WsTaperDatePrinted.PadRight(1));
        result.Append(_WsTaperDateActionFlag.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsGeneralWorkAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller1(extracted);
        }
        offset += 16;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsVector(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsReturnCode(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsNumber(parsedInt);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _WsFileFlags.SetWsFileFlagsAsString(data.Substring(offset, 6));
        }
        else
        {
            _WsFileFlags.SetWsFileFlagsAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 32 <= data.Length)
        {
            _WsPgmNames.SetWsPgmNamesAsString(data.Substring(offset, 32));
        }
        else
        {
            _WsPgmNames.SetWsPgmNamesAsString(data.Substring(offset));
        }
        offset += 32;
        if (offset + 16 <= data.Length)
        {
            _WsFileNames.SetWsFileNamesAsString(data.Substring(offset, 16));
        }
        else
        {
            _WsFileNames.SetWsFileNamesAsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 34 <= data.Length)
        {
            _WsProgDetails.SetWsProgDetailsAsString(data.Substring(offset, 34));
        }
        else
        {
            _WsProgDetails.SetWsProgDetailsAsString(data.Substring(offset));
        }
        offset += 34;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsHoldingFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsBondFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsCloseD8Flag(extracted);
        }
        offset += 1;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWsTaperDate(extracted);
        }
        offset += 7;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsTaperDatePrinted(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsTaperDateActionFlag(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsGeneralWorkAsString();
    }
    // Set<>String Override function
    public void SetWsGeneralWork(string value)
    {
        SetWsGeneralWorkAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetWsVector()
    {
        return _WsVector;
    }
    
    // Standard Setter
    public void SetWsVector(string value)
    {
        _WsVector = value;
    }
    
    // Get<>AsString()
    public string GetWsVectorAsString()
    {
        return _WsVector.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsVectorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsVector = value;
    }
    
    // Standard Getter
    public int GetWsReturnCode()
    {
        return _WsReturnCode;
    }
    
    // Standard Setter
    public void SetWsReturnCode(int value)
    {
        _WsReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetWsReturnCodeAsString()
    {
        return _WsReturnCode.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsReturnCode = parsed;
    }
    
    // Standard Getter
    public int GetWsNumber()
    {
        return _WsNumber;
    }
    
    // Standard Setter
    public void SetWsNumber(int value)
    {
        _WsNumber = value;
    }
    
    // Get<>AsString()
    public string GetWsNumberAsString()
    {
        return _WsNumber.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsNumber = parsed;
    }
    
    // Standard Getter
    public WsFileFlags GetWsFileFlags()
    {
        return _WsFileFlags;
    }
    
    // Standard Setter
    public void SetWsFileFlags(WsFileFlags value)
    {
        _WsFileFlags = value;
    }
    
    // Get<>AsString()
    public string GetWsFileFlagsAsString()
    {
        return _WsFileFlags != null ? _WsFileFlags.GetWsFileFlagsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsFileFlagsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsFileFlags == null)
        {
            _WsFileFlags = new WsFileFlags();
        }
        _WsFileFlags.SetWsFileFlagsAsString(value);
    }
    
    // Standard Getter
    public WsPgmNames GetWsPgmNames()
    {
        return _WsPgmNames;
    }
    
    // Standard Setter
    public void SetWsPgmNames(WsPgmNames value)
    {
        _WsPgmNames = value;
    }
    
    // Get<>AsString()
    public string GetWsPgmNamesAsString()
    {
        return _WsPgmNames != null ? _WsPgmNames.GetWsPgmNamesAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsPgmNamesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsPgmNames == null)
        {
            _WsPgmNames = new WsPgmNames();
        }
        _WsPgmNames.SetWsPgmNamesAsString(value);
    }
    
    // Standard Getter
    public WsFileNames GetWsFileNames()
    {
        return _WsFileNames;
    }
    
    // Standard Setter
    public void SetWsFileNames(WsFileNames value)
    {
        _WsFileNames = value;
    }
    
    // Get<>AsString()
    public string GetWsFileNamesAsString()
    {
        return _WsFileNames != null ? _WsFileNames.GetWsFileNamesAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsFileNamesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsFileNames == null)
        {
            _WsFileNames = new WsFileNames();
        }
        _WsFileNames.SetWsFileNamesAsString(value);
    }
    
    // Standard Getter
    public WsProgDetails GetWsProgDetails()
    {
        return _WsProgDetails;
    }
    
    // Standard Setter
    public void SetWsProgDetails(WsProgDetails value)
    {
        _WsProgDetails = value;
    }
    
    // Get<>AsString()
    public string GetWsProgDetailsAsString()
    {
        return _WsProgDetails != null ? _WsProgDetails.GetWsProgDetailsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsProgDetailsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsProgDetails == null)
        {
            _WsProgDetails = new WsProgDetails();
        }
        _WsProgDetails.SetWsProgDetailsAsString(value);
    }
    
    // Standard Getter
    public string GetWsHoldingFlag()
    {
        return _WsHoldingFlag;
    }
    
    // Standard Setter
    public void SetWsHoldingFlag(string value)
    {
        _WsHoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsHoldingFlagAsString()
    {
        return _WsHoldingFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsHoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsHoldingFlag = value;
    }
    
    // Standard Getter
    public string GetWsBondFlag()
    {
        return _WsBondFlag;
    }
    
    // Standard Setter
    public void SetWsBondFlag(string value)
    {
        _WsBondFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsBondFlagAsString()
    {
        return _WsBondFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsBondFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsBondFlag = value;
    }
    
    // Standard Getter
    public string GetWsCloseD8Flag()
    {
        return _WsCloseD8Flag;
    }
    
    // Standard Setter
    public void SetWsCloseD8Flag(string value)
    {
        _WsCloseD8Flag = value;
    }
    
    // Get<>AsString()
    public string GetWsCloseD8FlagAsString()
    {
        return _WsCloseD8Flag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsCloseD8FlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCloseD8Flag = value;
    }
    
    // Standard Getter
    public string GetWsTaperDate()
    {
        return _WsTaperDate;
    }
    
    // Standard Setter
    public void SetWsTaperDate(string value)
    {
        _WsTaperDate = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperDateAsString()
    {
        return _WsTaperDate.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWsTaperDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsTaperDate = value;
    }
    
    // Standard Getter
    public string GetWsTaperDatePrinted()
    {
        return _WsTaperDatePrinted;
    }
    
    // Standard Setter
    public void SetWsTaperDatePrinted(string value)
    {
        _WsTaperDatePrinted = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperDatePrintedAsString()
    {
        return _WsTaperDatePrinted.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsTaperDatePrintedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsTaperDatePrinted = value;
    }
    
    // Standard Getter
    public string GetWsTaperDateActionFlag()
    {
        return _WsTaperDateActionFlag;
    }
    
    // Standard Setter
    public void SetWsTaperDateActionFlag(string value)
    {
        _WsTaperDateActionFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperDateActionFlagAsString()
    {
        return _WsTaperDateActionFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsTaperDateActionFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsTaperDateActionFlag = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsFileFlags(string value)
    {
        _WsFileFlags.SetWsFileFlagsAsString(value);
    }
    // Nested Class: WsFileFlags
    public class WsFileFlags
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsDataOpen, is_external=, is_static_class=False, static_prefix=
        private string _WsDataOpen ="N";
        
        
        
        
        // [DEBUG] Field: WsSchedOpen, is_external=, is_static_class=False, static_prefix=
        private string _WsSchedOpen ="N";
        
        
        
        
        // [DEBUG] Field: WsFundOpen, is_external=, is_static_class=False, static_prefix=
        private string _WsFundOpen ="N";
        
        
        
        
        // [DEBUG] Field: WsMsgOpen, is_external=, is_static_class=False, static_prefix=
        private string _WsMsgOpen ="N";
        
        
        
        
        // [DEBUG] Field: WsCtryOpen, is_external=, is_static_class=False, static_prefix=
        private string _WsCtryOpen ="N";
        
        
        
        
        // [DEBUG] Field: WsGrpOpen, is_external=, is_static_class=False, static_prefix=
        private string _WsGrpOpen ="N";
        
        
        
        
    public WsFileFlags() {}
    
    public WsFileFlags(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsDataOpen(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsSchedOpen(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsFundOpen(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsMsgOpen(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsCtryOpen(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsGrpOpen(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetWsFileFlagsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsDataOpen.PadRight(1));
        result.Append(_WsSchedOpen.PadRight(1));
        result.Append(_WsFundOpen.PadRight(1));
        result.Append(_WsMsgOpen.PadRight(1));
        result.Append(_WsCtryOpen.PadRight(1));
        result.Append(_WsGrpOpen.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsFileFlagsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsDataOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsSchedOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsFundOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsMsgOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsCtryOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsGrpOpen(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsDataOpen()
    {
        return _WsDataOpen;
    }
    
    // Standard Setter
    public void SetWsDataOpen(string value)
    {
        _WsDataOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsDataOpenAsString()
    {
        return _WsDataOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsDataOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsDataOpen = value;
    }
    
    // Standard Getter
    public string GetWsSchedOpen()
    {
        return _WsSchedOpen;
    }
    
    // Standard Setter
    public void SetWsSchedOpen(string value)
    {
        _WsSchedOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsSchedOpenAsString()
    {
        return _WsSchedOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsSchedOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsSchedOpen = value;
    }
    
    // Standard Getter
    public string GetWsFundOpen()
    {
        return _WsFundOpen;
    }
    
    // Standard Setter
    public void SetWsFundOpen(string value)
    {
        _WsFundOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsFundOpenAsString()
    {
        return _WsFundOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsFundOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsFundOpen = value;
    }
    
    // Standard Getter
    public string GetWsMsgOpen()
    {
        return _WsMsgOpen;
    }
    
    // Standard Setter
    public void SetWsMsgOpen(string value)
    {
        _WsMsgOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsMsgOpenAsString()
    {
        return _WsMsgOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsMsgOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsMsgOpen = value;
    }
    
    // Standard Getter
    public string GetWsCtryOpen()
    {
        return _WsCtryOpen;
    }
    
    // Standard Setter
    public void SetWsCtryOpen(string value)
    {
        _WsCtryOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsCtryOpenAsString()
    {
        return _WsCtryOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsCtryOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCtryOpen = value;
    }
    
    // Standard Getter
    public string GetWsGrpOpen()
    {
        return _WsGrpOpen;
    }
    
    // Standard Setter
    public void SetWsGrpOpen(string value)
    {
        _WsGrpOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsGrpOpenAsString()
    {
        return _WsGrpOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsGrpOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsGrpOpen = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWsPgmNames(string value)
{
    _WsPgmNames.SetWsPgmNamesAsString(value);
}
// Nested Class: WsPgmNames
public class WsPgmNames
{
    private static int _size = 32;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsCioPgm, is_external=, is_static_class=False, static_prefix=
    private string _WsCioPgm ="ELCGCIO";
    
    
    
    
    // [DEBUG] Field: WsYioPgm, is_external=, is_static_class=False, static_prefix=
    private string _WsYioPgm ="ELCGYIO";
    
    
    
    
    // [DEBUG] Field: WsDioPgm, is_external=, is_static_class=False, static_prefix=
    private string _WsDioPgm ="ELCGYIO";
    
    
    
    
    // [DEBUG] Field: WsFundPgm, is_external=, is_static_class=False, static_prefix=
    private string _WsFundPgm ="ELCGTIO";
    
    
    
    
public WsPgmNames() {}

public WsPgmNames(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsCioPgm(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWsYioPgm(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWsDioPgm(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWsFundPgm(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetWsPgmNamesAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsCioPgm.PadRight(8));
    result.Append(_WsYioPgm.PadRight(8));
    result.Append(_WsDioPgm.PadRight(8));
    result.Append(_WsFundPgm.PadRight(8));
    
    return result.ToString();
}

public void SetWsPgmNamesAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWsCioPgm(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWsYioPgm(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWsDioPgm(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWsFundPgm(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetWsCioPgm()
{
    return _WsCioPgm;
}

// Standard Setter
public void SetWsCioPgm(string value)
{
    _WsCioPgm = value;
}

// Get<>AsString()
public string GetWsCioPgmAsString()
{
    return _WsCioPgm.PadRight(8);
}

// Set<>AsString()
public void SetWsCioPgmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCioPgm = value;
}

// Standard Getter
public string GetWsYioPgm()
{
    return _WsYioPgm;
}

// Standard Setter
public void SetWsYioPgm(string value)
{
    _WsYioPgm = value;
}

// Get<>AsString()
public string GetWsYioPgmAsString()
{
    return _WsYioPgm.PadRight(8);
}

// Set<>AsString()
public void SetWsYioPgmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsYioPgm = value;
}

// Standard Getter
public string GetWsDioPgm()
{
    return _WsDioPgm;
}

// Standard Setter
public void SetWsDioPgm(string value)
{
    _WsDioPgm = value;
}

// Get<>AsString()
public string GetWsDioPgmAsString()
{
    return _WsDioPgm.PadRight(8);
}

// Set<>AsString()
public void SetWsDioPgmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsDioPgm = value;
}

// Standard Getter
public string GetWsFundPgm()
{
    return _WsFundPgm;
}

// Standard Setter
public void SetWsFundPgm(string value)
{
    _WsFundPgm = value;
}

// Get<>AsString()
public string GetWsFundPgmAsString()
{
    return _WsFundPgm.PadRight(8);
}

// Set<>AsString()
public void SetWsFundPgmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsFundPgm = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsFileNames(string value)
{
    _WsFileNames.SetWsFileNamesAsString(value);
}
// Nested Class: WsFileNames
public class WsFileNames
{
    private static int _size = 16;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsDataFile, is_external=, is_static_class=False, static_prefix=
    private WsFileNames.WsDataFile _WsDataFile = new WsFileNames.WsDataFile();
    
    
    
    
    // [DEBUG] Field: WsSchedFile, is_external=, is_static_class=False, static_prefix=
    private WsFileNames.WsSchedFile _WsSchedFile = new WsFileNames.WsSchedFile();
    
    
    
    
public WsFileNames() {}

public WsFileNames(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WsDataFile.SetWsDataFileAsString(data.Substring(offset, WsDataFile.GetSize()));
    offset += 8;
    _WsSchedFile.SetWsSchedFileAsString(data.Substring(offset, WsSchedFile.GetSize()));
    offset += 8;
    
}

// Serialization methods
public string GetWsFileNamesAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsDataFile.GetWsDataFileAsString());
    result.Append(_WsSchedFile.GetWsSchedFileAsString());
    
    return result.ToString();
}

public void SetWsFileNamesAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        _WsDataFile.SetWsDataFileAsString(data.Substring(offset, 8));
    }
    else
    {
        _WsDataFile.SetWsDataFileAsString(data.Substring(offset));
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        _WsSchedFile.SetWsSchedFileAsString(data.Substring(offset, 8));
    }
    else
    {
        _WsSchedFile.SetWsSchedFileAsString(data.Substring(offset));
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public WsDataFile GetWsDataFile()
{
    return _WsDataFile;
}

// Standard Setter
public void SetWsDataFile(WsDataFile value)
{
    _WsDataFile = value;
}

// Get<>AsString()
public string GetWsDataFileAsString()
{
    return _WsDataFile != null ? _WsDataFile.GetWsDataFileAsString() : "";
}

// Set<>AsString()
public void SetWsDataFileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsDataFile == null)
    {
        _WsDataFile = new WsDataFile();
    }
    _WsDataFile.SetWsDataFileAsString(value);
}

// Standard Getter
public WsSchedFile GetWsSchedFile()
{
    return _WsSchedFile;
}

// Standard Setter
public void SetWsSchedFile(WsSchedFile value)
{
    _WsSchedFile = value;
}

// Get<>AsString()
public string GetWsSchedFileAsString()
{
    return _WsSchedFile != null ? _WsSchedFile.GetWsSchedFileAsString() : "";
}

// Set<>AsString()
public void SetWsSchedFileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsSchedFile == null)
    {
        _WsSchedFile = new WsSchedFile();
    }
    _WsSchedFile.SetWsSchedFileAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsDataFile
public class WsDataFile
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
    private string _Filler2 ="CGTD";
    
    
    
    
    // [DEBUG] Field: WsDataType, is_external=, is_static_class=False, static_prefix=
    private string _WsDataType ="X";
    
    
    
    
    // [DEBUG] Field: Filler3, is_external=, is_static_class=False, static_prefix=
    private string _Filler3 =" ";
    
    
    
    
public WsDataFile() {}

public WsDataFile(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller2(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWsDataType(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller3(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWsDataFileAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler2.PadRight(4));
    result.Append(_WsDataType.PadRight(1));
    result.Append(_Filler3.PadRight(3));
    
    return result.ToString();
}

public void SetWsDataFileAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller2(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWsDataType(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller3(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller2()
{
    return _Filler2;
}

// Standard Setter
public void SetFiller2(string value)
{
    _Filler2 = value;
}

// Get<>AsString()
public string GetFiller2AsString()
{
    return _Filler2.PadRight(4);
}

// Set<>AsString()
public void SetFiller2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler2 = value;
}

// Standard Getter
public string GetWsDataType()
{
    return _WsDataType;
}

// Standard Setter
public void SetWsDataType(string value)
{
    _WsDataType = value;
}

// Get<>AsString()
public string GetWsDataTypeAsString()
{
    return _WsDataType.PadRight(1);
}

// Set<>AsString()
public void SetWsDataTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsDataType = value;
}

// Standard Getter
public string GetFiller3()
{
    return _Filler3;
}

// Standard Setter
public void SetFiller3(string value)
{
    _Filler3 = value;
}

// Get<>AsString()
public string GetFiller3AsString()
{
    return _Filler3.PadRight(3);
}

// Set<>AsString()
public void SetFiller3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler3 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WsSchedFile
public class WsSchedFile
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
    private string _Filler4 ="CGTP";
    
    
    
    
    // [DEBUG] Field: WsSchedType, is_external=, is_static_class=False, static_prefix=
    private string _WsSchedType ="X";
    
    
    
    
    // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
    private string _Filler5 =" ";
    
    
    
    
public WsSchedFile() {}

public WsSchedFile(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller4(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWsSchedType(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller5(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWsSchedFileAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler4.PadRight(4));
    result.Append(_WsSchedType.PadRight(1));
    result.Append(_Filler5.PadRight(3));
    
    return result.ToString();
}

public void SetWsSchedFileAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller4(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWsSchedType(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller5(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller4()
{
    return _Filler4;
}

// Standard Setter
public void SetFiller4(string value)
{
    _Filler4 = value;
}

// Get<>AsString()
public string GetFiller4AsString()
{
    return _Filler4.PadRight(4);
}

// Set<>AsString()
public void SetFiller4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler4 = value;
}

// Standard Getter
public string GetWsSchedType()
{
    return _WsSchedType;
}

// Standard Setter
public void SetWsSchedType(string value)
{
    _WsSchedType = value;
}

// Get<>AsString()
public string GetWsSchedTypeAsString()
{
    return _WsSchedType.PadRight(1);
}

// Set<>AsString()
public void SetWsSchedTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsSchedType = value;
}

// Standard Getter
public string GetFiller5()
{
    return _Filler5;
}

// Standard Setter
public void SetFiller5(string value)
{
    _Filler5 = value;
}

// Get<>AsString()
public string GetFiller5AsString()
{
    return _Filler5.PadRight(3);
}

// Set<>AsString()
public void SetFiller5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler5 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsProgDetails(string value)
{
    _WsProgDetails.SetWsProgDetailsAsString(value);
}
// Nested Class: WsProgDetails
public class WsProgDetails
{
    private static int _size = 34;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsWhenCompiled, is_external=, is_static_class=False, static_prefix=
    private WsProgDetails.WsWhenCompiled _WsWhenCompiled = new WsProgDetails.WsWhenCompiled();
    
    
    
    
    // [DEBUG] Field: WDateTime, is_external=, is_static_class=False, static_prefix=
    private WsProgDetails.WDateTime _WDateTime = new WsProgDetails.WDateTime();
    
    
    
    
public WsProgDetails() {}

public WsProgDetails(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WsWhenCompiled.SetWsWhenCompiledAsString(data.Substring(offset, WsWhenCompiled.GetSize()));
    offset += 20;
    _WDateTime.SetWDateTimeAsString(data.Substring(offset, WDateTime.GetSize()));
    offset += 14;
    
}

// Serialization methods
public string GetWsProgDetailsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsWhenCompiled.GetWsWhenCompiledAsString());
    result.Append(_WDateTime.GetWDateTimeAsString());
    
    return result.ToString();
}

public void SetWsProgDetailsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        _WsWhenCompiled.SetWsWhenCompiledAsString(data.Substring(offset, 20));
    }
    else
    {
        _WsWhenCompiled.SetWsWhenCompiledAsString(data.Substring(offset));
    }
    offset += 20;
    if (offset + 14 <= data.Length)
    {
        _WDateTime.SetWDateTimeAsString(data.Substring(offset, 14));
    }
    else
    {
        _WDateTime.SetWDateTimeAsString(data.Substring(offset));
    }
    offset += 14;
}

// Getter and Setter methods

// Standard Getter
public WsWhenCompiled GetWsWhenCompiled()
{
    return _WsWhenCompiled;
}

// Standard Setter
public void SetWsWhenCompiled(WsWhenCompiled value)
{
    _WsWhenCompiled = value;
}

// Get<>AsString()
public string GetWsWhenCompiledAsString()
{
    return _WsWhenCompiled != null ? _WsWhenCompiled.GetWsWhenCompiledAsString() : "";
}

// Set<>AsString()
public void SetWsWhenCompiledAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsWhenCompiled == null)
    {
        _WsWhenCompiled = new WsWhenCompiled();
    }
    _WsWhenCompiled.SetWsWhenCompiledAsString(value);
}

// Standard Getter
public WDateTime GetWDateTime()
{
    return _WDateTime;
}

// Standard Setter
public void SetWDateTime(WDateTime value)
{
    _WDateTime = value;
}

// Get<>AsString()
public string GetWDateTimeAsString()
{
    return _WDateTime != null ? _WDateTime.GetWDateTimeAsString() : "";
}

// Set<>AsString()
public void SetWDateTimeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WDateTime == null)
    {
        _WDateTime = new WDateTime();
    }
    _WDateTime.SetWDateTimeAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsWhenCompiled
public class WsWhenCompiled
{
    private static int _size = 20;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsCompTime, is_external=, is_static_class=False, static_prefix=
    private string _WsCompTime ="";
    
    
    
    
    // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
    private string _Filler6 ="";
    
    
    
    
    // [DEBUG] Field: WsCompDate, is_external=, is_static_class=False, static_prefix=
    private string _WsCompDate ="";
    
    
    
    
public WsWhenCompiled() {}

public WsWhenCompiled(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsCompTime(data.Substring(offset, 5).Trim());
    offset += 5;
    SetFiller6(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWsCompDate(data.Substring(offset, 12).Trim());
    offset += 12;
    
}

// Serialization methods
public string GetWsWhenCompiledAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsCompTime.PadRight(5));
    result.Append(_Filler6.PadRight(3));
    result.Append(_WsCompDate.PadRight(12));
    
    return result.ToString();
}

public void SetWsWhenCompiledAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        SetWsCompTime(extracted);
    }
    offset += 5;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller6(extracted);
    }
    offset += 3;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetWsCompDate(extracted);
    }
    offset += 12;
}

// Getter and Setter methods

// Standard Getter
public string GetWsCompTime()
{
    return _WsCompTime;
}

// Standard Setter
public void SetWsCompTime(string value)
{
    _WsCompTime = value;
}

// Get<>AsString()
public string GetWsCompTimeAsString()
{
    return _WsCompTime.PadRight(5);
}

// Set<>AsString()
public void SetWsCompTimeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCompTime = value;
}

// Standard Getter
public string GetFiller6()
{
    return _Filler6;
}

// Standard Setter
public void SetFiller6(string value)
{
    _Filler6 = value;
}

// Get<>AsString()
public string GetFiller6AsString()
{
    return _Filler6.PadRight(3);
}

// Set<>AsString()
public void SetFiller6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler6 = value;
}

// Standard Getter
public string GetWsCompDate()
{
    return _WsCompDate;
}

// Standard Setter
public void SetWsCompDate(string value)
{
    _WsCompDate = value;
}

// Get<>AsString()
public string GetWsCompDateAsString()
{
    return _WsCompDate.PadRight(12);
}

// Set<>AsString()
public void SetWsCompDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCompDate = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WDateTime
public class WDateTime
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WDateM, is_external=, is_static_class=False, static_prefix=
    private WDateTime.WDateM _WDateM = new WDateTime.WDateM();
    
    
    
    
    // [DEBUG] Field: WTimeM, is_external=, is_static_class=False, static_prefix=
    private WDateTime.WTimeM _WTimeM = new WDateTime.WTimeM();
    
    
    
    
    // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
    private string _Filler7 ="";
    
    
    
    
public WDateTime() {}

public WDateTime(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WDateM.SetWDateMAsString(data.Substring(offset, WDateM.GetSize()));
    offset += 6;
    _WTimeM.SetWTimeMAsString(data.Substring(offset, WTimeM.GetSize()));
    offset += 6;
    SetFiller7(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWDateTimeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WDateM.GetWDateMAsString());
    result.Append(_WTimeM.GetWTimeMAsString());
    result.Append(_Filler7.PadRight(2));
    
    return result.ToString();
}

public void SetWDateTimeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        _WDateM.SetWDateMAsString(data.Substring(offset, 6));
    }
    else
    {
        _WDateM.SetWDateMAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _WTimeM.SetWTimeMAsString(data.Substring(offset, 6));
    }
    else
    {
        _WTimeM.SetWTimeMAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller7(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WDateM GetWDateM()
{
    return _WDateM;
}

// Standard Setter
public void SetWDateM(WDateM value)
{
    _WDateM = value;
}

// Get<>AsString()
public string GetWDateMAsString()
{
    return _WDateM != null ? _WDateM.GetWDateMAsString() : "";
}

// Set<>AsString()
public void SetWDateMAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WDateM == null)
    {
        _WDateM = new WDateM();
    }
    _WDateM.SetWDateMAsString(value);
}

// Standard Getter
public WTimeM GetWTimeM()
{
    return _WTimeM;
}

// Standard Setter
public void SetWTimeM(WTimeM value)
{
    _WTimeM = value;
}

// Get<>AsString()
public string GetWTimeMAsString()
{
    return _WTimeM != null ? _WTimeM.GetWTimeMAsString() : "";
}

// Set<>AsString()
public void SetWTimeMAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WTimeM == null)
    {
        _WTimeM = new WTimeM();
    }
    _WTimeM.SetWTimeMAsString(value);
}

// Standard Getter
public string GetFiller7()
{
    return _Filler7;
}

// Standard Setter
public void SetFiller7(string value)
{
    _Filler7 = value;
}

// Get<>AsString()
public string GetFiller7AsString()
{
    return _Filler7.PadRight(2);
}

// Set<>AsString()
public void SetFiller7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler7 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WDateM
public class WDateM
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WYearNo, is_external=, is_static_class=False, static_prefix=
    private string _WYearNo ="";
    
    
    
    
    // [DEBUG] Field: WMonthNo, is_external=, is_static_class=False, static_prefix=
    private string _WMonthNo ="";
    
    
    
    
    // [DEBUG] Field: WDayNo, is_external=, is_static_class=False, static_prefix=
    private string _WDayNo ="";
    
    
    
    
public WDateM() {}

public WDateM(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWYearNo(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWMonthNo(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWDayNo(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWDateMAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WYearNo.PadRight(2));
    result.Append(_WMonthNo.PadRight(2));
    result.Append(_WDayNo.PadRight(2));
    
    return result.ToString();
}

public void SetWDateMAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWYearNo(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWMonthNo(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWDayNo(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWYearNo()
{
    return _WYearNo;
}

// Standard Setter
public void SetWYearNo(string value)
{
    _WYearNo = value;
}

// Get<>AsString()
public string GetWYearNoAsString()
{
    return _WYearNo.PadRight(2);
}

// Set<>AsString()
public void SetWYearNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WYearNo = value;
}

// Standard Getter
public string GetWMonthNo()
{
    return _WMonthNo;
}

// Standard Setter
public void SetWMonthNo(string value)
{
    _WMonthNo = value;
}

// Get<>AsString()
public string GetWMonthNoAsString()
{
    return _WMonthNo.PadRight(2);
}

// Set<>AsString()
public void SetWMonthNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WMonthNo = value;
}

// Standard Getter
public string GetWDayNo()
{
    return _WDayNo;
}

// Standard Setter
public void SetWDayNo(string value)
{
    _WDayNo = value;
}

// Get<>AsString()
public string GetWDayNoAsString()
{
    return _WDayNo.PadRight(2);
}

// Set<>AsString()
public void SetWDayNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WDayNo = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WTimeM
public class WTimeM
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WTimeHh, is_external=, is_static_class=False, static_prefix=
    private string _WTimeHh ="";
    
    
    
    
    // [DEBUG] Field: WTimeMm, is_external=, is_static_class=False, static_prefix=
    private string _WTimeMm ="";
    
    
    
    
    // [DEBUG] Field: WTimeSs, is_external=, is_static_class=False, static_prefix=
    private string _WTimeSs ="";
    
    
    
    
public WTimeM() {}

public WTimeM(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWTimeHh(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWTimeMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWTimeSs(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWTimeMAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WTimeHh.PadRight(2));
    result.Append(_WTimeMm.PadRight(2));
    result.Append(_WTimeSs.PadRight(2));
    
    return result.ToString();
}

public void SetWTimeMAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWTimeHh(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWTimeMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWTimeSs(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWTimeHh()
{
    return _WTimeHh;
}

// Standard Setter
public void SetWTimeHh(string value)
{
    _WTimeHh = value;
}

// Get<>AsString()
public string GetWTimeHhAsString()
{
    return _WTimeHh.PadRight(2);
}

// Set<>AsString()
public void SetWTimeHhAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WTimeHh = value;
}

// Standard Getter
public string GetWTimeMm()
{
    return _WTimeMm;
}

// Standard Setter
public void SetWTimeMm(string value)
{
    _WTimeMm = value;
}

// Get<>AsString()
public string GetWTimeMmAsString()
{
    return _WTimeMm.PadRight(2);
}

// Set<>AsString()
public void SetWTimeMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WTimeMm = value;
}

// Standard Getter
public string GetWTimeSs()
{
    return _WTimeSs;
}

// Standard Setter
public void SetWTimeSs(string value)
{
    _WTimeSs = value;
}

// Get<>AsString()
public string GetWTimeSsAsString()
{
    return _WTimeSs.PadRight(2);
}

// Set<>AsString()
public void SetWTimeSsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WTimeSs = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}
