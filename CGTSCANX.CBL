      $SET ANS85

      *************************************************************************
      *                                                                       *
      * Copyright:      Wolters Kluwer UK 2004                                *
      *                                                                       *
      * This Software is provided under the terms of a Licence Agreement and  *
      * may only be used  and/or copied in accordance with the terms of such  *
      * Agreement. Neither this Software nor any copy thereof may be provided *
      * or otherwise made available to any other person.  No title to or      *
      * ownership of this software is hereby transferred.                     *
      *                                                                       *
      *************************************************************************

      *
      *      Computation Request - Gain/Loss Extract
      *      =======================================
      *

       IDENTIFICATION DIVISION.
       PROGRAM-ID.                 CGTSCANX.
       AUTHOR.                     E M <PERSON>mour.
       DATE-WRITTEN.               29 July 1995.
      *
      ***********************************************************************
      *   Log    |   Date   | Comments                                      |
      ***********************************************************************
EMG   * CGT/154  | 29/07/95 | New program, copied from CGTSCOT1 and         |
      *          |          | CGTSCAN to create new Gain/Loss extract       |
      *          |          | ASCII file from Realised Schedule File        |
      ***********************************************************************
CDS1  *          | 15/12/95 | Add New Descriptions 'CT TO' & 'GS TO'        |
      ***********************************************************************
CDS4  *          | 21/12/95 | Facility to allow multiple indexation         |
      *          |          | limits (S-INDEXATION-LIMIT)                   |
      ***********************************************************************
CDS5  * CGT/154  | 11/01/96 | See Log  Multiple Changes                     |
      ***********************************************************************
CDS6  * CGT/154  | 11/01/96 |          -"-                                  |
      ***********************************************************************
CDS7  * CGT/154  | 11/01/96 |          -"-                                  |
      ***********************************************************************
CDS8  * CGT/154  | 11/01/96 |          -"-                                  |
      ***********************************************************************
BB1   * CGT/189  | 20/06/96 | Don't reset holding flag when writing         |
BB1   *          |          | disposal record to the export file.           |
      ***********************************************************************
BB2   * CGT/296  | 17/06/99 | Make export file date Y2K compliant.          |
      ***********************************************************************
BB3   * WINDOWS  | 18/01/00 | Add paths to file names.                      |
      ***********************************************************************
BB4   * WINDOWS  | 26/06/00 | Inhibit screen handling code                  |
      ***********************************************************************
BB5   *Log 10218 | 20/02/03 | Add 4 new fields to Realised Gain export.     |
BB5   *          |          | Add a second output file: "Gain/Loss export"  |
BB5   *          |          |  which conslidates results by disposal using  |
BB5   *          |          |  CGTTEMP temporary file to accumulate results.|
BB5   *          |          | Add parm to request either or both formats    |
BB5   *          |          |  of export file.                              |
BB5   *          |          | Calls to CGTLOG moved to allow messages to be |
BB5   *          |          |  written to log file.                         |
      ***********************************************************************
      * DTV      | 14/06/12 | Commented all BIT-32 directive and the        |
      *          |          | section which are inside of BIT-32 = 0 test   | 
      *********************************************************************** 
      * DTV      | 26/06/12 | call to CGTFILES modified                     |
      ***********************************************************************  
      * DTV      | 09/07/12 | cammented unused code                         |
      ***********************************************************************   
      *
       ENVIRONMENT DIVISION.
       CONFIGURATION SECTION.
       SPECIAL-NAMES.
           CRT STATUS  IS W-CRT
           CURSOR      IS W-CURSOR-POSITION
           CONSOLE     CRT.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
BB3        SELECT REALISED-EXPORT-FILE ASSIGN REPORT-FILE-NAME
                  ORGANIZATION LINE SEQUENTIAL
                  STATUS IS W-FILE-RETURN-CODE.

       DATA DIVISION.
       FILE SECTION.
       FD  REALISED-EXPORT-FILE.
       01  GL-RECORD.
         02  GL-RECORD-AREA            PIC X(161).
         02  FILLER REDEFINES GL-RECORD-AREA.
           03  FILLER                          PIC X(01).
           03  GL-FUND                         PIC X(04).
           03  FILLER                          PIC X(03).
           03  GL-SEDOL                        PIC X(07).
           03  FILLER                          PIC X(02).
           03  GL-QTY                          PIC 9(14).
           03  FILLER                          PIC X(01).
           03  GL-PRCDS                        PIC 9(14).
           03  FILLER                          PIC X(01).
           03  GL-BOOK                         PIC 9(14).
           03  FILLER                          PIC X(01).
           03  GL-IND-VAL                      PIC 9(14).
           03  FILLER                          PIC X(01).
           03  GL-IND-USE                      PIC 9(14).
           03  FILLER                          PIC X(01).
           03  GL-GAIN-LOSS                    PIC +9(13).
           03  FILLER                          PIC X(02).
           03  GL-CGT-FLAG                     PIC X(01).
           03  FILLER                          PIC X(03).
           03  GL-IND-FLAG                     PIC X(01).
           03  FILLER                          PIC X(03).
BB2        03  GL-MOVE-DATE                    PIC X(08).
           03  FILLER                          PIC X(03).
BB5        03  GL-TRANCHE-FLAG                 PIC X(01).
BB5        03  FILLER                          PIC X(03).
BB5        03  GL-DISPOSAL-CONTRACT-NO         PIC X(10).
BB5        03  FILLER                          PIC X(03).
BB5        03  GL-GROUP-CODE                   PIC X(02).
BB5        03  FILLER                          PIC X(03).
BB5        03  GL-COUNTRY-CODE                 PIC X(03).
           03  FILLER                          PIC X(01).
           03  GL-WITH-SIGN.                                               *> Bug #77180
               05  FILLER                      PIC X(02).
               05  GL-SIGN                     PIC X(01).
               05  FILLER                      PIC X(01).
BB5            05  FILLER                      PIC X(04).
           03  GL-WITHOUT-SIGN 
               REDEFINES GL-WITH-SIGN          PIC X(08).

BB5    01  GL-RECORD2.
BB5        03  FILLER                          PIC X(01).
BB5        03  GL-FUND2                        PIC X(04).
BB5        03  FILLER                          PIC X(03).
BB5        03  GL-SEDOL2                       PIC X(07).
BB5        03  FILLER                          PIC X(02).
BB5        03  GL-HOLDING                      PIC 9(14).
BB5        03  FILLER                          PIC X(02).
BB5        03  GL-DISPOSAL-DATE                PIC X(08).
BB5        03  FILLER                          PIC X(03).
BB5        03  GL-TODAYS-DATE                  PIC X(08).
BB5        03  FILLER                          PIC X(02).
BB5        03  GL-CAPITAL-GAIN                 PIC 9(14).
BB5        03  FILLER                          PIC X(01).
BB5        03  GL-CAPITAL-LOSS                 PIC 9(14).
BB5        03  FILLER                          PIC X(02).
BB5        03  GL-HOLDING-FLAG                 PIC X(01).
BB5        03  FILLER                          PIC X(03).
BB5        03  GL-TRANCHE-FLAG2                PIC X(01).
           03  FILLER                          PIC X(01).
           03  GL-WITH-SIGN2.                                          *> Bug #77180
BB5            05  FILLER                      PIC X(02).
               05  GL-SIGN-2                   PIC X(01).
               05  FILLER                      PIC X(01).
           03  GL-WITHOUT-SIGN2 
               REDEFINES GL-WITH-SIGN2         PIC X(04).     

       WORKING-STORAGE SECTION.
       
       01  W-NEW-DERIVATIVE-EXPORT-FORMAT            PIC x(5).         *> Bug #77180
           88  CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT   VALUE "True".
               
       01  W-WRITE-OL-SP-FLAG                  PIC X VALUE 'N'.        *> Bug #77180
           88  WRITE-OL-SP-Y                         VALUE 'Y'.
           88  WRITE-OL-SP-N                         VALUE 'N'.
           
       01  W-PROGRAM-NAME                      PIC X(8)      VALUE 'CGTSCANX'.
       01  VERSION-NUMBER                      PIC X(3)      VALUE '1.0'.
       01  W-PANEL-ID                          PIC 9(4) COMP.
       01  W-FILE-RETURN-CODE                  PIC X(2).
       01  W-CONTINUE                          PIC X.
       01  W-FILE-TABLE.
           03  W-FILE-ELEMENT                  OCCURS 30.
               05  W-FILE-CHAR                 PIC X.
       01  W-CRT.
           03  W-CRT-1.
               05  CRT-1                       PIC X.
               05  CRT-2                       PIC 9(2) COMP.
           03  W-CRT-3                         PIC X.
       01  W-CURSOR-POSITION.
           03  W-LINE                          PIC 99.
           03  W-COLUMN                        PIC 99.
       01  SUBSCRIPTS.
           03  W-SUB                           PIC 9(5) COMP-3.
       01  WS-MESSAGE-NO                       PIC 9(15) VALUE ZERO.
       01  WS-PROCESS-FLAG                     PIC X.
           88  QUIT-PROCESS                        VALUE 'Q'.
       01  WS-WRITE-FLAG                       PIC X VALUE 'N'.
           88  WRITE-Y                             VALUE 'Y'.
           88  WRITE-N                             VALUE 'N'.
       01  WS-FIRST-VALUE-FLAG                 PIC X VALUE 'Y'.
           88 WS-FIRST-VALUE                       VALUE 'Y'.
       01  WS-DESC-FLAG                        PIC X VALUE 'N'.
           88  DESC-Y                              VALUE 'Y'.
           88  DESC-N                              VALUE 'N'.
       01  WS-FIRST-GROUP                      PIC X    VALUE 'Y'.
       01  TIME-STAMP.
           03  WS-HH                           PIC 99.
           03  WS-NN                           PIC 99.
           03  FILLER                          PIC 99.
           03  FILLER                          PIC 99.
       01  DATE-STAMP.
           03  WS-YY                           PIC 99.
           03  WS-MM                           PIC 99.
           03  WS-DD                           PIC 99.
       01  WS-TEMP-FIELDS.
CDS1       03  WS-TEMP-DESCRIPTION             PIC X(6).
CDS4       03  WS-ORIG-HOLDING-COST            PIC S9(14)V99.
CDS4       03  WS-ORIG-INDEX-COST              PIC S9(14)V99.
           03 WS-GL-QTY                        PIC S9(14).
           03 WS-GL-QTY-S                      REDEFINES WS-GL-QTY     PIC S9(12)V9(02).
           03 WS-GL-PRCDS                      PIC S9(14).
           03 WS-GL-PRCDS-S                    REDEFINES WS-GL-PRCDS   PIC S9(12)V9(02).
           03 WS-GL-BOOK                       PIC S9(14).
           03 WS-GL-BOOK-S                     REDEFINES WS-GL-BOOK    PIC S9(12)V9(02).
           03 WS-GL-IND-VAL                    PIC S9(14).
           03 WS-GL-IND-VAL-S                  REDEFINES WS-GL-IND-VAL PIC S9(12)V9(02).
           03 WS-GL-IND-USE                    PIC S9(14).
           03 WS-GL-IND-USE-S                  REDEFINES WS-GL-IND-USE PIC S9(12)V9(02).
           03 WS-GL-GAIN-LOSS-S                PIC S9(12)V9(02).
           03 FILLER                           REDEFINES WS-GL-GAIN-LOSS-S.
              05 WS-GL-GAIN-LOSS-9             PIC S9(14).
           03 WS-GL-GAIN-LOSS-X                REDEFINES WS-GL-GAIN-LOSS-S PIC X(14).          *> Bug #77180
           03 WS-TEMP-HOLDING-COSTS            PIC S9(14).
           03 FILLER                           REDEFINES WS-TEMP-HOLDING-COSTS.
              05 WS-TEMP-HOLDING-COSTS-S       PIC S9(12)V9(02).

           03 WS-TEMP-INDEXATION-COSTS         PIC S9(14).
           03 FILLER                           REDEFINES WS-TEMP-INDEXATION-COSTS.
              05 WS-TEMP-INDEXATION-COSTS-S    PIC S9(12)V9(02).

BB5    01  WS-GROUP-CODE-CHAR-1                PIC X(01).

       01  FLAGS.
           03  W-COMPLETE                      PIC X VALUE 'N'.
               88 COMPLETE                         VALUE 'Y'.
           03  W-VALID                         PIC X VALUE 'N'.
               88 VALID                            VALUE 'Y'.
           03  W-FIRST-TIME                    PIC X VALUE 'Y'.
               88  FIRST-TIME                      VALUE 'Y'.
           03  W-1982-FLAG                     PIC X VALUE 'N'.
               88 W-1982-FOUND                     VALUE 'Y'.

       01  WS-WHEN-COMPILED.
           05  WS-COMP-TIME                    PIC X(8).
           05  WS-COMP-DATE                    PIC X(12).

       01 WS-MESSAGES.
           03 WS-MESSAGE-1                     PIC X(69).
           03 WS-MESSAGE-2.
              05 FILLER                        PIC X(8) VALUE 'RUNTIME '.
              05 WS-MESS-DD                    PIC 99.
              05 FILLER                        PIC X VALUE '/'.
              05 WS-MESS-MM                    PIC 99.
              05 FILLER                        PIC X VALUE '/'.
              05 WS-MESS-YY                    PIC 99.
              05 FILLER                        PIC X.
              05 WS-MESS-HH                    PIC 99.
              05 FILLER                        PIC X VALUE ':'.
              05 WS-MESS-NN                    PIC 99.
           03 WS-MESSAGE-11.
              05 FILLER                        PIC X(20)  VALUE
              '... PROCESSING FUND '.
              05 WS-MESS-11-FUND               PIC X(4)  VALUE SPACES.
           03 WS-MESSAGE-3                     PIC X(24)
              VALUE 'Please Enter Report Name'.
           03 WS-MESSAGE-4                     PIC X(45).

       01  WS-ERRORS.
           03  WS-ERROR1                       PIC X(45)
               VALUE 'First Character MUST be a $ ($nnnnD5x)'.
           03  WS-ERROR2                       PIC X(45)
               VALUE 'User Number (nnnn) MUST Be Numeric ($nnnnD5x)'.
           03  WS-ERROR3                       PIC X(45)
               VALUE 'Report Type MUST Be D5 ($nnnnD5x)'.
           03  WS-ERROR4                       PIC X(45)
               VALUE 'Report Number (x) MUST be Numeric ($nnnnD5x)'.

       01  WT-DESCRIPTIONS-D.
           03  FILLER                          PIC X(16)
                                                   VALUE 'DESCRIPTIONS-D=='.
           03  WTDD-OCCURS                     PIC S9(3) VALUE +10.                *> Bug #72038
           03  WTDD-DESCRIPTIONS-TABLE.
               05  FILLER                      PIC X(16) VALUE 'DISPOSAL'.
               05  FILLER                      PIC X(16) VALUE 'STOCK SALE'.
               05  FILLER                      PIC X(16) VALUE 'RIGHTS SALE'.
               05  FILLER                      PIC X(16) VALUE 'CAPITAL DISTBTN'.
               05  FILLER                      PIC X(16) VALUE 'RIGHTS LAPSE'.
               05  FILLER                      PIC X(16) VALUE 'RIGHTS FRACTION'.
               05  FILLER                      PIC X(16) VALUE 'REDEMPTION'.
               05  FILLER                      PIC X(16) VALUE 'LIQUIDATION'.
               05  FILLER                      PIC X(16) VALUE 'EQUALISATION'.
               05  FILLER                      PIC X(16) VALUE 'OPTION LAPSE'.     *> Bug #72038
               
           03  WTDD-R                          REDEFINES WTDD-DESCRIPTIONS-TABLE.
               05  WTDD-DESC                   OCCURS 10 INDEXED BY WTDD-INX       *> Bug #72038
                                               PIC X(16).

       01  W-FILE-SPLIT.
           03  W-F-DOLLAR                      PIC X.
           03  W-F-USER                        PIC X(4).
           03  W-F-D5                          PIC XX.
           03  W-F-REPNO                       PIC X.
           03  W-F-EXTN                        PIC X(4).

BB3    01  REPORT-FILE-NAME                    PIC X(256).
       01  REPORT-FILE.
           05  FILLER                          PIC X(8)    VALUE ' '.
           05  RF-EXT                          PIC X(4)    VALUE '.REP'.

               COPY ELCSCH02.

       01  W-INITIAL-RECORD.
           03  FILLER                          PIC X(01)       VALUE '"'.
           03  FILLER                          PIC X(04)       VALUE SPACES.       *> GL-FUND
           03  FILLER                          PIC X(03)       VALUE '","'.
           03  FILLER                          PIC X(07)       VALUE SPACES.       *> GL-SEDOL
           03  FILLER                          PIC X(02)       VALUE '",'.
           03  FILLER                          PIC 9(12)V9(02) VALUE 0.            *> GL-QTY
           03  FILLER                          PIC X(01)       VALUE ','.
           03  FILLER                          PIC 9(12)V9(02) VALUE 0.            *> GL-PRCDS
           03  FILLER                          PIC X(01)       VALUE ','.
           03  FILLER                          PIC 9(12)V9(02) VALUE 0.            *> GL-BOOK
           03  FILLER                          PIC X(01)       VALUE ','.
           03  FILLER                          PIC 9(12)V9(02) VALUE 0.            *> GL-IND-VAL
           03  FILLER                          PIC X(01)       VALUE ','.
           03  FILLER                          PIC 9(12)V9(02) VALUE 0.            *> GL-IND-USE
           03  FILLER                          PIC X(01)       VALUE ','.
           03  FILLER                          PIC 9(12)V9(02) VALUE 0.            *> GL-GAIN-LOSS
           03  FILLER                          PIC X(02)       VALUE ',"'.
           03  FILLER                          PIC X(01)       VALUE SPACES.       *> GL-CGT-FLAG
           03  FILLER                          PIC X(03)       VALUE '","'.
           03  FILLER                          PIC X(01)       VALUE SPACES.       *> GL-IND-FLAG
           03  FILLER                          PIC X(03)       VALUE '","'.
BB2        03  FILLER                          PIC X(08)       VALUE SPACES.       *> GL-MOVE-DATE
BB5        03  FILLER                          PIC X(03)       VALUE '","'.
BB5        03  FILLER                          PIC X(01)       VALUE SPACES.       *> GL-TRANCHE-FLAG
BB5        03  FILLER                          PIC X(03)       VALUE '","'.
BB5        03  FILLER                          PIC X(10)       VALUE SPACES.       *> GL-DISPOSAL-CONTRACT-NO
BB5        03  FILLER                          PIC X(03)       VALUE '","'.
BB5        03  FILLER                          PIC X(02)       VALUE SPACES.       *> GL-GROUP-CODE
BB5        03  FILLER                          PIC X(03)       VALUE '","'.
BB5        03  FILLER                          PIC X(03)       VALUE SPACES.       *> GL-COUNTRY-CODE
           03  FILLER                          PIC X(01)       VALUE '"'.
           03  FILLER                          PIC X(02)       VALUE ',"'.
           03  FILLER                          PIC X(01)       VALUE SPACES.       *>   GL-SIGN
           03  FILLER                          PIC X(01)       VALUE '"'.

BB5    01  W-INITIAL-RECORD2.
BB5        03  FILLER                          PIC X(01)     VALUE '"'.
BB5        03  FILLER                          PIC X(04)     VALUE SPACES.         *> GL-FUND2
BB5        03  FILLER                          PIC X(03)       VALUE '","'.
BB5        03  FILLER                          PIC X(07)     VALUE SPACES.         *> GL-SEDOL2
BB5        03  FILLER                          PIC X(02)       VALUE '",'.
BB5        03  FILLER                          PIC 9(12)V99  VALUE 0.              *> GL-HOLDING
BB5        03  FILLER                          PIC X(02)     VALUE ',"'.
BB5        03  FILLER                          PIC X(08)     VALUE SPACES.         *>  GL-DISPOSAL-DATE
BB5        03  FILLER                          PIC X(03)     VALUE '","'.
BB5        03  I-TODAYS-DATE                   PIC X(08)     VALUE SPACES.         *>  GL-TODAYS-DATE
BB5        03  FILLER                          PIC X(02)     VALUE '",'.
BB5        03  FILLER                          PIC 9(12)V99  VALUE 0.              *>  GL-CAPITAL-GAIN
BB5        03  FILLER                          PIC X(01)     VALUE ','.
BB5        03  FILLER                          PIC 9(12)V99  VALUE 0.              *> GL-CAPITAL-LOSS
BB5        03  FILLER                          PIC X(02)     VALUE ',"'.
BB5        03  FILLER                          PIC X(01)     VALUE 'N'.            *> GL-HOLDING-FLAG
BB5        03  FILLER                          PIC X(03)     VALUE '","'.
BB5        03  FILLER                          PIC X(01)     VALUE 'N'.            *> GL-TRANCHE-FLAG2
BB5        03  FILLER                          PIC X(01)     VALUE '"'.
           03  FILLER                          PIC X(02)       VALUE ',"'.                                 *> Bug #77180
           03  FILLER                          PIC X(01)       VALUE SPACES.       *>   GL-SIGN-2
           03  FILLER                          PIC X(01)       VALUE '"'.

           COPY CGTFILES.COB.
           COPY CGTHELP.COB.
           COPY CGTPANEL.COB.
           COPY CGTPPB.COB.
           COPY CGTABORT.COB.
           COPY CGTMESS.COB.
           COPY CGTGET.COB.
           COPY CGT0109.COB.
BB3        COPY EQTPATH.COB.
BB3        COPY EQTDEBUG.COB.

       COPY CGTSCANX.DDS.
       01  CGTSCANX-02    REDEFINES CGTSCANX-00   .
           03 FILLER            PIC X(0922).
           03 CGTSCANX-FILE-ID  PIC X(0008).

       01  CGT0109A-LINKAGE.
          03  CGT0109A-MESSAGE-CODE        PIC X(4).
          03  CGT0109A-ACTION              PIC X.
               78  INITIALISATION          VALUE 'I'.
               78  CLOSE-DOWN              VALUE 'C'.
               78  DELETE-MESSAGE          VALUE 'D'.
               78  DISPLAY-HELP            VALUE 'H'.
               78  DISPLAY-MESSAGE         VALUE 'M'.
               78  REWRITE-CGT0109         VALUE 'R'.
               78  QUIT-LOAD-PROCESS       VALUE 'Q'.

           COPY LINKAGE1.COB.
           COPY CGTLOG.COB.
BB2        COPY CGTDATE2.COB.
BB4        COPY EQTGPARM.EXT.
BB5        COPY CGTTEMP.COB.

BB5    01  TEMP-FIELDS.
BB5        05  TEMP-KEY.
BB5            10  TEMP-K-FUND-CODE            PIC  X(04).
BB5            10  TEMP-K-SEDOL-CODE           PIC  X(07).
BB5            10  TEMP-K-CONTRACT-NO          PIC  X(10).
BB5        05  TEMP-DETAILS.
BB5            10  TEMP-QUANTITY               PIC S9(014) VALUE 0.
BB5            10  TEMP-DISPOSAL-DATE.
BB5                15  TEMP-DISPOSAL-DD        PIC  X(02).
BB5                15  TEMP-DISPOSAL-MM        PIC  X(02).
BB5                15  TEMP-DISPOSAL-CCYY      PIC  X(04).
BB5            10  TEMP-GAIN-LOSS              PIC S9(014) VALUE 0.
BB5            10  TEMP-HOLDING-FLAG           PIC  X(01)  VALUE 'N'.
BB5            10  TEMP-TRANCHE-FLAG           PIC  X(01)  VALUE 'N'.
               10  TEMP-SIGN                   PIC  X(01).                 *> Bug #77180

           COPY CGTSCOT1.COB.
           COPY ConfigItems.Cpy.                                           *> Bug #77180
           COPY ELCGMIO.Cob.                                               *> Bug #77180
           
       PROCEDURE DIVISION.
       MAINLINE SECTION.

           MOVE W-PROGRAM-NAME TO L-LOG-PROGRAM
           MOVE OPEN-OUTPUT  TO L-LOG-ACTION
           MOVE SPACES       TO L-LOG-FILE-NAME
           PERFORM X4-CGTLOG

           MOVE     ZERO     TO WS-MESSAGE-NO
           MOVE      'I'     TO L-LOG-MESSAGE-TYPE
           MOVE WRITE-RECORD TO L-LOG-ACTION
           ACCEPT TIME-STAMP FROM TIME
           ACCEPT DATE-STAMP FROM DATE
           MOVE WS-DD TO WS-MESS-DD
           MOVE WS-MM TO WS-MESS-MM
           MOVE WS-YY TO WS-MESS-YY
           MOVE WS-HH TO WS-MESS-HH
           MOVE WS-NN TO WS-MESS-NN
           MOVE WHEN-COMPILED        TO WS-WHEN-COMPILED
           STRING W-PROGRAM-NAME ': Version ' VERSION-NUMBER ' '
                   WS-COMP-DATE ' ' WS-COMP-TIME '; ' WS-MESSAGE-2
                   DELIMITED BY SIZE
                   INTO WS-MESSAGE-1
           MOVE WS-MESSAGE-1 TO L-LOG-MESSAGE
           PERFORM X4-CGTLOG.

           PERFORM AA-ASK-FILE.
           IF QUIT-PROCESS
BB4          MOVE 5 TO RETURN-CODE
             GO TO MAIN-EXIT.
           PERFORM A-INITIALISE
           PERFORM B-READ-ACCUMULATE UNTIL NOT SUCCESSFUL
                                           OR QUIT-PROCESS
           PERFORM C-END.
       MAIN-EXIT.
BB4        EXIT PROGRAM.

       AA-ASK-FILE SECTION.
BB4        PERFORM A40-VALIDATE.
       AA-EXIT.
           EXIT.

      /
       A40-VALIDATE SECTION.
BB4        MOVE G-USER-NO          TO L-USER-NO
                                      CGTTEMP-USER-NO
BB4        MOVE G-REPORT-NO        TO L-REPORT-NO
                                      CGTTEMP-REPORT-NUMBER
           MOVE REALISED-DATA-FILE TO L-FILE-NAME
           MOVE OPEN-INPUT         TO L-FILE-ACTION
dtv        PERFORM X-CALL-CGTFILES       
           IF SUCCESSFUL
              PERFORM A411-FIRST-RECORD
           ELSE
BB4           MOVE 5 TO RETURN-CODE
BB4           EXIT PROGRAM
           END-IF.
      *
BB5        IF CGTSCOT1-GAIN-LOSS-EXPORT
BB5            MOVE CGTTEMP-DELETE-FILE TO CGTTEMP-ACTION
BB5            PERFORM X-CALL-CGTTEMP
      *
BB5            MOVE CGTTEMP-OPEN-IO     TO CGTTEMP-ACTION
BB5            PERFORM X-CALL-CGTTEMP
BB5            IF CGTTEMP-STATUS > '05'
BB5                STRING 'ERROR ' CGTTEMP-STATUS
BB5                       ' OPENING TEMP FILE'
BB5                       DELIMITED BY SIZE
BB5                       INTO L-LOG-MESSAGE
BB5                PERFORM X4-CGTLOG
BB5                MOVE 8 TO RETURN-CODE
BB5                EXIT PROGRAM
BB5            END-IF
BB5        END-IF.
       A40-EXIT.
           EXIT.

      /
       A411-FIRST-RECORD SECTION.
           MOVE SPACES              TO L-FILE-RECORD-AREA
           MOVE START-NOT-LESS-THAN TO L-FILE-ACTION
dtv        PERFORM X-CALL-CGTFILES       
           MOVE L-FILE-RECORD-AREA  TO SCHEDULE-RECORD.
           IF SUCCESSFUL
               NEXT SENTENCE
           ELSE
               MOVE   'N'      TO W-VALID
BB4            MOVE CLOSE-FILE               TO L-FILE-ACTION
dtv            PERFORM X-CALL-CGTFILES       
BB4            MOVE    5                     TO RETURN-CODE
           END-IF.
       A411-EXIT.
           EXIT.
      /

       A-INITIALISE SECTION.
           MOVE READ-NEXT              TO L-FILE-ACTION.
BB4        STRING '$'        
BB4                L-USER-NO
BB4               'D5'       
BB4                L-REPORT-NO
BB4               '.REP'
BB4                DELIMITED BY SIZE
BB4                INTO REPORT-FILE
BB3        MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB3        MOVE REPORT-FILE    TO EQTPATH-FILE-NAME
BB3        PERFORM X-CALL-EQTPATH
BB3        MOVE EQTPATH-PATH-FILE-NAME TO REPORT-FILE-NAME
           
           MOVE NEW-DERIVATIVE-EXPORT-FORMAT   TO ELCGMIO-LINKAGE-2              *> Bug #77180
           PERFORM X-CALL-MF-HANDLER-FOR-CONFIG
           MOVE W-CONFIG-ITEM                  TO W-NEW-DERIVATIVE-EXPORT-FORMAT.
           
BB5        IF CGTSCOT1-REALISED-EXTRACT
               OPEN OUTPUT REALISED-EXPORT-FILE
               MOVE W-INITIAL-RECORD TO GL-RECORD

BB5            MOVE SPACES TO L-LOG-MESSAGE
BB5            STRING 'Creating Realised Gain Extract: ' REPORT-FILE
BB5                   DELIMITED BY SIZE
BB5                   INTO L-LOG-MESSAGE
BB5            PERFORM X4-CGTLOG
BB5        END-IF.

BB5        STRING WS-MESS-YY, WS-MESS-MM, WS-MESS-DD
BB5               DELIMITED BY SIZE
BB5               INTO CGTDATE2-YYMMDD2
BB5        CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE2
BB5        STRING WS-MESS-DD WS-MESS-MM CGTDATE2-C-CC2 WS-MESS-YY
BB5               DELIMITED BY SIZE
BB5               INTO I-TODAYS-DATE.
           
       A-EXIT.
           EXIT.

       B-READ-ACCUMULATE SECTION.
           MOVE READ-NEXT TO L-FILE-ACTION
dtv        PERFORM X-CALL-CGTFILES       

           IF SUCCESSFUL
              MOVE L-FILE-RECORD-AREA TO SCHEDULE-RECORD
              PERFORM B4-TEST-OL-SP                                    *> Bug #77180
              
              EVALUATE S-RECORD-TYPE  
              WHEN '1'
                 PERFORM B1-NEXT-FUND-SEDOL
              WHEN '2'
                 EVALUATE S-LINE-NUMBER
                 WHEN '00000'
                    CONTINUE
                 WHEN OTHER
BB5                 IF  S-DISPOSAL-CONTRACT-NO <> SPACES
BB5                     MOVE S-DISPOSAL-CONTRACT-NO
BB5                                        TO GL-DISPOSAL-CONTRACT-NO
BB5                 END-IF
BB5                 IF S-TRANCHE-FLAG = 'Y' OR 'Y'
BB5                     MOVE 'Y' TO GL-TRANCHE-FLAG
BB5                 END-IF

                    EVALUATE S-BDV-LINE-INDICATOR
                    WHEN ' '
                       IF S-INDEXATION-LIMIT IS EQUAL TO 'A'
                          PERFORM B5-1982-INDEXATION
                       ELSE
                          PERFORM B6-INDEXATION 
                       END-IF
CDS4                WHEN '0'
CDS4                   PERFORM B9-DEEMED-PROCEEDS
                    WHEN '1'
                       PERFORM B7-STOCK-HEADER 
CDS8                WHEN '3'
CDS8                   PERFORM B11-PRIOR-GAIN
CDS5                WHEN '4'
CDS5                   PERFORM B10-ADJUSTMENTS
CDS8                WHEN '5'
CDS8                   PERFORM B12-PRIOR-INDEX
                    WHEN '6'
                       PERFORM B9-DEEMED-PROCEEDS
                    WHEN '7'
                       PERFORM B8-1982-RECORD
                    END-EVALUATE
                 END-EVALUATE
              END-EVALUATE
           END-IF.
       B-EXIT.
           EXIT.


       B1-NEXT-FUND-SEDOL SECTION.
          IF WRITE-Y
             PERFORM B2-WRITE-RECORD.

           IF S-CO-AC-LK NOT = GL-FUND
               MOVE S-CO-AC-LK     TO WS-MESS-11-FUND
               MOVE WS-MESSAGE-11  TO L-LOG-MESSAGE
               MOVE S-CO-AC-LK     TO GL-FUND
           END-IF

           MOVE S-SEDOL-NUMBER  TO GL-SEDOL
           MOVE ZERO            TO WS-GL-IND-VAL
           MOVE ZERO            TO WS-GL-IND-USE
           MOVE ZERO            TO WS-GL-GAIN-LOSS-9
           MOVE ZERO            TO WS-GL-QTY
           MOVE ZERO            TO WS-GL-PRCDS
           MOVE ZERO            TO WS-GL-BOOK
           MOVE ZERO            TO WS-TEMP-HOLDING-COSTS
           MOVE ZERO            TO WS-TEMP-INDEXATION-COSTS
           MOVE ZERO            TO GL-MOVE-DATE
           IF S-HOLDING-FLAG IS EQUAL  X'00'
              MOVE SPACES TO GL-CGT-FLAG
           ELSE
              MOVE S-HOLDING-FLAG  TO GL-CGT-FLAG.
           MOVE SPACES          TO GL-IND-FLAG.
BB5        MOVE 'N'                TO GL-TRANCHE-FLAG
BB5        MOVE SPACES             TO GL-DISPOSAL-CONTRACT-NO
BB5   *   ignore first character of group code
BB5        UNSTRING S-MAIN-GROUP INTO WS-GROUP-CODE-CHAR-1
BB5                                   GL-GROUP-CODE
BB5        MOVE S-COUNTRY-CODE     TO GL-COUNTRY-CODE.
       B1-EXIT.
           EXIT.

       B2-WRITE-RECORD SECTION.
           
           EVALUATE WS-WRITE-FLAG
           WHEN 'Y'
              MOVE 'N' TO WS-WRITE-FLAG
              MOVE 'Y' TO WS-FIRST-VALUE-FLAG
              IF W-1982-FOUND
                 COMPUTE WS-GL-BOOK-S =
                   (WS-ORIG-HOLDING-COST * -1) +
                   WS-TEMP-HOLDING-COSTS-S + WS-GL-BOOK-S
                 COMPUTE WS-GL-IND-VAL-S =
                   (WS-ORIG-INDEX-COST * -1) +
                   WS-TEMP-INDEXATION-COSTS-S + WS-GL-IND-VAL-S
              END-IF
              
              IF( S-Short-Written-Derivative )
                   COMPUTE WS-GL-IND-USE-S = WS-GL-PRCDS-S -            
                        WS-GL-GAIN-LOSS-S - (WS-GL-BOOK-S)
              ELSE
                   COMPUTE WS-GL-IND-USE-S = WS-GL-PRCDS-S -            
                        WS-GL-GAIN-LOSS-S - (WS-GL-BOOK-S * -1)
              END-IF
              
              MOVE WS-GL-IND-VAL     TO GL-IND-VAL         
CDS7          IF WS-GL-IND-USE IS GREATER THAN OR EQUAL TO ZERO
CDS7             MOVE WS-GL-IND-USE     TO GL-IND-USE          
CDS7          ELSE
CDS7             MOVE ZERO TO GL-IND-USE
CDS7          END-IF
              MOVE WS-GL-QTY         TO GL-QTY 
              MOVE WS-GL-BOOK        TO GL-BOOK    
              MOVE WS-GL-GAIN-LOSS-9 TO GL-GAIN-LOSS   
              MOVE WS-GL-PRCDS       TO GL-PRCDS   

BB5           IF CGTSCOT1-REALISED-EXTRACT
                   IF CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT              *> Bug #77180
                       PERFORM B21-SET-SIGN
                   ELSE
                       INITIALIZE GL-WITHOUT-SIGN    
                   END-IF                                              *> Bug #77180
                   WRITE GL-RECORD
BB5           END-IF

BB5           IF CGTSCOT1-GAIN-LOSS-EXPORT
BB5   *
BB5               INITIALIZE TEMP-DETAILS
BB5   *          setup temp file key fields
BB5               MOVE GL-FUND                 TO TEMP-K-FUND-CODE
BB5               MOVE GL-SEDOL                TO TEMP-K-SEDOL-CODE
BB5               MOVE GL-DISPOSAL-CONTRACT-NO TO TEMP-K-CONTRACT-NO
BB5   *          and temp file data fields
BB5               MOVE WS-GL-QTY               TO TEMP-QUANTITY
BB5               MOVE WS-GL-GAIN-LOSS-9       TO TEMP-GAIN-LOSS
BB5               UNSTRING GL-MOVE-DATE      INTO TEMP-DISPOSAL-CCYY
BB5                                               TEMP-DISPOSAL-MM
BB5                                               TEMP-DISPOSAL-DD
BB5               IF GL-CGT-FLAG = 'Y' or 'y'
BB5                   MOVE GL-CGT-FLAG         TO TEMP-HOLDING-FLAG
BB5               END-IF
BB5               IF GL-TRANCHE-FLAG = 'Y' or 'y'
BB5                   MOVE GL-TRANCHE-FLAG     TO TEMP-TRANCHE-FLAG
BB5               END-IF
      *           set the sign for gain-loss-export report 
                  MOVE GL-SIGN                 TO TEMP-SIGN                        *> Bug #77180
BB5               MOVE TEMP-KEY                TO CGTTEMP-KEY
BB5               MOVE TEMP-DETAILS            TO CGTTEMP-DETAILS
BB5               MOVE CGTTEMP-WRITE           TO CGTTEMP-ACTION
BB5               PERFORM X-CALL-CGTTEMP

BB5   *          if write fails with a duplicate key, 
BB5               IF CGTTEMP-STATUS = '22'
BB5   *              read the record back in
BB5                   MOVE CGTTEMP-READ-ON-KEY TO CGTTEMP-ACTION
BB5                   PERFORM X-CALL-CGTTEMP
BB5                   MOVE CGTTEMP-DETAILS     TO TEMP-DETAILS
BB5   *              add current details in to the temp record
BB5                   ADD WS-GL-QTY            TO TEMP-QUANTITY
BB5                   ADD WS-GL-GAIN-LOSS-9    TO TEMP-GAIN-LOSS
BB5   *              set flags on temp record if set on current input record
BB5                   IF GL-CGT-FLAG = 'Y'
BB5                       MOVE GL-CGT-FLAG     TO TEMP-HOLDING-FLAG
BB5                   END-IF
BB5                   IF GL-TRANCHE-FLAG = 'Y'
BB5                       MOVE GL-TRANCHE-FLAG TO TEMP-TRANCHE-FLAG
BB5                   END-IF
BB5   *              rewrite the temp record
BB5                   MOVE TEMP-DETAILS        TO CGTTEMP-DETAILS
BB5                   MOVE CGTTEMP-REWRITE     TO CGTTEMP-ACTION
BB5                   PERFORM X-CALL-CGTTEMP
BB5               END-IF
BB5           END-IF

              MOVE ZERO            TO WS-GL-IND-VAL
              MOVE ZERO            TO WS-GL-IND-USE
              MOVE ZERO            TO WS-GL-GAIN-LOSS-S
              MOVE ZERO            TO WS-GL-QTY
              MOVE ZERO            TO WS-GL-PRCDS
              MOVE ZERO            TO WS-GL-BOOK
              MOVE ZERO            TO WS-TEMP-HOLDING-COSTS
              MOVE ZERO            TO WS-TEMP-INDEXATION-COSTS
              MOVE ZERO            TO GL-MOVE-DATE
              MOVE SPACES          TO GL-IND-FLAG
              MOVE 'N'             TO W-1982-FLAG
           END-EVALUATE.
       B2-EXIT.
           EXIT.
           
       B21-SET-SIGN SECTION.
           IF S-Short-Written-Derivative 
	          MOVE '+'                TO GL-SIGN
	       ELSE
	          MOVE '-'                TO GL-SIGN
	       END-IF.     
       B21-EXIT.
           EXIT.
           
       B3-DESC-TABLE SECTION.
           MOVE 'N' TO WS-DESC-FLAG
DTV        PERFORM VARYING WTDD-INX FROM 1 BY 1
             UNTIL WTDD-INX > WTDD-OCCURS
               OR WTDD-DESC (WTDD-INX) = S-MOVEMENT-DESCRIPTION.  
           IF WTDD-INX IS LESS THAN OR EQUAL TO WTDD-OCCURS
CDS1          MOVE 'Y' TO WS-DESC-FLAG
CDS1       ELSE
CDS1          MOVE S-MOVEMENT-DESCRIPTION TO WS-TEMP-DESCRIPTION
CDS1          IF WS-TEMP-DESCRIPTION = 'CT TO' 
              OR WS-TEMP-DESCRIPTION = 'GS TO'   
              OR (WS-TEMP-DESCRIPTION = 'EX FM' AND S-EP-WC-EXERCISE )
CDS1             MOVE 'Y' TO WS-DESC-FLAG
              END-IF
           END-IF.
       B3-EXIT.
           EXIT.
       
       B4-TEST-OL-SP SECTION.       
           SET WRITE-OL-SP-N           TO TRUE                                       *> Bug #72038
           IF S-CAPITAL-GAIN-OR-LOSS-9 NUMERIC
           AND ((S-MOVEMENT-DESCRIPTION = 'STOCK PURCHASE' AND S-Short-Written-Derivative)
           OR (S-MOVEMENT-DESCRIPTION = 'OPTION LAPSE'))
               MOVE 'Y'                TO WS-WRITE-FLAG
               MOVE 1                  TO S-BDV-LINE-INDICATOR
               SET WRITE-OL-SP-Y       TO TRUE
           END-IF. 
       B4-EXIT.
           EXIT.    
       
       B5-1982-INDEXATION SECTION.
           IF WRITE-Y
              IF S-DISPOSAL-PROCEEDS-X NOT = SPACES
                 MOVE S-DISPOSAL-PROCEEDS-9 TO WS-GL-PRCDS-S
              END-IF
              IF S-CAPITAL-GAIN-OR-LOSS-X NOT = SPACES
                 MOVE S-CAPITAL-GAIN-OR-LOSS-9 TO WS-GL-GAIN-LOSS-S
              END-IF

              MOVE 'Y' TO W-1982-FLAG

CDS8          IF (S-DISPOSAL-PROCEEDS-X NOT = SPACES)
CDS8          AND (S-CAPITAL-GAIN-OR-LOSS-X NOT = SPACES)
CDS6             PERFORM B2-WRITE-RECORD
              END-IF
CDS8       ELSE
CDS8          IF (S-DISPOSAL-PROCEEDS-X = SPACES)
CDS8          AND (S-CAPITAL-GAIN-OR-LOSS-X = SPACES)
CDS8              MOVE 'Y' TO W-1982-FLAG
              END-IF
           END-IF.

       B5-EXIT.
           EXIT.

       B6-INDEXATION SECTION.
           IF WRITE-Y
              IF S-INDEXATION-LIMIT = 'I'
                 MOVE S-INDEXATION-LIMIT TO GL-IND-FLAG
              END-IF
              IF S-DISPOSAL-PROCEEDS-X NOT = SPACES
                 MOVE S-DISPOSAL-PROCEEDS-9 TO WS-GL-PRCDS-S   *> 9641
              END-IF
              IF S-CAPITAL-GAIN-OR-LOSS-X NOT = SPACES
                 MOVE S-CAPITAL-GAIN-OR-LOSS-9 TO WS-GL-GAIN-LOSS-S    *> 6342
              END-IF
CDS4          IF S-INDEXATION-LIMIT IS EQUAL 'B' OR 'C'
CDS4             NEXT SENTENCE
CDS4          ELSE
CDS8             IF (S-DISPOSAL-PROCEEDS-X NOT = SPACES)
                    PERFORM B2-WRITE-RECORD
                 END-IF
CDS4          END-IF
           END-IF.
         
       B6-EXIT.
           EXIT.

       B7-STOCK-HEADER SECTION.
           MOVE 'N' TO WS-DESC-FLAG
           PERFORM B3-DESC-TABLE
           IF DESC-Y
           OR WRITE-OL-SP-Y                                                    *> Bug #77180
BB2           MOVE S-MOVEMENT-DATE-9 TO CGTDATE2-YYMMDD1
BB2           CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE1
BB2           MOVE CGTDATE2-CCYYMMDD1 TO GL-MOVE-DATE

              IF S-HOLDING-UNITS-X NOT = SPACES
                 MOVE S-HOLDING-UNITS-9 TO WS-GL-QTY-S
              END-IF
              IF S-INDEXATION-COST-X IS EQUAL TO SPACES
                 MOVE S-HOLDING-COST-9 TO WS-GL-IND-VAL-S
              ELSE
                 MOVE S-INDEXATION-COST-9 TO WS-GL-IND-VAL-S
              END-IF
              IF S-HOLDING-COST-X NOT = SPACES
                 MOVE S-HOLDING-COST-9 TO WS-GL-BOOK-S
              END-IF
              MOVE 'Y' TO WS-WRITE-FLAG
CDS4          IF WS-FIRST-VALUE
CDS4             MOVE S-HOLDING-COST-9    TO WS-ORIG-HOLDING-COST
CDS4             MOVE S-INDEXATION-COST-9 TO WS-ORIG-INDEX-COST
CDS4             MOVE 'N' TO WS-FIRST-VALUE-FLAG
              END-IF
           END-IF.
                        
           PERFORM B6-INDEXATION.
             
       B7-EXIT.
           EXIT.

       B8-1982-RECORD SECTION.
           IF S-HOLDING-COST-X NOT = SPACES
              MOVE S-HOLDING-COST-9 TO WS-TEMP-HOLDING-COSTS-S
           END-IF
           IF S-INDEXATION-COST-X NOT = SPACES
              MOVE S-INDEXATION-COST-9 TO WS-TEMP-INDEXATION-COSTS-S
           END-IF.

       B8-EXIT.
           EXIT.

       B9-DEEMED-PROCEEDS SECTION.
          IF S-HOLDING-COST-9 IS NUMERIC
             ADD S-HOLDING-COST-9 TO WS-GL-BOOK-S.
          IF S-INDEXATION-COST-9 IS NUMERIC
             ADD S-INDEXATION-COST-9 TO WS-GL-IND-VAL-S
          ELSE
             IF S-BDV-LINE-INDICATOR = '0'
                AND S-HOLDING-COST-9 IS NUMERIC
                ADD S-HOLDING-COST-9 TO WS-GL-IND-VAL-S.
       B9-EXIT.
           EXIT.

CDS5   B10-ADJUSTMENTS SECTION.
       B10.

           IF S-HOLDING-COST-X NOT = SPACES
              ADD S-HOLDING-COST-9 TO WS-GL-BOOK-S
           END-IF
           IF S-INDEXATION-COST-X NOT = SPACES
              ADD S-INDEXATION-COST-9 TO WS-GL-IND-VAL-S
           END-IF.

       B10-EXIT.
           EXIT.

CDS8   B11-PRIOR-GAIN SECTION.
CDS8   B11.
CDS8
CDS8       IF S-HOLDING-COST-X NOT = SPACES
CDS8          ADD S-HOLDING-COST-9 TO WS-GL-BOOK-S
CDS8          ADD S-HOLDING-COST-9 TO WS-GL-IND-VAL-S.
CDS8
CDS8   B11-EXIT.
CDS8       EXIT.

CDS8   B12-PRIOR-INDEX SECTION.
CDS8   B12.
CDS8
CDS8       IF S-HOLDING-COST-X NOT = SPACES
CDS8          SUBTRACT S-HOLDING-COST-9 FROM WS-GL-BOOK-S.
CDS8
CDS8   B12-EXIT.
CDS8       EXIT.

       C-END SECTION.
           STRING '1 CGTSCANX - C-END'
                  DELIMITED BY SIZE 
                  INTO EQTDEBUG-TEXT
           PERFORM X-CALL-EQTDEBUG

           STRING '2 CGTSCANX - C-END'
                  DELIMITED BY SIZE 
                  INTO EQTDEBUG-TEXT
           PERFORM X-CALL-EQTDEBUG

           MOVE CLOSE-FILE         TO L-FILE-ACTION
dtv        PERFORM X-CALL-CGTFILES.       

BB5        IF CGTSCOT1-REALISED-EXTRACT
               CLOSE REALISED-EXPORT-FILE
BB5        END-IF. 

           STRING '3 CGTSCANX - C-END'
                  DELIMITED BY SIZE 
                  INTO EQTDEBUG-TEXT
           PERFORM X-CALL-EQTDEBUG

           STRING '4 CGTSCANX - C-END'
                  DELIMITED BY SIZE 
                  INTO EQTDEBUG-TEXT
           PERFORM X-CALL-EQTDEBUG.

BB5        IF CGTSCOT1-GAIN-LOSS-EXPORT
BB5            STRING '$'        
BB5                    L-USER-NO
BB5                   '5D'       
BB5                    L-REPORT-NO
BB5                   '.REP'
BB5                    DELIMITED BY SIZE
BB5                    INTO REPORT-FILE

BB5            MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
BB5            MOVE REPORT-FILE    TO EQTPATH-FILE-NAME
BB5            PERFORM X-CALL-EQTPATH
BB5            MOVE EQTPATH-PATH-FILE-NAME TO REPORT-FILE-NAME
BB5            MOVE SPACES TO GL-RECORD

BB5            OPEN OUTPUT REALISED-EXPORT-FILE

BB5            MOVE SPACES TO L-LOG-MESSAGE
BB5            STRING 'Creating Gain/Loss Export:      ' REPORT-FILE
BB5                   DELIMITED BY SIZE
BB5                   INTO L-LOG-MESSAGE
BB5            PERFORM X4-CGTLOG

BB5            MOVE SPACES TO CGTTEMP-KEY
BB5            MOVE CGTTEMP-START-NOT-LESS-THAN TO CGTTEMP-ACTION
BB5            PERFORM X-CALL-CGTTEMP
BB5   * Read in the temp records and output to export file format2
BB5            MOVE CGTTEMP-READ TO CGTTEMP-ACTION
BB5            PERFORM X-CALL-CGTTEMP
BB5            PERFORM UNTIL CGTTEMP-STATUS NOT = '00'
BB5                MOVE CGTTEMP-KEY     TO TEMP-KEY
BB5                MOVE CGTTEMP-DETAILS TO TEMP-DETAILS
BB5   * Format & write out the export record     
BB5                PERFORM C1-FORMAT-WRITE-EXPORT2
BB5                PERFORM X-CALL-CGTTEMP
BB5            END-PERFORM
BB5   *
BB5            MOVE CGTTEMP-CLOSE TO CGTTEMP-ACTION
BB5            PERFORM X-CALL-CGTTEMP
BB5            CLOSE REALISED-EXPORT-FILE
BB5        END-IF.

           MOVE     'F'    TO L-LOG-MESSAGE-TYPE
           MOVE CLOSE-FILE TO L-LOG-ACTION
           PERFORM X4-CGTLOG.
       C-EXIT.
           EXIT.

BB5    C1-FORMAT-WRITE-EXPORT2 SECTION.
BB5        MOVE W-INITIAL-RECORD2  TO GL-RECORD2
BB5        MOVE TEMP-K-FUND-CODE     TO GL-FUND2       
BB5        MOVE TEMP-K-SEDOL-CODE    TO GL-SEDOL2      
BB5        MOVE TEMP-QUANTITY      TO GL-HOLDING        
BB5        MOVE TEMP-DISPOSAL-DATE TO GL-DISPOSAL-DATE

BB5        IF TEMP-GAIN-LOSS > ZERO
BB5            MOVE TEMP-GAIN-LOSS TO GL-CAPITAL-GAIN
BB5            MOVE ZERO           TO GL-CAPITAL-LOSS
BB5        ELSE
BB5            MOVE ZERO           TO GL-CAPITAL-GAIN
BB5            MOVE TEMP-GAIN-LOSS TO GL-CAPITAL-LOSS
BB5        END-IF

BB5        IF TEMP-HOLDING-FLAG = 'Y'
BB5            MOVE TEMP-HOLDING-FLAG  TO GL-HOLDING-FLAG
BB5        END-IF

BB5        IF TEMP-TRANCHE-FLAG = 'Y'
BB5            MOVE TEMP-TRANCHE-FLAG  TO GL-TRANCHE-FLAG2
BB5        END-IF
           
           IF CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT              *> Bug #77180
               MOVE TEMP-SIGN TO GL-SIGN-2
           ELSE
               INITIALIZE GL-WITHOUT-SIGN2    
           END-IF                                              *> Bug #77180
BB5        WRITE GL-RECORD2.
BB5    C-EXIT.
BB5        EXIT.

       X4-CGTLOG SECTION.
           ADD 1 TO WS-MESSAGE-NO
           MOVE WS-MESSAGE-NO TO L-LOG-MESSAGE-NO
           CALL 'CGTLOG' USING CGTLOG-LINKAGE-AREA-1
                               CGTLOG-LINKAGE-AREA-2

           IF  L-LOG-MESSAGE-TYPE = 'Q'
               MOVE 'Q' TO WS-PROCESS-FLAG
               MOVE 'P' TO L-LOG-MESSAGE-TYPE
           END-IF.

           MOVE SPACES TO L-LOG-MESSAGE.
      /

BB3    COPY EQTPATH.CPY.
BB4    COPY EQTDEBUG.CPY.
dtv    COPY EQTFILES.CPY.       
       COPY ConfigCode.Cpy.                                    *> Bug #77180
       
BB5    X-CALL-CGTTEMP SECTION.
BB5        CALL 'CGTTEMP' USING CGTTEMP-LINKAGE.

