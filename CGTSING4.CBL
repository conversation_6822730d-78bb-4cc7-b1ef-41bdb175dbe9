
      *************************************************************************
      *                                                                       *
      * Copyright:      Wolters Kluwer UK 2004                                *
      *                                                                       *
      * This Software is provided under the terms of a Licence Agreement and  *
      * may only be used  and/or copied in accordance with the terms of such  *
      * Agreement. Neither this Software nor any copy thereof may be provided *
      * or otherwise made available to any other person.  No title to or      *
      * ownership of this software is hereby transferred.                     *
      *                                                                       *
      *************************************************************************

      *
      *      Computation Request - Taper Schedule Report / Taper Export
      *      ==========================================================
      *
      *      Parameter specifies which report to produce:
      *
      *      CGTSING3-SCHEDULE-TYPE   Report
      *      ======================   ================================
      *             'R'               Realised Taper Schedule Report
      *                               Realised Taper Export
      *             Other             Unrealised Taper Schedule Report
      *                               Unrealised Taper Export
      *

       IDENTIFICATION DIVISION.
       PROGRAM-ID.                 CGTSING4.
       AUTHOR.                     Bill Bridge.
       DATE-WRITTEN.               29 January 2000.

      *
      * New program written Taper Report/Export, copied from CGTSING3.CBL
      *
      * Uses CGTTEMP to sort part disposals into following sequence:
      *   Client/TaperRate/SortCode/SedolCode/AcquisitionDate
      *

      ************************************************************************
      *   Log    |   Date   | Comments                                       *
      ************************************************************************
BB1   * ISTCCRNO | 13/02/03 | Use taper gain for tranches from post 5/4/98   *
BB1   *          |          | inter-spouse transfers for individuals         *
      ************************************************************************
BB2   * 10219    | 26/02/03 | Pick up taper proceeds from schedule data.     *
BB2   *          |          | Only output recs with units/gain/proceeds > 0  *
      ************************************************************************
BB3   * 10228    | 06/06/03 | Taper date not being picked up where a tranche *
BB3   *          |          | header follows a sedol header with the same    *
BB3   *          |          | number (note: acq date, contract no and line   *
BB3   *          |          | number are erroneously set on sedol headers!)  *
      ************************************************************************
BB4   * 10232    | 28/07/03 | Capital distributions: Not picked up on taper  *
BB4   *          |          | tranches and Show units on non taper tranches. *
      ************************************************************************
BB5   * S&F171104| 14/12/04 | Amended to allocate B/F and Income losses.     *
BB5   *          |          | Additional column added to export and report   *
BB5   *          |          | to show type of loss allocated.                *
BB5   *          |          | Restructured so that a Disposal can be split   *
BB5   *          |          | into multiple output lines when multiple loss  *
BB5   *          |          | types are allocated to it.                     *
      ************************************************************************
BB6   * CGT/10243| 26/01/05 | Fix printing of external disposal descriptions *
      ************************************************************************
BB7   * CGT/10245| 03/02/05 | Income losses being allocated where none exist *
BB7   *          |          | for client.                                    *
      ************************************************************************
      * DTV      | 20/06/12 | Commented all BIT-32 directive and the section * 
      *          |          | which are inside of BIT-32 = 0 test            *                         
      ************************************************************************
      * DTV      | 26/06/12 | call to CGTFILES modified                      |
      ************************************************************************    
      *
       ENVIRONMENT DIVISION.
       CONFIGURATION SECTION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT D111-FILE ASSIGN REPORT-FILE-NAME
                  ORGANIZATION LINE SEQUENTIAL
                  STATUS IS WS-FILE-RETURN-CODE.

           SELECT D110-FILE ASSIGN EXPORT-FILE-NAME
                  ORGANIZATION LINE SEQUENTIAL
                  STATUS IS WS-FILE-RETURN-CODE.

BB5        SELECT D157-FILE ASSIGN LOSS-EXPORT-FILE-NAME
BB5               ORGANIZATION LINE SEQUENTIAL
BB5               STATUS IS WS-FILE-RETURN-CODE.

       DATA DIVISION.
       FILE SECTION.
       FD  D111-FILE.
       COPY TAPERREP.CPY.

       FD  D110-FILE.
       COPY TAPEREXP.CPY.

BB5    FD  D157-FILE.
BB5    COPY CliPerLD.CPY.

       WORKING-STORAGE SECTION.
       01  PROGRAM-NAME                PIC  X(008) VALUE 'CGTSING4'.
       01  VERSION-NUMBER              PIC  X(003) VALUE '1.0'.
       01  WS-FILE-RETURN-CODE         PIC  X(002).
       01  WS-MESSAGE-NO               PIC  9(015)   VALUE 0.
       01  WS-CLIENT-GAIN              PIC  9(13)V99 VALUE 0.
       01  WS-CLIENT-LOSS              PIC  9(13)V99 VALUE 0.
       01  WS-LAST-FUND-CODE           PIC  X(04).
       01  WS-LAST-TEMP-SEDOL          PIC  X(07).
       01  WS-LAST-TEMP-FUND           PIC  X(04).
       01  WS-LAST-TEMP-DESCRIPTION    PIC  X(40).
       01  WS-LAST-TEMP-GAINS-LOSS     PIC S9(13)V99 VALUE 0.
       01  WS-LAST-YEAR-LOADED         PIC  9(03)    VALUE 0.
       01  WS-WORK-ACQUISITION-DATE.
           05  WS-WORK-CCYY            PIC  9(04).
           05  FILLER                  PIC  X(04).
       01  WS-NO-OF-YEARS-HELD         PIC  9(03)    VALUE 0.
       01  WS-EXTRA-YEARS              PIC  9(03)    VALUE 0.
       01  WS-TAPER-SUB                PIC  9(03)    VALUE 0.
       01  WS-SEDOL-LINES              PIC  9(03)    VALUE 0.
       01  WS-PROCESSING-SALE          PIC  X(001).
           88  PROCESSING-SALE     VALUE 'Y' 'C'.
           88  NOT-PROCESSING-SALE VALUE 'N'.
BB4        88  PROCESSING-CD-SALE  VALUE 'C'.
BB4        88  PROCESSING-NON-CD-SALE VALUE 'Y'.
       01  WS-PROCESS-FLAG             PIC  X(001).
           88  QUIT-PROCESS VALUE 'Q'.
       01  WS-REPORT-STATUS-FLAG       PIC  X(001).
           88 INITIATE-GAINS    VALUE '1'.
           88 PROCESSING-GAINS  VALUE '2'.
           88 INITIATE-LOSSES   VALUE '3'.
           88 PROCESSING-LOSSES VALUE '4'.
       01  WS-HOLDING-FLAG             PIC  X(001).
           88  HOLDING-FLAG-ON     VALUE 'Y'.
           88  HOLDING-FLAG-OFF    VALUE 'N'.
       01  WS-USERFUND-OPEN-FLAG       PIC  X(001).
           88  USERFUND-FILE-WAS-ALREADY-OPEN VALUE 'Y'.
           88  USERFUND-FILE-WAS-NOT-OPEN     VALUE 'N'.
       01  WS-TRANCHE-FLAG             PIC  X(001).
           88  TRANCHE-FLAG-ON     VALUE 'Y'.
           88  TRANCHE-FLAG-OFF    VALUE 'N'.
       01  WS-PREV-SCHEDULE-REC-DETAILS.
           05  WS-PREV-FUND-CODE       PIC X(04).
           05  WS-PREV-SEDOL-CODE      PIC X(07).
           05  WS-PREV-ACQ-DATE        PIC X(06).
BB1        05  WS-PREV-ACQ-CONTRACT    PIC X(10).
BB1    01  WS-TAPER-DATE               PIC X(06).
       
       01  WS-EXERCISE-PROCESS         PIC  X(001).
           88  WS-EXERCISE-PROCESS-YES     VALUE 'Y'.
           88  WS-EXERCISE-PROCESS-NO      VALUE 'N'.
                  
       01  TIME-STAMP.
           03  WS-HH                   PIC  9(002).
           03  WS-NN                   PIC  9(002).
           03  FILLER                  PIC  9(002).
           03  FILLER                  PIC  9(002).
       01  DATE-STAMP.
           03  WS-YY                   PIC  9(002).
           03  WS-MM                   PIC  9(002).
           03  WS-DD                   PIC  9(002).
BB6    78  DESCRIPTION-OTHER                             VALUE "OTHER".

BB5    01  WS-CLIENT-GAIN-LEFT             PIC  9(13)V99 VALUE 0.

BB5    78  MAX-LOSS-TYPES                                VALUE 4.
BB5    01  WS-LOSS-SUB                     PIC  9(03)    VALUE 0.
BB5    01  WS-DB-LOSS-TABLE.
BB5        05  WS-CURYR-LOSSES.
BB5            10  WS-BF-CURYR-LOSS        PIC  9(13)V99 VALUE 0.
BB5            10  WS-CURYR-LOSS-USED      PIC  9(13)V99 VALUE 0.
BB5            10  WS-CURYR-LOSS-YTD       PIC  9(13)V99 VALUE 0.
BB5        05  WS-CURYR-INCOME-LOSSES.
BB5            10  WS-BF-INCOME-LOSS       PIC  9(13)V99 VALUE 0.
BB5            10  WS-INCOME-LOSS-USED     PIC  9(13)V99 VALUE 0.
BB5            10  WS-INCOME-LOSS-YTD      PIC  9(13)V99 VALUE 0.
BB5        05  WS-9697-LOSSES.
BB5            10  WS-BF-9697-LOSS         PIC  9(13)V99 VALUE 0.
BB5            10  WS-9697-LOSS-USED       PIC  9(13)V99 VALUE 0.
BB5            10  WS-9697-LOSS-YTD        PIC  9(13)V99 VALUE 0.
BB5        05  WS-9596-LOSSES.
BB5            10  WS-BF-9596-LOSS         PIC  9(13)V99 VALUE 0.
BB5            10  WS-9596-LOSS-USED       PIC  9(13)V99 VALUE 0.
BB5            10  WS-9596-LOSS-YTD        PIC  9(13)V99 VALUE 0.
BB5    01  FILLER REDEFINES WS-DB-LOSS-TABLE.
BB5        05  WS-LOSS-ELEMENT OCCURS 4.
BB5            10  WS-BF-LOSS              PIC  9(13)V99.
BB5            10  WS-LOSS-USED            PIC  9(13)V99.
BB5            10  WS-LOSS-YTD             PIC  9(13)V99.
BB5    01  WS-LOSS-TEXT-TABLE.
BB5            10  WS-CURYR-LETTER         PIC  X(01)    VALUE 'C'.
BB5            10  WS-CURYR-DESCRIPTION    PIC  X(35)    
BB5                VALUE 'Current Year Losses Used'.
BB5            10  WS-INCOME-LETTER        PIC  X(01)    VALUE 'I'.
BB5            10  WS-INCOME-DESCRIPTION   PIC  X(35)
BB5                VALUE 'Income Losses Used'.
BB5            10  WS-9697-LETTER          PIC  X(01)    VALUE 'B'.
BB5            10  WS-9697-DESCRIPTION     PIC  X(35)
BB5                VALUE 'B/F Losses 96/97 and Later Used'.
BB5            10  WS-9596-LETTER          PIC  X(01)    VALUE 'B'.
BB5            10  WS-9596-DESCRIPTION     PIC  X(35) 
BB5                VALUE 'B/F Losses 95/96 and Earlier Used'.
BB5    01  FILLER REDEFINES WS-LOSS-TEXT-TABLE.
BB5        05  WS-LOSS-TEXT-ELEMENT OCCURS 4.
BB5            10  WS-LOSS-LETTER          PIC X(01).
BB5            10  WS-LOSS-DESCRIPTION     PIC X(35).

BB5    78  CURYR-LOSSES                                  VALUE 1.
BB5    78  INCOME-LOSSES                                 VALUE 2.
BB5    78  9697-LOSSES                                   VALUE 3.
BB5    78  9596-LOSSES                                   VALUE 4.

BB5    01  WS-ALLOWANCE-DATE-CCYYMMDD      PIC  X(08).
BB5    01  WS-D37-START-DATE-CCYYMMDD      PIC  X(08).
BB5    01  WS-D37-END-DATE-CCYYMMDD        PIC  X(08).
BB5    01  WS-TEMP-RECORD-WRITTEN          PIC  X(001).
BB5        88  TEMP-RECORD-WRITTEN                       VALUE 'Y'.
BB5        88  TEMP-RECORD-NOT-WRITTEN                   VALUE 'N'.
BB5    01  WS-REMAINDER                    PIC X(01).
BB5    01  WS-Work-Start-Date              PIC X(10).
BB5    01  WS-Work-End-Date                PIC X(10).

BB5   * Table containing allowances since year ending 5/4/1996
BB5   * To allow 10 free slots for future years, need to define
BB5   * (ccyy - 1995) + 10 elements. For yearend 5/4/2005 = 20
BB5    78  WS-MAX-ALLOWANCES                         VALUE 20.
BB5    01  WS-ALLOWANCES-OCCURS            PIC 9(03) VALUE 0.
BB5    01  WS-ALLOWANCES-SUB               PIC 9(03) VALUE 0.
BB5    01  WS-ALLOWANCES-TABLE.
BB5        05  WS-ALLOWANCES-ELEMENT OCCURS WS-MAX-ALLOWANCES.
BB5            10  WS-TAX-YEAR-END-DATE.
BB5                15  WS-TAX-YEAR-END-DATE-DD PIC X(02).
BB5                15  FILLER                  PIC X(01).
BB5                15  WS-TAX-YEAR-END-DATE-MM PIC X(02).
BB5                15  FILLER                  PIC X(01).
BB5                15  WS-TAX-YEAR-END-DATE-CC PIC X(02).
BB5                15  WS-TAX-YEAR-END-DATE-YY PIC X(02).
BB5            10  WS-GAINS-ALLOWANCE          PIC 9(09).
BB5            10  WS-TRUST-ALLOWANCE          PIC 9(09).
      
BB5    01  WS-DB-LOSSES.
BB5        05  WS-DB-LOSSES-KEY.
BB5            10  WS-Client-Fund-Code         PIC X(04).
BB5            10  WS-Master-File-Year         PIC X(02).
BB5        05  WS-DB-LOSSES-DETAILS.
BB5            10  WS-Period-Start-Date        PIC X(08).
BB5            10  WS-Period-End-Date          PIC X(08).
BB5            10  WS-Status                   PIC X(01).
BB5            10  WS-Income-Losses            PIC X(15).
BB5            10  WS-BF9697AndLaterLosses     PIC X(15).
BB5            10  WS-BF9596AndEarlierLosses   PIC X(15).
BB5            10  WS-9697AndLaterLosses-YTD   PIC X(15).
BB5            10  WS-9596AndEarlierLosses-YTD PIC X(15).
BB5            10  WS-Income-Losses-YTD        PIC X(15).
BB5            10  WS-LossesAdjustment         PIC X(16).
BB5            10  WS-Override-Year-End        PIC X(01).
BB5    01  WS-DB-LOSSES-N REDEFINES WS-DB-LOSSES.
BB5        05  FILLER                          PIC X(04).
BB5        05  WS-Master-File-Year-N           PIC 9(02).
BB5        05  FILLER                          PIC X(08).
BB5        05  FILLER                          PIC X(08).
BB5        05  FILLER                          PIC X(01).
BB5        05  WS-Income-Losses-N              PIC 9(013)V9(02).
BB5        05  WS-BF9697AndLaterLosses-N       PIC 9(013)V9(02).
BB5        05  WS-BF9596AndEarlierLosses-N     PIC 9(013)V9(02).
BB5        05  WS-9697AndLaterLosses-YTD-N     PIC 9(013)V9(02).
BB5        05  WS-9596AndEarlierLosses-YTD-N   PIC 9(013)V9(02).
BB5        05  WS-Income-Losses-YTD-N          PIC 9(013)V9(02).
BB5        05  WS-LossesAdjustment-Sign        PIC X(01).
BB5        05  WS-LossesAdjustment-N           PIC 9(013)V9(02).
BB5        05  FILLER                          PIC X(01).

BB5    01  WS-DB-DISPOSALS.
BB5        05  W-Disposal-Date                 PIC X(10).
BB5        05  W-Acquisition-Date              PIC X(10).
BB5        05  W-Cost                          PIC X(15).
BB5        05  W-Proceeds                      PIC X(15).
BB5        05  W-Gain-Loss                     PIC X(16).
BB5        05  W-Percent-Business              PIC X(05).

BB5        05  W-NUM-SIGN                      PIC X(01).
BB5        88  NUMBER-POSITIVE                            VALUE 'Y'.
BB5        88  NUMBER-NEGATIVE                            VALUE 'N'.
BB5        05  W-NUM-IN                        PIC X(16).
BB5        05  W-NUM-IN-TAB REDEFINES W-NUM-IN.
BB5            10  W-NUM-IN-BYTE               PIC X(01) OCCURS 16.
BB5        05  W-NUM-OUT                       PIC X(16).
BB5        05  W-NUM-OUT-TAB REDEFINES W-NUM-OUT.
BB5            10  W-NUM-OUT-BYTE              PIC X(01) OCCURS 16.
BB5        05  W-NUM-OUT-9 REDEFINES W-NUM-OUT PIC 9(14)V9(02).
BB5        05  W-SUB-I                         PIC 9(02).
BB5        05  W-SUB-O                         PIC 9(02).
BB5        05  W-START-SUB                     PIC 9(02).
BB5        05  W-END-SUB                       PIC 9(02).
BB5        05  W-DP-SUB                        PIC 9(02).

       01  WS-WHEN-COMPILED.
           05  WS-COMP-TIME            PIC  X(008).
           05  WS-COMP-DATE            PIC  X(012).
      
       01 WS-MESSAGES.
           03 WS-MESSAGE-1             PIC  X(069).
           03 WS-MESSAGE-2.
              05 FILLER                PIC  X(008)    VALUE 'RUNTIME '.
              05 WS-MESS-DD            PIC  9(002).
              05 FILLER                PIC  X         VALUE '/'.
              05 WS-MESS-MM            PIC  9(002).
              05 FILLER                PIC  X         VALUE '/'.
              05 WS-MESS-YY            PIC  9(002).
              05 FILLER                PIC  X(001).
              05 WS-MESS-HH            PIC  9(002).
              05 FILLER                PIC  X(001)    VALUE ':'.
              05 WS-MESS-NN            PIC  9(002).
           03 WS-MESSAGE-11.
              05 FILLER                PIC  X(022)    VALUE
              '... PROCESSING CLIENT '.
              05 WS-MESS-11-FUND       PIC  X(004)    VALUE SPACES.
      
       01  REPORT-FILE-NAME            PIC  X(256).
       01  REPORT-FILE.
           05  FILLER                  PIC  X(001)    VALUE '$'.
           05  REPORT-USER-NO          PIC  X(004)    VALUE '9999'.
           05  REPORT-TYPE             PIC  X(002)    VALUE 'RW'.
           05  REPORT-GEN-NO           PIC  X(001)    VALUE 'G'.
           05  FILLER                  PIC  X(004)    VALUE '.REP'.
      
       01  EXPORT-FILE-NAME            PIC  X(256).
       01  EXPORT-FILE.
           05  FILLER                  PIC  X(001)    VALUE '$'.
           05  EXPORT-USER-NO          PIC  X(004)    VALUE '9999'.
           05  EXPORT-TYPE             PIC  X(002)    VALUE 'RY'.
           05  EXPORT-GEN-NO           PIC  X(001)    VALUE 'G'.
           05  FILLER                  PIC  X(004)    VALUE '.REP'.

BB5    01  LOSS-EXPORT-FILE-NAME       PIC  X(256).
BB5    01  LOSS-EXPORT-FILE.
BB5        05  FILLER                  PIC  X(001)    VALUE '$'.
BB5        05  LOSS-EXPORT-USER-NO     PIC  X(004)    VALUE '9999'.
BB5        05  FILLER                  PIC  X(007)    VALUE 'LSE.DAT'.

       01  WS-TAPER-RATES-TABLE.
           05  WS-TAPER-RATES-ELEMENT OCCURS 33.   *> (11 ELEMENTS FOR 3 YEARS)
               10  WS-TAPER-RATES-DATE-EFFECTIVE   PIC X(08).
               10  WS-BUS-ASSET-RATE               PIC 9(3)V99.
               10  WS-NON-BUS-ASSET-RATE           PIC 9(3)V99.

       01  WS-TAPER-RATES-LOADED                   PIC 99.
       01  WS-TAPER-RATE-OFFSET                    PIC 99.
       78  TAPER-RATES-PER-YEAR                    VALUE 11.
       78  MAX-TAPER-SUB                           VALUE 33.

       01  W-FA-2008-FLAG                  PIC 9.
           88  PRE-FA-2008                         VALUE 0.
           88  POST-FA-2008                        VALUE 1.

       01  TEMP-FIELDS.
           05  TEMP-KEY.
               10  TEMP-FUND-CODE          PIC X(04).
               10  TEMP-INVERSE-TAPER-RATE PIC 9(03)V99 VALUE 0.
               10  TEMP-SORT-CODE          PIC X(18).
               10  TEMP-SEDOL-CODE         PIC X(07).
               10  TEMP-INVERSE-ACQUISITION-DATE PIC X(08).
               10  TEMP-SEQUENCE-NO        PIC 9(08).
               10  TEMP-BUSINESS-USE       PIC X(01).
           05  TEMP-DETAILS.
               10  TEMP-TAPER-RATE         PIC 9(03)V99 VALUE 0.
               10  TEMP-ACQUISITION-DATE   PIC X(08).
               10  FILLER REDEFINES TEMP-ACQUISITION-DATE.
                   15  FILLER              PIC X(02).
                   15  TEMP-YYMMDD         PIC X(06).
               10  TEMP-ISSUER-NAME        PIC X(35).
               10  TEMP-DESCRIPTION        PIC X(40).
               10  TEMP-QUOTED-IND         PIC X(01).
               10  TEMP-QUANTITY           PIC S9(012)V99   VALUE 0.
               10  TEMP-PROCEEDS           PIC 9(012)V99   VALUE 0.
               10  TEMP-GAIN-LOSS          PIC S9(012)V99  VALUE 0.
               10  TEMP-DISPOSAL-DATE      PIC X(08).
               10  TEMP-ESTIMATED-IND      PIC X(01)       VALUE 'N'.
               10  TEMP-1982-IND           PIC X(01).
               10  TEMP-HOLDING-IND        PIC X(01).
               10  TEMP-TRANCHE-IND        PIC X(01).
               10  TEMP-TRANCHE-FLAG       PIC X(01).
BB1            10  TEMP-ACQ-CONTRACT       PIC X(10). 
               10  TEMP-SIGN               PIC X(01).
                   88  TEMP-SIGN-PLUS      VALUE '+'.
                   88  TEMP-SIGN-MINUS     VALUE '-'. 
               10  TEMP-SECURITY-TYPE      PIC  X(001).
                   88  TEMP-SHORT-WRITTEN-DERIVATIVE VALUES 'G' 'K' 'L'.   
                   
       01  BUSIENSS-USE-WORK-FIELDS.
           05  BUS-PERCENT-USE             PIC 9(003)V9(15) VALUE 0.
           05  BUS-TAPER-RATE              PIC 9(003)V99    VALUE 0.
           05  BUS-QUANTITY                PIC 9(012)V99    VALUE 0.
           05  BUS-PROCEEDS                PIC 9(012)V99    VALUE 0.
           05  BUS-GAIN-LOSS               PIC S9(012)V99   VALUE 0.
 
       01  REPORT-FIELDS.
           05  REPORT-LINE-COUNT           PIC 9(9)         VALUE 0.
           05  REPORT-PAGE-COUNT           PIC 9(9)         VALUE 0.
           05  REPORT-TOTAL-GAIN           PIC S9(13)V99    VALUE 0.
           05  REPORT-TOTAL-LOSS           PIC S9(13)V99    VALUE 0.
           05  REPORT-TOTAL-LOSS-USED      PIC S9(13)V99    VALUE 0.
           05  REPORT-TOTAL-TAPERED-GAIN   PIC S9(13)V99    VALUE 0.
           05  REPORT-LINE-COUNT-STORE     PIC 9(9)         VALUE 0. 

       01  REPORT-CONSTANTS.
           05  MAX-LINES                   PIC 9(9)         VALUE 58.
           05  REPORT-HEADER-OFFSET-LINES  PIC 9(9)         VALUE 0.
           05  REPORT-FOOTER-OFFSET-LINES  PIC 9(9)         VALUE 0.

       01  REPORT-HEADER-1.
           05  FILLER                      PIC X(01).
           05  REPORT-REF                  PIC X(70).
           05  REPORT-TITLE                PIC X(98).
           05  FILLER                      PIC X(03).
           05  FILLER                      PIC X(06)
               VALUE 'Page: '.
           05  REPORT-PAGE-NO              PIC ZZ9.   

       01  REPORT-HEADER-2.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(08)
               VALUE 'Client:'.
           05  REPORT-FUND-CODE            PIC X(04).
           05  FILLER                      PIC X(03)
               VALUE ' - '.
           05  REPORT-FUND-NAME            PIC X(40).

       01  REPORT-HEADER-3.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(08)
               VALUE 'Period: '.
           05  REPORT-FUND-FROM-DATE       PIC X(08).
           05  FILLER                      PIC X(03)
               VALUE ' - '.
           05  REPORT-FUND-TO-DATE         PIC X(08).

       01  REPORT-COL-HEADER-1.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(07) 
               VALUE SPACES.   
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(40).
           05  FILLER                      PIC X(15) 
               VALUE SPACES.
           05  FILLER                      PIC X(02).
           05  FILLER                      PIC X(10) 
               VALUE 'Q/U  1982'.
           05  FILLER                      PIC X(08) 
               VALUE '  Acq.'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(08) 
               VALUE '  Sale'.
           05  FILLER                      PIC X(15) 
               VALUE '       Disposal'.
           05  FILLER                      PIC X(16) 
               VALUE '     Chargeable '.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(03).
           05  FILLER                      PIC X(06) 
               VALUE ' Taper'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(15) 
               VALUE '               '.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(15) 
               VALUE '    Gains After'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(15) 
               VALUE '        Tapered'.
           05  FILLER                      PIC X(01).

       01  REPORT-COL-HEADER-2.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(07) 
               VALUE 'Sedol'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(40) 
               VALUE 'Name of Security'.
           05  FILLER                      PIC X(15) 
               VALUE '        Holding'.
           05  FILLER                      PIC X(02).
           05  FILLER                      PIC X(10) 
               VALUE '   Est    '.
           05  FILLER                      PIC X(08) 
               VALUE '  Date'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(08) 
               VALUE '  Date'.
           05  FILLER                      PIC X(15) 
               VALUE '       Proceeds'.
           05  FILLER                      PIC X(16) 
               VALUE '          Gains '.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(03)
               VALUE 'Bus'.
           05  FILLER                      PIC X(06) 
               VALUE '  Rate'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(15) 
               VALUE '         Losses'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(15) 
               VALUE '         Losses'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(15) 
               VALUE '          Gains'.
           05  FILLER                      PIC X(01).

       01  REPORT-COL-HEADER-3.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(07) 
               VALUE SPACES.   
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(40).
           05  FILLER                      PIC X(15) 
               VALUE SPACES.
           05  FILLER                      PIC X(02).
           05  FILLER                      PIC X(10) 
               VALUE 'Q/U  1982'.
           05  FILLER                      PIC X(08) 
               VALUE '  Acq.'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(08) 
               VALUE '  Sale'.
           05  FILLER                      PIC X(15) 
               VALUE '       Disposal'.
           05  FILLER                      PIC X(16) 
               VALUE '         Losses '.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(03).

       01  REPORT-COL-HEADER-4.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(07) 
               VALUE 'Sedol'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(40) 
               VALUE 'Name of Security'.
           05  FILLER                      PIC X(15) 
               VALUE '        Holding'.
           05  FILLER                      PIC X(02).
           05  FILLER                      PIC X(10) 
               VALUE '   Est    '.
           05  FILLER                      PIC X(08) 
               VALUE '  Date'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(08) 
               VALUE '  Date'.
           05  FILLER                      PIC X(15) 
               VALUE '       Proceeds'.
           05  FILLER                      PIC X(16) 
               VALUE '        Arising'.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(03)
               VALUE 'Bus'.

       01  REPORT-DETAIL.
           05  FILLER                      PIC X(01).
           05  REPORT-SEDOL-CODE           PIC X(07).
           05  FILLER                      PIC X(01).
           05  REPORT-TEXT                 PIC X(40).
           05  REPORT-QUANTITY             PIC Z(11)9.99.
           05  REPORT-QUANTITY-SIGN        PIC X(01).
           05  FILLER                      PIC X(02).
           05  REPORT-QUOTED-IND           PIC X(01).
           05  FILLER                      PIC X(02).
           05  REPORT-ESTIMATED-IND        PIC X(01).
           05  FILLER                      PIC X(02).
           05  REPORT-1982-IND             PIC X(01).
           05  FILLER                      PIC X(01).
           05  REPORT-TRANCHE-FLAG         PIC X(01).
           05  REPORT-ACQUISITION-DATE     PIC X(08).
           05  FILLER                      PIC X(01).
           05  REPORT-DISPOSAL-DATE        PIC X(08).
           05  REPORT-PROCEEDS             PIC Z(11)9.99.
           05  REPORT-GAIN-LOSS            PIC Z(11)9.99-.
           05  FILLER                      PIC X(02).
           05  REPORT-BUSINESS-USE         PIC X(01).
           05  FILLER                      PIC X(01).
           05  REPORT-TAPER-RATE           PIC ZZ9.99.
           05  REPORT-PERCENTAGE-SIGN      PIC X(01).
           05  REPORT-LOSS-USED            PIC Z(11)9.99.
BB5        05  REPORT-LOSS-TYPE            PIC X(01).
           05  REPORT-GAIN-LESS-LOSS       PIC Z(11)9.99.
           05  FILLER                      PIC X(01).
           05  REPORT-TAPERED-GAIN         PIC Z(11)9.99.
           05  FILLER                      PIC X(01).

       01  REPORT-GAIN-TOTAL-TEXT.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(91).
           05  FILLER                      PIC X(11)
               VALUE 'Total Gains'.

       01  REPORT-LOSS-TOTAL-TEXT.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(90).
           05  FILLER                      PIC X(12)
               VALUE 'Total Losses'.

       01  REPORT-NET-GAIN-TOTAL-TEXT.
           05  FILLER                      PIC X(01).
           05  FILLER                      PIC X(90).
           05  FILLER                      PIC X(12)
               VALUE '    Net Gain'.

       01  REPORT-FOOTER.
           05  FILLER                      PIC X(001).
           05  REPORT-FOOTER-TEXT          PIC X(180) VALUE 'Notes - Est. indicates Estimate/Valuation used, Acq. Date is later of Acquisition Date and 16/3/98, computation does not include losses brought forward'.    
BB5    01  REPORT-FOOTER-2.
BB5        05  FILLER                      PIC X(001).
BB5        05  REPORT-FOOTER-TEXT-2        PIC X(180) VALUE 'Notes - Est. indicates Estimate/Valuation used, Acq. Date is later of Acquisition Date and 16/3/98'.
           COPY SCHED.CPY.
           COPY STOCK.CPY.
           COPY PARAM.CPY.
           COPY USERS-F.CPY.
           COPY TAPERATE.CPY.
BB5        COPY ExtDisLd.CPY.

           COPY CGTFILES.COB.
           COPY LINKAGE1.COB.
           COPY CGTLOG.COB.
           COPY CGTDATE2.COB.
           COPY CGTTEMP.COB.
           COPY CGTINVRT.COB.      
           COPY CGTABORT.COB.      
           COPY EQTPATH.COB.
           
           COPY elcgmio.Cob.
           COPY ConfigItems.Cpy.
           
           01  W-NEW-DERIVATIVE-EXPORT-FORMAT             PIC X(5).
                88  CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT   VALUE "True".
      
       LINKAGE SECTION.
           COPY CGTSING3.COB.
      
      
       PROCEDURE DIVISION USING CGTSING3-LINKAGE.
       MAINLINE SECTION.
           PERFORM A-INITIALISE
           PERFORM B-READ-ACCUMULATE UNTIL NOT SUCCESSFUL
                                           OR QUIT-PROCESS
           PERFORM D-END
           EXIT PROGRAM.
           STOP RUN.    
      
       A-INITIALISE SECTION.
        
      * Get config item "NewDerivativeExportFormat" 
      * - if the parameter is true,  set the new field (SECURITY-SIGN) to "+" for short/written securities and "-" for anything else
      * - if the parameter is not true, output is  without the new field (SECURITY-SIGN)
           MOVE NEW-DERIVATIVE-EXPORT-FORMAT TO ELCGMIO-LINKAGE-2
           PERFORM X-CALL-MF-HANDLER-FOR-CONFIG
           MOVE W-CONFIG-ITEM                TO W-NEW-DERIVATIVE-EXPORT-FORMAT
           
      * Get report footer text from file if found, otherwise use W-S value
           MOVE USER-DATA-PATH         TO EQTPATH-PATH-ENV-VARIABLE
           MOVE 'TaperFooter.txt'      TO EQTPATH-FILE-NAME
           PERFORM X-CALL-EQTPATH
           MOVE EQTPATH-PATH-FILE-NAME TO L-FILE-RECORD-AREA    
             
           MOVE TRANSACTION-FILE       TO L-FILE-NAME.
           MOVE OPEN-INPUT             TO L-FILE-ACTION.
           PERFORM X-CALL-CGTFILES.
           IF SUCCESSFUL
               MOVE READ-RECORD TO L-FILE-ACTION
               PERFORM X-CALL-CGTFILES
               IF SUCCESSFUL
                   MOVE L-FILE-RECORD-AREA TO REPORT-FOOTER-TEXT
BB5                                           REPORT-FOOTER-TEXT-2
               END-IF
           END-IF.
      *
           MOVE CLOSE-FILE TO L-FILE-ACTION.
           PERFORM X-CALL-CGTFILES.
      *
      * Set up valiables containing user & report generation numbers
           MOVE CGTSING3-USER-NO       TO L-USER-NO
                                          REPORT-USER-NO
                                          EXPORT-USER-NO
BB5                                       LOSS-EXPORT-USER-NO
                                          CGTTEMP-USER-NO
           MOVE CGTSING3-REPORT-NUMBER TO L-REPORT-NO
                                          REPORT-GEN-NO
                                          EXPORT-GEN-NO
                                          CGTTEMP-REPORT-NUMBER
           IF CGTSING3-SCHEDULE-TYPE = 'R'
               MOVE REALISED-DATA-FILE   TO L-FILE-NAME
               MOVE 'RW'                 TO REPORT-TYPE
               MOVE 'RY'                 TO EXPORT-TYPE
               MOVE 'SCHEDULE OF REALISED TAPERED GAINS' 
                                         TO REPORT-TITLE
           ELSE
               MOVE UNREALISED-DATA-FILE TO L-FILE-NAME
               MOVE 'RX'                 TO REPORT-TYPE
               MOVE 'RZ'                 TO EXPORT-TYPE
               MOVE 'SCHEDULE OF UNREALISED TAPERED GAINS' 
                                         TO REPORT-TITLE
           END-IF

           MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
           MOVE REPORT-FILE    TO EQTPATH-FILE-NAME
           PERFORM X-CALL-EQTPATH
           MOVE EQTPATH-PATH-FILE-NAME TO REPORT-FILE-NAME
       
           MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
           MOVE EXPORT-FILE    TO EQTPATH-FILE-NAME
           PERFORM X-CALL-EQTPATH
           MOVE EQTPATH-PATH-FILE-NAME TO EXPORT-FILE-NAME
       
BB5        MOVE USER-DATA-PATH         TO EQTPATH-PATH-ENV-VARIABLE
BB5        MOVE LOSS-EXPORT-FILE       TO EQTPATH-FILE-NAME
BB5        PERFORM X-CALL-EQTPATH
BB5        MOVE EQTPATH-PATH-FILE-NAME TO LOSS-EXPORT-FILE-NAME
           MOVE OPEN-INPUT             TO L-FILE-ACTION
           PERFORM X-CALL-CGTFILES
      *
           *> open and read taper rates into internal table for use later
           MOVE ZERO                   TO WS-TAPER-RATES-LOADED 
           MOVE TAPER-RATE-FILE        TO L-FILE-NAME   
           PERFORM X-CALL-CGTFILES
           IF SUCCESSFUL
               MOVE READ-NEXT TO L-FILE-ACTION
               MOVE 1         TO WS-TAPER-SUB
               PERFORM UNTIL NOT SUCCESSFUL
               OR WS-TAPER-SUB > MAX-TAPER-SUB
dtv           PERFORM UNTIL WS-TAPER-SUB > MAX-TAPER-SUB
                   PERFORM X-CALL-CGTFILES
                   IF SUCCESSFUL
                       MOVE L-FILE-RECORD-AREA      TO D112-RECORD
                       MOVE D112-DATE-EFFECTIVE     TO WS-TAPER-RATES-DATE-EFFECTIVE(WS-TAPER-SUB)
                       MOVE D112-BUS-ASSET-RATE     TO WS-BUS-ASSET-RATE            (WS-TAPER-SUB)
                       MOVE D112-NON-BUS-ASSET-RATE TO WS-NON-BUS-ASSET-RATE        (WS-TAPER-SUB)
                       ADD  1                       TO WS-TAPER-SUB
                   END-IF
               END-PERFORM
               
               MOVE WS-TAPER-SUB                     TO WS-TAPER-RATES-LOADED 
           END-IF               
      *
           MOVE OPEN-INPUT             TO L-FILE-ACTION
           MOVE USER-FUND-FILE         TO L-FILE-NAME   
           PERFORM X-CALL-CGTFILES
           IF FILE-WAS-ALREADY-OPEN
               SET USERFUND-FILE-WAS-ALREADY-OPEN TO TRUE
           ELSE
               SET USERFUND-FILE-WAS-NOT-OPEN     TO TRUE
           END-IF
      *
           OPEN OUTPUT D110-FILE.
           OPEN OUTPUT D111-FILE.
      *
           MOVE PROGRAM-NAME TO L-LOG-PROGRAM
           MOVE OPEN-OUTPUT  TO L-LOG-ACTION
           MOVE SPACES       TO L-LOG-FILE-NAME
           PERFORM X4-CGTLOG
      
           MOVE     ZERO     TO WS-MESSAGE-NO
           MOVE      'I'     TO L-LOG-MESSAGE-TYPE
           MOVE WRITE-RECORD TO L-LOG-ACTION
           ACCEPT TIME-STAMP FROM TIME
           ACCEPT DATE-STAMP FROM DATE
           MOVE WS-DD TO WS-MESS-DD
           MOVE WS-MM TO WS-MESS-MM
           MOVE WS-YY TO WS-MESS-YY
           MOVE WS-HH TO WS-MESS-HH
           MOVE WS-NN TO WS-MESS-NN
           MOVE WHEN-COMPILED        TO WS-WHEN-COMPILED
           STRING  PROGRAM-NAME ': Version ' VERSION-NUMBER ' '
                   WS-COMP-DATE ' ' WS-COMP-TIME '; ' WS-MESSAGE-2
                   DELIMITED BY SIZE
                   INTO WS-MESSAGE-1
           MOVE WS-MESSAGE-1 TO L-LOG-MESSAGE
           PERFORM X4-CGTLOG.
      *
      * Open parameter file if in Windows...
           MOVE OPEN-INPUT     TO L-FILE-ACTION.
           MOVE PARAMETER-FILE TO L-FILE-NAME.
           PERFORM X-CALL-CGTFILES
           IF NOT SUCCESSFUL
               PERFORM X-CALL-CGTABORT
           END-IF
      * Read parameter file to get value of 'Tilney flag'
           MOVE 'CGT'          TO D8-PARAM-KEY.
           MOVE D8-RECORD      TO L-FILE-RECORD-AREA.
           MOVE READ-RECORD    TO L-FILE-ACTION.
           MOVE PARAMETER-FILE TO L-FILE-NAME.
           PERFORM X-CALL-CGTFILES
           IF NOT SUCCESSFUL
               PERFORM X-CALL-CGTABORT
           ELSE
               MOVE L-FILE-RECORD-AREA         TO D8-RECORD
           END-IF
      *
      * ...and close it
           MOVE CLOSE-FILE     TO L-FILE-ACTION.
           PERFORM X-CALL-CGTFILES
           IF NOT SUCCESSFUL
               PERFORM X-CALL-CGTABORT
           END-IF
      *
           IF D8-REPORT-OFFSET-FLAG = 'Y'
               MOVE D8-REPORT-HEADER-OFFSET-LINES 
                                        TO REPORT-HEADER-OFFSET-LINES
               MOVE D8-REPORT-FOOTER-OFFSET-LINES 
                                        TO REPORT-FOOTER-OFFSET-LINES
           ELSE
               MOVE ZERO                TO REPORT-HEADER-OFFSET-LINES
               MOVE ZERO                TO REPORT-FOOTER-OFFSET-LINES
           END-IF
      *
BB5        MOVE ZERO TO WS-ALLOWANCES-OCCURS

BB5        IF D8-USE-LOSSES = 'Y'
BB5            PERFORM A1-OPEN-LOSS-ALLOCATION-FILES
BB5        END-IF

BB5        IF WS-ALLOWANCES-OCCURS = ZERO
BB5            MOVE 1      TO WS-ALLOWANCES-SUB
BB5                           WS-ALLOWANCES-OCCURS
BB5            MOVE SPACES TO WS-TAX-YEAR-END-DATE(WS-ALLOWANCES-SUB)
BB5            MOVE ZERO   TO WS-GAINS-ALLOWANCE  (WS-ALLOWANCES-SUB)
BB5                           WS-TRUST-ALLOWANCE  (WS-ALLOWANCES-SUB)
BB5        END-IF
      *
      * Read user fund file to get value of report ref
           MOVE SPACES         TO L-FILE-RECORD-AREA.
           MOVE READ-RECORD    TO L-FILE-ACTION.
           MOVE USER-FUND-FILE TO L-FILE-NAME.
           PERFORM X-CALL-CGTFILES
           IF NOT SUCCESSFUL
               PERFORM X-CALL-CGTABORT
           ELSE
               MOVE L-FILE-RECORD-AREA   TO D37-RECORD
               MOVE D37-GLOBAL-COMP-NAME TO REPORT-REF
           END-IF
      *
           MOVE CGTTEMP-OPEN-OUTPUT TO CGTTEMP-ACTION
           PERFORM X-CALL-CGTTEMP.
       A-EXIT.
           EXIT.
      
BB5    A1-OPEN-LOSS-ALLOCATION-FILES SECTION.
      *-------------------------------------------------------------
      * Open allowances file and load into a table for later use
      *-------------------------------------------------------------
BB5        MOVE OPEN-INPUT              TO L-FILE-ACTION
BB5        MOVE ALLOWANCES-FROM-DB-FILE TO L-FILE-NAME   
BB5        PERFORM X-CALL-CGTFILES
BB5        IF SUCCESSFUL
BB5            MOVE READ-NEXT           TO L-FILE-ACTION
BB5   *       ignore first record containing column headings
BB5            PERFORM X-CALL-CGTFILES
BB5            MOVE ZERO        TO WS-ALLOWANCES-OCCURS
BB5            PERFORM UNTIL NOT SUCCESSFUL
BB5            OR WS-ALLOWANCES-OCCURS = WS-MAX-ALLOWANCES
BB5                PERFORM X-CALL-CGTFILES
BB5                IF SUCCESSFUL
BB5                    ADD 1 TO WS-ALLOWANCES-OCCURS
BB5                    UNSTRING L-FILE-RECORD-AREA
BB5                    DELIMITED BY ","
BB5                    INTO WS-TAX-YEAR-END-DATE(WS-ALLOWANCES-OCCURS)
BB5                         WS-GAINS-ALLOWANCE  (WS-ALLOWANCES-OCCURS)
BB5                         WS-TRUST-ALLOWANCE  (WS-ALLOWANCES-OCCURS)
BB5                         WS-REMAINDER                               
BB5                END-IF
BB5            END-PERFORM
BB5        END-IF
BB5        MOVE CLOSE-FILE              TO L-FILE-ACTION
BB5        PERFORM X-CALL-CGTFILES
      *-------------------------------------------------------------
      * Open Losses file
      *-------------------------------------------------------------
BB5        MOVE OPEN-INPUT          TO L-FILE-ACTION
BB5        MOVE LOSSES-FROM-DB-FILE TO L-FILE-NAME   
BB5        PERFORM X-CALL-CGTFILES
BB5        MOVE READ-NEXT           TO L-FILE-ACTION
BB5   *   ignore first record containing column headings
BB5        PERFORM X-CALL-CGTFILES
       
BB5        IF SUCCESSFUL
BB5            MOVE SPACES      TO WS-Client-Fund-Code
BB5                                WS-Master-File-Year
BB5        ELSE
BB5            MOVE HIGH-VALUES TO WS-Client-Fund-Code
BB5                                WS-Master-File-Year
BB5        END-IF
      *-------------------------------------------------------------
      * Open External Disposals file
      *-------------------------------------------------------------
BB5        IF CGTSING3-SCHEDULE-TYPE = 'R'
BB5            MOVE OPEN-INPUT              TO L-FILE-ACTION
BB5            MOVE DISPOSALS-FROM-DB-FILE  TO L-FILE-NAME   
BB5            PERFORM X-CALL-CGTFILES
BB5            MOVE READ-NEXT           TO L-FILE-ACTION
BB5   *       ignore first record containing column headings
BB5            PERFORM X-CALL-CGTFILES
BB5            IF SUCCESSFUL
BB5                MOVE SPACES      TO D158-Client-Fund-Code
BB5            ELSE
BB5                MOVE HIGH-VALUES TO D158-Client-Fund-Code
BB5            END-IF
      *-------------------------------------------------------------
      * Open YTD Loss export file and write header record
      *-------------------------------------------------------------
BB5            OPEN OUTPUT D157-FILE
BB5            MOVE D157-Header-Record-2 TO D157-Record
BB5            WRITE D157-RECORD
BB5        END-IF.
BB5    A1-EXIT.
BB5        EXIT.
      
       B-READ-ACCUMULATE SECTION.
           IF CGTSING3-SCHEDULE-TYPE = 'R'
               MOVE REALISED-DATA-FILE   TO L-FILE-NAME
           ELSE
               MOVE UNREALISED-DATA-FILE TO L-FILE-NAME
           END-IF
           MOVE READ-NEXT              TO L-FILE-ACTION.
           PERFORM X-CALL-CGTFILES
           IF SUCCESSFUL
               MOVE L-FILE-RECORD-AREA TO SCHEDULE-DATA-RECORD
               EVALUATE SK-RECORD-TYPE
               WHEN '1'
                     IF PROCESSING-SALE
                         PERFORM B1-WRITE-TEMP-RECORD
                     END-IF
                     PERFORM B2-STORE-HEADER-DETAILS
                     SET NOT-PROCESSING-SALE TO TRUE
                     MOVE 'N' TO WS-TRANCHE-FLAG
               WHEN '2'            
      *             if next tranche, reset tranche flag & store taper date
                     IF (WS-PREV-FUND-CODE  NOT = SK-FUND-CODE
                     OR  WS-PREV-SEDOL-CODE NOT = SK-SORT-SEDOL-CODE
BB1                  OR  WS-PREV-ACQ-CONTRACT NOT = SD-CONTRACT-NO)
                         MOVE 'N'           TO WS-TRANCHE-FLAG
BB1BB3                   MOVE  SPACES       TO WS-TAPER-DATE
                     END-IF
      *             set tranche flag if found anywhere within tranche
                     IF SD-TRANCHE-FLAG = 'Y' 
                         MOVE 'Y' TO WS-TRANCHE-FLAG
                     END-IF
      *             set taper date if found anywhere within tranche
BB3                  IF SD-TAPER-DATE NUMERIC
BB3                      MOVE SD-TAPER-DATE TO WS-TAPER-DATE
BB3                  END-IF
      * new filter added for O&F
                     SET WS-EXERCISE-PROCESS-NO TO TRUE
                     IF (STOCK-PURCHASE AND SK-SHORT-WRITTEN-DERIVATIVE)
                     OR  OPTION-LAPSE
                     OR  (EXERCISE-FM AND SD-OPTION-EX-WC) 
                     OR  (EXERCISE-FM AND SD-OPTION-EX-EP)
                     OR  (SALE AND NOT SK-SHORT-WRITTEN-DERIVATIVE)
                           SET WS-EXERCISE-PROCESS-YES TO TRUE
                     END-IF

                     EVALUATE TRUE
                     WHEN TEMP-FUND-CODE    NOT = SK-FUND-CODE
                     WHEN TEMP-SEDOL-CODE   NOT = SK-SORT-SEDOL-CODE
BB1                  WHEN TEMP-ACQ-CONTRACT NOT = SD-CONTRACT-NO
                     WHEN SD-R-BALANCE                                 *> equivalent for 'BALANCE        '
                     WHEN SD-MOVEMENT-DESCRIPTION = 'BALANCE        '
                     WHEN PROCESSING-SALE 
                      AND (SALE
                           OR CD-SALE
                           OR CHG-TRANSFER-SALE 
                           OR GRP-TRANSFER-SALE
                           OR WS-EXERCISE-PROCESS-YES)
                          IF PROCESSING-SALE
                              PERFORM B1-WRITE-TEMP-RECORD
                          END-IF

                          IF (SALE AND NOT SK-SHORT-WRITTEN-DERIVATIVE)
                          OR CD-SALE
                          OR CHG-TRANSFER-SALE
                          OR GRP-TRANSFER-SALE
                          OR WS-EXERCISE-PROCESS-YES
                              PERFORM B3-NEXT-SALE
BB4                           IF CD-SALE
BB4                               SET PROCESSING-CD-SALE     TO TRUE
BB4                           ELSE
                                  SET PROCESSING-NON-CD-SALE TO TRUE
BB4                           END-IF
                          ELSE
                              SET NOT-PROCESSING-SALE TO TRUE
                          END-IF
                     END-EVALUATE
                     
                     IF PROCESSING-SALE
                     AND NOT EXERCISE-TO
                         MOVE SK-SECURITY-TYPE TO TEMP-SECURITY-TYPE 
BB1                      IF WS-TAPER-DATE NOT NUMERIC
                         AND NOT TO-POOL
                             IF (SD-QUANTITY NUMERIC
                                 AND SD-QUANTITY NEGATIVE
BB4                              AND PROCESSING-NON-CD-SALE)
                                     ADD SD-QUANTITY TO TEMP-QUANTITY 
                             ELSE
                                 IF (SD-QUANTITY NUMERIC
                                 AND SD-QUANTITY POSITIVE
                                 AND SK-SHORT-WRITTEN-DERIVATIVE)
                                    ADD SD-QUANTITY TO TEMP-QUANTITY 
                                END-IF 
                             END-IF
                        
BB1                      END-IF  
        
BB2                      IF WS-TAPER-DATE NOT NUMERIC
                             IF  SD-PROCEEDS-X NOT = SPACES
                                 ADD SD-PROCEEDS TO TEMP-PROCEEDS 
                             END-IF
BB2                      ELSE
BB2                          IF SD-TAPER-PROC-X NOT = SPACES
BB2                             ADD SD-TAPER-PROC TO TEMP-PROCEEDS 
BB2                          END-IF
BB2                      END-IF
          
                         IF  SD-PROCEEDS-X NOT = SPACES
                         AND SD-GAINLOSS-X NOT = SPACES

BB1                          IF WS-TAPER-DATE NUMERIC
BB1                              IF (SD-TAPER-UNITS NUMERIC
BB1                              AND SD-TAPER-UNITS NEGATIVE
BB4                              AND PROCESSING-NON-CD-SALE)
BB1                                  SUBTRACT SD-TAPER-UNITS 
BB1                                                  FROM TEMP-QUANTITY
BB1                              END-IF  

BB1                              EVALUATE SD-TAPER-GAIN
BB1                              WHEN > 0
BB1                                   ADD SD-TAPER-GAIN 
BB1                                                   TO WS-CLIENT-GAIN
BB1                              WHEN < 0
BB1                                   SUBTRACT SD-TAPER-GAIN
BB1                                                 FROM WS-CLIENT-LOSS
BB1                              END-EVALUATE
BB1                              ADD SD-TAPER-GAIN TO TEMP-GAIN-LOSS
BB1                          ELSE
                                 EVALUATE SD-GAINLOSS
                                 WHEN > 0
                                      ADD SD-GAINLOSS TO WS-CLIENT-GAIN
                                 WHEN < 0
                                           SUBTRACT SD-GAINLOSS FROM WS-CLIENT-LOSS    
                                 END-EVALUATE
                                 ADD SD-GAINLOSS TO TEMP-GAIN-LOSS                                
BB1                          END-IF   
                         END-IF

                         IF (SD-INDEXATION-LIMIT 
                                          = 'A' OR 'B' OR 'C' OR 'V')
                         OR (HOLDING-FLAG-ON)
                         OR (TRANCHE-FLAG-ON)
                         OR (SD-TRANCHE-FLAG = 'Y')
                             MOVE 'Y' TO TEMP-ESTIMATED-IND
                         END-IF

                         IF (HOLDING-FLAG-ON)
                             MOVE 'Y' TO TEMP-HOLDING-IND
                         END-IF          

                         IF (SD-TRANCHE-FLAG = 'Y')
                         OR (TRANCHE-FLAG-ON)
                             MOVE 'Y' TO TEMP-TRANCHE-IND
                         END-IF          
                         
                         IF (SD-PERCENTAGE-BUSINESS-X NOT = SPACES)
                             MOVE SD-PERCENTAGE-BUSINESS-9 
                                                   TO BUS-PERCENT-USE
                         END-IF
                         
                         IF BUS-PERCENT-USE NOT NUMERIC
                            INITIALIZE BUS-PERCENT-USE
                         END-IF  
                     END-IF
               END-EVALUATE

               MOVE SK-FUND-CODE          TO WS-PREV-FUND-CODE
               MOVE SK-SORT-SEDOL-CODE    TO WS-PREV-SEDOL-CODE
BB3            IF SK-RECORD-TYPE = '2'
                   MOVE SD-ACQUISITION-DATE-X TO WS-PREV-ACQ-DATE
BB1BB3             MOVE SD-CONTRACT-NO        TO WS-PREV-ACQ-CONTRACT
BB3            ELSE
BB3                MOVE SPACES                TO WS-PREV-ACQ-DATE
BB3                                              WS-PREV-ACQ-CONTRACT
BB3            END-IF
      * Reset status for main loop control
               SET SUCCESSFUL TO TRUE
           END-IF.
       B-EXIT.
           EXIT.
       
       B1-WRITE-TEMP-RECORD SECTION.           
      *
      * set flag if pre-1982 holding
           IF TEMP-ACQUISITION-DATE < '19820401'
               MOVE 'Y' TO TEMP-1982-IND
           ELSE
               MOVE 'N' TO TEMP-1982-IND
           END-IF
      *
      * if TEMP-TRANCHE-IND is true
           IF TEMP-TRANCHE-IND = 'Y'
               MOVE '*' TO TEMP-TRANCHE-FLAG
           END-IF
      *
      * if asset acquired prior to 17/3/98, get extra year of relief
      * (unless business asset sold after 5/4/2000, see below)
           IF TEMP-ACQUISITION-DATE < '19980317'
               MOVE '19980316' TO TEMP-ACQUISITION-DATE
               MOVE  1         TO WS-EXTRA-YEARS
           ELSE
               MOVE  0         TO WS-EXTRA-YEARS
           END-IF
           MOVE TEMP-ACQUISITION-DATE TO WS-WORK-ACQUISITION-DATE
      *
      * if asset acquired prior to 6/4/98, treated as acquired on 6/4/98
      * for purposes of counting number of years held
           IF WS-WORK-ACQUISITION-DATE < '19980406'
               MOVE '19980406' TO WS-WORK-ACQUISITION-DATE
           END-IF
      *
           PERFORM VARYING WS-NO-OF-YEARS-HELD FROM 0 BY 1 
           UNTIL WS-NO-OF-YEARS-HELD > 11
           OR    WS-WORK-ACQUISITION-DATE > TEMP-DISPOSAL-DATE
               ADD 1 TO WS-WORK-CCYY
           END-PERFORM
           SUBTRACT 1              FROM WS-NO-OF-YEARS-HELD
           ADD      WS-EXTRA-YEARS TO   WS-NO-OF-YEARS-HELD
      *
      * taper rate table has 11 elements 
      *  element(01) = held 0 years
      *  element(11) = held 10 years or more
           IF WS-NO-OF-YEARS-HELD > 10
               MOVE 10 TO WS-NO-OF-YEARS-HELD
           END-IF

           IF POST-FA-2008 OR WS-TAPER-RATES-LOADED < 1
                MOVE 1 TO WS-TAPER-SUB
           END-IF
      *
      * no tapering for losses, 
           IF TEMP-GAIN-LOSS < 0
               MOVE ZERO TO TEMP-TAPER-RATE
               MOVE 'N'  TO TEMP-BUSINESS-USE
           ELSE
               ADD 1                   TO WS-NO-OF-YEARS-HELD GIVING WS-TAPER-SUB
               ADD WS-TAPER-RATE-OFFSET TO WS-TAPER-SUB
               EVALUATE BUS-PERCENT-USE
               WHEN ZERO
                    MOVE 'N' TO TEMP-BUSINESS-USE
                    MOVE WS-NON-BUS-ASSET-RATE(WS-TAPER-SUB) 
                             TO TEMP-TAPER-RATE
               WHEN 100
                    MOVE 'Y' TO TEMP-BUSINESS-USE
                    IF TEMP-DISPOSAL-DATE 
                                     >= CLIENT-PERIOD-START-DATE-00-01
                        SUBTRACT WS-EXTRA-YEARS FROM WS-TAPER-SUB
                    END-IF
                    MOVE WS-BUS-ASSET-RATE(WS-TAPER-SUB)
                         TO TEMP-TAPER-RATE
               WHEN OTHER
      * mixed use asset - write non-business record with business use % deducted...
                    MOVE 'N' TO TEMP-BUSINESS-USE
                    MOVE WS-NON-BUS-ASSET-RATE(WS-TAPER-SUB)
                             TO TEMP-TAPER-RATE
                    COMPUTE BUS-QUANTITY ROUNDED
                          = TEMP-QUANTITY * BUS-PERCENT-USE / 100
                    SUBTRACT BUS-QUANTITY FROM TEMP-QUANTITY
                    COMPUTE BUS-PROCEEDS  ROUNDED
                          = TEMP-PROCEEDS * BUS-PERCENT-USE / 100
                    SUBTRACT BUS-PROCEEDS FROM TEMP-PROCEEDS
                    COMPUTE BUS-GAIN-LOSS ROUNDED
                          = TEMP-GAIN-LOSS * BUS-PERCENT-USE / 100
                    SUBTRACT BUS-GAIN-LOSS FROM TEMP-GAIN-LOSS
               END-EVALUATE

               IF POST-FA-2008 OR WS-TAPER-RATES-LOADED < 1
                    MOVE 100 TO TEMP-TAPER-RATE
               END-IF
           END-IF
      *           
           MOVE TEMP-TAPER-RATE       TO CGTINVRT-LINKAGE 
           PERFORM X-CALL-CGTINVRT
           MOVE CGTINVRT-LINKAGE      TO TEMP-INVERSE-TAPER-RATE
      *
           MOVE TEMP-ACQUISITION-DATE TO CGTINVRT-LINKAGE 
           PERFORM X-CALL-CGTINVRT
           MOVE CGTINVRT-LINKAGE      TO TEMP-INVERSE-ACQUISITION-DATE
      *
           MOVE SK-SEQUENCE-NO        TO TEMP-SEQUENCE-NO
           MOVE TEMP-KEY              TO CGTTEMP-KEY
            
           IF CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT
               IF TEMP-SHORT-WRITTEN-DERIVATIVE 
                       SET TEMP-SIGN TO '+'
               ELSE
                       SET TEMP-SIGN TO '-'
               END-IF   
           END-IF.  
           MOVE TEMP-DETAILS          TO CGTTEMP-DETAILS           
           MOVE CGTTEMP-WRITE         TO CGTTEMP-ACTION
BB2        IF TEMP-QUANTITY  <> 0
BB2        OR TEMP-PROCEEDS  <> 0
BB2        OR TEMP-GAIN-LOSS <> 0
               PERFORM X-CALL-CGTTEMP
BB2        END-IF
      *
           IF BUS-PERCENT-USE > ZERO 
           AND BUS-PERCENT-USE < 100
           AND TEMP-GAIN-LOSS NOT < 0
      * ...and write second record for business use
               MOVE 'Y'           TO TEMP-BUSINESS-USE
               IF TEMP-DISPOSAL-DATE 
                                >= CLIENT-PERIOD-START-DATE-00-01
                   SUBTRACT WS-EXTRA-YEARS FROM WS-TAPER-SUB
               END-IF

               MOVE WS-BUS-ASSET-RATE(WS-TAPER-SUB)
                        TO TEMP-TAPER-RATE
                           CGTINVRT-LINKAGE 

               IF POST-FA-2008 OR WS-TAPER-RATES-LOADED < 1
                    MOVE 100             TO TEMP-TAPER-RATE
                    MOVE TEMP-TAPER-RATE TO CGTINVRT-LINKAGE 
               END-IF

               PERFORM X-CALL-CGTINVRT
               MOVE CGTINVRT-LINKAGE
                                  TO TEMP-INVERSE-TAPER-RATE
               MOVE BUS-QUANTITY  TO TEMP-QUANTITY
               MOVE BUS-PROCEEDS  TO TEMP-PROCEEDS
               MOVE BUS-GAIN-LOSS TO TEMP-GAIN-LOSS
               MOVE TEMP-KEY      TO CGTTEMP-KEY
               MOVE TEMP-DETAILS  TO CGTTEMP-DETAILS
BB2            IF TEMP-QUANTITY  <> 0
BB2            OR TEMP-PROCEEDS  <> 0
BB2            OR TEMP-GAIN-LOSS <> 0
                   PERFORM X-CALL-CGTTEMP
BB2            END-IF
           END-IF

           INITIALIZE TEMP-FIELDS.
       B1-EXIT.
           EXIT.     

       B2-STORE-HEADER-DETAILS SECTION.
           IF SS-HOLDING-FLAG = "Y"
               SET HOLDING-FLAG-ON  TO TRUE
           ELSE
               SET HOLDING-FLAG-OFF TO TRUE
           END-IF

           MOVE SS-SEDOL-NUMBER        TO D3-SEDOL-CODE 
           MOVE SS-ISSUERS-NAME        TO D3-ISSUER
           MOVE SS-STOCK-DESCRIPTION   TO D3-DESCRIPTION
           MOVE SK-SECURITY-SORT-CODE  TO D3-SORT-CODE
           MOVE SS-QUOTED-INDICATOR    TO D3-QUOTED-UNQUOTED
           .
       B2-EXIT.
           EXIT.

       B3-NEXT-SALE SECTION.
           IF WS-LAST-FUND-CODE > SPACES
           AND WS-LAST-FUND-CODE < SK-FUND-CODE
               PERFORM C-PROCESS-CLIENT
           END-IF
      *
           IF SK-FUND-CODE NOT = WS-LAST-FUND-CODE
               MOVE SK-FUND-CODE    TO WS-MESS-11-FUND
               MOVE WS-MESSAGE-11   TO L-LOG-MESSAGE
               MOVE SK-FUND-CODE        TO D37-FUND-CODE
               MOVE D37-RECORD-FORMAT-2 TO L-FILE-RECORD-AREA
               MOVE READ-RECORD         TO L-FILE-ACTION
               MOVE USER-FUND-FILE      TO L-FILE-NAME
               PERFORM X-CALL-CGTFILES
               IF SUCCESSFUL
                   MOVE L-FILE-RECORD-AREA TO D37-RECORD-FORMAT-2
               ELSE
      *
      * deal with this error instead of aborting - computation has 
      *  processed a GT to a fund which is not on the user-fund file!
      *
                   MOVE "*** Missing from User's file ***"
                                        TO D37-NAME
                   MOVE FIRST-PERIOD-DATE-YYMMDD
                                        TO D37-PERIOD-START-DATE
                   MOVE LAST-PERIOD-DATE-YYMMDD
                                        TO D37-PERIOD-END-DATE
               END-IF
               PERFORM B31-GET-TAPER-RATES-FOR-CLIENT
      *------------------------------------------------------------
BB5            IF D8-USE-LOSSES = 'Y'
BB5                PERFORM B32-GET-ALLOWANCES-FOR-CLIENT
BB5                PERFORM B33-GET-LOSSES-FOR-CLIENT
BB5                PERFORM B34-GET-DISPOSALS-FOR-CLIENT
BB5            ELSE
BB5                INITIALIZE WS-DB-LOSSES-DETAILS
BB7                INITIALIZE WS-DB-LOSS-TABLE
BB5            END-IF
      *------------------------------------------------------------
           END-IF
      *
           MOVE SK-FUND-CODE          TO WS-LAST-FUND-CODE
                                         TEMP-FUND-CODE
      *
           MOVE SK-SORT-SEDOL-CODE    TO TEMP-SEDOL-CODE
      *
BB1        IF SD-TAPER-DATE NUMERIC
BB1            MOVE SD-TAPER-DATE         TO CGTDATE2-YYMMDD1
BB1        ELSE
               MOVE SD-ACQUISITION-DATE-X TO CGTDATE2-YYMMDD1
BB1        END-IF
           CALL 'CGTDATE2' USING CGTDATE2-LINKAGE-DATE1
           MOVE CGTDATE2-CCYYMMDD1    TO TEMP-ACQUISITION-DATE
      *
           MOVE SD-CONTRACT-NO        TO TEMP-ACQ-CONTRACT
      *
           MOVE SD-MOVEMENT-DATE-X    TO CGTDATE2-YYMMDD1
           CALL 'CGTDATE2' USING CGTDATE2-LINKAGE-DATE1
           MOVE CGTDATE2-CCYYMMDD1    TO TEMP-DISPOSAL-DATE.
      *
           MOVE D3-SEDOL-CODE      TO TEMP-SEDOL-CODE
           MOVE D3-ISSUER          TO TEMP-ISSUER-NAME
           MOVE D3-DESCRIPTION     TO TEMP-DESCRIPTION
           MOVE D3-SORT-CODE       TO TEMP-SORT-CODE
           IF D3-QUOTED-UNQUOTED = 0 
               MOVE 'Q' TO TEMP-QUOTED-IND
           ELSE
               MOVE 'U' TO TEMP-QUOTED-IND
           END-IF
      *
           MOVE 'N'  TO TEMP-ESTIMATED-IND.
           MOVE ZERO TO BUS-PERCENT-USE.
       B3-EXIT.
           EXIT.
      
       B31-GET-TAPER-RATES-FOR-CLIENT SECTION.
           MOVE D37-PERIOD-END-DATE TO CGTDATE2-YYMMDD1
           CALL 'CGTDATE2' USING CGTDATE2-LINKAGE-DATE1
           MOVE ZERO                TO WS-TAPER-RATE-OFFSET
           IF  CGTDATE2-CCYYMMDD1 < CREATE-2008-POOL-DATE           
      *
      * Get the taper rates relevant to client period  
      *
               MOVE D37-PERIOD-START-DATE    TO CGTDATE2-YYMMDD1
               CALL 'CGTDATE2' USING CGTDATE2-LINKAGE-DATE1
               MOVE CGTDATE2-CCYYMMDD1       TO CGTINVRT-LINKAGE
BB5                                             WS-D37-START-DATE-CCYYMMDD
               PERFORM VARYING WS-TAPER-SUB FROM 1 BY TAPER-RATES-PER-YEAR
               UNTIL WS-TAPER-SUB > MAX-TAPER-SUB
                   IF CGTDATE2-CCYYMMDD1 >= WS-TAPER-RATES-DATE-EFFECTIVE(WS-TAPER-SUB)
                       MOVE WS-TAPER-SUB TO WS-TAPER-RATE-OFFSET 
                   END-IF
               END-PERFORM
               IF WS-TAPER-RATE-OFFSET > ZERO
                   SUBTRACT 1 FROM WS-TAPER-RATE-OFFSET
               END-IF
               SET PRE-FA-2008  TO TRUE
           ELSE
               SET POST-FA-2008 TO TRUE
           END-IF

      * Frig return code for main loop
           SET SUCCESSFUL TO TRUE.
       B31-EXIT.
           EXIT.
      
BB5    B32-GET-ALLOWANCES-FOR-CLIENT SECTION.
      *------------------------------------------------------------
      * Loop through allowances until allowance period end date 
      * >= client period end date or reach last record.
      *------------------------------------------------------------
BB5        MOVE SPACES              TO WS-ALLOWANCE-DATE-CCYYMMDD
BB5        MOVE D37-PERIOD-END-DATE TO CGTDATE2-YYMMDD2
BB5        CALL 'CGTDATE2' USING CGTDATE2-LINKAGE-DATE2
BB5        MOVE CGTDATE2-CCYYMMDD2  TO WS-D37-END-DATE-CCYYMMDD

BB5        PERFORM VARYING WS-ALLOWANCES-SUB 
BB5        FROM 1 BY 1 
BB5        UNTIL WS-ALLOWANCES-SUB = WS-ALLOWANCES-OCCURS
BB5        OR WS-ALLOWANCE-DATE-CCYYMMDD >= WS-D37-END-DATE-CCYYMMDD
BB5           STRING WS-TAX-YEAR-END-DATE-CC(WS-ALLOWANCES-SUB)
BB5                  WS-TAX-YEAR-END-DATE-YY(WS-ALLOWANCES-SUB)
BB5                  WS-TAX-YEAR-END-DATE-MM(WS-ALLOWANCES-SUB)
BB5                  WS-TAX-YEAR-END-DATE-DD(WS-ALLOWANCES-SUB)
BB5           DELIMITED BY SIZE
BB5           INTO WS-ALLOWANCE-DATE-CCYYMMDD
BB5           IF WS-ALLOWANCE-DATE-CCYYMMDD >= WS-D37-END-DATE-CCYYMMDD
BB5               GO TO B32-EXIT
BB5           END-IF
BB5        END-PERFORM.
BB5    B32-EXIT.
BB5        EXIT.

BB5    B33-GET-LOSSES-FOR-CLIENT SECTION.
      * Set ytd loss fields to zero
BB5        INITIALIZE WS-DB-LOSSES-DETAILS
BB7        INITIALIZE WS-DB-LOSS-TABLE
      * 
BB5        IF D37-USE-LOSSES-FLAG = 'Y'
      *----------------------------------------------------------------
      *       If client is using losses, try and find matching record 
      *       in csv file, and store details if found
      *----------------------------------------------------------------
BB5            MOVE LOSSES-FROM-DB-FILE TO L-FILE-NAME   
BB5            MOVE READ-NEXT           TO L-FILE-ACTION
BB5            PERFORM UNTIL WS-Client-Fund-Code >= D37-FUND-CODE
BB5                PERFORM X-CALL-CGTFILES
BB5                IF SUCCESSFUL
BB5                    PERFORM X5-GET-LOSS-DETAILS
BB5                ELSE
BB5                    MOVE HIGH-VALUES TO WS-Client-Fund-Code
BB5                                        WS-Master-File-Year
BB5                END-IF
BB5            END-PERFORM
BB5            IF WS-Client-Fund-Code = D37-FUND-CODE
BB5                IF CGTSING3-SCHEDULE-TYPE = 'R'
BB5                    ADD WS-BF9697AndLaterLosses-N   
BB5                                      TO WS-BF-LOSS (9697-LOSSES)
BB5                                         WS-LOSS-YTD(9697-LOSSES)
BB5                    IF WS-LossesAdjustment-Sign = '-'
BB5                        SUBTRACT WS-LossesAdjustment-N
BB5                                    FROM WS-BF-LOSS (9697-LOSSES)
BB5                                         WS-LOSS-YTD(9697-LOSSES)
BB5                    ELSE
BB5                        ADD WS-LossesAdjustment-N
BB5                                      TO WS-BF-LOSS (9697-LOSSES)
BB5                                         WS-LOSS-YTD(9697-LOSSES)
BB5                    END-IF
BB5                    ADD WS-BF9596AndEarlierLosses-N 
BB5                                      TO WS-BF-LOSS (9596-LOSSES)
BB5                                         WS-LOSS-YTD(9596-LOSSES)
BB5                    ADD WS-Income-Losses-N
BB5                                      TO WS-BF-LOSS (INCOME-LOSSES)
BB5                                         WS-LOSS-YTD(INCOME-LOSSES)
BB5                ELSE
BB5                    ADD WS-9697AndLaterLosses-YTD-N   
BB5                                      TO WS-BF-LOSS (9697-LOSSES)
BB5                                         WS-LOSS-YTD(9697-LOSSES)
BB5                    ADD WS-9596AndEarlierLosses-YTD-N 
BB5                                      TO WS-BF-LOSS (9596-LOSSES)
BB5                                         WS-LOSS-YTD(9596-LOSSES)
BB5                    ADD WS-Income-Losses-YTD-N
BB5                                      TO WS-BF-LOSS (INCOME-LOSSES)
BB5                                         WS-LOSS-YTD(INCOME-LOSSES)
BB5                END-IF
BB5            END-IF
BB5        END-IF.
BB5    B33-EXIT.
BB5        EXIT.

BB5    B34-GET-DISPOSALS-FOR-CLIENT SECTION.
BB5        IF D37-USE-LOSSES-FLAG = 'Y'
BB5        AND CGTSING3-SCHEDULE-TYPE = 'R'
      *----------------------------------------------------------------
      *       If client is using disposals, find matching records 
      *       in csv file, and write to temp file
      *----------------------------------------------------------------
BB5            MOVE DISPOSALS-FROM-DB-FILE TO L-FILE-NAME   
BB5            MOVE READ-NEXT              TO L-FILE-ACTION
BB5            PERFORM UNTIL D158-Client-Fund-Code > D37-FUND-CODE
BB5                IF D158-Client-Fund-Code = D37-FUND-CODE
BB5                AND D158-Disposal-Date >= WS-D37-START-DATE-CCYYMMDD
BB5                AND D158-Disposal-Date <= WS-D37-END-DATE-CCYYMMDD
BB5                    MOVE SPACES             TO TEMP-DETAILS
BB5                    MOVE D37-FUND-CODE       TO TEMP-FUND-CODE
BB5                    MOVE DESCRIPTION-OTHER  TO TEMP-SEDOL-CODE
BB5                    MOVE D158-Disposal-Id   TO TEMP-SORT-CODE
BB5                                               SK-SEQUENCE-NO
BB5                    MOVE 'N'                TO TEMP-BUSINESS-USE
BB5                    MOVE ZERO               TO TEMP-QUANTITY
BB5                    MOVE D158-Proceeds-N    TO TEMP-PROCEEDS
BB5                    IF D158-Gain-Loss-Sign = '-'
BB5                        SUBTRACT D158-Gain-Loss-N FROM ZERO
BB5                                          GIVING TEMP-GAIN-LOSS
BB5                        ADD  D158-Gain-Loss-N TO WS-CLIENT-LOSS
BB5                    ELSE
BB5                        MOVE D158-Gain-Loss-N TO TEMP-GAIN-LOSS
BB5                        ADD  D158-Gain-Loss-N TO WS-CLIENT-GAIN
BB5                    END-IF
BB5                    MOVE D158-Acquisition-Date
BB5                                            TO TEMP-ACQUISITION-DATE
BB5                    MOVE D158-Description   TO TEMP-ISSUER-NAME
BB5                    MOVE D158-Description(35:220)
BB5                                            TO TEMP-DESCRIPTION
BB5                    MOVE D158-Percent-Business-N
BB5                                            TO BUS-PERCENT-USE
BB5                    MOVE D158-Quoted        TO TEMP-QUOTED-IND
BB5                    MOVE D158-Disposal-Date TO TEMP-DISPOSAL-DATE
BB5                    IF D158-Estimated = ZERO 
BB5                        MOVE 'N'            TO TEMP-ESTIMATED-IND
BB5                    ELSE
BB5                        MOVE 'Y'            TO TEMP-ESTIMATED-IND
BB5                    END-IF
BB5
BB5                    PERFORM B1-WRITE-TEMP-RECORD
BB5                END-IF
BB5                PERFORM X-CALL-CGTFILES
BB5                IF SUCCESSFUL
BB5                    PERFORM X6-GET-DISPOSAL-DETAILS
BB5                ELSE
BB5                    MOVE HIGH-VALUES TO D158-Client-Fund-Code
BB5                END-IF
BB5            END-PERFORM
BB5        END-IF.
BB5    B34-EXIT.
BB5        EXIT.
                  
       C-PROCESS-CLIENT SECTION.
           SET INITIATE-GAINS TO TRUE
           PERFORM X-REPORT-HEADINGS
      *
           MOVE CGTTEMP-CLOSE TO CGTTEMP-ACTION
           PERFORM X-CALL-CGTTEMP.
      *
           MOVE CGTTEMP-OPEN-IO TO CGTTEMP-ACTION
           PERFORM X-CALL-CGTTEMP.
      *
           PERFORM C3-CALCULATE-LOSSES
           PERFORM C4-WRITE-YTD-LOSSES
      *
      * Read in the temp records and output to report/export files
           MOVE CGTTEMP-READ TO CGTTEMP-ACTION
           PERFORM X-CALL-CGTTEMP.
           PERFORM UNTIL CGTTEMP-STATUS NOT = '00'
               MOVE CGTTEMP-KEY     TO TEMP-KEY
               MOVE CGTTEMP-DETAILS TO TEMP-DETAILS

BB5            SET TEMP-RECORD-NOT-WRITTEN TO TRUE
BB5            PERFORM VARYING WS-LOSS-SUB 
BB5            FROM 1 BY 1
BB5            UNTIL WS-LOSS-SUB > MAX-LOSS-TYPES
BB5                IF WS-LOSS-USED(WS-LOSS-SUB) > ZERO
                       PERFORM C1-FORMAT-WRITE-EXPORT
                       PERFORM C2-FORMAT-WRITE-REPORT
BB5                    SET TEMP-RECORD-WRITTEN TO TRUE
BB5                    IF D8-USE-LOSSES <> 'Y'
BB5                    OR D37-USE-LOSSES-FLAG <> 'Y'
BB5                    OR TEMP-GAIN-LOSS <= ZERO
      *                  quit after first iteration if loss flags are not 
      *                  set or we are processing a sale which made a loss
BB5                       MOVE MAX-LOSS-TYPES TO WS-LOSS-SUB
BB5                    END-IF

                       MOVE TEMP-SEDOL-CODE TO WS-LAST-TEMP-SEDOL                      
                       MOVE TEMP-FUND-CODE  TO WS-LAST-TEMP-FUND
BB5                END-IF
BB5            END-PERFORM

BB5            IF TEMP-RECORD-NOT-WRITTEN
BB5            OR TEMP-GAIN-LOSS > ZERO
BB5                MOVE 1 TO WS-LOSS-SUB
BB5                PERFORM C1-FORMAT-WRITE-EXPORT
BB5                PERFORM C2-FORMAT-WRITE-REPORT
BB5                MOVE TEMP-SEDOL-CODE TO WS-LAST-TEMP-SEDOL
BB5                MOVE TEMP-FUND-CODE  TO WS-LAST-TEMP-FUND
BB5            END-IF

               PERFORM X-CALL-CGTTEMP
           END-PERFORM
      *
           IF PROCESSING-GAINS
               PERFORM X-FINISH-SEDOL-DESC
               SET INITIATE-LOSSES TO TRUE
               PERFORM X-GAIN-TOTALS
           END-IF

BB5   *   list off the other losses
BB5        IF D8-USE-LOSSES = 'Y'
BB5        AND D37-USE-LOSSES-FLAG = 'Y'
BB5            MOVE '-' TO D110-GAIN-LOSS-SIGN
BB5            PERFORM VARYING WS-LOSS-SUB 
BB5            FROM 2 BY 1
BB5            UNTIL WS-LOSS-SUB > MAX-LOSS-TYPES
BB5                IF WS-BF-LOSS(WS-LOSS-SUB) > WS-LOSS-YTD(WS-LOSS-SUB)
BB5                   INITIALIZE TEMP-FIELDS
BB5                   MOVE WS-LOSS-DESCRIPTION(WS-LOSS-SUB) 
BB5                                               TO TEMP-ISSUER-NAME
BB5                   COMPUTE TEMP-GAIN-LOSS = WS-LOSS-YTD(WS-LOSS-SUB) 
BB5                                          - WS-BF-LOSS(WS-LOSS-SUB)
BB5                   MOVE TEMP-GAIN-LOSS           TO D110-GAIN-LOSS
BB5                   PERFORM C2-FORMAT-WRITE-REPORT
BB5               END-IF
BB5            END-PERFORM
BB5        END-IF

           PERFORM X-LOSS-TOTALS
      *
           MOVE CGTTEMP-CLOSE TO CGTTEMP-ACTION
           PERFORM X-CALL-CGTTEMP.
      *
           MOVE CGTTEMP-OPEN-OUTPUT TO CGTTEMP-ACTION
           PERFORM X-CALL-CGTTEMP.
      *
           IF REPORT-LINE-COUNT
                           > MAX-LINES - REPORT-FOOTER-OFFSET-LINES
               PERFORM X-REPORT-HEADINGS
           END-IF

           PERFORM UNTIL REPORT-LINE-COUNT 
                           > MAX-LINES - REPORT-FOOTER-OFFSET-LINES - 1
               PERFORM X-WRITE-REPORT-DETAIL
           END-PERFORM

BB5        IF D8-USE-LOSSES = 'Y'
BB5        AND D37-USE-LOSSES-FLAG = 'Y'
BB5            MOVE REPORT-FOOTER-2 TO REPORT-DETAIL
BB5        ELSE
               MOVE REPORT-FOOTER   TO REPORT-DETAIL
BB5        END-IF
           PERFORM X-WRITE-REPORT-DETAIL
      *
           MOVE ZERO TO WS-CLIENT-GAIN
                        WS-CLIENT-LOSS
           INITIALIZE REPORT-FIELDS
           INITIALIZE TEMP-FIELDS.
       C-EXIT.
           EXIT.

       C1-FORMAT-WRITE-EXPORT SECTION.
           MOVE TEMP-FUND-CODE        TO D110-FUND-CODE
           MOVE TEMP-SEDOL-CODE       TO D110-SEDOL-CODE
           MOVE TEMP-ACQUISITION-DATE TO D110-ACQUISITION-DATE
           MOVE TEMP-DISPOSAL-DATE    TO D110-DISPOSAL-DATE
           MOVE TEMP-TAPER-RATE       TO D110-TAPER-RATE
           MOVE TEMP-ISSUER-NAME      TO D110-ISSUER-NAME 
           MOVE TEMP-DESCRIPTION      TO D110-DESCRIPTION 
           MOVE TEMP-QUOTED-IND       TO D110-QUOTED-IND 
           MOVE TEMP-ESTIMATED-IND    TO D110-ESTIMATED-IND
           MOVE TEMP-1982-IND         TO D110-1982-IND
           MOVE TEMP-HOLDING-IND      TO D110-HOLDING-IND
           MOVE TEMP-TRANCHE-IND      TO D110-TRANCHE-IND
           MOVE TEMP-SEQUENCE-NO      TO D110-SEQUENCE-NO 
           MOVE TEMP-BUSINESS-USE     TO D110-BUSINESS-USE
     
BB5        IF TEMP-RECORD-NOT-WRITTEN
               MOVE TEMP-QUANTITY         TO D110-QUANTITY
               MOVE TEMP-PROCEEDS         TO D110-PROCEEDS
BB5        ELSE  
BB5            MOVE ZERO                  TO D110-QUANTITY
BB5                                          D110-PROCEEDS
BB5                                          TEMP-QUANTITY
BB5                                          TEMP-PROCEEDS
BB5        END-IF
      *  
BB5        EVALUATE TEMP-GAIN-LOSS 
BB5        WHEN <= ZERO 
               IF TEMP-GAIN-LOSS < ZERO 
                   MOVE '-' TO D110-GAIN-LOSS-SIGN  
               END-IF
               MOVE ZERO TO D110-LOSS-ALLOCATED
                            D110-GAIN-LESS-LOSS
                            D110-TAPERED-GAIN
BB5            MOVE TEMP-GAIN-LOSS TO D110-GAIN-LOSS
BB5        WHEN OTHER
              MOVE '+' TO D110-GAIN-LOSS-SIGN
BB5           EVALUATE WS-LOSS-USED(WS-LOSS-SUB)
              WHEN > TEMP-GAIN-LOSS
                 MOVE TEMP-GAIN-LOSS       TO D110-LOSS-ALLOCATED
BB5                                           D110-GAIN-LOSS
BB5              SUBTRACT TEMP-GAIN-LOSS FROM WS-LOSS-USED(WS-LOSS-SUB)
BB5              MOVE ZERO                 TO TEMP-GAIN-LOSS 
              WHEN > ZERO
BB5              MOVE WS-LOSS-USED(WS-LOSS-SUB) TO D110-LOSS-ALLOCATED
BB5                                                D110-GAIN-LOSS
BB5              SUBTRACT WS-LOSS-USED(WS-LOSS-SUB) FROM TEMP-GAIN-LOSS 
BB5              MOVE ZERO TO WS-LOSS-USED(WS-LOSS-SUB)
              WHEN OTHER  
BB5              MOVE TEMP-GAIN-LOSS TO D110-GAIN-LOSS
                 MOVE ZERO           TO D110-LOSS-ALLOCATED
BB5                                     TEMP-GAIN-LOSS 
              END-EVALUATE
              
              COMPUTE D110-GAIN-LESS-LOSS = D110-GAIN-LOSS - D110-LOSS-ALLOCATED
              COMPUTE D110-TAPERED-GAIN   = D110-GAIN-LESS-LOSS * (D110-TAPER-RATE / 100)              
           END-EVALUATE

BB5        IF D110-LOSS-ALLOCATED = ZERO
BB5            MOVE SPACES                      TO D110-LOSS-TYPE
BB5        ELSE
BB5            MOVE WS-LOSS-LETTER(WS-LOSS-SUB) TO D110-LOSS-TYPE
BB5        END-IF
           MOVE TEMP-DISPOSAL-DATE          TO D110-DISPOSAL-DATE     
           MOVE TEMP-SIGN                   TO D110-SECURITY-SIGN 
    
           WRITE D110-RECORD.
       C1-EXIT.
           EXIT.

       C2-FORMAT-WRITE-REPORT SECTION.
           IF TEMP-SEDOL-CODE NOT = WS-LAST-TEMP-SEDOL
           OR TEMP-FUND-CODE NOT = WS-LAST-TEMP-FUND
BB5        OR (D110-GAIN-LOSS-SIGN = '-' 
BB5                               AND WS-LAST-TEMP-GAINS-LOSS >= ZERO)
               PERFORM X-FINISH-SEDOL-DESC
           ELSE
               ADD  1 TO WS-SEDOL-LINES
           END-IF

BB6        IF TEMP-SEDOL-CODE = DESCRIPTION-OTHER
BB6            MOVE 1 TO WS-SEDOL-LINES
BB6        END-IF

           IF REPORT-LINE-COUNT
                           > MAX-LINES - REPORT-FOOTER-OFFSET-LINES
               PERFORM X-REPORT-HEADINGS
           END-IF

BB5        IF D110-GAIN-LOSS-SIGN = '-'
           AND (INITIATE-GAINS OR PROCESSING-GAINS)
                   SET INITIATE-LOSSES TO TRUE
                   PERFORM X-GAIN-TOTALS    
           END-IF

           MOVE TEMP-FUND-CODE        TO REPORT-FUND-CODE

           EVALUATE WS-SEDOL-LINES
           WHEN 1
                IF REPORT-LINE-COUNT 
                           > MAX-LINES - REPORT-FOOTER-OFFSET-LINES - 2
                    PERFORM X-REPORT-HEADINGS
                END-IF
                PERFORM X-WRITE-REPORT-DETAIL
                MOVE TEMP-SEDOL-CODE  TO REPORT-SEDOL-CODE
                MOVE TEMP-ISSUER-NAME TO REPORT-TEXT
                MOVE TEMP-DESCRIPTION TO WS-LAST-TEMP-DESCRIPTION
           WHEN 2
                MOVE SPACES           TO REPORT-SEDOL-CODE
                MOVE TEMP-DESCRIPTION TO REPORT-TEXT
           WHEN OTHER
                MOVE SPACES           TO REPORT-SEDOL-CODE
                                         REPORT-TEXT
           END-EVALUATE

           STRING TEMP-ACQUISITION-DATE(7:2)
                  '/'
                  TEMP-ACQUISITION-DATE(5:2)
                  '/'
                  TEMP-ACQUISITION-DATE(3:2)
                  DELIMITED BY SIZE 
                  INTO REPORT-ACQUISITION-DATE
           STRING TEMP-DISPOSAL-DATE(7:2)
                  '/'
                  TEMP-DISPOSAL-DATE(5:2)
                  '/'
                  TEMP-DISPOSAL-DATE(3:2)
                  DELIMITED BY SIZE 
                  INTO REPORT-DISPOSAL-DATE
           MOVE TEMP-QUOTED-IND       TO REPORT-QUOTED-IND 
           MOVE TEMP-QUANTITY         TO REPORT-QUANTITY
           
           MOVE TEMP-SIGN TO REPORT-QUANTITY-SIGN

           MOVE TEMP-PROCEEDS         TO REPORT-PROCEEDS
           MOVE TEMP-ESTIMATED-IND    TO REPORT-ESTIMATED-IND
           MOVE TEMP-1982-IND         TO REPORT-1982-IND
           MOVE TEMP-TRANCHE-FLAG     TO REPORT-TRANCHE-FLAG
           
           IF D110-GAIN-LOSS-SIGN = '+'
               MOVE D110-GAIN-LOSS      TO REPORT-GAIN-LOSS
               MOVE TEMP-TAPER-RATE     TO REPORT-TAPER-RATE
               MOVE '%'                 TO REPORT-PERCENTAGE-SIGN
               MOVE D110-LOSS-ALLOCATED TO REPORT-LOSS-USED
               MOVE D110-GAIN-LESS-LOSS TO REPORT-GAIN-LESS-LOSS
               MOVE D110-TAPERED-GAIN   TO REPORT-TAPERED-GAIN
BB5            ADD  D110-GAIN-LOSS      TO REPORT-TOTAL-GAIN
               ADD  D110-LOSS-ALLOCATED TO REPORT-TOTAL-LOSS-USED
               ADD  D110-TAPERED-GAIN   TO REPORT-TOTAL-TAPERED-GAIN
               SET PROCESSING-GAINS     TO TRUE
BB5            MOVE D110-LOSS-TYPE      TO REPORT-LOSS-TYPE
           ELSE
BB5            SUBTRACT D110-GAIN-LOSS FROM ZERO 
BB5                                  GIVING REPORT-GAIN-LOSS
BB5            SUBTRACT D110-GAIN-LOSS FROM REPORT-TOTAL-LOSS
               SET PROCESSING-LOSSES     TO TRUE
           END-IF

           MOVE TEMP-BUSINESS-USE     TO REPORT-BUSINESS-USE

           PERFORM X-WRITE-REPORT-DETAIL

BB5        IF D110-GAIN-LOSS-SIGN = '-'
BB5            SUBTRACT D110-GAIN-LOSS 
BB5                  FROM ZERO GIVING WS-LAST-TEMP-GAINS-LOSS
BB5        ELSE
BB5            MOVE D110-GAIN-LOSS TO WS-LAST-TEMP-GAINS-LOSS
BB5        END-IF.
       C2-EXIT.
           EXIT.

BB5    C3-CALCULATE-LOSSES SECTION.
BB5   *------------------------------------------------------------
BB5   * calculate how much of each loss type is used/left reducing
BB5   * ws-client-gain-left by loss used until there is no gain left
BB5   * to allocate losses against.
BB5   *------------------------------------------------------------
BB5        MOVE WS-CLIENT-GAIN TO WS-CLIENT-GAIN-LEFT
BB5        MOVE WS-CLIENT-LOSS TO WS-BF-LOSS(CURYR-LOSSES)
BB5                               WS-LOSS-YTD(CURYR-LOSSES)

BB5        PERFORM VARYING WS-LOSS-SUB 
BB5        FROM 1 BY 1
BB5        UNTIL WS-LOSS-SUB > MAX-LOSS-TYPES
BB5   *------------------------------------------------------------
BB5   *       brought forward losses can only reduce gain to allowance
BB5   *------------------------------------------------------------
BB5            IF WS-LOSS-SUB = 9697-LOSSES
BB5                IF WS-CLIENT-GAIN-LEFT > 
BB5                            WS-GAINS-ALLOWANCE(WS-ALLOWANCES-SUB)
BB5                   SUBTRACT WS-GAINS-ALLOWANCE(WS-ALLOWANCES-SUB)
BB5                       FROM WS-CLIENT-GAIN-LEFT
BB5                ELSE
BB5   *               gain now fully relieved, so quit section
BB5                    GO TO C3-EXIT
BB5                END-IF
BB5            END-IF

BB5   *------------------------------------------------------------
BB5   *       now calculate the loss type
BB5   *------------------------------------------------------------
BB5            IF WS-CLIENT-GAIN-LEFT < WS-BF-LOSS(WS-LOSS-SUB)
BB5   *           more loss than gain so limit to gain
BB5                MOVE WS-CLIENT-GAIN-LEFT       
BB5                                     TO WS-LOSS-USED(WS-LOSS-SUB)
BB5                SUBTRACT WS-CLIENT-GAIN-LEFT 
BB5                                   FROM WS-BF-LOSS(WS-LOSS-SUB)
BB5                                 GIVING WS-LOSS-YTD(WS-LOSS-SUB)
BB5   *           gain now fully relieved, so quit section
BB5                GO TO C3-EXIT
BB5            ELSE
BB5   *           use all loss available
BB5                MOVE WS-BF-LOSS(WS-LOSS-SUB)
BB5                                     TO WS-LOSS-USED(WS-LOSS-SUB)
BB5                MOVE ZERO            TO WS-LOSS-YTD(WS-LOSS-SUB)
BB5                SUBTRACT WS-BF-LOSS(WS-LOSS-SUB) 
BB5                                   FROM WS-CLIENT-GAIN-LEFT
BB5            END-IF
BB5        END-PERFORM.
BB5    C3-EXIT.
BB5        EXIT.

BB5    C4-WRITE-YTD-LOSSES SECTION.
BB5        IF D8-USE-LOSSES = 'Y'
BB5        AND D37-USE-LOSSES-FLAG = 'Y'
BB5        AND CGTSING3-SCHEDULE-TYPE = 'R'
           AND WS-Client-Fund-Code = WS-LAST-FUND-CODE
BB5            MOVE SPACES               TO D157-RECORD

BB5            MOVE WS-Client-Fund-Code  TO D157-Client-Fund-Code
BB5            MOVE WS-Master-File-Year  TO D157-Master-File-Year
BB5            MOVE WS-Period-Start-Date TO D157-Period-Start-Date
BB5            MOVE WS-Period-End-Date   TO D157-Period-End-Date
BB5            MOVE WS-LOSS-YTD(CURYR-LOSSES)    
BB5                                 TO D157-9697AndLaterLossesYTD-N
BB5            ADD  WS-LOSS-YTD(9697-LOSSES)     
BB5                                 TO D157-9697AndLaterLossesYTD-N
BB5            MOVE WS-LOSS-YTD(9596-LOSSES)     
BB5                                 TO D157-9596AndEarlierLossesYTD-N
BB5            MOVE WS-LOSS-YTD(INCOME-LOSSES) 
BB5                                 TO D157-Income-LossesYTD-N

BB5            WRITE D157-RECORD
BB5        END-IF.
BB5    C4-EXIT.
BB5        EXIT.

       D-END SECTION.
           IF PROCESSING-SALE
               PERFORM B1-WRITE-TEMP-RECORD
           END-IF
      
           IF WS-LAST-FUND-CODE > SPACES
               PERFORM C-PROCESS-CLIENT
           END-IF

           MOVE SPACES TO WS-MESSAGE-1
           IF CGTSING3-SCHEDULE-TYPE = 'R'
               STRING  'Realised Tapered Gains Export File: ' 
                       EXPORT-FILE
                       ' created '
                       DELIMITED BY SIZE
                       INTO WS-MESSAGE-1
               MOVE REALISED-DATA-FILE   TO L-FILE-NAME
           ELSE
               STRING  'Unrealised Tapered Gains Export File: ' 
                       EXPORT-FILE
                       ' created '
                       DELIMITED BY SIZE
                       INTO WS-MESSAGE-1
               MOVE UNREALISED-DATA-FILE TO L-FILE-NAME
           END-IF
           MOVE WS-MESSAGE-1 TO L-LOG-MESSAGE
           PERFORM X4-CGTLOG.
      
           MOVE     'F'    TO L-LOG-MESSAGE-TYPE
           MOVE CLOSE-FILE TO L-LOG-ACTION
           PERFORM X4-CGTLOG
      *
      *Close the schedule data file
      *
           MOVE CLOSE-FILE         TO L-FILE-ACTION
           PERFORM X-CALL-CGTFILES.
      *
      *and other file as required
      *      
           MOVE TAPER-RATE-FILE    TO L-FILE-NAME   
           PERFORM X-CALL-CGTFILES

           IF USERFUND-FILE-WAS-NOT-OPEN
               MOVE USER-FUND-FILE     TO L-FILE-NAME   
               PERFORM X-CALL-CGTFILES
           END-IF
      *
BB5        IF D8-USE-LOSSES = 'Y'
BB5            MOVE LOSSES-FROM-DB-FILE     TO L-FILE-NAME   
BB5            PERFORM X-CALL-CGTFILES
BB5            IF CGTSING3-SCHEDULE-TYPE = 'R'
BB5                MOVE DISPOSALS-FROM-DB-FILE     TO L-FILE-NAME   
BB5                PERFORM X-CALL-CGTFILES

BB5                MOVE D157-Trailer-Record-2 TO D157-Record
BB5                WRITE D157-RECORD
BB5                CLOSE D157-FILE
BB5            END-IF
BB5        END-IF
      *
           MOVE CGTTEMP-CLOSE TO CGTTEMP-ACTION
           PERFORM X-CALL-CGTTEMP

           CLOSE D110-FILE.
           CLOSE D111-FILE.
       D-EXIT.
           EXIT.

       X-REPORT-HEADINGS SECTION.
           ADD  1 TO REPORT-PAGE-COUNT
           MOVE REPORT-PAGE-COUNT TO REPORT-PAGE-NO
           MOVE D37-FUND-CODE TO REPORT-FUND-CODE
           MOVE D37-NAME      TO REPORT-FUND-NAME

           MOVE SPACES TO D111-RECORD
           WRITE D111-RECORD AFTER ADVANCING PAGE

           IF REPORT-HEADER-OFFSET-LINES > 0 
               WRITE D111-RECORD 
                     AFTER ADVANCING REPORT-HEADER-OFFSET-LINES LINES
           END-IF

           WRITE D111-RECORD 
                 FROM REPORT-HEADER-1 
                 AFTER ADVANCING 1 LINE

           ADD 2 TO REPORT-HEADER-OFFSET-LINES GIVING REPORT-LINE-COUNT

           WRITE D111-RECORD 
                 FROM REPORT-HEADER-2
                 AFTER ADVANCING 3 LINES
           ADD 3 TO REPORT-LINE-COUNT

           STRING D37-PERIOD-START-DATE-DD
                  '/'
                  D37-PERIOD-START-DATE-MM
                  '/'
                  D37-PERIOD-START-DATE-YY
                  DELIMITED BY SIZE 
                  INTO REPORT-FUND-FROM-DATE
           STRING D37-PERIOD-END-DATE-DD
                  '/'
                  D37-PERIOD-END-DATE-MM
                  '/'
                  D37-PERIOD-END-DATE-YY
                  DELIMITED BY SIZE 
                  INTO REPORT-FUND-TO-DATE
           WRITE D111-RECORD 
                 FROM REPORT-HEADER-3
                 AFTER ADVANCING 1 LINES
           ADD 1 TO REPORT-LINE-COUNT

           IF INITIATE-LOSSES OR PROCESSING-LOSSES
               WRITE D111-RECORD 
                     FROM REPORT-COL-HEADER-3
                     AFTER ADVANCING 1 LINES
               ADD 1 TO REPORT-LINE-COUNT
    
               WRITE D111-RECORD 
                     FROM REPORT-COL-HEADER-4
                     AFTER ADVANCING 1 LINES
               ADD 1 TO REPORT-LINE-COUNT
           ELSE
               WRITE D111-RECORD 
                     FROM REPORT-COL-HEADER-1
                     AFTER ADVANCING 1 LINES
               ADD 1 TO REPORT-LINE-COUNT
    
               WRITE D111-RECORD 
                     FROM REPORT-COL-HEADER-2
                     AFTER ADVANCING 1 LINES
               ADD 1 TO REPORT-LINE-COUNT
           END-IF
           PERFORM X-WRITE-REPORT-DETAIL

           MOVE 1 TO WS-SEDOL-LINES.
       X-EXIT.
           EXIT.

       X-WRITE-REPORT-DETAIL SECTION.
           WRITE D111-RECORD FROM REPORT-DETAIL
           ADD 1 TO REPORT-LINE-COUNT
           MOVE  SPACES TO REPORT-DETAIL.
       X-EXIT.
           EXIT.

       X-FINISH-SEDOL-DESC SECTION.
           IF WS-SEDOL-LINES = 1 
           AND (PROCESSING-GAINS OR PROCESSING-LOSSES)
               MOVE WS-LAST-TEMP-DESCRIPTION TO REPORT-TEXT
               PERFORM X-WRITE-REPORT-DETAIL
           ELSE
               MOVE 1 TO WS-SEDOL-LINES
           END-IF.
       X-EXIT.
           EXIT.

       X-GAIN-TOTALS SECTION.
           IF REPORT-LINE-COUNT > 56 - REPORT-FOOTER-OFFSET-LINES
               PERFORM X-REPORT-HEADINGS
           END-IF

           IF REPORT-TOTAL-LOSS = 0
               MOVE REPORT-LINE-COUNT TO REPORT-LINE-COUNT-STORE 
               PERFORM X-FINISH-SEDOL-DESC
               IF REPORT-LINE-COUNT = REPORT-LINE-COUNT-STORE
                   PERFORM X-WRITE-REPORT-DETAIL
               END-IF
               SET INITIATE-LOSSES TO TRUE
               MOVE REPORT-GAIN-TOTAL-TEXT    TO REPORT-DETAIL
               MOVE REPORT-TOTAL-GAIN         TO REPORT-GAIN-LOSS
               MOVE REPORT-TOTAL-LOSS-USED    TO REPORT-LOSS-USED
               MOVE REPORT-TOTAL-TAPERED-GAIN TO REPORT-TAPERED-GAIN
               PERFORM X-WRITE-REPORT-DETAIL

               WRITE D111-RECORD 
                     FROM REPORT-COL-HEADER-3
                     AFTER ADVANCING 2 LINES
               ADD 2 TO REPORT-LINE-COUNT

               WRITE D111-RECORD 
                     FROM REPORT-COL-HEADER-4
                     AFTER ADVANCING 1 LINES
               ADD 1 TO REPORT-LINE-COUNT

               MOVE SPACES TO REPORT-DETAIL
               PERFORM X-WRITE-REPORT-DETAIL
           END-IF.
       X-EXIT.
           EXIT.

       X-LOSS-TOTALS SECTION.
           IF REPORT-LINE-COUNT > 56 - REPORT-FOOTER-OFFSET-LINES
               PERFORM X-REPORT-HEADINGS
           END-IF

           MOVE REPORT-LINE-COUNT TO REPORT-LINE-COUNT-STORE
           PERFORM X-FINISH-SEDOL-DESC
           IF REPORT-LINE-COUNT = REPORT-LINE-COUNT-STORE
               PERFORM X-WRITE-REPORT-DETAIL
           END-IF
           MOVE REPORT-LOSS-TOTAL-TEXT TO REPORT-DETAIL
           MOVE REPORT-TOTAL-LOSS      TO REPORT-GAIN-LOSS
           PERFORM X-WRITE-REPORT-DETAIL.

           PERFORM X-WRITE-REPORT-DETAIL
           MOVE REPORT-NET-GAIN-TOTAL-TEXT TO REPORT-DETAIL
           ADD  REPORT-TOTAL-LOSS          TO REPORT-TOTAL-GAIN
           MOVE REPORT-TOTAL-GAIN          TO REPORT-GAIN-LOSS
           PERFORM X-WRITE-REPORT-DETAIL.
       X-EXIT.
           EXIT.
          
       X-CALL-CGTTEMP SECTION.
           CALL 'CGTTEMP' USING CGTTEMP-LINKAGE.

      
       X-CALL-CGTABORT SECTION.
           MOVE L-FILE-RETURN-CODE TO L-ABORT-FILE-STATUS.
           MOVE PROGRAM-NAME       TO L-ABORT-PROGRAM-NAME.
           MOVE L-FILE-NAME        TO L-ABORT-FILE-NAME.
           CALL 'CGTABORT' USING COMMON-LINKAGE CGTABORT-LINKAGE.

      
       X-CALL-CGTINVRT SECTION.
           CALL 'CGTINVRT' USING CGTINVRT-LINKAGE.

      
       X4-CGTLOG SECTION.
           ADD 1 TO WS-MESSAGE-NO
           MOVE WS-MESSAGE-NO TO L-LOG-MESSAGE-NO
           CALL 'CGTLOG' USING CGTLOG-LINKAGE-AREA-1
                               CGTLOG-LINKAGE-AREA-2
      
           IF  L-LOG-MESSAGE-TYPE = 'Q'
               MOVE 'Q' TO WS-PROCESS-FLAG
               MOVE 'P' TO L-LOG-MESSAGE-TYPE
           END-IF.


BB5    X5-GET-LOSS-DETAILS SECTION.
BB5        UNSTRING L-FILE-RECORD-AREA
BB5        DELIMITED BY ","
BB5        INTO WS-Client-Fund-Code
BB5             WS-Master-File-Year-N
BB5             WS-Work-Start-Date
BB5             WS-Work-End-Date
BB5             WS-Status   
BB5             WS-Income-Losses
BB5             WS-BF9697AndLaterLosses
BB5             WS-BF9596AndEarlierLosses
BB5             WS-9697AndLaterLosses-YTD
BB5             WS-9596AndEarlierLosses-YTD
BB5             WS-Income-Losses-YTD
BB5             WS-LossesAdjustment
BB5             WS-Override-Year-End
BB5             WS-REMAINDER.

BB5        STRING WS-Work-Start-Date(7:4)
BB5               WS-Work-Start-Date(4:2)
BB5               WS-Work-Start-Date(1:2)
BB5               DELIMITED BY SIZE INTO WS-Period-Start-Date

BB5        STRING WS-Work-End-Date(7:4)
BB5               WS-Work-End-Date(4:2)
BB5               WS-Work-End-Date(1:2)
BB5               DELIMITED BY SIZE INTO WS-Period-End-Date

BB5        MOVE WS-Income-Losses          TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9               TO WS-Income-Losses-N

BB5        MOVE WS-BF9697AndLaterLosses   TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9               TO WS-BF9697AndLaterLosses-N

BB5        MOVE WS-BF9596AndEarlierLosses TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9             TO WS-BF9596AndEarlierLosses-N

BB5        MOVE WS-9697AndLaterLosses-YTD TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9             TO WS-9697AndLaterLosses-YTD-N

BB5        MOVE WS-9596AndEarlierLosses-YTD TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9            TO WS-9596AndEarlierLosses-YTD-N

BB5        MOVE WS-Income-Losses-YTD      TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9               TO WS-Income-Losses-YTD-N

BB5        MOVE WS-LossesAdjustment       TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9               TO WS-LossesAdjustment-N
BB5        IF NUMBER-NEGATIVE
BB5            MOVE '-'                   TO WS-LossesAdjustment-Sign
BB5        ELSE
BB5            MOVE '+'                   TO WS-LossesAdjustment-Sign
BB5        END-IF.
BB5    X5-EXIT.
BB5        EXIT.

BB5    X6-GET-DISPOSAL-DETAILS SECTION.
BB5        UNSTRING L-FILE-RECORD-AREA
BB5        DELIMITED BY ","
BB5        INTO D158-Disposal-ID-N
BB5             D158-Client-Fund-Code
BB5             W-Disposal-Date
BB5             W-Acquisition-Date
BB5             D158-Description
BB5             W-Cost
BB5             W-Proceeds
BB5             W-Gain-Loss
BB5             W-Percent-Business
BB5             D158-Quoted
BB5             D158-Estimated
BB5             WS-REMAINDER.
          
BB5        STRING W-Disposal-Date(7:4)
BB5               W-Disposal-Date(4:2)
BB5               W-Disposal-Date(1:2)
BB5               DELIMITED BY SIZE INTO D158-Disposal-Date

BB5        STRING W-Acquisition-Date(7:4)
BB5               W-Acquisition-Date(4:2)
BB5               W-Acquisition-Date(1:2)
BB5               DELIMITED BY SIZE INTO D158-Acquisition-Date

BB5        MOVE W-Cost             TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9         TO D158-Cost-N

BB5        MOVE W-Proceeds          TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9         TO D158-Proceeds-N

BB5        MOVE W-Gain-Loss         TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9         TO D158-Gain-Loss-N
BB5        IF NUMBER-NEGATIVE
BB5            MOVE '-'             TO D158-Gain-Loss-Sign
BB5        ELSE
BB5            MOVE '+'             TO D158-Gain-Loss-Sign
BB5        END-IF

BB5        MOVE W-Percent-Business  TO W-NUM-IN
BB5        PERFORM X7-CONVERT-TO-NUMBER
BB5        MOVE W-NUM-OUT-9         TO D158-Percent-Business-N.
BB5    X6-EXIT.
BB5        EXIT.

BB5    X7-CONVERT-TO-NUMBER SECTION.
BB5        IF W-NUM-IN-BYTE(1) = '-'
BB5            SET NUMBER-NEGATIVE TO TRUE
BB5            MOVE 2 TO W-START-SUB
BB5        ELSE
BB5            SET NUMBER-POSITIVE TO TRUE
BB5            MOVE 1 TO W-START-SUB	
BB5        END-IF

BB5        MOVE ZERO TO W-DP-SUB
BB5        MOVE ZERO TO W-NUM-OUT-9

BB5        PERFORM VARYING W-SUB-I
BB5        FROM W-START-SUB BY 1 
BB5        UNTIL W-SUB-I > 16
BB5        OR    W-NUM-IN-BYTE(W-SUB-I) = SPACE
BB5            IF W-NUM-IN-BYTE(W-SUB-I) = '.'
BB5                MOVE W-SUB-I TO W-DP-SUB
BB5            END-IF
BB5            IF W-SUB-I > W-DP-SUB
BB5            AND W-DP-SUB > 0
BB5            AND W-NUM-IN-BYTE(W-SUB-I) <> SPACE
BB5                COMPUTE W-SUB-O = 14 + W-SUB-I - W-DP-SUB
BB5                MOVE W-NUM-IN-BYTE(W-SUB-I) 
BB5                  TO W-NUM-OUT-BYTE(W-SUB-O) 
BB5            END-IF
BB5        END-PERFORM

BB5        SUBTRACT 1 FROM W-SUB-I
BB5        IF W-DP-SUB > 0 
BB5            SUBTRACT 1 FROM W-DP-SUB GIVING W-SUB-I
BB5        END-IF 

BB5        PERFORM VARYING W-SUB-O
BB5        FROM 14 BY -1
BB5        UNTIL W-SUB-O < 1
BB5           OR W-SUB-I < W-START-SUB
BB5            MOVE W-NUM-IN-BYTE(W-SUB-I) 
BB5              TO W-NUM-OUT-BYTE(W-SUB-O)
BB5            SUBTRACT 1 FROM W-SUB-I
BB5        END-PERFORM.
BB5    X7-EXIT.
BB5        EXIT.

       COPY EQTPATH.CPY.      
dtv    COPY EQTFILES.CPY.
       COPY ConfigCode.Cpy.