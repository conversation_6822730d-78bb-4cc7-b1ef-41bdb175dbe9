using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportColumnHeading1 Data Structure

public class ReportColumnHeading1
{
    private static int _size = 126;
    // [DEBUG] Class: ReportColumnHeading1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler135, is_external=, is_static_class=False, static_prefix=
    private string _Filler135 ="FUND";
    
    
    
    
    // [DEBUG] Field: Filler136, is_external=, is_static_class=False, static_prefix=
    private string _Filler136 ="  SEDOL";
    
    
    
    
    // [DEBUG] Field: Filler137, is_external=, is_static_class=False, static_prefix=
    private string _Filler137 ="CONTRACT";
    
    
    
    
    // [DEBUG] Field: Filler138, is_external=, is_static_class=False, static_prefix=
    private string _Filler138 ="       TYPE";
    
    
    
    
    // [DEBUG] Field: Filler139, is_external=, is_static_class=False, static_prefix=
    private string _Filler139 ="BARGAIN";
    
    
    
    
    // [DEBUG] Field: Filler140, is_external=, is_static_class=False, static_prefix=
    private string _Filler140 ="         UNITS";
    
    
    
    
    // [DEBUG] Field: Filler141, is_external=, is_static_class=False, static_prefix=
    private string _Filler141 ="         VALUE";
    
    
    
    
    // [DEBUG] Field: Filler142, is_external=, is_static_class=False, static_prefix=
    private string _Filler142 ="ACTION";
    
    
    
    
    
    // Serialization methods
    public string GetReportColumnHeading1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler135.PadRight(0));
        result.Append(_Filler136.PadRight(11));
        result.Append(_Filler137.PadRight(12));
        result.Append(_Filler138.PadRight(22));
        result.Append(_Filler139.PadRight(10));
        result.Append(_Filler140.PadRight(16));
        result.Append(_Filler141.PadRight(16));
        result.Append(_Filler142.PadRight(39));
        
        return result.ToString();
    }
    
    public void SetReportColumnHeading1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller135(extracted);
        }
        offset += 0;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetFiller136(extracted);
        }
        offset += 11;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetFiller137(extracted);
        }
        offset += 12;
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetFiller138(extracted);
        }
        offset += 22;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetFiller139(extracted);
        }
        offset += 10;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller140(extracted);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller141(extracted);
        }
        offset += 16;
        if (offset + 39 <= data.Length)
        {
            string extracted = data.Substring(offset, 39).Trim();
            SetFiller142(extracted);
        }
        offset += 39;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportColumnHeading1AsString();
    }
    // Set<>String Override function
    public void SetReportColumnHeading1(string value)
    {
        SetReportColumnHeading1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller135()
    {
        return _Filler135;
    }
    
    // Standard Setter
    public void SetFiller135(string value)
    {
        _Filler135 = value;
    }
    
    // Get<>AsString()
    public string GetFiller135AsString()
    {
        return _Filler135.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller135AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler135 = value;
    }
    
    // Standard Getter
    public string GetFiller136()
    {
        return _Filler136;
    }
    
    // Standard Setter
    public void SetFiller136(string value)
    {
        _Filler136 = value;
    }
    
    // Get<>AsString()
    public string GetFiller136AsString()
    {
        return _Filler136.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetFiller136AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler136 = value;
    }
    
    // Standard Getter
    public string GetFiller137()
    {
        return _Filler137;
    }
    
    // Standard Setter
    public void SetFiller137(string value)
    {
        _Filler137 = value;
    }
    
    // Get<>AsString()
    public string GetFiller137AsString()
    {
        return _Filler137.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetFiller137AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler137 = value;
    }
    
    // Standard Getter
    public string GetFiller138()
    {
        return _Filler138;
    }
    
    // Standard Setter
    public void SetFiller138(string value)
    {
        _Filler138 = value;
    }
    
    // Get<>AsString()
    public string GetFiller138AsString()
    {
        return _Filler138.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetFiller138AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler138 = value;
    }
    
    // Standard Getter
    public string GetFiller139()
    {
        return _Filler139;
    }
    
    // Standard Setter
    public void SetFiller139(string value)
    {
        _Filler139 = value;
    }
    
    // Get<>AsString()
    public string GetFiller139AsString()
    {
        return _Filler139.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetFiller139AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler139 = value;
    }
    
    // Standard Getter
    public string GetFiller140()
    {
        return _Filler140;
    }
    
    // Standard Setter
    public void SetFiller140(string value)
    {
        _Filler140 = value;
    }
    
    // Get<>AsString()
    public string GetFiller140AsString()
    {
        return _Filler140.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller140AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler140 = value;
    }
    
    // Standard Getter
    public string GetFiller141()
    {
        return _Filler141;
    }
    
    // Standard Setter
    public void SetFiller141(string value)
    {
        _Filler141 = value;
    }
    
    // Get<>AsString()
    public string GetFiller141AsString()
    {
        return _Filler141.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller141AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler141 = value;
    }
    
    // Standard Getter
    public string GetFiller142()
    {
        return _Filler142;
    }
    
    // Standard Setter
    public void SetFiller142(string value)
    {
        _Filler142 = value;
    }
    
    // Get<>AsString()
    public string GetFiller142AsString()
    {
        return _Filler142.PadRight(39);
    }
    
    // Set<>AsString()
    public void SetFiller142AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler142 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}