using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportColumnHeading2 Data Structure

public class ReportColumnHeading2
{
    private static int _size = 126;
    // [DEBUG] Class: ReportColumnHeading2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler143, is_external=, is_static_class=False, static_prefix=
    private string _Filler143 ="____";
    
    
    
    
    // [DEBUG] Field: Filler144, is_external=, is_static_class=False, static_prefix=
    private string _Filler144 ="_________";
    
    
    
    
    // [DEBUG] Field: Filler145, is_external=, is_static_class=False, static_prefix=
    private string _Filler145 ="__________";
    
    
    
    
    // [DEBUG] Field: Filler146, is_external=, is_static_class=False, static_prefix=
    private string _Filler146 ="____________________";
    
    
    
    
    // [DEBUG] Field: Filler147, is_external=, is_static_class=False, static_prefix=
    private string _Filler147 ="________";
    
    
    
    
    // [DEBUG] Field: Filler148, is_external=, is_static_class=False, static_prefix=
    private string _Filler148 ="______________";
    
    
    
    
    // [DEBUG] Field: Filler149, is_external=, is_static_class=False, static_prefix=
    private string _Filler149 ="______________";
    
    
    
    
    // [DEBUG] Field: Filler150, is_external=, is_static_class=False, static_prefix=
    private string _Filler150 ="ALL'_'";
    
    
    
    
    
    // Serialization methods
    public string GetReportColumnHeading2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler143.PadRight(0));
        result.Append(_Filler144.PadRight(11));
        result.Append(_Filler145.PadRight(12));
        result.Append(_Filler146.PadRight(22));
        result.Append(_Filler147.PadRight(10));
        result.Append(_Filler148.PadRight(16));
        result.Append(_Filler149.PadRight(16));
        result.Append(_Filler150.PadRight(39));
        
        return result.ToString();
    }
    
    public void SetReportColumnHeading2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller143(extracted);
        }
        offset += 0;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetFiller144(extracted);
        }
        offset += 11;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetFiller145(extracted);
        }
        offset += 12;
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetFiller146(extracted);
        }
        offset += 22;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetFiller147(extracted);
        }
        offset += 10;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller148(extracted);
        }
        offset += 16;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller149(extracted);
        }
        offset += 16;
        if (offset + 39 <= data.Length)
        {
            string extracted = data.Substring(offset, 39).Trim();
            SetFiller150(extracted);
        }
        offset += 39;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportColumnHeading2AsString();
    }
    // Set<>String Override function
    public void SetReportColumnHeading2(string value)
    {
        SetReportColumnHeading2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller143()
    {
        return _Filler143;
    }
    
    // Standard Setter
    public void SetFiller143(string value)
    {
        _Filler143 = value;
    }
    
    // Get<>AsString()
    public string GetFiller143AsString()
    {
        return _Filler143.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller143AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler143 = value;
    }
    
    // Standard Getter
    public string GetFiller144()
    {
        return _Filler144;
    }
    
    // Standard Setter
    public void SetFiller144(string value)
    {
        _Filler144 = value;
    }
    
    // Get<>AsString()
    public string GetFiller144AsString()
    {
        return _Filler144.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetFiller144AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler144 = value;
    }
    
    // Standard Getter
    public string GetFiller145()
    {
        return _Filler145;
    }
    
    // Standard Setter
    public void SetFiller145(string value)
    {
        _Filler145 = value;
    }
    
    // Get<>AsString()
    public string GetFiller145AsString()
    {
        return _Filler145.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetFiller145AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler145 = value;
    }
    
    // Standard Getter
    public string GetFiller146()
    {
        return _Filler146;
    }
    
    // Standard Setter
    public void SetFiller146(string value)
    {
        _Filler146 = value;
    }
    
    // Get<>AsString()
    public string GetFiller146AsString()
    {
        return _Filler146.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetFiller146AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler146 = value;
    }
    
    // Standard Getter
    public string GetFiller147()
    {
        return _Filler147;
    }
    
    // Standard Setter
    public void SetFiller147(string value)
    {
        _Filler147 = value;
    }
    
    // Get<>AsString()
    public string GetFiller147AsString()
    {
        return _Filler147.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetFiller147AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler147 = value;
    }
    
    // Standard Getter
    public string GetFiller148()
    {
        return _Filler148;
    }
    
    // Standard Setter
    public void SetFiller148(string value)
    {
        _Filler148 = value;
    }
    
    // Get<>AsString()
    public string GetFiller148AsString()
    {
        return _Filler148.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller148AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler148 = value;
    }
    
    // Standard Getter
    public string GetFiller149()
    {
        return _Filler149;
    }
    
    // Standard Setter
    public void SetFiller149(string value)
    {
        _Filler149 = value;
    }
    
    // Get<>AsString()
    public string GetFiller149AsString()
    {
        return _Filler149.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller149AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler149 = value;
    }
    
    // Standard Getter
    public string GetFiller150()
    {
        return _Filler150;
    }
    
    // Standard Setter
    public void SetFiller150(string value)
    {
        _Filler150 = value;
    }
    
    // Get<>AsString()
    public string GetFiller150AsString()
    {
        return _Filler150.PadRight(39);
    }
    
    // Set<>AsString()
    public void SetFiller150AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler150 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}