using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing D8Record Data Structure

public class D8Record
{
    private static int _size = 329;
    // [DEBUG] Class: D8Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D8Key, is_external=, is_static_class=False, static_prefix=
    private D8Key _D8Key = new D8Key();
    
    
    
    
    // [DEBUG] Field: D8ProduceUSchedule, is_external=, is_static_class=False, static_prefix=
    private string _D8ProduceUSchedule ="";
    
    
    
    
    // [DEBUG] Field: D8RealSchdContent, is_external=, is_static_class=False, static_prefix=
    private string _D8RealSchdContent ="";
    
    
    
    
    // [DEBUG] Field: D8UseBvForProfit, is_external=, is_static_class=False, static_prefix=
    private string _D8UseBvForProfit ="";
    
    
    
    
    // [DEBUG] Field: D8IndexCalcRounding, is_external=, is_static_class=False, static_prefix=
    private string _D8IndexCalcRounding ="";
    
    
    
    
    // [DEBUG] Field: D8Cg01Content, is_external=, is_static_class=False, static_prefix=
    private string _D8Cg01Content ="";
    
    
    
    
    // [DEBUG] Field: D8Cg02Content, is_external=, is_static_class=False, static_prefix=
    private string _D8Cg02Content ="";
    
    
    
    
    // [DEBUG] Field: D8Cg03Content, is_external=, is_static_class=False, static_prefix=
    private string _D8Cg03Content ="";
    
    
    
    
    // [DEBUG] Field: D8YeDeletions, is_external=, is_static_class=False, static_prefix=
    private string _D8YeDeletions ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultFundName, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultFundName ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultFundType, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultFundType ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultStartDateI, is_external=, is_static_class=False, static_prefix=
    private D8DefaultStartDateI _D8DefaultStartDateI = new D8DefaultStartDateI();
    
    
    
    
    // [DEBUG] Field: D8DefaultEndDateI, is_external=, is_static_class=False, static_prefix=
    private D8DefaultEndDateI _D8DefaultEndDateI = new D8DefaultEndDateI();
    
    
    
    
    // [DEBUG] Field: D8DefaultStartDateC, is_external=, is_static_class=False, static_prefix=
    private D8DefaultStartDateC _D8DefaultStartDateC = new D8DefaultStartDateC();
    
    
    
    
    // [DEBUG] Field: D8DefaultEndDateC, is_external=, is_static_class=False, static_prefix=
    private D8DefaultEndDateC _D8DefaultEndDateC = new D8DefaultEndDateC();
    
    
    
    
    // [DEBUG] Field: D8DefaultE1965Fi, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultE1965Fi ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultE1965Ord, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultE1965Ord ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultE1982, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultE1982 ="";
    
    
    
    
    // [DEBUG] Field: D8LastUserNo, is_external=, is_static_class=False, static_prefix=
    private int _D8LastUserNo =0;
    
    
    
    
    // [DEBUG] Field: D8LastContractNo, is_external=, is_static_class=False, static_prefix=
    private int _D8LastContractNo =0;
    
    
    
    
    // [DEBUG] Field: D8LastBargainNo, is_external=, is_static_class=False, static_prefix=
    private int _D8LastBargainNo =0;
    
    
    
    
    // [DEBUG] Field: D8StartUpYear, is_external=, is_static_class=False, static_prefix=
    private int _D8StartUpYear =0;
    
    
    
    
    // [DEBUG] Field: D8CurrentYear, is_external=, is_static_class=False, static_prefix=
    private int _D8CurrentYear =0;
    
    
    
    
    // [DEBUG] Field: D8SedolValidation, is_external=, is_static_class=False, static_prefix=
    private string _D8SedolValidation ="";
    
    
    
    
    // [DEBUG] Field: D8PrinterTableCode, is_external=, is_static_class=False, static_prefix=
    private string _D8PrinterTableCode ="";
    
    
    
    
    // [DEBUG] Field: D8PrinterCurrencySign, is_external=, is_static_class=False, static_prefix=
    private string _D8PrinterCurrencySign ="";
    
    
    
    
    // [DEBUG] Field: D8SystemPrinter, is_external=, is_static_class=False, static_prefix=
    private string _D8SystemPrinter ="";
    
    
    
    
    // [DEBUG] Field: D8LocalPrinter, is_external=, is_static_class=False, static_prefix=
    private string _D8LocalPrinter ="";
    
    
    
    
    // [DEBUG] Field: D8UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D8UpdateCount =0;
    
    
    
    
    // [DEBUG] Field: D8CommsCommand, is_external=, is_static_class=False, static_prefix=
    private string _D8CommsCommand ="";
    
    
    
    
    // [DEBUG] Field: D8SuppressUpdate, is_external=, is_static_class=False, static_prefix=
    private string _D8SuppressUpdate ="";
    
    
    
    
    // [DEBUG] Field: D8SuppressCgtr04, is_external=, is_static_class=False, static_prefix=
    private string _D8SuppressCgtr04 ="";
    
    
    
    
    // [DEBUG] Field: D8SuppressCgtr05, is_external=, is_static_class=False, static_prefix=
    private string _D8SuppressCgtr05 ="";
    
    
    
    
    // [DEBUG] Field: D8CalculationProgram, is_external=, is_static_class=False, static_prefix=
    private string _D8CalculationProgram ="";
    
    
    
    
    // [DEBUG] Field: D8PrintBanners, is_external=, is_static_class=False, static_prefix=
    private string _D8PrintBanners ="";
    
    
    
    
    // [DEBUG] Field: D8PrintDisclaimer, is_external=, is_static_class=False, static_prefix=
    private string _D8PrintDisclaimer ="";
    
    
    
    
    // [DEBUG] Field: D8Filler, is_external=, is_static_class=False, static_prefix=
    private string _D8Filler ="";
    
    
    
    
    // [DEBUG] Field: D8ProduceGainlossReport, is_external=, is_static_class=False, static_prefix=
    private string _D8ProduceGainlossReport ="";
    
    
    
    
    // [DEBUG] Field: D8RealisedProgram, is_external=, is_static_class=False, static_prefix=
    private string _D8RealisedProgram ="";
    
    
    
    
    // [DEBUG] Field: D8LastBackupDrivePath, is_external=, is_static_class=False, static_prefix=
    private D8LastBackupDrivePath _D8LastBackupDrivePath = new D8LastBackupDrivePath();
    
    
    
    
    // [DEBUG] Field: D8StockDefaults, is_external=, is_static_class=False, static_prefix=
    private D8StockDefaults _D8StockDefaults = new D8StockDefaults();
    
    
    
    
    // [DEBUG] Field: D8UnrealisedProgram, is_external=, is_static_class=False, static_prefix=
    private string _D8UnrealisedProgram ="";
    
    
    
    
    // [DEBUG] Field: D8MaxNetworkUsers, is_external=, is_static_class=False, static_prefix=
    private int _D8MaxNetworkUsers =0;
    
    
    
    
    // [DEBUG] Field: D8OnlineAutoGenErrors, is_external=, is_static_class=False, static_prefix=
    private string _D8OnlineAutoGenErrors ="";
    
    
    
    
    // [DEBUG] Field: D8OnlineAutoGenRSchedule, is_external=, is_static_class=False, static_prefix=
    private string _D8OnlineAutoGenRSchedule ="";
    
    
    
    
    // [DEBUG] Field: D8BatchAutoGenErrors, is_external=, is_static_class=False, static_prefix=
    private string _D8BatchAutoGenErrors ="";
    
    
    
    
    // [DEBUG] Field: D8BatchAutoGenRSchedule, is_external=, is_static_class=False, static_prefix=
    private string _D8BatchAutoGenRSchedule ="";
    
    
    
    
    // [DEBUG] Field: D8BatchAutoGenUSchedule, is_external=, is_static_class=False, static_prefix=
    private string _D8BatchAutoGenUSchedule ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultStockExchangeInd, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultStockExchangeInd ="";
    
    
    
    
    // [DEBUG] Field: D8RealisedReportSelections, is_external=, is_static_class=False, static_prefix=
    private D8RealisedReportSelections _D8RealisedReportSelections = new D8RealisedReportSelections();
    
    
    
    
    // [DEBUG] Field: D8Fa1990SegregationFlag, is_external=, is_static_class=False, static_prefix=
    private string _D8Fa1990SegregationFlag ="";
    
    
    
    
    // [DEBUG] Field: D8SuppressD84, is_external=, is_static_class=False, static_prefix=
    private string _D8SuppressD84 ="";
    
    
    
    
    // [DEBUG] Field: D8HoldingFlagText, is_external=, is_static_class=False, static_prefix=
    private string _D8HoldingFlagText ="";
    
    
    
    
    // [DEBUG] Field: D8ReportOffsetFlag, is_external=, is_static_class=False, static_prefix=
    private string _D8ReportOffsetFlag ="";
    
    
    
    
    // [DEBUG] Field: D8ReportHeaderOffsetLines, is_external=, is_static_class=False, static_prefix=
    private int _D8ReportHeaderOffsetLines =0;
    
    
    
    
    // [DEBUG] Field: D8ReportFooterOffsetLines, is_external=, is_static_class=False, static_prefix=
    private int _D8ReportFooterOffsetLines =0;
    
    
    
    
    // [DEBUG] Field: D8ThreadneedleSchedules, is_external=, is_static_class=False, static_prefix=
    private string _D8ThreadneedleSchedules ="";
    
    
    
    
    // [DEBUG] Field: D8AuditTrail, is_external=, is_static_class=False, static_prefix=
    private string _D8AuditTrail ="";
    
    
    
    
    // [DEBUG] Field: D8ResultsDb, is_external=, is_static_class=False, static_prefix=
    private string _D8ResultsDb ="";
    
    
    
    
    // [DEBUG] Field: D8UsePeriodEndCalendars, is_external=, is_static_class=False, static_prefix=
    private string _D8UsePeriodEndCalendars ="";
    
    
    
    
    // [DEBUG] Field: D8UseLosses, is_external=, is_static_class=False, static_prefix=
    private string _D8UseLosses ="";
    
    
    
    
    // [DEBUG] Field: D8UsePreProcessor, is_external=, is_static_class=False, static_prefix=
    private string _D8UsePreProcessor ="";
    
    
    // 88-level condition checks for D8UsePreProcessor
    public bool IsSysparmUsePreProcessor()
    {
        if (this._D8UsePreProcessor == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D8GenerateSummaryFunds, is_external=, is_static_class=False, static_prefix=
    private string _D8GenerateSummaryFunds ="";
    
    
    // 88-level condition checks for D8GenerateSummaryFunds
    public bool IsSysparmGenerateSummaryFunds()
    {
        if (this._D8GenerateSummaryFunds == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D8PolicySales, is_external=, is_static_class=False, static_prefix=
    private string _D8PolicySales ="";
    
    
    
    
    // [DEBUG] Field: D8PolicyAcquisitions, is_external=, is_static_class=False, static_prefix=
    private string _D8PolicyAcquisitions ="";
    
    
    
    
    // [DEBUG] Field: D8PolicyCapitalEvents, is_external=, is_static_class=False, static_prefix=
    private string _D8PolicyCapitalEvents ="";
    
    
    
    
    // [DEBUG] Field: D8PolicyFundTransfers, is_external=, is_static_class=False, static_prefix=
    private string _D8PolicyFundTransfers ="";
    
    
    
    
    // [DEBUG] Field: D8CobolFilesVersionNumber, is_external=, is_static_class=False, static_prefix=
    private int _D8CobolFilesVersionNumber =0;
    
    
    
    
    // [DEBUG] Field: D8LastPendingId, is_external=, is_static_class=False, static_prefix=
    private int _D8LastPendingId =0;
    
    
    
    
    // [DEBUG] Field: D8DeemedDisposalFlag, is_external=, is_static_class=False, static_prefix=
    private string _D8DeemedDisposalFlag ="";
    
    
    
    
    // [DEBUG] Field: D8AddLiabToNipiProceedsFlag, is_external=, is_static_class=False, static_prefix=
    private string _D8AddLiabToNipiProceedsFlag ="";
    
    
    
    
    // [DEBUG] Field: D8Spare, is_external=, is_static_class=False, static_prefix=
    private string _D8Spare ="";
    
    
    
    
    
    // Serialization methods
    public string GetD8RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D8Key.GetD8KeyAsString());
        result.Append(_D8ProduceUSchedule.PadRight(1));
        result.Append(_D8RealSchdContent.PadRight(2));
        result.Append(_D8UseBvForProfit.PadRight(1));
        result.Append(_D8IndexCalcRounding.PadRight(1));
        result.Append(_D8Cg01Content.PadRight(1));
        result.Append(_D8Cg02Content.PadRight(1));
        result.Append(_D8Cg03Content.PadRight(1));
        result.Append(_D8YeDeletions.PadRight(1));
        result.Append(_D8DefaultFundName.PadRight(30));
        result.Append(_D8DefaultFundType.PadRight(1));
        result.Append(_D8DefaultStartDateI.GetD8DefaultStartDateIAsString());
        result.Append(_D8DefaultEndDateI.GetD8DefaultEndDateIAsString());
        result.Append(_D8DefaultStartDateC.GetD8DefaultStartDateCAsString());
        result.Append(_D8DefaultEndDateC.GetD8DefaultEndDateCAsString());
        result.Append(_D8DefaultE1965Fi.PadRight(1));
        result.Append(_D8DefaultE1965Ord.PadRight(1));
        result.Append(_D8DefaultE1982.PadRight(1));
        result.Append(_D8LastUserNo.ToString().PadLeft(4, '0'));
        result.Append(_D8LastContractNo.ToString().PadLeft(10, '0'));
        result.Append(_D8LastBargainNo.ToString().PadLeft(10, '0'));
        result.Append(_D8StartUpYear.ToString().PadLeft(2, '0'));
        result.Append(_D8CurrentYear.ToString().PadLeft(2, '0'));
        result.Append(_D8SedolValidation.PadRight(1));
        result.Append(_D8PrinterTableCode.PadRight(2));
        result.Append(_D8PrinterCurrencySign.PadRight(1));
        result.Append(_D8SystemPrinter.PadRight(12));
        result.Append(_D8LocalPrinter.PadRight(12));
        result.Append(_D8UpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_D8CommsCommand.PadRight(48));
        result.Append(_D8SuppressUpdate.PadRight(1));
        result.Append(_D8SuppressCgtr04.PadRight(1));
        result.Append(_D8SuppressCgtr05.PadRight(1));
        result.Append(_D8CalculationProgram.PadRight(8));
        result.Append(_D8PrintBanners.PadRight(1));
        result.Append(_D8PrintDisclaimer.PadRight(1));
        result.Append(_D8Filler.PadRight(1));
        result.Append(_D8ProduceGainlossReport.PadRight(1));
        result.Append(_D8RealisedProgram.PadRight(12));
        result.Append(_D8LastBackupDrivePath.GetD8LastBackupDrivePathAsString());
        result.Append(_D8StockDefaults.GetD8StockDefaultsAsString());
        result.Append(_D8UnrealisedProgram.PadRight(12));
        result.Append(_D8MaxNetworkUsers.ToString().PadLeft(4, '0'));
        result.Append(_D8OnlineAutoGenErrors.PadRight(1));
        result.Append(_D8OnlineAutoGenRSchedule.PadRight(1));
        result.Append(_D8BatchAutoGenErrors.PadRight(1));
        result.Append(_D8BatchAutoGenRSchedule.PadRight(1));
        result.Append(_D8BatchAutoGenUSchedule.PadRight(1));
        result.Append(_D8DefaultStockExchangeInd.PadRight(1));
        result.Append(_D8RealisedReportSelections.GetD8RealisedReportSelectionsAsString());
        result.Append(_D8Fa1990SegregationFlag.PadRight(0));
        result.Append(_D8SuppressD84.PadRight(0));
        result.Append(_D8HoldingFlagText.PadRight(18));
        result.Append(_D8ReportOffsetFlag.PadRight(0));
        result.Append(_D8ReportHeaderOffsetLines.ToString().PadLeft(2, '0'));
        result.Append(_D8ReportFooterOffsetLines.ToString().PadLeft(2, '0'));
        result.Append(_D8ThreadneedleSchedules.PadRight(0));
        result.Append(_D8AuditTrail.PadRight(0));
        result.Append(_D8ResultsDb.PadRight(0));
        result.Append(_D8UsePeriodEndCalendars.PadRight(0));
        result.Append(_D8UseLosses.PadRight(0));
        result.Append(_D8UsePreProcessor.PadRight(0));
        result.Append(_D8GenerateSummaryFunds.PadRight(0));
        result.Append(_D8PolicySales.PadRight(0));
        result.Append(_D8PolicyAcquisitions.PadRight(0));
        result.Append(_D8PolicyCapitalEvents.PadRight(0));
        result.Append(_D8PolicyFundTransfers.PadRight(0));
        result.Append(_D8CobolFilesVersionNumber.ToString().PadLeft(1, '0'));
        result.Append(_D8LastPendingId.ToString().PadLeft(6, '0'));
        result.Append(_D8DeemedDisposalFlag.PadRight(0));
        result.Append(_D8AddLiabToNipiProceedsFlag.PadRight(0));
        result.Append(_D8Spare.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD8RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            _D8Key.SetD8KeyAsString(data.Substring(offset, 3));
        }
        else
        {
            _D8Key.SetD8KeyAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8ProduceUSchedule(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD8RealSchdContent(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8UseBvForProfit(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8IndexCalcRounding(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8Cg01Content(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8Cg02Content(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8Cg03Content(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8YeDeletions(extracted);
        }
        offset += 1;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD8DefaultFundName(extracted);
        }
        offset += 30;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8DefaultFundType(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            _D8DefaultStartDateI.SetD8DefaultStartDateIAsString(data.Substring(offset, 4));
        }
        else
        {
            _D8DefaultStartDateI.SetD8DefaultStartDateIAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _D8DefaultEndDateI.SetD8DefaultEndDateIAsString(data.Substring(offset, 4));
        }
        else
        {
            _D8DefaultEndDateI.SetD8DefaultEndDateIAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _D8DefaultStartDateC.SetD8DefaultStartDateCAsString(data.Substring(offset, 4));
        }
        else
        {
            _D8DefaultStartDateC.SetD8DefaultStartDateCAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _D8DefaultEndDateC.SetD8DefaultEndDateCAsString(data.Substring(offset, 4));
        }
        else
        {
            _D8DefaultEndDateC.SetD8DefaultEndDateCAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8DefaultE1965Fi(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8DefaultE1965Ord(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8DefaultE1982(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8LastUserNo(parsedInt);
        }
        offset += 4;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8LastContractNo(parsedInt);
        }
        offset += 10;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8LastBargainNo(parsedInt);
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8StartUpYear(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8CurrentYear(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8SedolValidation(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD8PrinterTableCode(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8PrinterCurrencySign(extracted);
        }
        offset += 1;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetD8SystemPrinter(extracted);
        }
        offset += 12;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetD8LocalPrinter(extracted);
        }
        offset += 12;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8UpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 48 <= data.Length)
        {
            string extracted = data.Substring(offset, 48).Trim();
            SetD8CommsCommand(extracted);
        }
        offset += 48;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8SuppressUpdate(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8SuppressCgtr04(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8SuppressCgtr05(extracted);
        }
        offset += 1;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD8CalculationProgram(extracted);
        }
        offset += 8;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8PrintBanners(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8PrintDisclaimer(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8Filler(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8ProduceGainlossReport(extracted);
        }
        offset += 1;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetD8RealisedProgram(extracted);
        }
        offset += 12;
        if (offset + 32 <= data.Length)
        {
            _D8LastBackupDrivePath.SetD8LastBackupDrivePathAsString(data.Substring(offset, 32));
        }
        else
        {
            _D8LastBackupDrivePath.SetD8LastBackupDrivePathAsString(data.Substring(offset));
        }
        offset += 32;
        if (offset + 48 <= data.Length)
        {
            _D8StockDefaults.SetD8StockDefaultsAsString(data.Substring(offset, 48));
        }
        else
        {
            _D8StockDefaults.SetD8StockDefaultsAsString(data.Substring(offset));
        }
        offset += 48;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetD8UnrealisedProgram(extracted);
        }
        offset += 12;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8MaxNetworkUsers(parsedInt);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8OnlineAutoGenErrors(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8OnlineAutoGenRSchedule(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8BatchAutoGenErrors(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8BatchAutoGenRSchedule(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8BatchAutoGenUSchedule(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD8DefaultStockExchangeInd(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            _D8RealisedReportSelections.SetD8RealisedReportSelectionsAsString(data.Substring(offset, 1));
        }
        else
        {
            _D8RealisedReportSelections.SetD8RealisedReportSelectionsAsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8Fa1990SegregationFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8SuppressD84(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetD8HoldingFlagText(extracted);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8ReportOffsetFlag(extracted);
        }
        offset += 0;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8ReportHeaderOffsetLines(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8ReportFooterOffsetLines(parsedInt);
        }
        offset += 2;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8ThreadneedleSchedules(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8AuditTrail(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8ResultsDb(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8UsePeriodEndCalendars(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8UseLosses(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8UsePreProcessor(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8GenerateSummaryFunds(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8PolicySales(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8PolicyAcquisitions(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8PolicyCapitalEvents(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8PolicyFundTransfers(extracted);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8CobolFilesVersionNumber(parsedInt);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD8LastPendingId(parsedInt);
        }
        offset += 6;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8DeemedDisposalFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8AddLiabToNipiProceedsFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD8Spare(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD8RecordAsString();
    }
    // Set<>String Override function
    public void SetD8Record(string value)
    {
        SetD8RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D8Key GetD8Key()
    {
        return _D8Key;
    }
    
    // Standard Setter
    public void SetD8Key(D8Key value)
    {
        _D8Key = value;
    }
    
    // Get<>AsString()
    public string GetD8KeyAsString()
    {
        return _D8Key != null ? _D8Key.GetD8KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8Key == null)
        {
            _D8Key = new D8Key();
        }
        _D8Key.SetD8KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD8ProduceUSchedule()
    {
        return _D8ProduceUSchedule;
    }
    
    // Standard Setter
    public void SetD8ProduceUSchedule(string value)
    {
        _D8ProduceUSchedule = value;
    }
    
    // Get<>AsString()
    public string GetD8ProduceUScheduleAsString()
    {
        return _D8ProduceUSchedule.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8ProduceUScheduleAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8ProduceUSchedule = value;
    }
    
    // Standard Getter
    public string GetD8RealSchdContent()
    {
        return _D8RealSchdContent;
    }
    
    // Standard Setter
    public void SetD8RealSchdContent(string value)
    {
        _D8RealSchdContent = value;
    }
    
    // Get<>AsString()
    public string GetD8RealSchdContentAsString()
    {
        return _D8RealSchdContent.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD8RealSchdContentAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8RealSchdContent = value;
    }
    
    // Standard Getter
    public string GetD8UseBvForProfit()
    {
        return _D8UseBvForProfit;
    }
    
    // Standard Setter
    public void SetD8UseBvForProfit(string value)
    {
        _D8UseBvForProfit = value;
    }
    
    // Get<>AsString()
    public string GetD8UseBvForProfitAsString()
    {
        return _D8UseBvForProfit.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8UseBvForProfitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8UseBvForProfit = value;
    }
    
    // Standard Getter
    public string GetD8IndexCalcRounding()
    {
        return _D8IndexCalcRounding;
    }
    
    // Standard Setter
    public void SetD8IndexCalcRounding(string value)
    {
        _D8IndexCalcRounding = value;
    }
    
    // Get<>AsString()
    public string GetD8IndexCalcRoundingAsString()
    {
        return _D8IndexCalcRounding.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8IndexCalcRoundingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8IndexCalcRounding = value;
    }
    
    // Standard Getter
    public string GetD8Cg01Content()
    {
        return _D8Cg01Content;
    }
    
    // Standard Setter
    public void SetD8Cg01Content(string value)
    {
        _D8Cg01Content = value;
    }
    
    // Get<>AsString()
    public string GetD8Cg01ContentAsString()
    {
        return _D8Cg01Content.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8Cg01ContentAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8Cg01Content = value;
    }
    
    // Standard Getter
    public string GetD8Cg02Content()
    {
        return _D8Cg02Content;
    }
    
    // Standard Setter
    public void SetD8Cg02Content(string value)
    {
        _D8Cg02Content = value;
    }
    
    // Get<>AsString()
    public string GetD8Cg02ContentAsString()
    {
        return _D8Cg02Content.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8Cg02ContentAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8Cg02Content = value;
    }
    
    // Standard Getter
    public string GetD8Cg03Content()
    {
        return _D8Cg03Content;
    }
    
    // Standard Setter
    public void SetD8Cg03Content(string value)
    {
        _D8Cg03Content = value;
    }
    
    // Get<>AsString()
    public string GetD8Cg03ContentAsString()
    {
        return _D8Cg03Content.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8Cg03ContentAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8Cg03Content = value;
    }
    
    // Standard Getter
    public string GetD8YeDeletions()
    {
        return _D8YeDeletions;
    }
    
    // Standard Setter
    public void SetD8YeDeletions(string value)
    {
        _D8YeDeletions = value;
    }
    
    // Get<>AsString()
    public string GetD8YeDeletionsAsString()
    {
        return _D8YeDeletions.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8YeDeletionsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8YeDeletions = value;
    }
    
    // Standard Getter
    public string GetD8DefaultFundName()
    {
        return _D8DefaultFundName;
    }
    
    // Standard Setter
    public void SetD8DefaultFundName(string value)
    {
        _D8DefaultFundName = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultFundNameAsString()
    {
        return _D8DefaultFundName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD8DefaultFundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8DefaultFundName = value;
    }
    
    // Standard Getter
    public string GetD8DefaultFundType()
    {
        return _D8DefaultFundType;
    }
    
    // Standard Setter
    public void SetD8DefaultFundType(string value)
    {
        _D8DefaultFundType = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultFundTypeAsString()
    {
        return _D8DefaultFundType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8DefaultFundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8DefaultFundType = value;
    }
    
    // Standard Getter
    public D8DefaultStartDateI GetD8DefaultStartDateI()
    {
        return _D8DefaultStartDateI;
    }
    
    // Standard Setter
    public void SetD8DefaultStartDateI(D8DefaultStartDateI value)
    {
        _D8DefaultStartDateI = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultStartDateIAsString()
    {
        return _D8DefaultStartDateI != null ? _D8DefaultStartDateI.GetD8DefaultStartDateIAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8DefaultStartDateIAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8DefaultStartDateI == null)
        {
            _D8DefaultStartDateI = new D8DefaultStartDateI();
        }
        _D8DefaultStartDateI.SetD8DefaultStartDateIAsString(value);
    }
    
    // Standard Getter
    public D8DefaultEndDateI GetD8DefaultEndDateI()
    {
        return _D8DefaultEndDateI;
    }
    
    // Standard Setter
    public void SetD8DefaultEndDateI(D8DefaultEndDateI value)
    {
        _D8DefaultEndDateI = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultEndDateIAsString()
    {
        return _D8DefaultEndDateI != null ? _D8DefaultEndDateI.GetD8DefaultEndDateIAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8DefaultEndDateIAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8DefaultEndDateI == null)
        {
            _D8DefaultEndDateI = new D8DefaultEndDateI();
        }
        _D8DefaultEndDateI.SetD8DefaultEndDateIAsString(value);
    }
    
    // Standard Getter
    public D8DefaultStartDateC GetD8DefaultStartDateC()
    {
        return _D8DefaultStartDateC;
    }
    
    // Standard Setter
    public void SetD8DefaultStartDateC(D8DefaultStartDateC value)
    {
        _D8DefaultStartDateC = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultStartDateCAsString()
    {
        return _D8DefaultStartDateC != null ? _D8DefaultStartDateC.GetD8DefaultStartDateCAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8DefaultStartDateCAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8DefaultStartDateC == null)
        {
            _D8DefaultStartDateC = new D8DefaultStartDateC();
        }
        _D8DefaultStartDateC.SetD8DefaultStartDateCAsString(value);
    }
    
    // Standard Getter
    public D8DefaultEndDateC GetD8DefaultEndDateC()
    {
        return _D8DefaultEndDateC;
    }
    
    // Standard Setter
    public void SetD8DefaultEndDateC(D8DefaultEndDateC value)
    {
        _D8DefaultEndDateC = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultEndDateCAsString()
    {
        return _D8DefaultEndDateC != null ? _D8DefaultEndDateC.GetD8DefaultEndDateCAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8DefaultEndDateCAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8DefaultEndDateC == null)
        {
            _D8DefaultEndDateC = new D8DefaultEndDateC();
        }
        _D8DefaultEndDateC.SetD8DefaultEndDateCAsString(value);
    }
    
    // Standard Getter
    public string GetD8DefaultE1965Fi()
    {
        return _D8DefaultE1965Fi;
    }
    
    // Standard Setter
    public void SetD8DefaultE1965Fi(string value)
    {
        _D8DefaultE1965Fi = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultE1965FiAsString()
    {
        return _D8DefaultE1965Fi.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8DefaultE1965FiAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8DefaultE1965Fi = value;
    }
    
    // Standard Getter
    public string GetD8DefaultE1965Ord()
    {
        return _D8DefaultE1965Ord;
    }
    
    // Standard Setter
    public void SetD8DefaultE1965Ord(string value)
    {
        _D8DefaultE1965Ord = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultE1965OrdAsString()
    {
        return _D8DefaultE1965Ord.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8DefaultE1965OrdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8DefaultE1965Ord = value;
    }
    
    // Standard Getter
    public string GetD8DefaultE1982()
    {
        return _D8DefaultE1982;
    }
    
    // Standard Setter
    public void SetD8DefaultE1982(string value)
    {
        _D8DefaultE1982 = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultE1982AsString()
    {
        return _D8DefaultE1982.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8DefaultE1982AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8DefaultE1982 = value;
    }
    
    // Standard Getter
    public int GetD8LastUserNo()
    {
        return _D8LastUserNo;
    }
    
    // Standard Setter
    public void SetD8LastUserNo(int value)
    {
        _D8LastUserNo = value;
    }
    
    // Get<>AsString()
    public string GetD8LastUserNoAsString()
    {
        return _D8LastUserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD8LastUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8LastUserNo = parsed;
    }
    
    // Standard Getter
    public int GetD8LastContractNo()
    {
        return _D8LastContractNo;
    }
    
    // Standard Setter
    public void SetD8LastContractNo(int value)
    {
        _D8LastContractNo = value;
    }
    
    // Get<>AsString()
    public string GetD8LastContractNoAsString()
    {
        return _D8LastContractNo.ToString().PadLeft(10, '0');
    }
    
    // Set<>AsString()
    public void SetD8LastContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8LastContractNo = parsed;
    }
    
    // Standard Getter
    public int GetD8LastBargainNo()
    {
        return _D8LastBargainNo;
    }
    
    // Standard Setter
    public void SetD8LastBargainNo(int value)
    {
        _D8LastBargainNo = value;
    }
    
    // Get<>AsString()
    public string GetD8LastBargainNoAsString()
    {
        return _D8LastBargainNo.ToString().PadLeft(10, '0');
    }
    
    // Set<>AsString()
    public void SetD8LastBargainNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8LastBargainNo = parsed;
    }
    
    // Standard Getter
    public int GetD8StartUpYear()
    {
        return _D8StartUpYear;
    }
    
    // Standard Setter
    public void SetD8StartUpYear(int value)
    {
        _D8StartUpYear = value;
    }
    
    // Get<>AsString()
    public string GetD8StartUpYearAsString()
    {
        return _D8StartUpYear.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD8StartUpYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8StartUpYear = parsed;
    }
    
    // Standard Getter
    public int GetD8CurrentYear()
    {
        return _D8CurrentYear;
    }
    
    // Standard Setter
    public void SetD8CurrentYear(int value)
    {
        _D8CurrentYear = value;
    }
    
    // Get<>AsString()
    public string GetD8CurrentYearAsString()
    {
        return _D8CurrentYear.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD8CurrentYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8CurrentYear = parsed;
    }
    
    // Standard Getter
    public string GetD8SedolValidation()
    {
        return _D8SedolValidation;
    }
    
    // Standard Setter
    public void SetD8SedolValidation(string value)
    {
        _D8SedolValidation = value;
    }
    
    // Get<>AsString()
    public string GetD8SedolValidationAsString()
    {
        return _D8SedolValidation.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8SedolValidationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8SedolValidation = value;
    }
    
    // Standard Getter
    public string GetD8PrinterTableCode()
    {
        return _D8PrinterTableCode;
    }
    
    // Standard Setter
    public void SetD8PrinterTableCode(string value)
    {
        _D8PrinterTableCode = value;
    }
    
    // Get<>AsString()
    public string GetD8PrinterTableCodeAsString()
    {
        return _D8PrinterTableCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD8PrinterTableCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PrinterTableCode = value;
    }
    
    // Standard Getter
    public string GetD8PrinterCurrencySign()
    {
        return _D8PrinterCurrencySign;
    }
    
    // Standard Setter
    public void SetD8PrinterCurrencySign(string value)
    {
        _D8PrinterCurrencySign = value;
    }
    
    // Get<>AsString()
    public string GetD8PrinterCurrencySignAsString()
    {
        return _D8PrinterCurrencySign.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8PrinterCurrencySignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PrinterCurrencySign = value;
    }
    
    // Standard Getter
    public string GetD8SystemPrinter()
    {
        return _D8SystemPrinter;
    }
    
    // Standard Setter
    public void SetD8SystemPrinter(string value)
    {
        _D8SystemPrinter = value;
    }
    
    // Get<>AsString()
    public string GetD8SystemPrinterAsString()
    {
        return _D8SystemPrinter.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetD8SystemPrinterAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8SystemPrinter = value;
    }
    
    // Standard Getter
    public string GetD8LocalPrinter()
    {
        return _D8LocalPrinter;
    }
    
    // Standard Setter
    public void SetD8LocalPrinter(string value)
    {
        _D8LocalPrinter = value;
    }
    
    // Get<>AsString()
    public string GetD8LocalPrinterAsString()
    {
        return _D8LocalPrinter.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetD8LocalPrinterAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8LocalPrinter = value;
    }
    
    // Standard Getter
    public int GetD8UpdateCount()
    {
        return _D8UpdateCount;
    }
    
    // Standard Setter
    public void SetD8UpdateCount(int value)
    {
        _D8UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD8UpdateCountAsString()
    {
        return _D8UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD8UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8UpdateCount = parsed;
    }
    
    // Standard Getter
    public string GetD8CommsCommand()
    {
        return _D8CommsCommand;
    }
    
    // Standard Setter
    public void SetD8CommsCommand(string value)
    {
        _D8CommsCommand = value;
    }
    
    // Get<>AsString()
    public string GetD8CommsCommandAsString()
    {
        return _D8CommsCommand.PadRight(48);
    }
    
    // Set<>AsString()
    public void SetD8CommsCommandAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8CommsCommand = value;
    }
    
    // Standard Getter
    public string GetD8SuppressUpdate()
    {
        return _D8SuppressUpdate;
    }
    
    // Standard Setter
    public void SetD8SuppressUpdate(string value)
    {
        _D8SuppressUpdate = value;
    }
    
    // Get<>AsString()
    public string GetD8SuppressUpdateAsString()
    {
        return _D8SuppressUpdate.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8SuppressUpdateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8SuppressUpdate = value;
    }
    
    // Standard Getter
    public string GetD8SuppressCgtr04()
    {
        return _D8SuppressCgtr04;
    }
    
    // Standard Setter
    public void SetD8SuppressCgtr04(string value)
    {
        _D8SuppressCgtr04 = value;
    }
    
    // Get<>AsString()
    public string GetD8SuppressCgtr04AsString()
    {
        return _D8SuppressCgtr04.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8SuppressCgtr04AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8SuppressCgtr04 = value;
    }
    
    // Standard Getter
    public string GetD8SuppressCgtr05()
    {
        return _D8SuppressCgtr05;
    }
    
    // Standard Setter
    public void SetD8SuppressCgtr05(string value)
    {
        _D8SuppressCgtr05 = value;
    }
    
    // Get<>AsString()
    public string GetD8SuppressCgtr05AsString()
    {
        return _D8SuppressCgtr05.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8SuppressCgtr05AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8SuppressCgtr05 = value;
    }
    
    // Standard Getter
    public string GetD8CalculationProgram()
    {
        return _D8CalculationProgram;
    }
    
    // Standard Setter
    public void SetD8CalculationProgram(string value)
    {
        _D8CalculationProgram = value;
    }
    
    // Get<>AsString()
    public string GetD8CalculationProgramAsString()
    {
        return _D8CalculationProgram.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD8CalculationProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8CalculationProgram = value;
    }
    
    // Standard Getter
    public string GetD8PrintBanners()
    {
        return _D8PrintBanners;
    }
    
    // Standard Setter
    public void SetD8PrintBanners(string value)
    {
        _D8PrintBanners = value;
    }
    
    // Get<>AsString()
    public string GetD8PrintBannersAsString()
    {
        return _D8PrintBanners.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8PrintBannersAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PrintBanners = value;
    }
    
    // Standard Getter
    public string GetD8PrintDisclaimer()
    {
        return _D8PrintDisclaimer;
    }
    
    // Standard Setter
    public void SetD8PrintDisclaimer(string value)
    {
        _D8PrintDisclaimer = value;
    }
    
    // Get<>AsString()
    public string GetD8PrintDisclaimerAsString()
    {
        return _D8PrintDisclaimer.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8PrintDisclaimerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PrintDisclaimer = value;
    }
    
    // Standard Getter
    public string GetD8Filler()
    {
        return _D8Filler;
    }
    
    // Standard Setter
    public void SetD8Filler(string value)
    {
        _D8Filler = value;
    }
    
    // Get<>AsString()
    public string GetD8FillerAsString()
    {
        return _D8Filler.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8FillerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8Filler = value;
    }
    
    // Standard Getter
    public string GetD8ProduceGainlossReport()
    {
        return _D8ProduceGainlossReport;
    }
    
    // Standard Setter
    public void SetD8ProduceGainlossReport(string value)
    {
        _D8ProduceGainlossReport = value;
    }
    
    // Get<>AsString()
    public string GetD8ProduceGainlossReportAsString()
    {
        return _D8ProduceGainlossReport.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8ProduceGainlossReportAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8ProduceGainlossReport = value;
    }
    
    // Standard Getter
    public string GetD8RealisedProgram()
    {
        return _D8RealisedProgram;
    }
    
    // Standard Setter
    public void SetD8RealisedProgram(string value)
    {
        _D8RealisedProgram = value;
    }
    
    // Get<>AsString()
    public string GetD8RealisedProgramAsString()
    {
        return _D8RealisedProgram.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetD8RealisedProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8RealisedProgram = value;
    }
    
    // Standard Getter
    public D8LastBackupDrivePath GetD8LastBackupDrivePath()
    {
        return _D8LastBackupDrivePath;
    }
    
    // Standard Setter
    public void SetD8LastBackupDrivePath(D8LastBackupDrivePath value)
    {
        _D8LastBackupDrivePath = value;
    }
    
    // Get<>AsString()
    public string GetD8LastBackupDrivePathAsString()
    {
        return _D8LastBackupDrivePath != null ? _D8LastBackupDrivePath.GetD8LastBackupDrivePathAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8LastBackupDrivePathAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8LastBackupDrivePath == null)
        {
            _D8LastBackupDrivePath = new D8LastBackupDrivePath();
        }
        _D8LastBackupDrivePath.SetD8LastBackupDrivePathAsString(value);
    }
    
    // Standard Getter
    public D8StockDefaults GetD8StockDefaults()
    {
        return _D8StockDefaults;
    }
    
    // Standard Setter
    public void SetD8StockDefaults(D8StockDefaults value)
    {
        _D8StockDefaults = value;
    }
    
    // Get<>AsString()
    public string GetD8StockDefaultsAsString()
    {
        return _D8StockDefaults != null ? _D8StockDefaults.GetD8StockDefaultsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8StockDefaultsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8StockDefaults == null)
        {
            _D8StockDefaults = new D8StockDefaults();
        }
        _D8StockDefaults.SetD8StockDefaultsAsString(value);
    }
    
    // Standard Getter
    public string GetD8UnrealisedProgram()
    {
        return _D8UnrealisedProgram;
    }
    
    // Standard Setter
    public void SetD8UnrealisedProgram(string value)
    {
        _D8UnrealisedProgram = value;
    }
    
    // Get<>AsString()
    public string GetD8UnrealisedProgramAsString()
    {
        return _D8UnrealisedProgram.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetD8UnrealisedProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8UnrealisedProgram = value;
    }
    
    // Standard Getter
    public int GetD8MaxNetworkUsers()
    {
        return _D8MaxNetworkUsers;
    }
    
    // Standard Setter
    public void SetD8MaxNetworkUsers(int value)
    {
        _D8MaxNetworkUsers = value;
    }
    
    // Get<>AsString()
    public string GetD8MaxNetworkUsersAsString()
    {
        return _D8MaxNetworkUsers.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD8MaxNetworkUsersAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8MaxNetworkUsers = parsed;
    }
    
    // Standard Getter
    public string GetD8OnlineAutoGenErrors()
    {
        return _D8OnlineAutoGenErrors;
    }
    
    // Standard Setter
    public void SetD8OnlineAutoGenErrors(string value)
    {
        _D8OnlineAutoGenErrors = value;
    }
    
    // Get<>AsString()
    public string GetD8OnlineAutoGenErrorsAsString()
    {
        return _D8OnlineAutoGenErrors.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8OnlineAutoGenErrorsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8OnlineAutoGenErrors = value;
    }
    
    // Standard Getter
    public string GetD8OnlineAutoGenRSchedule()
    {
        return _D8OnlineAutoGenRSchedule;
    }
    
    // Standard Setter
    public void SetD8OnlineAutoGenRSchedule(string value)
    {
        _D8OnlineAutoGenRSchedule = value;
    }
    
    // Get<>AsString()
    public string GetD8OnlineAutoGenRScheduleAsString()
    {
        return _D8OnlineAutoGenRSchedule.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8OnlineAutoGenRScheduleAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8OnlineAutoGenRSchedule = value;
    }
    
    // Standard Getter
    public string GetD8BatchAutoGenErrors()
    {
        return _D8BatchAutoGenErrors;
    }
    
    // Standard Setter
    public void SetD8BatchAutoGenErrors(string value)
    {
        _D8BatchAutoGenErrors = value;
    }
    
    // Get<>AsString()
    public string GetD8BatchAutoGenErrorsAsString()
    {
        return _D8BatchAutoGenErrors.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8BatchAutoGenErrorsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8BatchAutoGenErrors = value;
    }
    
    // Standard Getter
    public string GetD8BatchAutoGenRSchedule()
    {
        return _D8BatchAutoGenRSchedule;
    }
    
    // Standard Setter
    public void SetD8BatchAutoGenRSchedule(string value)
    {
        _D8BatchAutoGenRSchedule = value;
    }
    
    // Get<>AsString()
    public string GetD8BatchAutoGenRScheduleAsString()
    {
        return _D8BatchAutoGenRSchedule.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8BatchAutoGenRScheduleAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8BatchAutoGenRSchedule = value;
    }
    
    // Standard Getter
    public string GetD8BatchAutoGenUSchedule()
    {
        return _D8BatchAutoGenUSchedule;
    }
    
    // Standard Setter
    public void SetD8BatchAutoGenUSchedule(string value)
    {
        _D8BatchAutoGenUSchedule = value;
    }
    
    // Get<>AsString()
    public string GetD8BatchAutoGenUScheduleAsString()
    {
        return _D8BatchAutoGenUSchedule.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8BatchAutoGenUScheduleAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8BatchAutoGenUSchedule = value;
    }
    
    // Standard Getter
    public string GetD8DefaultStockExchangeInd()
    {
        return _D8DefaultStockExchangeInd;
    }
    
    // Standard Setter
    public void SetD8DefaultStockExchangeInd(string value)
    {
        _D8DefaultStockExchangeInd = value;
    }
    
    // Get<>AsString()
    public string GetD8DefaultStockExchangeIndAsString()
    {
        return _D8DefaultStockExchangeInd.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD8DefaultStockExchangeIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8DefaultStockExchangeInd = value;
    }
    
    // Standard Getter
    public D8RealisedReportSelections GetD8RealisedReportSelections()
    {
        return _D8RealisedReportSelections;
    }
    
    // Standard Setter
    public void SetD8RealisedReportSelections(D8RealisedReportSelections value)
    {
        _D8RealisedReportSelections = value;
    }
    
    // Get<>AsString()
    public string GetD8RealisedReportSelectionsAsString()
    {
        return _D8RealisedReportSelections != null ? _D8RealisedReportSelections.GetD8RealisedReportSelectionsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD8RealisedReportSelectionsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D8RealisedReportSelections == null)
        {
            _D8RealisedReportSelections = new D8RealisedReportSelections();
        }
        _D8RealisedReportSelections.SetD8RealisedReportSelectionsAsString(value);
    }
    
    // Standard Getter
    public string GetD8Fa1990SegregationFlag()
    {
        return _D8Fa1990SegregationFlag;
    }
    
    // Standard Setter
    public void SetD8Fa1990SegregationFlag(string value)
    {
        _D8Fa1990SegregationFlag = value;
    }
    
    // Get<>AsString()
    public string GetD8Fa1990SegregationFlagAsString()
    {
        return _D8Fa1990SegregationFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8Fa1990SegregationFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8Fa1990SegregationFlag = value;
    }
    
    // Standard Getter
    public string GetD8SuppressD84()
    {
        return _D8SuppressD84;
    }
    
    // Standard Setter
    public void SetD8SuppressD84(string value)
    {
        _D8SuppressD84 = value;
    }
    
    // Get<>AsString()
    public string GetD8SuppressD84AsString()
    {
        return _D8SuppressD84.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8SuppressD84AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8SuppressD84 = value;
    }
    
    // Standard Getter
    public string GetD8HoldingFlagText()
    {
        return _D8HoldingFlagText;
    }
    
    // Standard Setter
    public void SetD8HoldingFlagText(string value)
    {
        _D8HoldingFlagText = value;
    }
    
    // Get<>AsString()
    public string GetD8HoldingFlagTextAsString()
    {
        return _D8HoldingFlagText.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetD8HoldingFlagTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8HoldingFlagText = value;
    }
    
    // Standard Getter
    public string GetD8ReportOffsetFlag()
    {
        return _D8ReportOffsetFlag;
    }
    
    // Standard Setter
    public void SetD8ReportOffsetFlag(string value)
    {
        _D8ReportOffsetFlag = value;
    }
    
    // Get<>AsString()
    public string GetD8ReportOffsetFlagAsString()
    {
        return _D8ReportOffsetFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8ReportOffsetFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8ReportOffsetFlag = value;
    }
    
    // Standard Getter
    public int GetD8ReportHeaderOffsetLines()
    {
        return _D8ReportHeaderOffsetLines;
    }
    
    // Standard Setter
    public void SetD8ReportHeaderOffsetLines(int value)
    {
        _D8ReportHeaderOffsetLines = value;
    }
    
    // Get<>AsString()
    public string GetD8ReportHeaderOffsetLinesAsString()
    {
        return _D8ReportHeaderOffsetLines.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD8ReportHeaderOffsetLinesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8ReportHeaderOffsetLines = parsed;
    }
    
    // Standard Getter
    public int GetD8ReportFooterOffsetLines()
    {
        return _D8ReportFooterOffsetLines;
    }
    
    // Standard Setter
    public void SetD8ReportFooterOffsetLines(int value)
    {
        _D8ReportFooterOffsetLines = value;
    }
    
    // Get<>AsString()
    public string GetD8ReportFooterOffsetLinesAsString()
    {
        return _D8ReportFooterOffsetLines.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD8ReportFooterOffsetLinesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8ReportFooterOffsetLines = parsed;
    }
    
    // Standard Getter
    public string GetD8ThreadneedleSchedules()
    {
        return _D8ThreadneedleSchedules;
    }
    
    // Standard Setter
    public void SetD8ThreadneedleSchedules(string value)
    {
        _D8ThreadneedleSchedules = value;
    }
    
    // Get<>AsString()
    public string GetD8ThreadneedleSchedulesAsString()
    {
        return _D8ThreadneedleSchedules.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8ThreadneedleSchedulesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8ThreadneedleSchedules = value;
    }
    
    // Standard Getter
    public string GetD8AuditTrail()
    {
        return _D8AuditTrail;
    }
    
    // Standard Setter
    public void SetD8AuditTrail(string value)
    {
        _D8AuditTrail = value;
    }
    
    // Get<>AsString()
    public string GetD8AuditTrailAsString()
    {
        return _D8AuditTrail.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8AuditTrailAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8AuditTrail = value;
    }
    
    // Standard Getter
    public string GetD8ResultsDb()
    {
        return _D8ResultsDb;
    }
    
    // Standard Setter
    public void SetD8ResultsDb(string value)
    {
        _D8ResultsDb = value;
    }
    
    // Get<>AsString()
    public string GetD8ResultsDbAsString()
    {
        return _D8ResultsDb.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8ResultsDbAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8ResultsDb = value;
    }
    
    // Standard Getter
    public string GetD8UsePeriodEndCalendars()
    {
        return _D8UsePeriodEndCalendars;
    }
    
    // Standard Setter
    public void SetD8UsePeriodEndCalendars(string value)
    {
        _D8UsePeriodEndCalendars = value;
    }
    
    // Get<>AsString()
    public string GetD8UsePeriodEndCalendarsAsString()
    {
        return _D8UsePeriodEndCalendars.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8UsePeriodEndCalendarsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8UsePeriodEndCalendars = value;
    }
    
    // Standard Getter
    public string GetD8UseLosses()
    {
        return _D8UseLosses;
    }
    
    // Standard Setter
    public void SetD8UseLosses(string value)
    {
        _D8UseLosses = value;
    }
    
    // Get<>AsString()
    public string GetD8UseLossesAsString()
    {
        return _D8UseLosses.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8UseLossesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8UseLosses = value;
    }
    
    // Standard Getter
    public string GetD8UsePreProcessor()
    {
        return _D8UsePreProcessor;
    }
    
    // Standard Setter
    public void SetD8UsePreProcessor(string value)
    {
        _D8UsePreProcessor = value;
    }
    
    // Get<>AsString()
    public string GetD8UsePreProcessorAsString()
    {
        return _D8UsePreProcessor.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8UsePreProcessorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8UsePreProcessor = value;
    }
    
    // Standard Getter
    public string GetD8GenerateSummaryFunds()
    {
        return _D8GenerateSummaryFunds;
    }
    
    // Standard Setter
    public void SetD8GenerateSummaryFunds(string value)
    {
        _D8GenerateSummaryFunds = value;
    }
    
    // Get<>AsString()
    public string GetD8GenerateSummaryFundsAsString()
    {
        return _D8GenerateSummaryFunds.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8GenerateSummaryFundsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8GenerateSummaryFunds = value;
    }
    
    // Standard Getter
    public string GetD8PolicySales()
    {
        return _D8PolicySales;
    }
    
    // Standard Setter
    public void SetD8PolicySales(string value)
    {
        _D8PolicySales = value;
    }
    
    // Get<>AsString()
    public string GetD8PolicySalesAsString()
    {
        return _D8PolicySales.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8PolicySalesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PolicySales = value;
    }
    
    // Standard Getter
    public string GetD8PolicyAcquisitions()
    {
        return _D8PolicyAcquisitions;
    }
    
    // Standard Setter
    public void SetD8PolicyAcquisitions(string value)
    {
        _D8PolicyAcquisitions = value;
    }
    
    // Get<>AsString()
    public string GetD8PolicyAcquisitionsAsString()
    {
        return _D8PolicyAcquisitions.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8PolicyAcquisitionsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PolicyAcquisitions = value;
    }
    
    // Standard Getter
    public string GetD8PolicyCapitalEvents()
    {
        return _D8PolicyCapitalEvents;
    }
    
    // Standard Setter
    public void SetD8PolicyCapitalEvents(string value)
    {
        _D8PolicyCapitalEvents = value;
    }
    
    // Get<>AsString()
    public string GetD8PolicyCapitalEventsAsString()
    {
        return _D8PolicyCapitalEvents.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8PolicyCapitalEventsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PolicyCapitalEvents = value;
    }
    
    // Standard Getter
    public string GetD8PolicyFundTransfers()
    {
        return _D8PolicyFundTransfers;
    }
    
    // Standard Setter
    public void SetD8PolicyFundTransfers(string value)
    {
        _D8PolicyFundTransfers = value;
    }
    
    // Get<>AsString()
    public string GetD8PolicyFundTransfersAsString()
    {
        return _D8PolicyFundTransfers.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8PolicyFundTransfersAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8PolicyFundTransfers = value;
    }
    
    // Standard Getter
    public int GetD8CobolFilesVersionNumber()
    {
        return _D8CobolFilesVersionNumber;
    }
    
    // Standard Setter
    public void SetD8CobolFilesVersionNumber(int value)
    {
        _D8CobolFilesVersionNumber = value;
    }
    
    // Get<>AsString()
    public string GetD8CobolFilesVersionNumberAsString()
    {
        return _D8CobolFilesVersionNumber.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD8CobolFilesVersionNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8CobolFilesVersionNumber = parsed;
    }
    
    // Standard Getter
    public int GetD8LastPendingId()
    {
        return _D8LastPendingId;
    }
    
    // Standard Setter
    public void SetD8LastPendingId(int value)
    {
        _D8LastPendingId = value;
    }
    
    // Get<>AsString()
    public string GetD8LastPendingIdAsString()
    {
        return _D8LastPendingId.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD8LastPendingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D8LastPendingId = parsed;
    }
    
    // Standard Getter
    public string GetD8DeemedDisposalFlag()
    {
        return _D8DeemedDisposalFlag;
    }
    
    // Standard Setter
    public void SetD8DeemedDisposalFlag(string value)
    {
        _D8DeemedDisposalFlag = value;
    }
    
    // Get<>AsString()
    public string GetD8DeemedDisposalFlagAsString()
    {
        return _D8DeemedDisposalFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8DeemedDisposalFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8DeemedDisposalFlag = value;
    }
    
    // Standard Getter
    public string GetD8AddLiabToNipiProceedsFlag()
    {
        return _D8AddLiabToNipiProceedsFlag;
    }
    
    // Standard Setter
    public void SetD8AddLiabToNipiProceedsFlag(string value)
    {
        _D8AddLiabToNipiProceedsFlag = value;
    }
    
    // Get<>AsString()
    public string GetD8AddLiabToNipiProceedsFlagAsString()
    {
        return _D8AddLiabToNipiProceedsFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8AddLiabToNipiProceedsFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8AddLiabToNipiProceedsFlag = value;
    }
    
    // Standard Getter
    public string GetD8Spare()
    {
        return _D8Spare;
    }
    
    // Standard Setter
    public void SetD8Spare(string value)
    {
        _D8Spare = value;
    }
    
    // Get<>AsString()
    public string GetD8SpareAsString()
    {
        return _D8Spare.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD8SpareAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8Spare = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD8Key(string value)
    {
        _D8Key.SetD8KeyAsString(value);
    }
    // Nested Class: D8Key
    public class D8Key
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D8ParamKey, is_external=, is_static_class=False, static_prefix=
        private string _D8ParamKey ="";
        
        
        
        
    public D8Key() {}
    
    public D8Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD8ParamKey(data.Substring(offset, 3).Trim());
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetD8KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D8ParamKey.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetD8KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD8ParamKey(extracted);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD8ParamKey()
    {
        return _D8ParamKey;
    }
    
    // Standard Setter
    public void SetD8ParamKey(string value)
    {
        _D8ParamKey = value;
    }
    
    // Get<>AsString()
    public string GetD8ParamKeyAsString()
    {
        return _D8ParamKey.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD8ParamKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D8ParamKey = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD8DefaultStartDateI(string value)
{
    _D8DefaultStartDateI.SetD8DefaultStartDateIAsString(value);
}
// Nested Class: D8DefaultStartDateI
public class D8DefaultStartDateI
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D8StartDateIMm, is_external=, is_static_class=False, static_prefix=
    private int _D8StartDateIMm =0;
    
    
    
    
    // [DEBUG] Field: D8StartDateIDd, is_external=, is_static_class=False, static_prefix=
    private int _D8StartDateIDd =0;
    
    
    
    
public D8DefaultStartDateI() {}

public D8DefaultStartDateI(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD8StartDateIMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD8StartDateIDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD8DefaultStartDateIAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D8StartDateIMm.ToString().PadLeft(2, '0'));
    result.Append(_D8StartDateIDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD8DefaultStartDateIAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8StartDateIMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8StartDateIDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD8StartDateIMm()
{
    return _D8StartDateIMm;
}

// Standard Setter
public void SetD8StartDateIMm(int value)
{
    _D8StartDateIMm = value;
}

// Get<>AsString()
public string GetD8StartDateIMmAsString()
{
    return _D8StartDateIMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8StartDateIMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8StartDateIMm = parsed;
}

// Standard Getter
public int GetD8StartDateIDd()
{
    return _D8StartDateIDd;
}

// Standard Setter
public void SetD8StartDateIDd(int value)
{
    _D8StartDateIDd = value;
}

// Get<>AsString()
public string GetD8StartDateIDdAsString()
{
    return _D8StartDateIDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8StartDateIDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8StartDateIDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD8DefaultEndDateI(string value)
{
    _D8DefaultEndDateI.SetD8DefaultEndDateIAsString(value);
}
// Nested Class: D8DefaultEndDateI
public class D8DefaultEndDateI
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D8EndDateIMm, is_external=, is_static_class=False, static_prefix=
    private int _D8EndDateIMm =0;
    
    
    
    
    // [DEBUG] Field: D8EndDateIDd, is_external=, is_static_class=False, static_prefix=
    private int _D8EndDateIDd =0;
    
    
    
    
public D8DefaultEndDateI() {}

public D8DefaultEndDateI(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD8EndDateIMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD8EndDateIDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD8DefaultEndDateIAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D8EndDateIMm.ToString().PadLeft(2, '0'));
    result.Append(_D8EndDateIDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD8DefaultEndDateIAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8EndDateIMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8EndDateIDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD8EndDateIMm()
{
    return _D8EndDateIMm;
}

// Standard Setter
public void SetD8EndDateIMm(int value)
{
    _D8EndDateIMm = value;
}

// Get<>AsString()
public string GetD8EndDateIMmAsString()
{
    return _D8EndDateIMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8EndDateIMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8EndDateIMm = parsed;
}

// Standard Getter
public int GetD8EndDateIDd()
{
    return _D8EndDateIDd;
}

// Standard Setter
public void SetD8EndDateIDd(int value)
{
    _D8EndDateIDd = value;
}

// Get<>AsString()
public string GetD8EndDateIDdAsString()
{
    return _D8EndDateIDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8EndDateIDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8EndDateIDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD8DefaultStartDateC(string value)
{
    _D8DefaultStartDateC.SetD8DefaultStartDateCAsString(value);
}
// Nested Class: D8DefaultStartDateC
public class D8DefaultStartDateC
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D8StartDateCMm, is_external=, is_static_class=False, static_prefix=
    private int _D8StartDateCMm =0;
    
    
    
    
    // [DEBUG] Field: D8StartDateCDd, is_external=, is_static_class=False, static_prefix=
    private int _D8StartDateCDd =0;
    
    
    
    
public D8DefaultStartDateC() {}

public D8DefaultStartDateC(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD8StartDateCMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD8StartDateCDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD8DefaultStartDateCAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D8StartDateCMm.ToString().PadLeft(2, '0'));
    result.Append(_D8StartDateCDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD8DefaultStartDateCAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8StartDateCMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8StartDateCDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD8StartDateCMm()
{
    return _D8StartDateCMm;
}

// Standard Setter
public void SetD8StartDateCMm(int value)
{
    _D8StartDateCMm = value;
}

// Get<>AsString()
public string GetD8StartDateCMmAsString()
{
    return _D8StartDateCMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8StartDateCMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8StartDateCMm = parsed;
}

// Standard Getter
public int GetD8StartDateCDd()
{
    return _D8StartDateCDd;
}

// Standard Setter
public void SetD8StartDateCDd(int value)
{
    _D8StartDateCDd = value;
}

// Get<>AsString()
public string GetD8StartDateCDdAsString()
{
    return _D8StartDateCDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8StartDateCDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8StartDateCDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD8DefaultEndDateC(string value)
{
    _D8DefaultEndDateC.SetD8DefaultEndDateCAsString(value);
}
// Nested Class: D8DefaultEndDateC
public class D8DefaultEndDateC
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D8EndDateCMm, is_external=, is_static_class=False, static_prefix=
    private int _D8EndDateCMm =0;
    
    
    
    
    // [DEBUG] Field: D8EndDateCDd, is_external=, is_static_class=False, static_prefix=
    private int _D8EndDateCDd =0;
    
    
    
    
public D8DefaultEndDateC() {}

public D8DefaultEndDateC(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD8EndDateCMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD8EndDateCDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD8DefaultEndDateCAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D8EndDateCMm.ToString().PadLeft(2, '0'));
    result.Append(_D8EndDateCDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD8DefaultEndDateCAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8EndDateCMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD8EndDateCDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD8EndDateCMm()
{
    return _D8EndDateCMm;
}

// Standard Setter
public void SetD8EndDateCMm(int value)
{
    _D8EndDateCMm = value;
}

// Get<>AsString()
public string GetD8EndDateCMmAsString()
{
    return _D8EndDateCMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8EndDateCMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8EndDateCMm = parsed;
}

// Standard Getter
public int GetD8EndDateCDd()
{
    return _D8EndDateCDd;
}

// Standard Setter
public void SetD8EndDateCDd(int value)
{
    _D8EndDateCDd = value;
}

// Get<>AsString()
public string GetD8EndDateCDdAsString()
{
    return _D8EndDateCDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD8EndDateCDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D8EndDateCDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD8LastBackupDrivePath(string value)
{
    _D8LastBackupDrivePath.SetD8LastBackupDrivePathAsString(value);
}
// Nested Class: D8LastBackupDrivePath
public class D8LastBackupDrivePath
{
    private static int _size = 32;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D8LastBackupDrive, is_external=, is_static_class=False, static_prefix=
    private string _D8LastBackupDrive ="";
    
    
    
    
    // [DEBUG] Field: D8LastBackupPath, is_external=, is_static_class=False, static_prefix=
    private string _D8LastBackupPath ="";
    
    
    
    
public D8LastBackupDrivePath() {}

public D8LastBackupDrivePath(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD8LastBackupDrive(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD8LastBackupPath(data.Substring(offset, 30).Trim());
    offset += 30;
    
}

// Serialization methods
public string GetD8LastBackupDrivePathAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D8LastBackupDrive.PadRight(2));
    result.Append(_D8LastBackupPath.PadRight(30));
    
    return result.ToString();
}

public void SetD8LastBackupDrivePathAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD8LastBackupDrive(extracted);
    }
    offset += 2;
    if (offset + 30 <= data.Length)
    {
        string extracted = data.Substring(offset, 30).Trim();
        SetD8LastBackupPath(extracted);
    }
    offset += 30;
}

// Getter and Setter methods

// Standard Getter
public string GetD8LastBackupDrive()
{
    return _D8LastBackupDrive;
}

// Standard Setter
public void SetD8LastBackupDrive(string value)
{
    _D8LastBackupDrive = value;
}

// Get<>AsString()
public string GetD8LastBackupDriveAsString()
{
    return _D8LastBackupDrive.PadRight(2);
}

// Set<>AsString()
public void SetD8LastBackupDriveAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8LastBackupDrive = value;
}

// Standard Getter
public string GetD8LastBackupPath()
{
    return _D8LastBackupPath;
}

// Standard Setter
public void SetD8LastBackupPath(string value)
{
    _D8LastBackupPath = value;
}

// Get<>AsString()
public string GetD8LastBackupPathAsString()
{
    return _D8LastBackupPath.PadRight(30);
}

// Set<>AsString()
public void SetD8LastBackupPathAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8LastBackupPath = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD8StockDefaults(string value)
{
    _D8StockDefaults.SetD8StockDefaultsAsString(value);
}
// Nested Class: D8StockDefaults
public class D8StockDefaults
{
    private static int _size = 48;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D8DefaultStockText, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultStockText ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultStockType, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultStockType ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultStockCountry, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultStockCountry ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultStockGroup, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultStockGroup ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultStockQuoted, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultStockQuoted ="";
    
    
    
    
    // [DEBUG] Field: D8DefaultStockPpi, is_external=, is_static_class=False, static_prefix=
    private string _D8DefaultStockPpi ="";
    
    
    
    
public D8StockDefaults() {}

public D8StockDefaults(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD8DefaultStockText(data.Substring(offset, 40).Trim());
    offset += 40;
    SetD8DefaultStockType(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD8DefaultStockCountry(data.Substring(offset, 3).Trim());
    offset += 3;
    SetD8DefaultStockGroup(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD8DefaultStockQuoted(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD8DefaultStockPpi(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetD8StockDefaultsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D8DefaultStockText.PadRight(40));
    result.Append(_D8DefaultStockType.PadRight(1));
    result.Append(_D8DefaultStockCountry.PadRight(3));
    result.Append(_D8DefaultStockGroup.PadRight(2));
    result.Append(_D8DefaultStockQuoted.PadRight(1));
    result.Append(_D8DefaultStockPpi.PadRight(1));
    
    return result.ToString();
}

public void SetD8StockDefaultsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 40 <= data.Length)
    {
        string extracted = data.Substring(offset, 40).Trim();
        SetD8DefaultStockText(extracted);
    }
    offset += 40;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD8DefaultStockType(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetD8DefaultStockCountry(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD8DefaultStockGroup(extracted);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD8DefaultStockQuoted(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD8DefaultStockPpi(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD8DefaultStockText()
{
    return _D8DefaultStockText;
}

// Standard Setter
public void SetD8DefaultStockText(string value)
{
    _D8DefaultStockText = value;
}

// Get<>AsString()
public string GetD8DefaultStockTextAsString()
{
    return _D8DefaultStockText.PadRight(40);
}

// Set<>AsString()
public void SetD8DefaultStockTextAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8DefaultStockText = value;
}

// Standard Getter
public string GetD8DefaultStockType()
{
    return _D8DefaultStockType;
}

// Standard Setter
public void SetD8DefaultStockType(string value)
{
    _D8DefaultStockType = value;
}

// Get<>AsString()
public string GetD8DefaultStockTypeAsString()
{
    return _D8DefaultStockType.PadRight(1);
}

// Set<>AsString()
public void SetD8DefaultStockTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8DefaultStockType = value;
}

// Standard Getter
public string GetD8DefaultStockCountry()
{
    return _D8DefaultStockCountry;
}

// Standard Setter
public void SetD8DefaultStockCountry(string value)
{
    _D8DefaultStockCountry = value;
}

// Get<>AsString()
public string GetD8DefaultStockCountryAsString()
{
    return _D8DefaultStockCountry.PadRight(3);
}

// Set<>AsString()
public void SetD8DefaultStockCountryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8DefaultStockCountry = value;
}

// Standard Getter
public string GetD8DefaultStockGroup()
{
    return _D8DefaultStockGroup;
}

// Standard Setter
public void SetD8DefaultStockGroup(string value)
{
    _D8DefaultStockGroup = value;
}

// Get<>AsString()
public string GetD8DefaultStockGroupAsString()
{
    return _D8DefaultStockGroup.PadRight(2);
}

// Set<>AsString()
public void SetD8DefaultStockGroupAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8DefaultStockGroup = value;
}

// Standard Getter
public string GetD8DefaultStockQuoted()
{
    return _D8DefaultStockQuoted;
}

// Standard Setter
public void SetD8DefaultStockQuoted(string value)
{
    _D8DefaultStockQuoted = value;
}

// Get<>AsString()
public string GetD8DefaultStockQuotedAsString()
{
    return _D8DefaultStockQuoted.PadRight(1);
}

// Set<>AsString()
public void SetD8DefaultStockQuotedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8DefaultStockQuoted = value;
}

// Standard Getter
public string GetD8DefaultStockPpi()
{
    return _D8DefaultStockPpi;
}

// Standard Setter
public void SetD8DefaultStockPpi(string value)
{
    _D8DefaultStockPpi = value;
}

// Get<>AsString()
public string GetD8DefaultStockPpiAsString()
{
    return _D8DefaultStockPpi.PadRight(1);
}

// Set<>AsString()
public void SetD8DefaultStockPpiAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D8DefaultStockPpi = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD8RealisedReportSelections(string value)
{
    _D8RealisedReportSelections.SetD8RealisedReportSelectionsAsString(value);
}
// Nested Class: D8RealisedReportSelections
public class D8RealisedReportSelections
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D8ReportSelection, is_external=, is_static_class=False, static_prefix=
    private string[] _D8ReportSelection = new string[8];
    
    
    
    
public D8RealisedReportSelections() {}

public D8RealisedReportSelections(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 8; i++)
    {
        string value = data.Substring(offset, 1);
        _D8ReportSelection[i] = value.Trim();
        offset += 1;
    }
    
}

// Serialization methods
public string GetD8RealisedReportSelectionsAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 8; i++)
    {
        result.Append(_D8ReportSelection[i].PadRight(1));
    }
    
    return result.ToString();
}

public void SetD8RealisedReportSelectionsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 8; i++)
    {
        if (offset + 1 > data.Length) break;
        string val = data.Substring(offset, 1);
        
        _D8ReportSelection[i] = val.Trim();
        offset += 1;
    }
}

// Getter and Setter methods

// Array Accessors for D8ReportSelection
public string GetD8ReportSelectionAt(int index)
{
    return _D8ReportSelection[index];
}

public void SetD8ReportSelectionAt(int index, string value)
{
    _D8ReportSelection[index] = value;
}

public string GetD8ReportSelectionAsStringAt(int index)
{
    return _D8ReportSelection[index].PadRight(1);
}

public void SetD8ReportSelectionAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _D8ReportSelection[index] = value;
}

// Flattened accessors (index 0)
public string GetD8ReportSelection()
{
    return _D8ReportSelection != null && _D8ReportSelection.Length > 0
    ? _D8ReportSelection[0]
    : default(string);
}

public void SetD8ReportSelection(string value)
{
    if (_D8ReportSelection == null || _D8ReportSelection.Length == 0)
    _D8ReportSelection = new string[1];
    _D8ReportSelection[0] = value;
}

public string GetD8ReportSelectionAsString()
{
    return _D8ReportSelection != null && _D8ReportSelection.Length > 0
    ? _D8ReportSelection[0].ToString()
    : string.Empty;
}

public void SetD8ReportSelectionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_D8ReportSelection == null || _D8ReportSelection.Length == 0)
    _D8ReportSelection = new string[1];
    
    _D8ReportSelection[0] = value;
}




public static int GetSize()
{
    return _size;
}

}

}}
