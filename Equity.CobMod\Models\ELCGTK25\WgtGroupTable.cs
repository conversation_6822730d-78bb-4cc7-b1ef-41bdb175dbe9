using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing WgtGroupTable Data Structure

public class WgtGroupTable
{
    private static int _size = 43000;
    // [DEBUG] Class: WgtGroupTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WgtEntry, is_external=, is_static_class=False, static_prefix=
    private WgtEntry[] _WgtEntry = new WgtEntry[1000];
    
    public void InitializeWgtEntryArray()
    {
        for (int i = 0; i < 1000; i++)
        {
            _WgtEntry[i] = new WgtEntry();
        }
    }
    
    
    
    
    // Serialization methods
    public string GetWgtGroupTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 1000; i++)
        {
            result.Append(_WgtEntry[i].GetWgtEntryAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWgtGroupTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 1000; i++)
        {
            if (offset + 43 > data.Length) break;
            string val = data.Substring(offset, 43);
            
            _WgtEntry[i].SetWgtEntryAsString(val);
            offset += 43;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWgtGroupTableAsString();
    }
    // Set<>String Override function
    public void SetWgtGroupTable(string value)
    {
        SetWgtGroupTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for WgtEntry
    public WgtEntry GetWgtEntryAt(int index)
    {
        return _WgtEntry[index];
    }
    
    public void SetWgtEntryAt(int index, WgtEntry value)
    {
        _WgtEntry[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WgtEntry GetWgtEntry()
    {
        return _WgtEntry != null && _WgtEntry.Length > 0
        ? _WgtEntry[0]
        : new WgtEntry();
    }
    
    public void SetWgtEntry(WgtEntry value)
    {
        if (_WgtEntry == null || _WgtEntry.Length == 0)
        _WgtEntry = new WgtEntry[1];
        _WgtEntry[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWgtEntry(string value)
    {
        if (!string.IsNullOrEmpty(value) && value.Length < WgtEntry.GetSize() * _WgtEntry.Length)
        {
            value = value.PadRight(WgtEntry.GetSize() * _WgtEntry.Length);
        }
        
        int offset = 0;
        for (int i = 0; i < _WgtEntry.Length; i++)
        {
            if (offset + WgtEntry.GetSize() > value.Length) break;
            string chunk = value.Substring(offset, WgtEntry.GetSize());
            _WgtEntry[i].SetWgtEntryAsString(chunk);
            offset += WgtEntry.GetSize();
        }
    }
    // Nested Class: WgtEntry
    public class WgtEntry
    {
        private static int _size = 43;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WgtGroup, is_external=, is_static_class=False, static_prefix=
        private WgtEntry.WgtGroup _WgtGroup = new WgtEntry.WgtGroup();
        
        
        
        
        // [DEBUG] Field: WgtGroupName, is_external=, is_static_class=False, static_prefix=
        private WgtEntry.WgtGroupName _WgtGroupName = new WgtEntry.WgtGroupName();
        
        
        
        
    public WgtEntry() {}
    
    public WgtEntry(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WgtGroup.SetWgtGroupAsString(data.Substring(offset, WgtGroup.GetSize()));
        offset += 3;
        _WgtGroupName.SetWgtGroupNameAsString(data.Substring(offset, WgtGroupName.GetSize()));
        offset += 40;
        
    }
    
    // Serialization methods
    public string GetWgtEntryAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WgtGroup.GetWgtGroupAsString());
        result.Append(_WgtGroupName.GetWgtGroupNameAsString());
        
        return result.ToString();
    }
    
    public void SetWgtEntryAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            _WgtGroup.SetWgtGroupAsString(data.Substring(offset, 3));
        }
        else
        {
            _WgtGroup.SetWgtGroupAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 40 <= data.Length)
        {
            _WgtGroupName.SetWgtGroupNameAsString(data.Substring(offset, 40));
        }
        else
        {
            _WgtGroupName.SetWgtGroupNameAsString(data.Substring(offset));
        }
        offset += 40;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WgtGroup GetWgtGroup()
    {
        return _WgtGroup;
    }
    
    // Standard Setter
    public void SetWgtGroup(WgtGroup value)
    {
        _WgtGroup = value;
    }
    
    // Get<>AsString()
    public string GetWgtGroupAsString()
    {
        return _WgtGroup != null ? _WgtGroup.GetWgtGroupAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWgtGroupAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WgtGroup == null)
        {
            _WgtGroup = new WgtGroup();
        }
        _WgtGroup.SetWgtGroupAsString(value);
    }
    
    // Standard Getter
    public WgtGroupName GetWgtGroupName()
    {
        return _WgtGroupName;
    }
    
    // Standard Setter
    public void SetWgtGroupName(WgtGroupName value)
    {
        _WgtGroupName = value;
    }
    
    // Get<>AsString()
    public string GetWgtGroupNameAsString()
    {
        return _WgtGroupName != null ? _WgtGroupName.GetWgtGroupNameAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWgtGroupNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WgtGroupName == null)
        {
            _WgtGroupName = new WgtGroupName();
        }
        _WgtGroupName.SetWgtGroupNameAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WgtGroup
    public class WgtGroup
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler93, is_external=, is_static_class=False, static_prefix=
        private string _Filler93 ="";
        
        
        
        
        // [DEBUG] Field: WgtGroupLast, is_external=, is_static_class=False, static_prefix=
        private string _WgtGroupLast ="";
        
        
        
        
    public WgtGroup() {}
    
    public WgtGroup(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller93(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWgtGroupLast(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWgtGroupAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler93.PadRight(1));
        result.Append(_WgtGroupLast.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetWgtGroupAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller93(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWgtGroupLast(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller93()
    {
        return _Filler93;
    }
    
    // Standard Setter
    public void SetFiller93(string value)
    {
        _Filler93 = value;
    }
    
    // Get<>AsString()
    public string GetFiller93AsString()
    {
        return _Filler93.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller93AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler93 = value;
    }
    
    // Standard Getter
    public string GetWgtGroupLast()
    {
        return _WgtGroupLast;
    }
    
    // Standard Setter
    public void SetWgtGroupLast(string value)
    {
        _WgtGroupLast = value;
    }
    
    // Get<>AsString()
    public string GetWgtGroupLastAsString()
    {
        return _WgtGroupLast.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWgtGroupLastAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WgtGroupLast = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: WgtGroupName
public class WgtGroupName
{
    private static int _size = 40;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WgtGroupNameSpaces, is_external=, is_static_class=False, static_prefix=
    private string _WgtGroupNameSpaces ="";
    
    
    
    
    // [DEBUG] Field: Filler94, is_external=, is_static_class=False, static_prefix=
    private string _Filler94 ="";
    
    
    
    
public WgtGroupName() {}

public WgtGroupName(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWgtGroupNameSpaces(data.Substring(offset, 27).Trim());
    offset += 27;
    SetFiller94(data.Substring(offset, 13).Trim());
    offset += 13;
    
}

// Serialization methods
public string GetWgtGroupNameAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WgtGroupNameSpaces.PadRight(27));
    result.Append(_Filler94.PadRight(13));
    
    return result.ToString();
}

public void SetWgtGroupNameAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 27 <= data.Length)
    {
        string extracted = data.Substring(offset, 27).Trim();
        SetWgtGroupNameSpaces(extracted);
    }
    offset += 27;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller94(extracted);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public string GetWgtGroupNameSpaces()
{
    return _WgtGroupNameSpaces;
}

// Standard Setter
public void SetWgtGroupNameSpaces(string value)
{
    _WgtGroupNameSpaces = value;
}

// Get<>AsString()
public string GetWgtGroupNameSpacesAsString()
{
    return _WgtGroupNameSpaces.PadRight(27);
}

// Set<>AsString()
public void SetWgtGroupNameSpacesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WgtGroupNameSpaces = value;
}

// Standard Getter
public string GetFiller94()
{
    return _Filler94;
}

// Standard Setter
public void SetFiller94(string value)
{
    _Filler94 = value;
}

// Get<>AsString()
public string GetFiller94AsString()
{
    return _Filler94.PadRight(13);
}

// Set<>AsString()
public void SetFiller94AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler94 = value;
}



public static int GetSize()
{
    return _size;
}

}
}

}}
