using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WttOutputRecord Data Structure

public class WttOutputRecord
{
    private static int _size = 16761;
    // [DEBUG] Class: WttOutputRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WttRecord, is_external=, is_static_class=False, static_prefix=
    private WttRecord _WttRecord = new WttRecord();
    
    
    
    
    
    // Serialization methods
    public string GetWttOutputRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttRecord.GetWttRecordAsString());
        
        return result.ToString();
    }
    
    public void SetWttOutputRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16761 <= data.Length)
        {
            _WttRecord.SetWttRecordAsString(data.Substring(offset, 16761));
        }
        else
        {
            _WttRecord.SetWttRecordAsString(data.Substring(offset));
        }
        offset += 16761;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWttOutputRecordAsString();
    }
    // Set<>String Override function
    public void SetWttOutputRecord(string value)
    {
        SetWttOutputRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WttRecord GetWttRecord()
    {
        return _WttRecord;
    }
    
    // Standard Setter
    public void SetWttRecord(WttRecord value)
    {
        _WttRecord = value;
    }
    
    // Get<>AsString()
    public string GetWttRecordAsString()
    {
        return _WttRecord != null ? _WttRecord.GetWttRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttRecord == null)
        {
            _WttRecord = new WttRecord();
        }
        _WttRecord.SetWttRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWttRecord(string value)
    {
        _WttRecord.SetWttRecordAsString(value);
    }
    // Nested Class: WttRecord
    public class WttRecord
    {
        private static int _size = 16761;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttKey, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttKey _WttKey = new WttRecord.WttKey();
        
        
        
        
        // [DEBUG] Field: WttTransactionCategory, is_external=, is_static_class=False, static_prefix=
        private string _WttTransactionCategory ="";
        
        
        // 88-level condition checks for WttTransactionCategory
        public bool IsWttIdentifiableTran()
        {
            if (this._WttTransactionCategory == "'00'") return true;
            if (this._WttTransactionCategory == "'SP'") return true;
            if (this._WttTransactionCategory == "'PP'") return true;
            if (this._WttTransactionCategory == "'PI'") return true;
            if (this._WttTransactionCategory == "'TB'") return true;
            if (this._WttTransactionCategory == "'NP'") return true;
            if (this._WttTransactionCategory == "'NI'") return true;
            if (this._WttTransactionCategory == "'RP'") return true;
            if (this._WttTransactionCategory == "'UL'") return true;
            if (this._WttTransactionCategory == "'01'") return true;
            if (this._WttTransactionCategory == "'SX'") return true;
            return false;
        }
        public bool IsWttApportionedTran()
        {
            if (this._WttTransactionCategory == "'RG'") return true;
            if (this._WttTransactionCategory == "'CN'") return true;
            if (this._WttTransactionCategory == "'TR'") return true;
            if (this._WttTransactionCategory == "'TS'") return true;
            if (this._WttTransactionCategory == "'CS'") return true;
            if (this._WttTransactionCategory == "'CD'") return true;
            if (this._WttTransactionCategory == "'RF'") return true;
            return false;
        }
        public bool IsWttApportionedTransfer()
        {
            if (this._WttTransactionCategory == "'RG'") return true;
            if (this._WttTransactionCategory == "'CN'") return true;
            if (this._WttTransactionCategory == "'TR'") return true;
            if (this._WttTransactionCategory == "'TS'") return true;
            if (this._WttTransactionCategory == "'CS'") return true;
            return false;
        }
        public bool IsWttTransToBeConsolidated()
        {
            if (this._WttTransactionCategory == "'RG'") return true;
            if (this._WttTransactionCategory == "'CN'") return true;
            if (this._WttTransactionCategory == "'TR'") return true;
            if (this._WttTransactionCategory == "'TS'") return true;
            if (this._WttTransactionCategory == "'CS'") return true;
            if (this._WttTransactionCategory == "'NS'") return true;
            if (this._WttTransactionCategory == "'TP'") return true;
            if (this._WttTransactionCategory == "'CP'") return true;
            if (this._WttTransactionCategory == "'GP'") return true;
            if (this._WttTransactionCategory == "'GS'") return true;
            if (this._WttTransactionCategory == "'RS'") return true;
            if (this._WttTransactionCategory == "'RL'") return true;
            if (this._WttTransactionCategory == "'RD'") return true;
            if (this._WttTransactionCategory == "'EQ'") return true;
            if (this._WttTransactionCategory == "'CD'") return true;
            if (this._WttTransactionCategory == "'LQ'") return true;
            if (this._WttTransactionCategory == "'RF'") return true;
            if (this._WttTransactionCategory == "'SS'") return true;
            return false;
        }
        public bool IsWttDisposalTran()
        {
            if (this._WttTransactionCategory == "'RS'") return true;
            if (this._WttTransactionCategory == "'RL'") return true;
            if (this._WttTransactionCategory == "'RD'") return true;
            if (this._WttTransactionCategory == "'NS'") return true;
            if (this._WttTransactionCategory == "'CD'") return true;
            if (this._WttTransactionCategory == "'LQ'") return true;
            if (this._WttTransactionCategory == "'RF'") return true;
            if (this._WttTransactionCategory == "'SS'") return true;
            return false;
        }
        public bool IsWttTransLinkedByBargain()
        {
            if (this._WttTransactionCategory == "'TS'") return true;
            if (this._WttTransactionCategory == "'CS'") return true;
            if (this._WttTransactionCategory == "'TP'") return true;
            if (this._WttTransactionCategory == "'CP'") return true;
            if (this._WttTransactionCategory == "'GP'") return true;
            if (this._WttTransactionCategory == "'GS'") return true;
            return false;
        }
        public bool IsWttTransLinkedByPrevRef()
        {
            if (this._WttTransactionCategory == "'RG'") return true;
            if (this._WttTransactionCategory == "'CN'") return true;
            if (this._WttTransactionCategory == "'TR'") return true;
            if (this._WttTransactionCategory == "'CL'") return true;
            if (this._WttTransactionCategory == "'RC'") return true;
            if (this._WttTransactionCategory == "'RR'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttDateTimeStamp, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttDateTimeStamp _WttDateTimeStamp = new WttRecord.WttDateTimeStamp();
        
        
        
        
        // [DEBUG] Field: Filler213, is_external=, is_static_class=False, static_prefix=
        private WttRecord.Filler213 _Filler213 = new WttRecord.Filler213();
        
        
        
        
        // [DEBUG] Field: WttCalParentSedolCode, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttCalParentSedolCode _WttCalParentSedolCode = new WttRecord.WttCalParentSedolCode();
        
        
        
        
        // [DEBUG] Field: WttCalPreviousSedolCode, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttCalPreviousSedolCode _WttCalPreviousSedolCode = new WttRecord.WttCalPreviousSedolCode();
        
        
        
        
        // [DEBUG] Field: WttOriginalBargainNo, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttOriginalBargainNo _WttOriginalBargainNo = new WttRecord.WttOriginalBargainNo();
        
        
        
        
        // [DEBUG] Field: WttBargainDate, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttBargainDate _WttBargainDate = new WttRecord.WttBargainDate();
        
        
        
        
        // [DEBUG] Field: WttBargainDate9, is_external=, is_static_class=False, static_prefix=
        private int _WttBargainDate9 =0;
        
        
        
        
        // [DEBUG] Field: WttSettlementDate, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttSettlementDate _WttSettlementDate = new WttRecord.WttSettlementDate();
        
        
        
        
        // [DEBUG] Field: WttCurrencyCode, is_external=, is_static_class=False, static_prefix=
        private string _WttCurrencyCode ="";
        
        
        
        
        // [DEBUG] Field: WttNotesComments, is_external=, is_static_class=False, static_prefix=
        private string _WttNotesComments ="";
        
        
        
        
        // [DEBUG] Field: WttBfNiPiFlag, is_external=, is_static_class=False, static_prefix=
        private string _WttBfNiPiFlag ="";
        
        
        
        
        // [DEBUG] Field: WttNiPiFlagYtd, is_external=, is_static_class=False, static_prefix=
        private string _WttNiPiFlagYtd ="";
        
        
        
        
        // [DEBUG] Field: WttTransactionExported, is_external=, is_static_class=False, static_prefix=
        private string _WttTransactionExported ="";
        
        
        
        
        // [DEBUG] Field: WttCtLinkFund, is_external=, is_static_class=False, static_prefix=
        private string _WttCtLinkFund ="";
        
        
        
        
        // [DEBUG] Field: WttSubFund, is_external=, is_static_class=False, static_prefix=
        private string _WttSubFund ="";
        
        
        
        
        // [DEBUG] Field: WttMicroOverride, is_external=, is_static_class=False, static_prefix=
        private string _WttMicroOverride ="";
        
        
        
        
        // [DEBUG] Field: WttParentMarketPrice, is_external=, is_static_class=False, static_prefix=
        private decimal _WttParentMarketPrice =0;
        
        
        
        
        // [DEBUG] Field: WttTransUnitPrice, is_external=, is_static_class=False, static_prefix=
        private decimal _WttTransUnitPrice =0;
        
        
        
        
        // [DEBUG] Field: WttPricePctIndicator, is_external=, is_static_class=False, static_prefix=
        private string _WttPricePctIndicator ="";
        
        
        
        
        // [DEBUG] Field: WttLiabilityPerShare, is_external=, is_static_class=False, static_prefix=
        private decimal _WttLiabilityPerShare =0;
        
        
        
        
        // [DEBUG] Field: WttOutstandingLiability, is_external=, is_static_class=False, static_prefix=
        private decimal _WttOutstandingLiability =0;
        
        
        
        
        // [DEBUG] Field: WttStockExchIndicator, is_external=, is_static_class=False, static_prefix=
        private string _WttStockExchIndicator ="";
        
        
        
        
        // [DEBUG] Field: WttRemainder, is_external=, is_static_class=False, static_prefix=
        private string _WttRemainder ="";
        
        
        
        
        // [DEBUG] Field: WttRecord02Fields, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttRecord02Fields _WttRecord02Fields = new WttRecord.WttRecord02Fields();
        
        
        
        
        // [DEBUG] Field: WttRecord03Fields, is_external=, is_static_class=False, static_prefix=
        private WttRecord.WttRecord03Fields _WttRecord03Fields = new WttRecord.WttRecord03Fields();
        
        
        
        
    public WttRecord() {}
    
    public WttRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WttKey.SetWttKeyAsString(data.Substring(offset, WttKey.GetSize()));
        offset += 25;
        SetWttTransactionCategory(data.Substring(offset, 2).Trim());
        offset += 2;
        _WttDateTimeStamp.SetWttDateTimeStampAsString(data.Substring(offset, WttDateTimeStamp.GetSize()));
        offset += 14;
        _Filler213.SetFiller213AsString(data.Substring(offset, Filler213.GetSize()));
        offset += 14;
        _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(data.Substring(offset, WttCalParentSedolCode.GetSize()));
        offset += 11;
        _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(data.Substring(offset, WttCalPreviousSedolCode.GetSize()));
        offset += 11;
        _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(data.Substring(offset, WttOriginalBargainNo.GetSize()));
        offset += 10;
        _WttBargainDate.SetWttBargainDateAsString(data.Substring(offset, WttBargainDate.GetSize()));
        offset += 6;
        SetWttBargainDate9(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        _WttSettlementDate.SetWttSettlementDateAsString(data.Substring(offset, WttSettlementDate.GetSize()));
        offset += 6;
        SetWttCurrencyCode(data.Substring(offset, 3).Trim());
        offset += 3;
        SetWttNotesComments(data.Substring(offset, 36).Trim());
        offset += 36;
        SetWttBfNiPiFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttNiPiFlagYtd(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttTransactionExported(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttCtLinkFund(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttSubFund(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttMicroOverride(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttParentMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWttTransUnitPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
        offset += 11;
        SetWttPricePctIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttLiabilityPerShare(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
        offset += 11;
        SetWttOutstandingLiability(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
        offset += 11;
        SetWttStockExchIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttRemainder(data.Substring(offset, 16108).Trim());
        offset += 16108;
        _WttRecord02Fields.SetWttRecord02FieldsAsString(data.Substring(offset, WttRecord02Fields.GetSize()));
        offset += 299;
        _WttRecord03Fields.SetWttRecord03FieldsAsString(data.Substring(offset, WttRecord03Fields.GetSize()));
        offset += 160;
        
    }
    
    // Serialization methods
    public string GetWttRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttKey.GetWttKeyAsString());
        result.Append(_WttTransactionCategory.PadRight(2));
        result.Append(_WttDateTimeStamp.GetWttDateTimeStampAsString());
        result.Append(_Filler213.GetFiller213AsString());
        result.Append(_WttCalParentSedolCode.GetWttCalParentSedolCodeAsString());
        result.Append(_WttCalPreviousSedolCode.GetWttCalPreviousSedolCodeAsString());
        result.Append(_WttOriginalBargainNo.GetWttOriginalBargainNoAsString());
        result.Append(_WttBargainDate.GetWttBargainDateAsString());
        result.Append(_WttBargainDate9.ToString().PadLeft(6, '0'));
        result.Append(_WttSettlementDate.GetWttSettlementDateAsString());
        result.Append(_WttCurrencyCode.PadRight(3));
        result.Append(_WttNotesComments.PadRight(36));
        result.Append(_WttBfNiPiFlag.PadRight(0));
        result.Append(_WttNiPiFlagYtd.PadRight(0));
        result.Append(_WttTransactionExported.PadRight(0));
        result.Append(_WttCtLinkFund.PadRight(0));
        result.Append(_WttSubFund.PadRight(0));
        result.Append(_WttMicroOverride.PadRight(0));
        result.Append(_WttParentMarketPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttTransUnitPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttPricePctIndicator.PadRight(1));
        result.Append(_WttLiabilityPerShare.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttOutstandingLiability.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttStockExchIndicator.PadRight(1));
        result.Append(_WttRemainder.PadRight(16108));
        result.Append(_WttRecord02Fields.GetWttRecord02FieldsAsString());
        result.Append(_WttRecord03Fields.GetWttRecord03FieldsAsString());
        
        return result.ToString();
    }
    
    public void SetWttRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            _WttKey.SetWttKeyAsString(data.Substring(offset, 25));
        }
        else
        {
            _WttKey.SetWttKeyAsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWttTransactionCategory(extracted);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            _WttDateTimeStamp.SetWttDateTimeStampAsString(data.Substring(offset, 14));
        }
        else
        {
            _WttDateTimeStamp.SetWttDateTimeStampAsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            _Filler213.SetFiller213AsString(data.Substring(offset, 14));
        }
        else
        {
            _Filler213.SetFiller213AsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 11 <= data.Length)
        {
            _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(data.Substring(offset, 11));
        }
        else
        {
            _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(data.Substring(offset, 11));
        }
        else
        {
            _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(data.Substring(offset, 10));
        }
        else
        {
            _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 6 <= data.Length)
        {
            _WttBargainDate.SetWttBargainDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WttBargainDate.SetWttBargainDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttBargainDate9(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _WttSettlementDate.SetWttSettlementDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WttSettlementDate.SetWttSettlementDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWttCurrencyCode(extracted);
        }
        offset += 3;
        if (offset + 36 <= data.Length)
        {
            string extracted = data.Substring(offset, 36).Trim();
            SetWttNotesComments(extracted);
        }
        offset += 36;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttBfNiPiFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttNiPiFlagYtd(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttTransactionExported(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttCtLinkFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttSubFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttMicroOverride(extracted);
        }
        offset += 0;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttParentMarketPrice(parsedDec);
        }
        offset += 15;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttTransUnitPrice(parsedDec);
        }
        offset += 11;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttPricePctIndicator(extracted);
        }
        offset += 1;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttLiabilityPerShare(parsedDec);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttOutstandingLiability(parsedDec);
        }
        offset += 11;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttStockExchIndicator(extracted);
        }
        offset += 1;
        if (offset + 16108 <= data.Length)
        {
            string extracted = data.Substring(offset, 16108).Trim();
            SetWttRemainder(extracted);
        }
        offset += 16108;
        if (offset + 299 <= data.Length)
        {
            _WttRecord02Fields.SetWttRecord02FieldsAsString(data.Substring(offset, 299));
        }
        else
        {
            _WttRecord02Fields.SetWttRecord02FieldsAsString(data.Substring(offset));
        }
        offset += 299;
        if (offset + 160 <= data.Length)
        {
            _WttRecord03Fields.SetWttRecord03FieldsAsString(data.Substring(offset, 160));
        }
        else
        {
            _WttRecord03Fields.SetWttRecord03FieldsAsString(data.Substring(offset));
        }
        offset += 160;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WttKey GetWttKey()
    {
        return _WttKey;
    }
    
    // Standard Setter
    public void SetWttKey(WttKey value)
    {
        _WttKey = value;
    }
    
    // Get<>AsString()
    public string GetWttKeyAsString()
    {
        return _WttKey != null ? _WttKey.GetWttKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttKey == null)
        {
            _WttKey = new WttKey();
        }
        _WttKey.SetWttKeyAsString(value);
    }
    
    // Standard Getter
    public string GetWttTransactionCategory()
    {
        return _WttTransactionCategory;
    }
    
    // Standard Setter
    public void SetWttTransactionCategory(string value)
    {
        _WttTransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetWttTransactionCategoryAsString()
    {
        return _WttTransactionCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWttTransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttTransactionCategory = value;
    }
    
    // Standard Getter
    public WttDateTimeStamp GetWttDateTimeStamp()
    {
        return _WttDateTimeStamp;
    }
    
    // Standard Setter
    public void SetWttDateTimeStamp(WttDateTimeStamp value)
    {
        _WttDateTimeStamp = value;
    }
    
    // Get<>AsString()
    public string GetWttDateTimeStampAsString()
    {
        return _WttDateTimeStamp != null ? _WttDateTimeStamp.GetWttDateTimeStampAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttDateTimeStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttDateTimeStamp == null)
        {
            _WttDateTimeStamp = new WttDateTimeStamp();
        }
        _WttDateTimeStamp.SetWttDateTimeStampAsString(value);
    }
    
    // Standard Getter
    public Filler213 GetFiller213()
    {
        return _Filler213;
    }
    
    // Standard Setter
    public void SetFiller213(Filler213 value)
    {
        _Filler213 = value;
    }
    
    // Get<>AsString()
    public string GetFiller213AsString()
    {
        return _Filler213 != null ? _Filler213.GetFiller213AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller213AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler213 == null)
        {
            _Filler213 = new Filler213();
        }
        _Filler213.SetFiller213AsString(value);
    }
    
    // Standard Getter
    public WttCalParentSedolCode GetWttCalParentSedolCode()
    {
        return _WttCalParentSedolCode;
    }
    
    // Standard Setter
    public void SetWttCalParentSedolCode(WttCalParentSedolCode value)
    {
        _WttCalParentSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetWttCalParentSedolCodeAsString()
    {
        return _WttCalParentSedolCode != null ? _WttCalParentSedolCode.GetWttCalParentSedolCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttCalParentSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttCalParentSedolCode == null)
        {
            _WttCalParentSedolCode = new WttCalParentSedolCode();
        }
        _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(value);
    }
    
    // Standard Getter
    public WttCalPreviousSedolCode GetWttCalPreviousSedolCode()
    {
        return _WttCalPreviousSedolCode;
    }
    
    // Standard Setter
    public void SetWttCalPreviousSedolCode(WttCalPreviousSedolCode value)
    {
        _WttCalPreviousSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetWttCalPreviousSedolCodeAsString()
    {
        return _WttCalPreviousSedolCode != null ? _WttCalPreviousSedolCode.GetWttCalPreviousSedolCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttCalPreviousSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttCalPreviousSedolCode == null)
        {
            _WttCalPreviousSedolCode = new WttCalPreviousSedolCode();
        }
        _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(value);
    }
    
    // Standard Getter
    public WttOriginalBargainNo GetWttOriginalBargainNo()
    {
        return _WttOriginalBargainNo;
    }
    
    // Standard Setter
    public void SetWttOriginalBargainNo(WttOriginalBargainNo value)
    {
        _WttOriginalBargainNo = value;
    }
    
    // Get<>AsString()
    public string GetWttOriginalBargainNoAsString()
    {
        return _WttOriginalBargainNo != null ? _WttOriginalBargainNo.GetWttOriginalBargainNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttOriginalBargainNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttOriginalBargainNo == null)
        {
            _WttOriginalBargainNo = new WttOriginalBargainNo();
        }
        _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(value);
    }
    
    // Standard Getter
    public WttBargainDate GetWttBargainDate()
    {
        return _WttBargainDate;
    }
    
    // Standard Setter
    public void SetWttBargainDate(WttBargainDate value)
    {
        _WttBargainDate = value;
    }
    
    // Get<>AsString()
    public string GetWttBargainDateAsString()
    {
        return _WttBargainDate != null ? _WttBargainDate.GetWttBargainDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttBargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttBargainDate == null)
        {
            _WttBargainDate = new WttBargainDate();
        }
        _WttBargainDate.SetWttBargainDateAsString(value);
    }
    
    // Standard Getter
    public int GetWttBargainDate9()
    {
        return _WttBargainDate9;
    }
    
    // Standard Setter
    public void SetWttBargainDate9(int value)
    {
        _WttBargainDate9 = value;
    }
    
    // Get<>AsString()
    public string GetWttBargainDate9AsString()
    {
        return _WttBargainDate9.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWttBargainDate9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttBargainDate9 = parsed;
    }
    
    // Standard Getter
    public WttSettlementDate GetWttSettlementDate()
    {
        return _WttSettlementDate;
    }
    
    // Standard Setter
    public void SetWttSettlementDate(WttSettlementDate value)
    {
        _WttSettlementDate = value;
    }
    
    // Get<>AsString()
    public string GetWttSettlementDateAsString()
    {
        return _WttSettlementDate != null ? _WttSettlementDate.GetWttSettlementDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttSettlementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttSettlementDate == null)
        {
            _WttSettlementDate = new WttSettlementDate();
        }
        _WttSettlementDate.SetWttSettlementDateAsString(value);
    }
    
    // Standard Getter
    public string GetWttCurrencyCode()
    {
        return _WttCurrencyCode;
    }
    
    // Standard Setter
    public void SetWttCurrencyCode(string value)
    {
        _WttCurrencyCode = value;
    }
    
    // Get<>AsString()
    public string GetWttCurrencyCodeAsString()
    {
        return _WttCurrencyCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWttCurrencyCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttCurrencyCode = value;
    }
    
    // Standard Getter
    public string GetWttNotesComments()
    {
        return _WttNotesComments;
    }
    
    // Standard Setter
    public void SetWttNotesComments(string value)
    {
        _WttNotesComments = value;
    }
    
    // Get<>AsString()
    public string GetWttNotesCommentsAsString()
    {
        return _WttNotesComments.PadRight(36);
    }
    
    // Set<>AsString()
    public void SetWttNotesCommentsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttNotesComments = value;
    }
    
    // Standard Getter
    public string GetWttBfNiPiFlag()
    {
        return _WttBfNiPiFlag;
    }
    
    // Standard Setter
    public void SetWttBfNiPiFlag(string value)
    {
        _WttBfNiPiFlag = value;
    }
    
    // Get<>AsString()
    public string GetWttBfNiPiFlagAsString()
    {
        return _WttBfNiPiFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttBfNiPiFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttBfNiPiFlag = value;
    }
    
    // Standard Getter
    public string GetWttNiPiFlagYtd()
    {
        return _WttNiPiFlagYtd;
    }
    
    // Standard Setter
    public void SetWttNiPiFlagYtd(string value)
    {
        _WttNiPiFlagYtd = value;
    }
    
    // Get<>AsString()
    public string GetWttNiPiFlagYtdAsString()
    {
        return _WttNiPiFlagYtd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttNiPiFlagYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttNiPiFlagYtd = value;
    }
    
    // Standard Getter
    public string GetWttTransactionExported()
    {
        return _WttTransactionExported;
    }
    
    // Standard Setter
    public void SetWttTransactionExported(string value)
    {
        _WttTransactionExported = value;
    }
    
    // Get<>AsString()
    public string GetWttTransactionExportedAsString()
    {
        return _WttTransactionExported.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttTransactionExportedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttTransactionExported = value;
    }
    
    // Standard Getter
    public string GetWttCtLinkFund()
    {
        return _WttCtLinkFund;
    }
    
    // Standard Setter
    public void SetWttCtLinkFund(string value)
    {
        _WttCtLinkFund = value;
    }
    
    // Get<>AsString()
    public string GetWttCtLinkFundAsString()
    {
        return _WttCtLinkFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttCtLinkFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttCtLinkFund = value;
    }
    
    // Standard Getter
    public string GetWttSubFund()
    {
        return _WttSubFund;
    }
    
    // Standard Setter
    public void SetWttSubFund(string value)
    {
        _WttSubFund = value;
    }
    
    // Get<>AsString()
    public string GetWttSubFundAsString()
    {
        return _WttSubFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttSubFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSubFund = value;
    }
    
    // Standard Getter
    public string GetWttMicroOverride()
    {
        return _WttMicroOverride;
    }
    
    // Standard Setter
    public void SetWttMicroOverride(string value)
    {
        _WttMicroOverride = value;
    }
    
    // Get<>AsString()
    public string GetWttMicroOverrideAsString()
    {
        return _WttMicroOverride.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttMicroOverrideAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttMicroOverride = value;
    }
    
    // Standard Getter
    public decimal GetWttParentMarketPrice()
    {
        return _WttParentMarketPrice;
    }
    
    // Standard Setter
    public void SetWttParentMarketPrice(decimal value)
    {
        _WttParentMarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetWttParentMarketPriceAsString()
    {
        return _WttParentMarketPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttParentMarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttParentMarketPrice = parsed;
    }
    
    // Standard Getter
    public decimal GetWttTransUnitPrice()
    {
        return _WttTransUnitPrice;
    }
    
    // Standard Setter
    public void SetWttTransUnitPrice(decimal value)
    {
        _WttTransUnitPrice = value;
    }
    
    // Get<>AsString()
    public string GetWttTransUnitPriceAsString()
    {
        return _WttTransUnitPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttTransUnitPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTransUnitPrice = parsed;
    }
    
    // Standard Getter
    public string GetWttPricePctIndicator()
    {
        return _WttPricePctIndicator;
    }
    
    // Standard Setter
    public void SetWttPricePctIndicator(string value)
    {
        _WttPricePctIndicator = value;
    }
    
    // Get<>AsString()
    public string GetWttPricePctIndicatorAsString()
    {
        return _WttPricePctIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttPricePctIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttPricePctIndicator = value;
    }
    
    // Standard Getter
    public decimal GetWttLiabilityPerShare()
    {
        return _WttLiabilityPerShare;
    }
    
    // Standard Setter
    public void SetWttLiabilityPerShare(decimal value)
    {
        _WttLiabilityPerShare = value;
    }
    
    // Get<>AsString()
    public string GetWttLiabilityPerShareAsString()
    {
        return _WttLiabilityPerShare.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttLiabilityPerShareAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttLiabilityPerShare = parsed;
    }
    
    // Standard Getter
    public decimal GetWttOutstandingLiability()
    {
        return _WttOutstandingLiability;
    }
    
    // Standard Setter
    public void SetWttOutstandingLiability(decimal value)
    {
        _WttOutstandingLiability = value;
    }
    
    // Get<>AsString()
    public string GetWttOutstandingLiabilityAsString()
    {
        return _WttOutstandingLiability.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttOutstandingLiabilityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttOutstandingLiability = parsed;
    }
    
    // Standard Getter
    public string GetWttStockExchIndicator()
    {
        return _WttStockExchIndicator;
    }
    
    // Standard Setter
    public void SetWttStockExchIndicator(string value)
    {
        _WttStockExchIndicator = value;
    }
    
    // Get<>AsString()
    public string GetWttStockExchIndicatorAsString()
    {
        return _WttStockExchIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttStockExchIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttStockExchIndicator = value;
    }
    
    // Standard Getter
    public string GetWttRemainder()
    {
        return _WttRemainder;
    }
    
    // Standard Setter
    public void SetWttRemainder(string value)
    {
        _WttRemainder = value;
    }
    
    // Get<>AsString()
    public string GetWttRemainderAsString()
    {
        return _WttRemainder.PadRight(16108);
    }
    
    // Set<>AsString()
    public void SetWttRemainderAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttRemainder = value;
    }
    
    // Standard Getter
    public WttRecord02Fields GetWttRecord02Fields()
    {
        return _WttRecord02Fields;
    }
    
    // Standard Setter
    public void SetWttRecord02Fields(WttRecord02Fields value)
    {
        _WttRecord02Fields = value;
    }
    
    // Get<>AsString()
    public string GetWttRecord02FieldsAsString()
    {
        return _WttRecord02Fields != null ? _WttRecord02Fields.GetWttRecord02FieldsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttRecord02FieldsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttRecord02Fields == null)
        {
            _WttRecord02Fields = new WttRecord02Fields();
        }
        _WttRecord02Fields.SetWttRecord02FieldsAsString(value);
    }
    
    // Standard Getter
    public WttRecord03Fields GetWttRecord03Fields()
    {
        return _WttRecord03Fields;
    }
    
    // Standard Setter
    public void SetWttRecord03Fields(WttRecord03Fields value)
    {
        _WttRecord03Fields = value;
    }
    
    // Get<>AsString()
    public string GetWttRecord03FieldsAsString()
    {
        return _WttRecord03Fields != null ? _WttRecord03Fields.GetWttRecord03FieldsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttRecord03FieldsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttRecord03Fields == null)
        {
            _WttRecord03Fields = new WttRecord03Fields();
        }
        _WttRecord03Fields.SetWttRecord03FieldsAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttKey
    public class WttKey
    {
        private static int _size = 25;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttCalSedol, is_external=, is_static_class=False, static_prefix=
        private WttKey.WttCalSedol _WttCalSedol = new WttKey.WttCalSedol();
        
        
        
        
        // [DEBUG] Field: WttContractNo, is_external=, is_static_class=False, static_prefix=
        private WttKey.WttContractNo _WttContractNo = new WttKey.WttContractNo();
        
        
        
        
        // [DEBUG] Field: WttRecordCode, is_external=, is_static_class=False, static_prefix=
        private int _WttRecordCode =0;
        
        
        
        
        // [DEBUG] Field: WttRecordCodeX, is_external=, is_static_class=False, static_prefix=
        private WttKey.WttRecordCodeX _WttRecordCodeX = new WttKey.WttRecordCodeX();
        
        
        
        
    public WttKey() {}
    
    public WttKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WttCalSedol.SetWttCalSedolAsString(data.Substring(offset, WttCalSedol.GetSize()));
        offset += 11;
        _WttContractNo.SetWttContractNoAsString(data.Substring(offset, WttContractNo.GetSize()));
        offset += 10;
        SetWttRecordCode(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _WttRecordCodeX.SetWttRecordCodeXAsString(data.Substring(offset, WttRecordCodeX.GetSize()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWttKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttCalSedol.GetWttCalSedolAsString());
        result.Append(_WttContractNo.GetWttContractNoAsString());
        result.Append(_WttRecordCode.ToString().PadLeft(2, '0'));
        result.Append(_WttRecordCodeX.GetWttRecordCodeXAsString());
        
        return result.ToString();
    }
    
    public void SetWttKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            _WttCalSedol.SetWttCalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _WttCalSedol.SetWttCalSedolAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            _WttContractNo.SetWttContractNoAsString(data.Substring(offset, 10));
        }
        else
        {
            _WttContractNo.SetWttContractNoAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttRecordCode(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            _WttRecordCodeX.SetWttRecordCodeXAsString(data.Substring(offset, 2));
        }
        else
        {
            _WttRecordCodeX.SetWttRecordCodeXAsString(data.Substring(offset));
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WttCalSedol GetWttCalSedol()
    {
        return _WttCalSedol;
    }
    
    // Standard Setter
    public void SetWttCalSedol(WttCalSedol value)
    {
        _WttCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetWttCalSedolAsString()
    {
        return _WttCalSedol != null ? _WttCalSedol.GetWttCalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttCalSedol == null)
        {
            _WttCalSedol = new WttCalSedol();
        }
        _WttCalSedol.SetWttCalSedolAsString(value);
    }
    
    // Standard Getter
    public WttContractNo GetWttContractNo()
    {
        return _WttContractNo;
    }
    
    // Standard Setter
    public void SetWttContractNo(WttContractNo value)
    {
        _WttContractNo = value;
    }
    
    // Get<>AsString()
    public string GetWttContractNoAsString()
    {
        return _WttContractNo != null ? _WttContractNo.GetWttContractNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttContractNo == null)
        {
            _WttContractNo = new WttContractNo();
        }
        _WttContractNo.SetWttContractNoAsString(value);
    }
    
    // Standard Getter
    public int GetWttRecordCode()
    {
        return _WttRecordCode;
    }
    
    // Standard Setter
    public void SetWttRecordCode(int value)
    {
        _WttRecordCode = value;
    }
    
    // Get<>AsString()
    public string GetWttRecordCodeAsString()
    {
        return _WttRecordCode.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWttRecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttRecordCode = parsed;
    }
    
    // Standard Getter
    public WttRecordCodeX GetWttRecordCodeX()
    {
        return _WttRecordCodeX;
    }
    
    // Standard Setter
    public void SetWttRecordCodeX(WttRecordCodeX value)
    {
        _WttRecordCodeX = value;
    }
    
    // Get<>AsString()
    public string GetWttRecordCodeXAsString()
    {
        return _WttRecordCodeX != null ? _WttRecordCodeX.GetWttRecordCodeXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttRecordCodeXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttRecordCodeX == null)
        {
            _WttRecordCodeX = new WttRecordCodeX();
        }
        _WttRecordCodeX.SetWttRecordCodeXAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttCalSedol
    public class WttCalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttCoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _WttCoAcLk ="";
        
        
        
        
        // [DEBUG] Field: WttSedol, is_external=, is_static_class=False, static_prefix=
        private string _WttSedol ="";
        
        
        
        
    public WttCalSedol() {}
    
    public WttCalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttCoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWttSedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetWttCalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttCoAcLk.PadRight(4));
        result.Append(_WttSedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetWttCalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWttCoAcLk(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWttSedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWttCoAcLk()
    {
        return _WttCoAcLk;
    }
    
    // Standard Setter
    public void SetWttCoAcLk(string value)
    {
        _WttCoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetWttCoAcLkAsString()
    {
        return _WttCoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWttCoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttCoAcLk = value;
    }
    
    // Standard Getter
    public string GetWttSedol()
    {
        return _WttSedol;
    }
    
    // Standard Setter
    public void SetWttSedol(string value)
    {
        _WttSedol = value;
    }
    
    // Get<>AsString()
    public string GetWttSedolAsString()
    {
        return _WttSedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWttSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: WttContractNo
public class WttContractNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttContractPrefix, is_external=, is_static_class=False, static_prefix=
    private string _WttContractPrefix ="";
    
    
    
    
    // [DEBUG] Field: Filler212, is_external=, is_static_class=False, static_prefix=
    private string _Filler212 ="";
    
    
    
    
public WttContractNo() {}

public WttContractNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttContractPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller212(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWttContractNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttContractPrefix.PadRight(7));
    result.Append(_Filler212.PadRight(3));
    
    return result.ToString();
}

public void SetWttContractNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttContractPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller212(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetWttContractPrefix()
{
    return _WttContractPrefix;
}

// Standard Setter
public void SetWttContractPrefix(string value)
{
    _WttContractPrefix = value;
}

// Get<>AsString()
public string GetWttContractPrefixAsString()
{
    return _WttContractPrefix.PadRight(7);
}

// Set<>AsString()
public void SetWttContractPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttContractPrefix = value;
}

// Standard Getter
public string GetFiller212()
{
    return _Filler212;
}

// Standard Setter
public void SetFiller212(string value)
{
    _Filler212 = value;
}

// Get<>AsString()
public string GetFiller212AsString()
{
    return _Filler212.PadRight(3);
}

// Set<>AsString()
public void SetFiller212AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler212 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttRecordCodeX
public class WttRecordCodeX
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttRecordCodeX1, is_external=, is_static_class=False, static_prefix=
    private string _WttRecordCodeX1 ="";
    
    
    
    
    // [DEBUG] Field: WttRecordCodeX2, is_external=, is_static_class=False, static_prefix=
    private string _WttRecordCodeX2 ="";
    
    
    
    
public WttRecordCodeX() {}

public WttRecordCodeX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttRecordCodeX1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttRecordCodeX2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetWttRecordCodeXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttRecordCodeX1.PadRight(1));
    result.Append(_WttRecordCodeX2.PadRight(1));
    
    return result.ToString();
}

public void SetWttRecordCodeXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttRecordCodeX1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttRecordCodeX2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWttRecordCodeX1()
{
    return _WttRecordCodeX1;
}

// Standard Setter
public void SetWttRecordCodeX1(string value)
{
    _WttRecordCodeX1 = value;
}

// Get<>AsString()
public string GetWttRecordCodeX1AsString()
{
    return _WttRecordCodeX1.PadRight(1);
}

// Set<>AsString()
public void SetWttRecordCodeX1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttRecordCodeX1 = value;
}

// Standard Getter
public string GetWttRecordCodeX2()
{
    return _WttRecordCodeX2;
}

// Standard Setter
public void SetWttRecordCodeX2(string value)
{
    _WttRecordCodeX2 = value;
}

// Get<>AsString()
public string GetWttRecordCodeX2AsString()
{
    return _WttRecordCodeX2.PadRight(1);
}

// Set<>AsString()
public void SetWttRecordCodeX2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttRecordCodeX2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttDateTimeStamp
public class WttDateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttDateStamp, is_external=, is_static_class=False, static_prefix=
    private string _WttDateStamp ="";
    
    
    
    
    // [DEBUG] Field: WttTimeStamp, is_external=, is_static_class=False, static_prefix=
    private string _WttTimeStamp ="";
    
    
    
    
public WttDateTimeStamp() {}

public WttDateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttDateStamp(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWttTimeStamp(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetWttDateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttDateStamp.PadRight(6));
    result.Append(_WttTimeStamp.PadRight(8));
    
    return result.ToString();
}

public void SetWttDateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWttDateStamp(extracted);
    }
    offset += 6;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttTimeStamp(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetWttDateStamp()
{
    return _WttDateStamp;
}

// Standard Setter
public void SetWttDateStamp(string value)
{
    _WttDateStamp = value;
}

// Get<>AsString()
public string GetWttDateStampAsString()
{
    return _WttDateStamp.PadRight(6);
}

// Set<>AsString()
public void SetWttDateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttDateStamp = value;
}

// Standard Getter
public string GetWttTimeStamp()
{
    return _WttTimeStamp;
}

// Standard Setter
public void SetWttTimeStamp(string value)
{
    _WttTimeStamp = value;
}

// Get<>AsString()
public string GetWttTimeStampAsString()
{
    return _WttTimeStamp.PadRight(8);
}

// Set<>AsString()
public void SetWttTimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTimeStamp = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler213
public class Filler213
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttStamp, is_external=, is_static_class=False, static_prefix=
    private string _WttStamp ="";
    
    
    
    
    // [DEBUG] Field: WttPartly, is_external=, is_static_class=False, static_prefix=
    private string _WttPartly ="";
    
    
    
    
public Filler213() {}

public Filler213(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttStamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetWttPartly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller213AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttStamp.PadRight(13));
    result.Append(_WttPartly.PadRight(1));
    
    return result.ToString();
}

public void SetFiller213AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetWttStamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttPartly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWttStamp()
{
    return _WttStamp;
}

// Standard Setter
public void SetWttStamp(string value)
{
    _WttStamp = value;
}

// Get<>AsString()
public string GetWttStampAsString()
{
    return _WttStamp.PadRight(13);
}

// Set<>AsString()
public void SetWttStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttStamp = value;
}

// Standard Getter
public string GetWttPartly()
{
    return _WttPartly;
}

// Standard Setter
public void SetWttPartly(string value)
{
    _WttPartly = value;
}

// Get<>AsString()
public string GetWttPartlyAsString()
{
    return _WttPartly.PadRight(1);
}

// Set<>AsString()
public void SetWttPartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPartly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttCalParentSedolCode
public class WttCalParentSedolCode
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttParentCal, is_external=, is_static_class=False, static_prefix=
    private string _WttParentCal ="";
    
    
    
    
    // [DEBUG] Field: WttParentSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _WttParentSedolCode ="";
    
    
    
    
public WttCalParentSedolCode() {}

public WttCalParentSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttParentCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWttParentSedolCode(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWttCalParentSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttParentCal.PadRight(4));
    result.Append(_WttParentSedolCode.PadRight(7));
    
    return result.ToString();
}

public void SetWttCalParentSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWttParentCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttParentSedolCode(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWttParentCal()
{
    return _WttParentCal;
}

// Standard Setter
public void SetWttParentCal(string value)
{
    _WttParentCal = value;
}

// Get<>AsString()
public string GetWttParentCalAsString()
{
    return _WttParentCal.PadRight(4);
}

// Set<>AsString()
public void SetWttParentCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttParentCal = value;
}

// Standard Getter
public string GetWttParentSedolCode()
{
    return _WttParentSedolCode;
}

// Standard Setter
public void SetWttParentSedolCode(string value)
{
    _WttParentSedolCode = value;
}

// Get<>AsString()
public string GetWttParentSedolCodeAsString()
{
    return _WttParentSedolCode.PadRight(7);
}

// Set<>AsString()
public void SetWttParentSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttParentSedolCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttCalPreviousSedolCode
public class WttCalPreviousSedolCode
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttPreviousCal, is_external=, is_static_class=False, static_prefix=
    private string _WttPreviousCal ="";
    
    
    
    
    // [DEBUG] Field: WttPreviousSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _WttPreviousSedolCode ="";
    
    
    
    
public WttCalPreviousSedolCode() {}

public WttCalPreviousSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttPreviousCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWttPreviousSedolCode(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWttCalPreviousSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttPreviousCal.PadRight(4));
    result.Append(_WttPreviousSedolCode.PadRight(7));
    
    return result.ToString();
}

public void SetWttCalPreviousSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWttPreviousCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttPreviousSedolCode(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWttPreviousCal()
{
    return _WttPreviousCal;
}

// Standard Setter
public void SetWttPreviousCal(string value)
{
    _WttPreviousCal = value;
}

// Get<>AsString()
public string GetWttPreviousCalAsString()
{
    return _WttPreviousCal.PadRight(4);
}

// Set<>AsString()
public void SetWttPreviousCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPreviousCal = value;
}

// Standard Getter
public string GetWttPreviousSedolCode()
{
    return _WttPreviousSedolCode;
}

// Standard Setter
public void SetWttPreviousSedolCode(string value)
{
    _WttPreviousSedolCode = value;
}

// Get<>AsString()
public string GetWttPreviousSedolCodeAsString()
{
    return _WttPreviousSedolCode.PadRight(7);
}

// Set<>AsString()
public void SetWttPreviousSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPreviousSedolCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttOriginalBargainNo
public class WttOriginalBargainNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBargainPrefix, is_external=, is_static_class=False, static_prefix=
    private string _WttBargainPrefix ="";
    
    
    
    
    // [DEBUG] Field: Filler214, is_external=, is_static_class=False, static_prefix=
    private string _Filler214 ="";
    
    
    
    
public WttOriginalBargainNo() {}

public WttOriginalBargainNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBargainPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller214(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWttOriginalBargainNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBargainPrefix.PadRight(7));
    result.Append(_Filler214.PadRight(3));
    
    return result.ToString();
}

public void SetWttOriginalBargainNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttBargainPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller214(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetWttBargainPrefix()
{
    return _WttBargainPrefix;
}

// Standard Setter
public void SetWttBargainPrefix(string value)
{
    _WttBargainPrefix = value;
}

// Get<>AsString()
public string GetWttBargainPrefixAsString()
{
    return _WttBargainPrefix.PadRight(7);
}

// Set<>AsString()
public void SetWttBargainPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBargainPrefix = value;
}

// Standard Getter
public string GetFiller214()
{
    return _Filler214;
}

// Standard Setter
public void SetFiller214(string value)
{
    _Filler214 = value;
}

// Get<>AsString()
public string GetFiller214AsString()
{
    return _Filler214.PadRight(3);
}

// Set<>AsString()
public void SetFiller214AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler214 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttBargainDate
public class WttBargainDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBargainDateYymm, is_external=, is_static_class=False, static_prefix=
    private WttBargainDate.WttBargainDateYymm _WttBargainDateYymm = new WttBargainDate.WttBargainDateYymm();
    
    
    
    
    // [DEBUG] Field: WttBargainDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WttBargainDateDd =0;
    
    
    
    
public WttBargainDate() {}

public WttBargainDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttBargainDateYymm.SetWttBargainDateYymmAsString(data.Substring(offset, WttBargainDateYymm.GetSize()));
    offset += 4;
    SetWttBargainDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttBargainDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBargainDateYymm.GetWttBargainDateYymmAsString());
    result.Append(_WttBargainDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttBargainDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _WttBargainDateYymm.SetWttBargainDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _WttBargainDateYymm.SetWttBargainDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBargainDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WttBargainDateYymm GetWttBargainDateYymm()
{
    return _WttBargainDateYymm;
}

// Standard Setter
public void SetWttBargainDateYymm(WttBargainDateYymm value)
{
    _WttBargainDateYymm = value;
}

// Get<>AsString()
public string GetWttBargainDateYymmAsString()
{
    return _WttBargainDateYymm != null ? _WttBargainDateYymm.GetWttBargainDateYymmAsString() : "";
}

// Set<>AsString()
public void SetWttBargainDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttBargainDateYymm == null)
    {
        _WttBargainDateYymm = new WttBargainDateYymm();
    }
    _WttBargainDateYymm.SetWttBargainDateYymmAsString(value);
}

// Standard Getter
public int GetWttBargainDateDd()
{
    return _WttBargainDateDd;
}

// Standard Setter
public void SetWttBargainDateDd(int value)
{
    _WttBargainDateDd = value;
}

// Get<>AsString()
public string GetWttBargainDateDdAsString()
{
    return _WttBargainDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBargainDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBargainDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttBargainDateYymm
public class WttBargainDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBargainDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WttBargainDateYy =0;
    
    
    
    
    // [DEBUG] Field: WttBargainDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WttBargainDateMm =0;
    
    
    
    
public WttBargainDateYymm() {}

public WttBargainDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBargainDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttBargainDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttBargainDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBargainDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WttBargainDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttBargainDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBargainDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBargainDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttBargainDateYy()
{
    return _WttBargainDateYy;
}

// Standard Setter
public void SetWttBargainDateYy(int value)
{
    _WttBargainDateYy = value;
}

// Get<>AsString()
public string GetWttBargainDateYyAsString()
{
    return _WttBargainDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBargainDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBargainDateYy = parsed;
}

// Standard Getter
public int GetWttBargainDateMm()
{
    return _WttBargainDateMm;
}

// Standard Setter
public void SetWttBargainDateMm(int value)
{
    _WttBargainDateMm = value;
}

// Get<>AsString()
public string GetWttBargainDateMmAsString()
{
    return _WttBargainDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBargainDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBargainDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttSettlementDate
public class WttSettlementDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttSettlementDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WttSettlementDateYy =0;
    
    
    
    
    // [DEBUG] Field: WttSettlementDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WttSettlementDateMm =0;
    
    
    
    
    // [DEBUG] Field: WttSettlementDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WttSettlementDateDd =0;
    
    
    
    
public WttSettlementDate() {}

public WttSettlementDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttSettlementDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttSettlementDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttSettlementDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttSettlementDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttSettlementDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WttSettlementDateMm.ToString().PadLeft(2, '0'));
    result.Append(_WttSettlementDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttSettlementDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttSettlementDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttSettlementDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttSettlementDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttSettlementDateYy()
{
    return _WttSettlementDateYy;
}

// Standard Setter
public void SetWttSettlementDateYy(int value)
{
    _WttSettlementDateYy = value;
}

// Get<>AsString()
public string GetWttSettlementDateYyAsString()
{
    return _WttSettlementDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttSettlementDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttSettlementDateYy = parsed;
}

// Standard Getter
public int GetWttSettlementDateMm()
{
    return _WttSettlementDateMm;
}

// Standard Setter
public void SetWttSettlementDateMm(int value)
{
    _WttSettlementDateMm = value;
}

// Get<>AsString()
public string GetWttSettlementDateMmAsString()
{
    return _WttSettlementDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttSettlementDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttSettlementDateMm = parsed;
}

// Standard Getter
public int GetWttSettlementDateDd()
{
    return _WttSettlementDateDd;
}

// Standard Setter
public void SetWttSettlementDateDd(int value)
{
    _WttSettlementDateDd = value;
}

// Get<>AsString()
public string GetWttSettlementDateDdAsString()
{
    return _WttSettlementDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttSettlementDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttSettlementDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttRecord02Fields
public class WttRecord02Fields
{
    private static int _size = 299;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttRecord02NonCostsFields, is_external=, is_static_class=False, static_prefix=
    private WttRecord02Fields.WttRecord02NonCostsFields _WttRecord02NonCostsFields = new WttRecord02Fields.WttRecord02NonCostsFields();
    
    
    
    
    // [DEBUG] Field: WttRecord02CostTable, is_external=, is_static_class=False, static_prefix=
    private WttRecord02Fields.WttRecord02CostTable _WttRecord02CostTable = new WttRecord02Fields.WttRecord02CostTable();
    
    
    
    
public WttRecord02Fields() {}

public WttRecord02Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttRecord02NonCostsFields.SetWttRecord02NonCostsFieldsAsString(data.Substring(offset, WttRecord02NonCostsFields.GetSize()));
    offset += 195;
    _WttRecord02CostTable.SetWttRecord02CostTableAsString(data.Substring(offset, WttRecord02CostTable.GetSize()));
    offset += 104;
    
}

// Serialization methods
public string GetWttRecord02FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttRecord02NonCostsFields.GetWttRecord02NonCostsFieldsAsString());
    result.Append(_WttRecord02CostTable.GetWttRecord02CostTableAsString());
    
    return result.ToString();
}

public void SetWttRecord02FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 195 <= data.Length)
    {
        _WttRecord02NonCostsFields.SetWttRecord02NonCostsFieldsAsString(data.Substring(offset, 195));
    }
    else
    {
        _WttRecord02NonCostsFields.SetWttRecord02NonCostsFieldsAsString(data.Substring(offset));
    }
    offset += 195;
    if (offset + 104 <= data.Length)
    {
        _WttRecord02CostTable.SetWttRecord02CostTableAsString(data.Substring(offset, 104));
    }
    else
    {
        _WttRecord02CostTable.SetWttRecord02CostTableAsString(data.Substring(offset));
    }
    offset += 104;
}

// Getter and Setter methods

// Standard Getter
public WttRecord02NonCostsFields GetWttRecord02NonCostsFields()
{
    return _WttRecord02NonCostsFields;
}

// Standard Setter
public void SetWttRecord02NonCostsFields(WttRecord02NonCostsFields value)
{
    _WttRecord02NonCostsFields = value;
}

// Get<>AsString()
public string GetWttRecord02NonCostsFieldsAsString()
{
    return _WttRecord02NonCostsFields != null ? _WttRecord02NonCostsFields.GetWttRecord02NonCostsFieldsAsString() : "";
}

// Set<>AsString()
public void SetWttRecord02NonCostsFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttRecord02NonCostsFields == null)
    {
        _WttRecord02NonCostsFields = new WttRecord02NonCostsFields();
    }
    _WttRecord02NonCostsFields.SetWttRecord02NonCostsFieldsAsString(value);
}

// Standard Getter
public WttRecord02CostTable GetWttRecord02CostTable()
{
    return _WttRecord02CostTable;
}

// Standard Setter
public void SetWttRecord02CostTable(WttRecord02CostTable value)
{
    _WttRecord02CostTable = value;
}

// Get<>AsString()
public string GetWttRecord02CostTableAsString()
{
    return _WttRecord02CostTable != null ? _WttRecord02CostTable.GetWttRecord02CostTableAsString() : "";
}

// Set<>AsString()
public void SetWttRecord02CostTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttRecord02CostTable == null)
    {
        _WttRecord02CostTable = new WttRecord02CostTable();
    }
    _WttRecord02CostTable.SetWttRecord02CostTableAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttRecord02NonCostsFields
public class WttRecord02NonCostsFields
{
    private static int _size = 195;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBfTrancheTotalUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfTrancheTotalUnits =0;
    
    
    
    
    // [DEBUG] Field: WttTrancheTotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttTrancheTotalUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: WttBfDispUnitsReac, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfDispUnitsReac =0;
    
    
    
    
    // [DEBUG] Field: WttDispUnitsReacYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttDispUnitsReacYtd =0;
    
    
    
    
    // [DEBUG] Field: WttUnderwritingCommission, is_external=, is_static_class=False, static_prefix=
    private decimal _WttUnderwritingCommission =0;
    
    
    
    
    // [DEBUG] Field: WttBookCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBookCost =0;
    
    
    
    
    // [DEBUG] Field: WttIssueSameClass, is_external=, is_static_class=False, static_prefix=
    private string _WttIssueSameClass ="";
    
    
    
    
    // [DEBUG] Field: WttBfDatePrevOpEvent, is_external=, is_static_class=False, static_prefix=
    private int _WttBfDatePrevOpEvent =0;
    
    
    
    
    // [DEBUG] Field: WttDatePrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private WttRecord02NonCostsFields.WttDatePrevOpEventYtd _WttDatePrevOpEventYtd = new WttRecord02NonCostsFields.WttDatePrevOpEventYtd();
    
    
    
    
    // [DEBUG] Field: WttFirstDayDealingPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _WttFirstDayDealingPrice =0;
    
    
    
    
    // [DEBUG] Field: WttBfIndexedCostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfIndexedCostBalance =0;
    
    
    
    
    // [DEBUG] Field: WttIndexedCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttIndexedCostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: WttUnindexableFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttUnindexableFlag ="";
    
    
    // 88-level condition checks for WttUnindexableFlag
    public bool IsWttUnindexableHolding()
    {
        if (this._WttUnindexableFlag == "'1'") return true;
        return false;
    }
    public bool IsWttMerged1982Pool()
    {
        if (this._WttUnindexableFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttGroupTransferFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttGroupTransferFlag ="";
    
    
    // 88-level condition checks for WttGroupTransferFlag
    public bool IsWttGroupTransferBalance()
    {
        if (this._WttGroupTransferFlag == "'1'") return true;
        return false;
    }
    public bool IsWttReorgBalance()
    {
        if (this._WttGroupTransferFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttBfLinkRecordIndic, is_external=, is_static_class=False, static_prefix=
    private int _WttBfLinkRecordIndic =0;
    
    
    
    
    // [DEBUG] Field: WttLinkRecordIndicYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttLinkRecordIndicYtd =0;
    
    
    
    
    // [DEBUG] Field: WttBfIndex85CostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfIndex85CostBalance =0;
    
    
    
    
    // [DEBUG] Field: WttIndex85CostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttIndex85CostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: WttTrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttTrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: WttResetTaperDate, is_external=, is_static_class=False, static_prefix=
    private string _WttResetTaperDate ="";
    
    
    
    
    // [DEBUG] Field: WttNoOfInitialBookCosts, is_external=, is_static_class=False, static_prefix=
    private int _WttNoOfInitialBookCosts =0;
    
    
    
    
    // [DEBUG] Field: WttBfNoOfCostsHeld, is_external=, is_static_class=False, static_prefix=
    private int _WttBfNoOfCostsHeld =0;
    
    
    
    
    // [DEBUG] Field: WttNoOfCostsHeldYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttNoOfCostsHeldYtd =0;
    
    
    
    
    // [DEBUG] Field: WttCapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttCapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WttProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WttProceedsOfDisposal, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProceedsOfDisposal =0;
    
    
    
    
public WttRecord02NonCostsFields() {}

public WttRecord02NonCostsFields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBfTrancheTotalUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttTrancheTotalUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttBfDispUnitsReac(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetWttDispUnitsReacYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetWttUnderwritingCommission(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetWttBookCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetWttIssueSameClass(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttBfDatePrevOpEvent(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(data.Substring(offset, WttDatePrevOpEventYtd.GetSize()));
    offset += 6;
    SetWttFirstDayDealingPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetWttBfIndexedCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttIndexedCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttUnindexableFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttGroupTransferFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttBfLinkRecordIndic(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetWttLinkRecordIndicYtd(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetWttBfIndex85CostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttIndex85CostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttTrancheFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttResetTaperDate(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttNoOfInitialBookCosts(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttBfNoOfCostsHeld(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttNoOfCostsHeldYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttCapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttProceedsOfDisposal(PackedDecimalConverter.ToDecimal(data.Substring(offset, 8)));
    offset += 8;
    
}

// Serialization methods
public string GetWttRecord02NonCostsFieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttTrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttDispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttUnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIssueSameClass.PadRight(1));
    result.Append(_WttBfDatePrevOpEvent.ToString().PadLeft(6, '0'));
    result.Append(_WttDatePrevOpEventYtd.GetWttDatePrevOpEventYtdAsString());
    result.Append(_WttFirstDayDealingPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttUnindexableFlag.PadRight(1));
    result.Append(_WttGroupTransferFlag.PadRight(1));
    result.Append(_WttBfLinkRecordIndic.ToString().PadLeft(1, '0'));
    result.Append(_WttLinkRecordIndicYtd.ToString().PadLeft(1, '0'));
    result.Append(_WttBfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIndex85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttTrancheFlag.PadRight(1));
    result.Append(_WttResetTaperDate.PadRight(1));
    result.Append(_WttNoOfInitialBookCosts.ToString().PadLeft(2, '0'));
    result.Append(_WttBfNoOfCostsHeld.ToString().PadLeft(2, '0'));
    result.Append(_WttNoOfCostsHeldYtd.ToString().PadLeft(2, '0'));
    result.Append(_WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProceedsOfDisposal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWttRecord02NonCostsFieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfTrancheTotalUnits(parsedDec);
    }
    offset += 13;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttTrancheTotalUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfDispUnitsReac(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttDispUnitsReacYtd(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttUnderwritingCommission(parsedDec);
    }
    offset += 7;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBookCost(parsedDec);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttIssueSameClass(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBfDatePrevOpEvent(parsedInt);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(data.Substring(offset, 6));
    }
    else
    {
        _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttFirstDayDealingPrice(parsedDec);
    }
    offset += 11;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfIndexedCostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttIndexedCostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttUnindexableFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttGroupTransferFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBfLinkRecordIndic(parsedInt);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttLinkRecordIndicYtd(parsedInt);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfIndex85CostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttIndex85CostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttTrancheFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttResetTaperDate(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttNoOfInitialBookCosts(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBfNoOfCostsHeld(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttNoOfCostsHeldYtd(parsedInt);
    }
    offset += 2;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttCapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProceedsOfDisposal(parsedDec);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWttBfTrancheTotalUnits()
{
    return _WttBfTrancheTotalUnits;
}

// Standard Setter
public void SetWttBfTrancheTotalUnits(decimal value)
{
    _WttBfTrancheTotalUnits = value;
}

// Get<>AsString()
public string GetWttBfTrancheTotalUnitsAsString()
{
    return _WttBfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfTrancheTotalUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfTrancheTotalUnits = parsed;
}

// Standard Getter
public decimal GetWttTrancheTotalUnitsYtd()
{
    return _WttTrancheTotalUnitsYtd;
}

// Standard Setter
public void SetWttTrancheTotalUnitsYtd(decimal value)
{
    _WttTrancheTotalUnitsYtd = value;
}

// Get<>AsString()
public string GetWttTrancheTotalUnitsYtdAsString()
{
    return _WttTrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttTrancheTotalUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTrancheTotalUnitsYtd = parsed;
}

// Standard Getter
public decimal GetWttBfDispUnitsReac()
{
    return _WttBfDispUnitsReac;
}

// Standard Setter
public void SetWttBfDispUnitsReac(decimal value)
{
    _WttBfDispUnitsReac = value;
}

// Get<>AsString()
public string GetWttBfDispUnitsReacAsString()
{
    return _WttBfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfDispUnitsReacAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfDispUnitsReac = parsed;
}

// Standard Getter
public decimal GetWttDispUnitsReacYtd()
{
    return _WttDispUnitsReacYtd;
}

// Standard Setter
public void SetWttDispUnitsReacYtd(decimal value)
{
    _WttDispUnitsReacYtd = value;
}

// Get<>AsString()
public string GetWttDispUnitsReacYtdAsString()
{
    return _WttDispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttDispUnitsReacYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttDispUnitsReacYtd = parsed;
}

// Standard Getter
public decimal GetWttUnderwritingCommission()
{
    return _WttUnderwritingCommission;
}

// Standard Setter
public void SetWttUnderwritingCommission(decimal value)
{
    _WttUnderwritingCommission = value;
}

// Get<>AsString()
public string GetWttUnderwritingCommissionAsString()
{
    return _WttUnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttUnderwritingCommissionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttUnderwritingCommission = parsed;
}

// Standard Getter
public decimal GetWttBookCost()
{
    return _WttBookCost;
}

// Standard Setter
public void SetWttBookCost(decimal value)
{
    _WttBookCost = value;
}

// Get<>AsString()
public string GetWttBookCostAsString()
{
    return _WttBookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBookCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBookCost = parsed;
}

// Standard Getter
public string GetWttIssueSameClass()
{
    return _WttIssueSameClass;
}

// Standard Setter
public void SetWttIssueSameClass(string value)
{
    _WttIssueSameClass = value;
}

// Get<>AsString()
public string GetWttIssueSameClassAsString()
{
    return _WttIssueSameClass.PadRight(1);
}

// Set<>AsString()
public void SetWttIssueSameClassAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttIssueSameClass = value;
}

// Standard Getter
public int GetWttBfDatePrevOpEvent()
{
    return _WttBfDatePrevOpEvent;
}

// Standard Setter
public void SetWttBfDatePrevOpEvent(int value)
{
    _WttBfDatePrevOpEvent = value;
}

// Get<>AsString()
public string GetWttBfDatePrevOpEventAsString()
{
    return _WttBfDatePrevOpEvent.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetWttBfDatePrevOpEventAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBfDatePrevOpEvent = parsed;
}

// Standard Getter
public WttDatePrevOpEventYtd GetWttDatePrevOpEventYtd()
{
    return _WttDatePrevOpEventYtd;
}

// Standard Setter
public void SetWttDatePrevOpEventYtd(WttDatePrevOpEventYtd value)
{
    _WttDatePrevOpEventYtd = value;
}

// Get<>AsString()
public string GetWttDatePrevOpEventYtdAsString()
{
    return _WttDatePrevOpEventYtd != null ? _WttDatePrevOpEventYtd.GetWttDatePrevOpEventYtdAsString() : "";
}

// Set<>AsString()
public void SetWttDatePrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttDatePrevOpEventYtd == null)
    {
        _WttDatePrevOpEventYtd = new WttDatePrevOpEventYtd();
    }
    _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(value);
}

// Standard Getter
public decimal GetWttFirstDayDealingPrice()
{
    return _WttFirstDayDealingPrice;
}

// Standard Setter
public void SetWttFirstDayDealingPrice(decimal value)
{
    _WttFirstDayDealingPrice = value;
}

// Get<>AsString()
public string GetWttFirstDayDealingPriceAsString()
{
    return _WttFirstDayDealingPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttFirstDayDealingPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttFirstDayDealingPrice = parsed;
}

// Standard Getter
public decimal GetWttBfIndexedCostBalance()
{
    return _WttBfIndexedCostBalance;
}

// Standard Setter
public void SetWttBfIndexedCostBalance(decimal value)
{
    _WttBfIndexedCostBalance = value;
}

// Get<>AsString()
public string GetWttBfIndexedCostBalanceAsString()
{
    return _WttBfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfIndexedCostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfIndexedCostBalance = parsed;
}

// Standard Getter
public decimal GetWttIndexedCostBalanceYtd()
{
    return _WttIndexedCostBalanceYtd;
}

// Standard Setter
public void SetWttIndexedCostBalanceYtd(decimal value)
{
    _WttIndexedCostBalanceYtd = value;
}

// Get<>AsString()
public string GetWttIndexedCostBalanceYtdAsString()
{
    return _WttIndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttIndexedCostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttIndexedCostBalanceYtd = parsed;
}

// Standard Getter
public string GetWttUnindexableFlag()
{
    return _WttUnindexableFlag;
}

// Standard Setter
public void SetWttUnindexableFlag(string value)
{
    _WttUnindexableFlag = value;
}

// Get<>AsString()
public string GetWttUnindexableFlagAsString()
{
    return _WttUnindexableFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttUnindexableFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttUnindexableFlag = value;
}

// Standard Getter
public string GetWttGroupTransferFlag()
{
    return _WttGroupTransferFlag;
}

// Standard Setter
public void SetWttGroupTransferFlag(string value)
{
    _WttGroupTransferFlag = value;
}

// Get<>AsString()
public string GetWttGroupTransferFlagAsString()
{
    return _WttGroupTransferFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttGroupTransferFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttGroupTransferFlag = value;
}

// Standard Getter
public int GetWttBfLinkRecordIndic()
{
    return _WttBfLinkRecordIndic;
}

// Standard Setter
public void SetWttBfLinkRecordIndic(int value)
{
    _WttBfLinkRecordIndic = value;
}

// Get<>AsString()
public string GetWttBfLinkRecordIndicAsString()
{
    return _WttBfLinkRecordIndic.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetWttBfLinkRecordIndicAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBfLinkRecordIndic = parsed;
}

// Standard Getter
public int GetWttLinkRecordIndicYtd()
{
    return _WttLinkRecordIndicYtd;
}

// Standard Setter
public void SetWttLinkRecordIndicYtd(int value)
{
    _WttLinkRecordIndicYtd = value;
}

// Get<>AsString()
public string GetWttLinkRecordIndicYtdAsString()
{
    return _WttLinkRecordIndicYtd.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetWttLinkRecordIndicYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttLinkRecordIndicYtd = parsed;
}

// Standard Getter
public decimal GetWttBfIndex85CostBalance()
{
    return _WttBfIndex85CostBalance;
}

// Standard Setter
public void SetWttBfIndex85CostBalance(decimal value)
{
    _WttBfIndex85CostBalance = value;
}

// Get<>AsString()
public string GetWttBfIndex85CostBalanceAsString()
{
    return _WttBfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfIndex85CostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfIndex85CostBalance = parsed;
}

// Standard Getter
public decimal GetWttIndex85CostBalanceYtd()
{
    return _WttIndex85CostBalanceYtd;
}

// Standard Setter
public void SetWttIndex85CostBalanceYtd(decimal value)
{
    _WttIndex85CostBalanceYtd = value;
}

// Get<>AsString()
public string GetWttIndex85CostBalanceYtdAsString()
{
    return _WttIndex85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttIndex85CostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttIndex85CostBalanceYtd = parsed;
}

// Standard Getter
public string GetWttTrancheFlag()
{
    return _WttTrancheFlag;
}

// Standard Setter
public void SetWttTrancheFlag(string value)
{
    _WttTrancheFlag = value;
}

// Get<>AsString()
public string GetWttTrancheFlagAsString()
{
    return _WttTrancheFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttTrancheFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTrancheFlag = value;
}

// Standard Getter
public string GetWttResetTaperDate()
{
    return _WttResetTaperDate;
}

// Standard Setter
public void SetWttResetTaperDate(string value)
{
    _WttResetTaperDate = value;
}

// Get<>AsString()
public string GetWttResetTaperDateAsString()
{
    return _WttResetTaperDate.PadRight(1);
}

// Set<>AsString()
public void SetWttResetTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttResetTaperDate = value;
}

// Standard Getter
public int GetWttNoOfInitialBookCosts()
{
    return _WttNoOfInitialBookCosts;
}

// Standard Setter
public void SetWttNoOfInitialBookCosts(int value)
{
    _WttNoOfInitialBookCosts = value;
}

// Get<>AsString()
public string GetWttNoOfInitialBookCostsAsString()
{
    return _WttNoOfInitialBookCosts.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttNoOfInitialBookCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttNoOfInitialBookCosts = parsed;
}

// Standard Getter
public int GetWttBfNoOfCostsHeld()
{
    return _WttBfNoOfCostsHeld;
}

// Standard Setter
public void SetWttBfNoOfCostsHeld(int value)
{
    _WttBfNoOfCostsHeld = value;
}

// Get<>AsString()
public string GetWttBfNoOfCostsHeldAsString()
{
    return _WttBfNoOfCostsHeld.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBfNoOfCostsHeldAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBfNoOfCostsHeld = parsed;
}

// Standard Getter
public int GetWttNoOfCostsHeldYtd()
{
    return _WttNoOfCostsHeldYtd;
}

// Standard Setter
public void SetWttNoOfCostsHeldYtd(int value)
{
    _WttNoOfCostsHeldYtd = value;
}

// Get<>AsString()
public string GetWttNoOfCostsHeldYtdAsString()
{
    return _WttNoOfCostsHeldYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttNoOfCostsHeldYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttNoOfCostsHeldYtd = parsed;
}

// Standard Getter
public decimal GetWttCapitalGainLoss()
{
    return _WttCapitalGainLoss;
}

// Standard Setter
public void SetWttCapitalGainLoss(decimal value)
{
    _WttCapitalGainLoss = value;
}

// Get<>AsString()
public string GetWttCapitalGainLossAsString()
{
    return _WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttCapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttCapitalGainLoss = parsed;
}

// Standard Getter
public decimal GetWttProfitLoss()
{
    return _WttProfitLoss;
}

// Standard Setter
public void SetWttProfitLoss(decimal value)
{
    _WttProfitLoss = value;
}

// Get<>AsString()
public string GetWttProfitLossAsString()
{
    return _WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProfitLoss = parsed;
}

// Standard Getter
public decimal GetWttProceedsOfDisposal()
{
    return _WttProceedsOfDisposal;
}

// Standard Setter
public void SetWttProceedsOfDisposal(decimal value)
{
    _WttProceedsOfDisposal = value;
}

// Get<>AsString()
public string GetWttProceedsOfDisposalAsString()
{
    return _WttProceedsOfDisposal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProceedsOfDisposalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProceedsOfDisposal = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttDatePrevOpEventYtd
public class WttDatePrevOpEventYtd
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttYymmPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttYymmPrevOpEventYtd =0;
    
    
    
    
    // [DEBUG] Field: WttDdPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttDdPrevOpEventYtd =0;
    
    
    
    
public WttDatePrevOpEventYtd() {}

public WttDatePrevOpEventYtd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttYymmPrevOpEventYtd(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetWttDdPrevOpEventYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttDatePrevOpEventYtdAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttYymmPrevOpEventYtd.ToString().PadLeft(4, '0'));
    result.Append(_WttDdPrevOpEventYtd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttDatePrevOpEventYtdAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttYymmPrevOpEventYtd(parsedInt);
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttDdPrevOpEventYtd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttYymmPrevOpEventYtd()
{
    return _WttYymmPrevOpEventYtd;
}

// Standard Setter
public void SetWttYymmPrevOpEventYtd(int value)
{
    _WttYymmPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetWttYymmPrevOpEventYtdAsString()
{
    return _WttYymmPrevOpEventYtd.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWttYymmPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttYymmPrevOpEventYtd = parsed;
}

// Standard Getter
public int GetWttDdPrevOpEventYtd()
{
    return _WttDdPrevOpEventYtd;
}

// Standard Setter
public void SetWttDdPrevOpEventYtd(int value)
{
    _WttDdPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetWttDdPrevOpEventYtdAsString()
{
    return _WttDdPrevOpEventYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttDdPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttDdPrevOpEventYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttRecord02CostTable
public class WttRecord02CostTable
{
    private static int _size = 104;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttCostTable, is_external=, is_static_class=False, static_prefix=
    private WttRecord02CostTable.WttCostTable[] _WttCostTable = new WttRecord02CostTable.WttCostTable[200];
    
    public void InitializeWttCostTableArray()
    {
        for (int i = 0; i < 200; i++)
        {
            _WttCostTable[i] = new WttRecord02CostTable.WttCostTable();
        }
    }
    
    
    
public WttRecord02CostTable() {}

public WttRecord02CostTable(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    InitializeWttCostTableArray();
    for (int i = 0; i < 200; i++)
    {
        _WttCostTable[i].SetWttCostTableAsString(data.Substring(offset, 104));
        offset += 104;
    }
    
}

// Serialization methods
public string GetWttRecord02CostTableAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 200; i++)
    {
        result.Append(_WttCostTable[i].GetWttCostTableAsString());
    }
    
    return result.ToString();
}

public void SetWttRecord02CostTableAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 200; i++)
    {
        if (offset + 104 > data.Length) break;
        string val = data.Substring(offset, 104);
        
        _WttCostTable[i].SetWttCostTableAsString(val);
        offset += 104;
    }
}

// Getter and Setter methods

// Array Accessors for WttCostTable
public WttCostTable GetWttCostTableAt(int index)
{
    return _WttCostTable[index];
}

public void SetWttCostTableAt(int index, WttCostTable value)
{
    _WttCostTable[index] = value;
}

// Flattened accessors (index 0)
public WttCostTable GetWttCostTable()
{
    return _WttCostTable != null && _WttCostTable.Length > 0
    ? _WttCostTable[0]
    : new WttCostTable();
}

public void SetWttCostTable(WttCostTable value)
{
    if (_WttCostTable == null || _WttCostTable.Length == 0)
    _WttCostTable = new WttCostTable[1];
    _WttCostTable[0] = value;
}





public static int GetSize()
{
    return _size;
}

// Nested Class: WttCostTable
public class WttCostTable
{
    private static int _size = 104;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBfBalanceUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfBalanceUnits =0;
    
    
    
    
    // [DEBUG] Field: WttBalanceUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBalanceUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: WttUnitsPresent, is_external=, is_static_class=False, static_prefix=
    private string _WttUnitsPresent ="";
    
    
    // 88-level condition checks for WttUnitsPresent
    public bool IsWttRealCost()
    {
        if (this._WttUnitsPresent == "'0'") return true;
        if (this._WttUnitsPresent == "'1'") return true;
        if (this._WttUnitsPresent == "'I'") return true;
        if (this._WttUnitsPresent == "'4'") return true;
        return false;
    }
    public bool IsWttCostWithUnits()
    {
        if (this._WttUnitsPresent == "'1'") return true;
        if (this._WttUnitsPresent == "'I'") return true;
        return false;
    }
    public bool IsWttIndexed1982Cost()
    {
        if (this._WttUnitsPresent == "'I'") return true;
        return false;
    }
    public bool IsWttGiltLoss()
    {
        if (this._WttUnitsPresent == "'2'") return true;
        return false;
    }
    public bool IsWttBfGain()
    {
        if (this._WttUnitsPresent == "'3'") return true;
        return false;
    }
    public bool IsWttSmallDistribution()
    {
        if (this._WttUnitsPresent == "'4'") return true;
        return false;
    }
    public bool IsWttBfIndxn()
    {
        if (this._WttUnitsPresent == "'5'") return true;
        return false;
    }
    public bool IsWttOsLiability()
    {
        if (this._WttUnitsPresent == "'6'") return true;
        return false;
    }
    public bool IsWtt1982Bdv()
    {
        if (this._WttUnitsPresent == "'7'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttBfUnindexedCostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfUnindexedCostBalance =0;
    
    
    
    
    // [DEBUG] Field: WttUnindexedCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttUnindexedCostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: WttBfBudgetDayValue, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfBudgetDayValue =0;
    
    
    
    
    // [DEBUG] Field: WttBudgetDayValueYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBudgetDayValueYtd =0;
    
    
    
    
    // [DEBUG] Field: WttDates, is_external=, is_static_class=False, static_prefix=
    private WttCostTable.WttDates _WttDates = new WttCostTable.WttDates();
    
    
    
    
    // [DEBUG] Field: WttNiNpPiCosts, is_external=, is_static_class=False, static_prefix=
    private string _WttNiNpPiCosts ="";
    
    
    
    
    // [DEBUG] Field: WttTaperDate, is_external=, is_static_class=False, static_prefix=
    private WttCostTable.WttTaperDate _WttTaperDate = new WttCostTable.WttTaperDate();
    
    
    
    
    // [DEBUG] Field: WttBfTaperUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfTaperUnits =0;
    
    
    
    
    // [DEBUG] Field: WttTaperUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttTaperUnitsYtd =0;
    
    
    
    
public WttCostTable() {}

public WttCostTable(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBfBalanceUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttBalanceUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttUnitsPresent(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttBfUnindexedCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttUnindexedCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttBfBudgetDayValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttBudgetDayValueYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    _WttDates.SetWttDatesAsString(data.Substring(offset, WttDates.GetSize()));
    offset += 12;
    SetWttNiNpPiCosts(data.Substring(offset, 1).Trim());
    offset += 1;
    _WttTaperDate.SetWttTaperDateAsString(data.Substring(offset, WttTaperDate.GetSize()));
    offset += 0;
    SetWttBfTaperUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 0)));
    offset += 0;
    SetWttTaperUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 0)));
    offset += 0;
    
}

// Serialization methods
public string GetWttCostTableAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttUnitsPresent.PadRight(1));
    result.Append(_WttBfUnindexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttUnindexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBfBudgetDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBudgetDayValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttDates.GetWttDatesAsString());
    result.Append(_WttNiNpPiCosts.PadRight(1));
    result.Append(_WttTaperDate.GetWttTaperDateAsString());
    result.Append(_WttBfTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWttCostTableAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfBalanceUnits(parsedDec);
    }
    offset += 13;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBalanceUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttUnitsPresent(extracted);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfUnindexedCostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttUnindexedCostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfBudgetDayValue(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBudgetDayValueYtd(parsedDec);
    }
    offset += 15;
    if (offset + 12 <= data.Length)
    {
        _WttDates.SetWttDatesAsString(data.Substring(offset, 12));
    }
    else
    {
        _WttDates.SetWttDatesAsString(data.Substring(offset));
    }
    offset += 12;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttNiNpPiCosts(extracted);
    }
    offset += 1;
    if (offset + 0 <= data.Length)
    {
        _WttTaperDate.SetWttTaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _WttTaperDate.SetWttTaperDateAsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfTaperUnits(parsedDec);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttTaperUnitsYtd(parsedDec);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWttBfBalanceUnits()
{
    return _WttBfBalanceUnits;
}

// Standard Setter
public void SetWttBfBalanceUnits(decimal value)
{
    _WttBfBalanceUnits = value;
}

// Get<>AsString()
public string GetWttBfBalanceUnitsAsString()
{
    return _WttBfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfBalanceUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfBalanceUnits = parsed;
}

// Standard Getter
public decimal GetWttBalanceUnitsYtd()
{
    return _WttBalanceUnitsYtd;
}

// Standard Setter
public void SetWttBalanceUnitsYtd(decimal value)
{
    _WttBalanceUnitsYtd = value;
}

// Get<>AsString()
public string GetWttBalanceUnitsYtdAsString()
{
    return _WttBalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBalanceUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBalanceUnitsYtd = parsed;
}

// Standard Getter
public string GetWttUnitsPresent()
{
    return _WttUnitsPresent;
}

// Standard Setter
public void SetWttUnitsPresent(string value)
{
    _WttUnitsPresent = value;
}

// Get<>AsString()
public string GetWttUnitsPresentAsString()
{
    return _WttUnitsPresent.PadRight(1);
}

// Set<>AsString()
public void SetWttUnitsPresentAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttUnitsPresent = value;
}

// Standard Getter
public decimal GetWttBfUnindexedCostBalance()
{
    return _WttBfUnindexedCostBalance;
}

// Standard Setter
public void SetWttBfUnindexedCostBalance(decimal value)
{
    _WttBfUnindexedCostBalance = value;
}

// Get<>AsString()
public string GetWttBfUnindexedCostBalanceAsString()
{
    return _WttBfUnindexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfUnindexedCostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfUnindexedCostBalance = parsed;
}

// Standard Getter
public decimal GetWttUnindexedCostBalanceYtd()
{
    return _WttUnindexedCostBalanceYtd;
}

// Standard Setter
public void SetWttUnindexedCostBalanceYtd(decimal value)
{
    _WttUnindexedCostBalanceYtd = value;
}

// Get<>AsString()
public string GetWttUnindexedCostBalanceYtdAsString()
{
    return _WttUnindexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttUnindexedCostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttUnindexedCostBalanceYtd = parsed;
}

// Standard Getter
public decimal GetWttBfBudgetDayValue()
{
    return _WttBfBudgetDayValue;
}

// Standard Setter
public void SetWttBfBudgetDayValue(decimal value)
{
    _WttBfBudgetDayValue = value;
}

// Get<>AsString()
public string GetWttBfBudgetDayValueAsString()
{
    return _WttBfBudgetDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfBudgetDayValueAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfBudgetDayValue = parsed;
}

// Standard Getter
public decimal GetWttBudgetDayValueYtd()
{
    return _WttBudgetDayValueYtd;
}

// Standard Setter
public void SetWttBudgetDayValueYtd(decimal value)
{
    _WttBudgetDayValueYtd = value;
}

// Get<>AsString()
public string GetWttBudgetDayValueYtdAsString()
{
    return _WttBudgetDayValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBudgetDayValueYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBudgetDayValueYtd = parsed;
}

// Standard Getter
public WttDates GetWttDates()
{
    return _WttDates;
}

// Standard Setter
public void SetWttDates(WttDates value)
{
    _WttDates = value;
}

// Get<>AsString()
public string GetWttDatesAsString()
{
    return _WttDates != null ? _WttDates.GetWttDatesAsString() : "";
}

// Set<>AsString()
public void SetWttDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttDates == null)
    {
        _WttDates = new WttDates();
    }
    _WttDates.SetWttDatesAsString(value);
}

// Standard Getter
public string GetWttNiNpPiCosts()
{
    return _WttNiNpPiCosts;
}

// Standard Setter
public void SetWttNiNpPiCosts(string value)
{
    _WttNiNpPiCosts = value;
}

// Get<>AsString()
public string GetWttNiNpPiCostsAsString()
{
    return _WttNiNpPiCosts.PadRight(1);
}

// Set<>AsString()
public void SetWttNiNpPiCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttNiNpPiCosts = value;
}

// Standard Getter
public WttTaperDate GetWttTaperDate()
{
    return _WttTaperDate;
}

// Standard Setter
public void SetWttTaperDate(WttTaperDate value)
{
    _WttTaperDate = value;
}

// Get<>AsString()
public string GetWttTaperDateAsString()
{
    return _WttTaperDate != null ? _WttTaperDate.GetWttTaperDateAsString() : "";
}

// Set<>AsString()
public void SetWttTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttTaperDate == null)
    {
        _WttTaperDate = new WttTaperDate();
    }
    _WttTaperDate.SetWttTaperDateAsString(value);
}

// Standard Getter
public decimal GetWttBfTaperUnits()
{
    return _WttBfTaperUnits;
}

// Standard Setter
public void SetWttBfTaperUnits(decimal value)
{
    _WttBfTaperUnits = value;
}

// Get<>AsString()
public string GetWttBfTaperUnitsAsString()
{
    return _WttBfTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfTaperUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfTaperUnits = parsed;
}

// Standard Getter
public decimal GetWttTaperUnitsYtd()
{
    return _WttTaperUnitsYtd;
}

// Standard Setter
public void SetWttTaperUnitsYtd(decimal value)
{
    _WttTaperUnitsYtd = value;
}

// Get<>AsString()
public string GetWttTaperUnitsYtdAsString()
{
    return _WttTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttTaperUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTaperUnitsYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttDates
public class WttDates
{
    private static int _size = 12;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttIndexDate, is_external=, is_static_class=False, static_prefix=
    private WttDates.WttIndexDate _WttIndexDate = new WttDates.WttIndexDate();
    
    
    
    
    // [DEBUG] Field: WttCostDate, is_external=, is_static_class=False, static_prefix=
    private WttDates.WttCostDate _WttCostDate = new WttDates.WttCostDate();
    
    
    
    
public WttDates() {}

public WttDates(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttIndexDate.SetWttIndexDateAsString(data.Substring(offset, WttIndexDate.GetSize()));
    offset += 6;
    _WttCostDate.SetWttCostDateAsString(data.Substring(offset, WttCostDate.GetSize()));
    offset += 6;
    
}

// Serialization methods
public string GetWttDatesAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttIndexDate.GetWttIndexDateAsString());
    result.Append(_WttCostDate.GetWttCostDateAsString());
    
    return result.ToString();
}

public void SetWttDatesAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        _WttIndexDate.SetWttIndexDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _WttIndexDate.SetWttIndexDateAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _WttCostDate.SetWttCostDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _WttCostDate.SetWttCostDateAsString(data.Substring(offset));
    }
    offset += 6;
}

// Getter and Setter methods

// Standard Getter
public WttIndexDate GetWttIndexDate()
{
    return _WttIndexDate;
}

// Standard Setter
public void SetWttIndexDate(WttIndexDate value)
{
    _WttIndexDate = value;
}

// Get<>AsString()
public string GetWttIndexDateAsString()
{
    return _WttIndexDate != null ? _WttIndexDate.GetWttIndexDateAsString() : "";
}

// Set<>AsString()
public void SetWttIndexDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttIndexDate == null)
    {
        _WttIndexDate = new WttIndexDate();
    }
    _WttIndexDate.SetWttIndexDateAsString(value);
}

// Standard Getter
public WttCostDate GetWttCostDate()
{
    return _WttCostDate;
}

// Standard Setter
public void SetWttCostDate(WttCostDate value)
{
    _WttCostDate = value;
}

// Get<>AsString()
public string GetWttCostDateAsString()
{
    return _WttCostDate != null ? _WttCostDate.GetWttCostDateAsString() : "";
}

// Set<>AsString()
public void SetWttCostDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttCostDate == null)
    {
        _WttCostDate = new WttCostDate();
    }
    _WttCostDate.SetWttCostDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttIndexDate
public class WttIndexDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttIndexDateYymm, is_external=, is_static_class=False, static_prefix=
    private WttIndexDate.WttIndexDateYymm _WttIndexDateYymm = new WttIndexDate.WttIndexDateYymm();
    
    
    
    
    // [DEBUG] Field: WttIndexDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WttIndexDateDd =0;
    
    
    
    
public WttIndexDate() {}

public WttIndexDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttIndexDateYymm.SetWttIndexDateYymmAsString(data.Substring(offset, WttIndexDateYymm.GetSize()));
    offset += 4;
    SetWttIndexDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttIndexDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttIndexDateYymm.GetWttIndexDateYymmAsString());
    result.Append(_WttIndexDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttIndexDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _WttIndexDateYymm.SetWttIndexDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _WttIndexDateYymm.SetWttIndexDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttIndexDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WttIndexDateYymm GetWttIndexDateYymm()
{
    return _WttIndexDateYymm;
}

// Standard Setter
public void SetWttIndexDateYymm(WttIndexDateYymm value)
{
    _WttIndexDateYymm = value;
}

// Get<>AsString()
public string GetWttIndexDateYymmAsString()
{
    return _WttIndexDateYymm != null ? _WttIndexDateYymm.GetWttIndexDateYymmAsString() : "";
}

// Set<>AsString()
public void SetWttIndexDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttIndexDateYymm == null)
    {
        _WttIndexDateYymm = new WttIndexDateYymm();
    }
    _WttIndexDateYymm.SetWttIndexDateYymmAsString(value);
}

// Standard Getter
public int GetWttIndexDateDd()
{
    return _WttIndexDateDd;
}

// Standard Setter
public void SetWttIndexDateDd(int value)
{
    _WttIndexDateDd = value;
}

// Get<>AsString()
public string GetWttIndexDateDdAsString()
{
    return _WttIndexDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttIndexDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttIndexDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttIndexDateYymm
public class WttIndexDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttIndexDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WttIndexDateYy =0;
    
    
    
    
    // [DEBUG] Field: WttIndexDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WttIndexDateMm =0;
    
    
    
    
public WttIndexDateYymm() {}

public WttIndexDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttIndexDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttIndexDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttIndexDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttIndexDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WttIndexDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttIndexDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttIndexDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttIndexDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttIndexDateYy()
{
    return _WttIndexDateYy;
}

// Standard Setter
public void SetWttIndexDateYy(int value)
{
    _WttIndexDateYy = value;
}

// Get<>AsString()
public string GetWttIndexDateYyAsString()
{
    return _WttIndexDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttIndexDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttIndexDateYy = parsed;
}

// Standard Getter
public int GetWttIndexDateMm()
{
    return _WttIndexDateMm;
}

// Standard Setter
public void SetWttIndexDateMm(int value)
{
    _WttIndexDateMm = value;
}

// Get<>AsString()
public string GetWttIndexDateMmAsString()
{
    return _WttIndexDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttIndexDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttIndexDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttCostDate
public class WttCostDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttCostDateYymm, is_external=, is_static_class=False, static_prefix=
    private WttCostDate.WttCostDateYymm _WttCostDateYymm = new WttCostDate.WttCostDateYymm();
    
    
    
    
    // [DEBUG] Field: WttCostDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WttCostDateDd =0;
    
    
    
    
public WttCostDate() {}

public WttCostDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttCostDateYymm.SetWttCostDateYymmAsString(data.Substring(offset, WttCostDateYymm.GetSize()));
    offset += 4;
    SetWttCostDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttCostDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttCostDateYymm.GetWttCostDateYymmAsString());
    result.Append(_WttCostDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttCostDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _WttCostDateYymm.SetWttCostDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _WttCostDateYymm.SetWttCostDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttCostDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WttCostDateYymm GetWttCostDateYymm()
{
    return _WttCostDateYymm;
}

// Standard Setter
public void SetWttCostDateYymm(WttCostDateYymm value)
{
    _WttCostDateYymm = value;
}

// Get<>AsString()
public string GetWttCostDateYymmAsString()
{
    return _WttCostDateYymm != null ? _WttCostDateYymm.GetWttCostDateYymmAsString() : "";
}

// Set<>AsString()
public void SetWttCostDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttCostDateYymm == null)
    {
        _WttCostDateYymm = new WttCostDateYymm();
    }
    _WttCostDateYymm.SetWttCostDateYymmAsString(value);
}

// Standard Getter
public int GetWttCostDateDd()
{
    return _WttCostDateDd;
}

// Standard Setter
public void SetWttCostDateDd(int value)
{
    _WttCostDateDd = value;
}

// Get<>AsString()
public string GetWttCostDateDdAsString()
{
    return _WttCostDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttCostDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttCostDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttCostDateYymm
public class WttCostDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttCostDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WttCostDateYy =0;
    
    
    
    
    // [DEBUG] Field: WttCostDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WttCostDateMm =0;
    
    
    
    
public WttCostDateYymm() {}

public WttCostDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttCostDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttCostDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttCostDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttCostDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WttCostDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttCostDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttCostDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttCostDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttCostDateYy()
{
    return _WttCostDateYy;
}

// Standard Setter
public void SetWttCostDateYy(int value)
{
    _WttCostDateYy = value;
}

// Get<>AsString()
public string GetWttCostDateYyAsString()
{
    return _WttCostDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttCostDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttCostDateYy = parsed;
}

// Standard Getter
public int GetWttCostDateMm()
{
    return _WttCostDateMm;
}

// Standard Setter
public void SetWttCostDateMm(int value)
{
    _WttCostDateMm = value;
}

// Get<>AsString()
public string GetWttCostDateMmAsString()
{
    return _WttCostDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttCostDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttCostDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Nested Class: WttTaperDate
public class WttTaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttTaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _WttTaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: WttTaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _WttTaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: WttTaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _WttTaperDateDd ="";
    
    
    
    
public WttTaperDate() {}

public WttTaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttTaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttTaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttTaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetWttTaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttTaperDateYy.PadRight(0));
    result.Append(_WttTaperDateMm.PadRight(0));
    result.Append(_WttTaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetWttTaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttTaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttTaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttTaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetWttTaperDateYy()
{
    return _WttTaperDateYy;
}

// Standard Setter
public void SetWttTaperDateYy(string value)
{
    _WttTaperDateYy = value;
}

// Get<>AsString()
public string GetWttTaperDateYyAsString()
{
    return _WttTaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetWttTaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTaperDateYy = value;
}

// Standard Getter
public string GetWttTaperDateMm()
{
    return _WttTaperDateMm;
}

// Standard Setter
public void SetWttTaperDateMm(string value)
{
    _WttTaperDateMm = value;
}

// Get<>AsString()
public string GetWttTaperDateMmAsString()
{
    return _WttTaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetWttTaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTaperDateMm = value;
}

// Standard Getter
public string GetWttTaperDateDd()
{
    return _WttTaperDateDd;
}

// Standard Setter
public void SetWttTaperDateDd(string value)
{
    _WttTaperDateDd = value;
}

// Get<>AsString()
public string GetWttTaperDateDdAsString()
{
    return _WttTaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetWttTaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}
// Nested Class: WttRecord03Fields
public class WttRecord03Fields
{
    private static int _size = 160;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttNumberOfUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttNumberOfUnits =0;
    
    
    
    
    // [DEBUG] Field: WttProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProceeds =0;
    
    
    
    
    // [DEBUG] Field: WttCapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttCapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WttForce2PctMatchFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttForce2PctMatchFlag ="";
    
    
    
    
    // [DEBUG] Field: WttCgtCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WttCgtCost =0;
    
    
    
    
    // [DEBUG] Field: WttProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WttIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _WttIndexation =0;
    
    
    
    
    // [DEBUG] Field: WttBondDisposal, is_external=, is_static_class=False, static_prefix=
    private string _WttBondDisposal ="";
    
    
    
    
    // [DEBUG] Field: WttConsolidatedFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttConsolidatedFlag ="";
    
    
    
    
    // [DEBUG] Field: WttStoreUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttStoreUnits =0;
    
    
    
    
    // [DEBUG] Field: WttStoreProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WttStoreProceeds =0;
    
    
    
    
    // [DEBUG] Field: WttConsolidateKey, is_external=, is_static_class=False, static_prefix=
    private string _WttConsolidateKey ="";
    
    
    
    
    // [DEBUG] Field: WttFa2003Exemption, is_external=, is_static_class=False, static_prefix=
    private string _WttFa2003Exemption ="";
    
    
    
    
    // [DEBUG] Field: WttGtProRataMatch, is_external=, is_static_class=False, static_prefix=
    private string _WttGtProRataMatch ="";
    
    
    
    
    // [DEBUG] Field: WttGtUseOriginalDates, is_external=, is_static_class=False, static_prefix=
    private string _WttGtUseOriginalDates ="";
    
    
    
    
    // [DEBUG] Field: WttNumberOfUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttNumberOfUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: WttProceedsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProceedsYtd =0;
    
    
    
    
public WttRecord03Fields() {}

public WttRecord03Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttNumberOfUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttCapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttForce2PctMatchFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttCgtCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttIndexation(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttBondDisposal(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttConsolidatedFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttStoreUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttStoreProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttConsolidateKey(data.Substring(offset, 23).Trim());
    offset += 23;
    SetWttFa2003Exemption(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttGtProRataMatch(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttGtUseOriginalDates(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttNumberOfUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetWttProceedsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    
}

// Serialization methods
public string GetWttRecord03FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttNumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttForce2PctMatchFlag.PadRight(1));
    result.Append(_WttCgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBondDisposal.PadRight(1));
    result.Append(_WttConsolidatedFlag.PadRight(1));
    result.Append(_WttStoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttStoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttConsolidateKey.PadRight(23));
    result.Append(_WttFa2003Exemption.PadRight(0));
    result.Append(_WttGtProRataMatch.PadRight(0));
    result.Append(_WttGtUseOriginalDates.PadRight(0));
    result.Append(_WttNumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWttRecord03FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttNumberOfUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProceeds(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttCapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttForce2PctMatchFlag(extracted);
    }
    offset += 1;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttCgtCost(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttIndexation(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttBondDisposal(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttConsolidatedFlag(extracted);
    }
    offset += 1;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttStoreUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttStoreProceeds(parsedDec);
    }
    offset += 15;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetWttConsolidateKey(extracted);
    }
    offset += 23;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttFa2003Exemption(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttGtProRataMatch(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttGtUseOriginalDates(extracted);
    }
    offset += 0;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttNumberOfUnitsYtd(parsedDec);
    }
    offset += 9;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProceedsYtd(parsedDec);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWttNumberOfUnits()
{
    return _WttNumberOfUnits;
}

// Standard Setter
public void SetWttNumberOfUnits(decimal value)
{
    _WttNumberOfUnits = value;
}

// Get<>AsString()
public string GetWttNumberOfUnitsAsString()
{
    return _WttNumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttNumberOfUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttNumberOfUnits = parsed;
}

// Standard Getter
public decimal GetWttProceeds()
{
    return _WttProceeds;
}

// Standard Setter
public void SetWttProceeds(decimal value)
{
    _WttProceeds = value;
}

// Get<>AsString()
public string GetWttProceedsAsString()
{
    return _WttProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProceeds = parsed;
}

// Standard Getter
public decimal GetWttCapitalGainLoss()
{
    return _WttCapitalGainLoss;
}

// Standard Setter
public void SetWttCapitalGainLoss(decimal value)
{
    _WttCapitalGainLoss = value;
}

// Get<>AsString()
public string GetWttCapitalGainLossAsString()
{
    return _WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttCapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttCapitalGainLoss = parsed;
}

// Standard Getter
public string GetWttForce2PctMatchFlag()
{
    return _WttForce2PctMatchFlag;
}

// Standard Setter
public void SetWttForce2PctMatchFlag(string value)
{
    _WttForce2PctMatchFlag = value;
}

// Get<>AsString()
public string GetWttForce2PctMatchFlagAsString()
{
    return _WttForce2PctMatchFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttForce2PctMatchFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttForce2PctMatchFlag = value;
}

// Standard Getter
public decimal GetWttCgtCost()
{
    return _WttCgtCost;
}

// Standard Setter
public void SetWttCgtCost(decimal value)
{
    _WttCgtCost = value;
}

// Get<>AsString()
public string GetWttCgtCostAsString()
{
    return _WttCgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttCgtCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttCgtCost = parsed;
}

// Standard Getter
public decimal GetWttProfitLoss()
{
    return _WttProfitLoss;
}

// Standard Setter
public void SetWttProfitLoss(decimal value)
{
    _WttProfitLoss = value;
}

// Get<>AsString()
public string GetWttProfitLossAsString()
{
    return _WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProfitLoss = parsed;
}

// Standard Getter
public decimal GetWttIndexation()
{
    return _WttIndexation;
}

// Standard Setter
public void SetWttIndexation(decimal value)
{
    _WttIndexation = value;
}

// Get<>AsString()
public string GetWttIndexationAsString()
{
    return _WttIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttIndexationAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttIndexation = parsed;
}

// Standard Getter
public string GetWttBondDisposal()
{
    return _WttBondDisposal;
}

// Standard Setter
public void SetWttBondDisposal(string value)
{
    _WttBondDisposal = value;
}

// Get<>AsString()
public string GetWttBondDisposalAsString()
{
    return _WttBondDisposal.PadRight(1);
}

// Set<>AsString()
public void SetWttBondDisposalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBondDisposal = value;
}

// Standard Getter
public string GetWttConsolidatedFlag()
{
    return _WttConsolidatedFlag;
}

// Standard Setter
public void SetWttConsolidatedFlag(string value)
{
    _WttConsolidatedFlag = value;
}

// Get<>AsString()
public string GetWttConsolidatedFlagAsString()
{
    return _WttConsolidatedFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttConsolidatedFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttConsolidatedFlag = value;
}

// Standard Getter
public decimal GetWttStoreUnits()
{
    return _WttStoreUnits;
}

// Standard Setter
public void SetWttStoreUnits(decimal value)
{
    _WttStoreUnits = value;
}

// Get<>AsString()
public string GetWttStoreUnitsAsString()
{
    return _WttStoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttStoreUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttStoreUnits = parsed;
}

// Standard Getter
public decimal GetWttStoreProceeds()
{
    return _WttStoreProceeds;
}

// Standard Setter
public void SetWttStoreProceeds(decimal value)
{
    _WttStoreProceeds = value;
}

// Get<>AsString()
public string GetWttStoreProceedsAsString()
{
    return _WttStoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttStoreProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttStoreProceeds = parsed;
}

// Standard Getter
public string GetWttConsolidateKey()
{
    return _WttConsolidateKey;
}

// Standard Setter
public void SetWttConsolidateKey(string value)
{
    _WttConsolidateKey = value;
}

// Get<>AsString()
public string GetWttConsolidateKeyAsString()
{
    return _WttConsolidateKey.PadRight(23);
}

// Set<>AsString()
public void SetWttConsolidateKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttConsolidateKey = value;
}

// Standard Getter
public string GetWttFa2003Exemption()
{
    return _WttFa2003Exemption;
}

// Standard Setter
public void SetWttFa2003Exemption(string value)
{
    _WttFa2003Exemption = value;
}

// Get<>AsString()
public string GetWttFa2003ExemptionAsString()
{
    return _WttFa2003Exemption.PadRight(0);
}

// Set<>AsString()
public void SetWttFa2003ExemptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttFa2003Exemption = value;
}

// Standard Getter
public string GetWttGtProRataMatch()
{
    return _WttGtProRataMatch;
}

// Standard Setter
public void SetWttGtProRataMatch(string value)
{
    _WttGtProRataMatch = value;
}

// Get<>AsString()
public string GetWttGtProRataMatchAsString()
{
    return _WttGtProRataMatch.PadRight(0);
}

// Set<>AsString()
public void SetWttGtProRataMatchAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttGtProRataMatch = value;
}

// Standard Getter
public string GetWttGtUseOriginalDates()
{
    return _WttGtUseOriginalDates;
}

// Standard Setter
public void SetWttGtUseOriginalDates(string value)
{
    _WttGtUseOriginalDates = value;
}

// Get<>AsString()
public string GetWttGtUseOriginalDatesAsString()
{
    return _WttGtUseOriginalDates.PadRight(0);
}

// Set<>AsString()
public void SetWttGtUseOriginalDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttGtUseOriginalDates = value;
}

// Standard Getter
public decimal GetWttNumberOfUnitsYtd()
{
    return _WttNumberOfUnitsYtd;
}

// Standard Setter
public void SetWttNumberOfUnitsYtd(decimal value)
{
    _WttNumberOfUnitsYtd = value;
}

// Get<>AsString()
public string GetWttNumberOfUnitsYtdAsString()
{
    return _WttNumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttNumberOfUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttNumberOfUnitsYtd = parsed;
}

// Standard Getter
public decimal GetWttProceedsYtd()
{
    return _WttProceedsYtd;
}

// Standard Setter
public void SetWttProceedsYtd(decimal value)
{
    _WttProceedsYtd = value;
}

// Get<>AsString()
public string GetWttProceedsYtdAsString()
{
    return _WttProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProceedsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProceedsYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}

}}