using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportOptTable Data Structure

public class ReportOptTable
{
    private static int _size = 640;
    // [DEBUG] Class: ReportOptTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ReportOptLine, is_external=, is_static_class=False, static_prefix=
    private string[] _ReportOptLine = new string[8];
    
    
    
    
    
    // Serialization methods
    public string GetReportOptTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 8; i++)
        {
            result.Append(_ReportOptLine[i].PadRight(80));
        }
        
        return result.ToString();
    }
    
    public void SetReportOptTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 8; i++)
        {
            if (offset + 80 > data.Length) break;
            string val = data.Substring(offset, 80);
            
            _ReportOptLine[i] = val.Trim();
            offset += 80;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportOptTableAsString();
    }
    // Set<>String Override function
    public void SetReportOptTable(string value)
    {
        SetReportOptTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for ReportOptLine
    public string GetReportOptLineAt(int index)
    {
        return _ReportOptLine[index];
    }
    
    public void SetReportOptLineAt(int index, string value)
    {
        _ReportOptLine[index] = value;
    }
    
    public string GetReportOptLineAsStringAt(int index)
    {
        return _ReportOptLine[index].PadRight(80);
    }
    
    public void SetReportOptLineAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _ReportOptLine[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetReportOptLine()
    {
        return _ReportOptLine != null && _ReportOptLine.Length > 0
        ? _ReportOptLine[0]
        : default(string);
    }
    
    public void SetReportOptLine(string value)
    {
        if (_ReportOptLine == null || _ReportOptLine.Length == 0)
        _ReportOptLine = new string[1];
        _ReportOptLine[0] = value;
    }
    
    public string GetReportOptLineAsString()
    {
        return _ReportOptLine != null && _ReportOptLine.Length > 0
        ? _ReportOptLine[0].ToString()
        : string.Empty;
    }
    
    public void SetReportOptLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_ReportOptLine == null || _ReportOptLine.Length == 0)
        _ReportOptLine = new string[1];
        
        _ReportOptLine[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}