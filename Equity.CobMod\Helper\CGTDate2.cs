﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.Cgtdate2DTO;

namespace EquityProject.Cgtdate2PGM
{
    // Cgtdate2 Class Definition
    public class Cgtdate2
    {
        // Declare Cgtdate2 Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare Cgtdate2 Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        // Run() method - Entry point
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Store passed parameters if needed by other potential methods
            _gvar = gvar;
            _ivar = ivar;

            // Call the main processing logic paragraph
            AMain(gvar, ivar);

            // Corresponds to EXIT PROGRAM / STOP RUN (end of execution flow)
        }

        // CallSub() method - Placeholder
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here if needed
            Console.WriteLine("CallSub method called (placeholder).");
        }

        // Methods representing paras under procedure division
        public void AMain(Gvar gvar, Ivar ivar)
        {
            // Get the main data structure from Ivar
            Cgtdate2ParDate parDate = ivar.GetCgtdate2ParDate();
            if (parDate == null)
            {
                Console.Error.WriteLine("Error: CGTDATE2 - Input parameter data (Cgtdate2ParDate) is null.");
                // Cannot proceed without data, potentially throw or handle error
                return; // Exit paragraph/method
            }

            // Extract necessary values from DTO for easier use and clarity
            // Use the flattened accessors provided in Cgtdate2ParDate for convenience
            string yyStr = parDate.GetCgtdate2Yy();
            string mmStr = parDate.GetCgtdate2Mm(); // String version if needed
            string ddStr = parDate.GetCgtdate2Dd(); // String version if needed
            int mmInt = parDate.GetCgtdate2Mm9();   // Numeric version for comparisons
            int ddInt = parDate.GetCgtdate2Dd9();   // Numeric version for comparisons

            // --- Implement COBOL 88 Level Logic in C# ---

            // 88 valid-year: Check if yyStr is numeric "00" through "99"
            bool isValidYear = int.TryParse(yyStr, out int yyInt) && yyInt >= 0 && yyInt <= 99;

            // 88 valid-month: Use the numeric month (mmInt)
            bool isValidMonth = mmInt >= 1 && mmInt <= 12;

            // 88 valid-day-1-31: Use the numeric day (ddInt)
            bool isValidDay1_31 = ddInt >= 1 && ddInt <= 31;

            // 88 31-day-month: Check mmInt against the list of 31-day months
            bool is31DayMonth = new[] { 1, 3, 5, 7, 8, 10, 12 }.Contains(mmInt);

            // 88 feb-month: Check mmInt
            bool isFebMonth = (mmInt == 2);

            // 88 leap-year: Check the numeric year (yyInt). Requires valid year first.
            // Mimics COBOL 88 level based on YY value being divisible by 4
            bool isLeapYear = isValidYear && (yyInt % 4 == 0);

            // --- Translate the Main IF Statement ---
            // Determine if the date is invalid based on the combined conditions
            bool isInvalid;

            // IF (not valid-year)
            if (!isValidYear)
            {
                isInvalid = true;
            }
            // or (not valid-month)
            else if (!isValidMonth)
            {
                isInvalid = true;
            }
            // or (not valid-day-1-31) - Basic range check
            else if (!isValidDay1_31)
            {
                isInvalid = true;
            }
            // or (not 31-day-month and CGTDATE2-DD-9 = 31)
            // Checks if it's day 31 in a month that *doesn't* have 31 days
            else if (!is31DayMonth && ddInt == 31)
            {
                isInvalid = true;
            }
            // or (feb-month and ((CGTDATE2-DD-9 = 30) or (not leap-year and CGTDATE2-DD-9 = 29)))
            // Checks for invalid Feb days: day 30 OR day 29 in a non-leap year
            else if (isFebMonth && (ddInt == 30 || (!isLeapYear && ddInt == 29)))
            {
                isInvalid = true;
            }
            // --- If none of the invalid conditions were met ---
            else
            {
                // All checks passed, date is considered valid by the rules
                isInvalid = false;
            }

            // --- Set the Output Century Code (CGTDATE2-CCC) ---
            if (isInvalid)
            {
                // MOVE CGTDATE2-YY TO CGTDATE2-CCC
                // Use the DTO setter to update the CCC field
                parDate.SetCgtdate2Ccc(yyStr);
            }
            else // Date is considered valid
            {
                // IF CGTDATE2-YY > "44"
                // Perform string comparison as in COBOL
                if (string.Compare(yyStr, "44") > 0) // yyStr is "45" through "99"
                {
                    // MOVE "19" TO CGTDATE2-CCC
                    parDate.SetCgtdate2Ccc("19");
                }
                else // yyStr is "00" through "44"
                {
                    // MOVE "20" TO CGTDATE2-CCC
                    parDate.SetCgtdate2Ccc("20");
                }
            }
            // End of A-MAIN logic
        }
    }
}
