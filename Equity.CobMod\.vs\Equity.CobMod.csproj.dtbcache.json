{"RootPath": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Business Logic Layer\\Equity.CobMod", "ProjectFileName": "Equity.CobMod.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Computations\\Eqtye.cs"}, {"SourceFile": "Exports\\Cgtdaily.cs"}, {"SourceFile": "Exports\\Cgthbose.cs"}, {"SourceFile": "Exports\\Cgtscot1.cs"}, {"SourceFile": "Exports\\Cgtscot2.cs"}, {"SourceFile": "Facade\\EquityFacadeClass.cs"}, {"SourceFile": "Facade\\EquityRouter.cs"}, {"SourceFile": "FIleHandlers\\AssetUsageCalendarDAL.cs"}, {"SourceFile": "FIleHandlers\\COUNTRYDAL.cs"}, {"SourceFile": "FIleHandlers\\Elcggio.cs"}, {"SourceFile": "FIleHandlers\\GetTemporaryBalancesDeleteNo.cs"}, {"SourceFile": "FIleHandlers\\GroupDAL.cs"}, {"SourceFile": "FIleHandlers\\MasterComputationalDAL.cs"}, {"SourceFile": "FIleHandlers\\MasterDAL.cs"}, {"SourceFile": "FIleHandlers\\ParametersDAL.cs"}, {"SourceFile": "FIleHandlers\\PriceDAL.cs"}, {"SourceFile": "FIleHandlers\\RPIDAL.cs"}, {"SourceFile": "FIleHandlers\\RunLogDAL.cs"}, {"SourceFile": "FIleHandlers\\TaperRateDAL.cs"}, {"SourceFile": "FIleHandlers\\UserFundProcessor.cs"}, {"SourceFile": "FIleHandlers\\UserFundsDAL.cs"}, {"SourceFile": "Helper\\Cgdate.cs"}, {"SourceFile": "Helper\\Cgtabort.cs"}, {"SourceFile": "Helper\\Cgtdate2.cs"}, {"SourceFile": "Helper\\Cgtdel.cs"}, {"SourceFile": "Helper\\Cgtinvrt.cs"}, {"SourceFile": "Helper\\Cgtlock.cs"}, {"SourceFile": "Helper\\Cgtname.cs"}, {"SourceFile": "Helper\\Cgtsched.cs"}, {"SourceFile": "Helper\\Cgtstat.cs"}, {"SourceFile": "Helper\\Cgttemp.cs"}, {"SourceFile": "Helper\\Cgttemp2.cs"}, {"SourceFile": "Helper\\CheckCat.cs"}, {"SourceFile": "Helper\\Cgtprice.cs"}, {"SourceFile": "Helper\\Eqtdebug.cs"}, {"SourceFile": "Helper\\Eqtlog.cs"}, {"SourceFile": "Helper\\Eqtpath.cs"}, {"SourceFile": "Helper\\Equsecal.cs"}, {"SourceFile": "Helper\\TempData.cs"}, {"SourceFile": "Models\\CGDate\\Gvar.cs"}, {"SourceFile": "Models\\CGDate\\Ivar.cs"}, {"SourceFile": "Models\\CGDate\\LValidDate.cs"}, {"SourceFile": "Models\\CGDate\\Ws2MonthTable.cs"}, {"SourceFile": "Models\\CGDate\\Ws3WorkArea.cs"}, {"SourceFile": "Models\\CGDate\\Ws4DisplayScreen.cs"}, {"SourceFile": "Models\\CGTABORT\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\CgtstatLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTABORT\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTABORT\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\EquityErrorMessages.cs"}, {"SourceFile": "Models\\CGTABORT\\EquityParameters.cs"}, {"SourceFile": "Models\\CGTABORT\\EquityStatusRecord.cs"}, {"SourceFile": "Models\\CGTABORT\\Filler21.cs"}, {"SourceFile": "Models\\CGTABORT\\Gvar.cs"}, {"SourceFile": "Models\\CGTABORT\\Ivar.cs"}, {"SourceFile": "Models\\CGTABORT\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTDAILY\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTDAILY\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTDAILY\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTDAILY\\D171Record.cs"}, {"SourceFile": "Models\\CGTDAILY\\DateStamp.cs"}, {"SourceFile": "Models\\CGTDAILY\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTDAILY\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTDAILY\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\Fvar.cs"}, {"SourceFile": "Models\\CGTDAILY\\Gvar.cs"}, {"SourceFile": "Models\\CGTDAILY\\Ivar.cs"}, {"SourceFile": "Models\\CGTDAILY\\ReportFile.cs"}, {"SourceFile": "Models\\CGTDAILY\\WHeaderRecord.cs"}, {"SourceFile": "Models\\CGTDAILY\\WInitialisedRecord.cs"}, {"SourceFile": "Models\\CGTDate2\\Cgtdate2ParDate.cs"}, {"SourceFile": "Models\\CGTDate2\\Gvar.cs"}, {"SourceFile": "Models\\CGTDate2\\Ivar.cs"}, {"SourceFile": "Models\\CGTDel\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\CGTDel\\Gvar.cs"}, {"SourceFile": "Models\\CGTDel\\Ivar.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D163Record.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D164Record.cs"}, {"SourceFile": "Models\\CGTHBOSE\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Fvar.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Gvar.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Ivar.cs"}, {"SourceFile": "Models\\CGTHBOSE\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTHBOSE\\ReportFile.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WInitialisedRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WTempRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WTodaysDate.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WWorkFields.cs"}, {"SourceFile": "Models\\CGTInvert\\Gvar.cs"}, {"SourceFile": "Models\\CGTInvert\\Ivar.cs"}, {"SourceFile": "Models\\CGTInvert\\LKey.cs"}, {"SourceFile": "Models\\CGTInvert\\WWorkArea.cs"}, {"SourceFile": "Models\\CGT1WinLinkage.cs"}, {"SourceFile": "Models\\CgtAbortLinkage.cs"}, {"SourceFile": "Models\\CgtinvrtLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtlockLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTLock\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTLock\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\D4Record.cs"}, {"SourceFile": "Models\\CGTLock\\Gvar.cs"}, {"SourceFile": "Models\\CGTLock\\Ivar.cs"}, {"SourceFile": "Models\\CGTLock\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CgtMessLinkage.cs"}, {"SourceFile": "Models\\CGTNAME\\Gvar.cs"}, {"SourceFile": "Models\\CGTNAME\\IlnameLinkage.cs"}, {"SourceFile": "Models\\CGTNAME\\Ivar.cs"}, {"SourceFile": "Models\\CGTNAME\\WWords.cs"}, {"SourceFile": "Models\\CgtPriceLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate3Linkage.cs"}, {"SourceFile": "Models\\CGTPrice\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\CgtpriceLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\D167Record.cs"}, {"SourceFile": "Models\\CGTPrice\\D3Record.cs"}, {"SourceFile": "Models\\CGTPrice\\D43Record.cs"}, {"SourceFile": "Models\\CGTPrice\\D4Record.cs"}, {"SourceFile": "Models\\CGTPrice\\Gvar.cs"}, {"SourceFile": "Models\\CGTPrice\\Ivar.cs"}, {"SourceFile": "Models\\CGTPrice\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTPrice\\WStorageForPricing.cs"}, {"SourceFile": "Models\\CGTSched\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\D100PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D102PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D1Detail.cs"}, {"SourceFile": "Models\\CGTSched\\D21PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D22PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D2Detail.cs"}, {"SourceFile": "Models\\CGTSched\\D82PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\Filler60.cs"}, {"SourceFile": "Models\\CGTSched\\Fvar.cs"}, {"SourceFile": "Models\\CGTSched\\Gvar.cs"}, {"SourceFile": "Models\\CGTSched\\H1NHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1RHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1THeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1UHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1XHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H2Heading.cs"}, {"SourceFile": "Models\\CGTSched\\Ivar.cs"}, {"SourceFile": "Models\\CGTSched\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSched\\PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\PrintRecTable.cs"}, {"SourceFile": "Models\\CGTSCOT1\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT1\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSCOT1\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Cgtscot1Linkage.cs"}, {"SourceFile": "Models\\CGTSCOT1\\D75Record.cs"}, {"SourceFile": "Models\\CGTSCOT1\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSCOT1\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSCOT1\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSCOT1\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Filler72.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Fvar.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Gvar.cs"}, {"SourceFile": "Models\\CGTSCOT1\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Ivar.cs"}, {"SourceFile": "Models\\CGTSCOT1\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSCOT1\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSCOT1\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSCOT1\\UGainsDataRecord.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WInitialRecord.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSCOT2\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Cgtscot2Linkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSCOT2\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\DTsbRecord.cs"}, {"SourceFile": "Models\\CGTSCOT2\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSCOT2\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSCOT2\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Filler48.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Fvar.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Gvar.cs"}, {"SourceFile": "Models\\CGTSCOT2\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Ivar.cs"}, {"SourceFile": "Models\\CGTSCOT2\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSCOT2\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSCOT2\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSCOT2\\UGainsDataRecord.cs"}, {"SourceFile": "Models\\CGTSCOT2\\WIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSCOT2\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSKR1\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKR1\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKR1\\D72Record.cs"}, {"SourceFile": "Models\\CGTSKR1\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKR1\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\Filler106.cs"}, {"SourceFile": "Models\\CGTSKR1\\Filler107.cs"}, {"SourceFile": "Models\\CGTSKR1\\Fvar.cs"}, {"SourceFile": "Models\\CGTSKR1\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKR1\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportColHd.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportD13Count.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportDtl.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportDtlCount.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportFinal.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportFund.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportHd1.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportHd2.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportHd3.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportLastLine.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportQuitLine.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportSedol.cs"}, {"SourceFile": "Models\\CGTSKR1\\WIds.cs"}, {"SourceFile": "Models\\CGTSKR2\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKR2\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKR2\\D73Record.cs"}, {"SourceFile": "Models\\CGTSKR2\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKR2\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\Fvar.cs"}, {"SourceFile": "Models\\CGTSKR2\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKR2\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportColHd.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportD13Count.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportDtlCount.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportFinal.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportFund.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd1.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd2.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd2A.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd3.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportLastLine.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportQuitLine.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportSdl.cs"}, {"SourceFile": "Models\\CGTSKR2\\WReportFields.cs"}, {"SourceFile": "Models\\CGTSKR3\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSKR3\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKR3\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKR3\\D74Record.cs"}, {"SourceFile": "Models\\CGTSKR3\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKR3\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\Fvar.cs"}, {"SourceFile": "Models\\CGTSKR3\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKR3\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKR3\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSKR3\\WInitialRecord.cs"}, {"SourceFile": "Models\\CgtstatLinkage.cs"}, {"SourceFile": "Models\\CGTStat\\CgtstatLinkage.cs"}, {"SourceFile": "Models\\CGTStat\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTStat\\FileStatus.cs"}, {"SourceFile": "Models\\CGTStat\\Gvar.cs"}, {"SourceFile": "Models\\CGTStat\\Ivar.cs"}, {"SourceFile": "Models\\CGTStat\\MessageTabAndBrew.cs"}, {"SourceFile": "Models\\CGTStat\\MessageTable.cs"}, {"SourceFile": "Models\\CGTStat\\WBinaryField.cs"}, {"SourceFile": "Models\\CGTStat\\WDefaultMessages.cs"}, {"SourceFile": "Models\\CGTStat\\WStatus.cs"}, {"SourceFile": "Models\\CGTStat\\WUnknown.cs"}, {"SourceFile": "Models\\CGTTEMP2\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\CGTTEMP2\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTTEMP2\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTTEMP2\\Fvar.cs"}, {"SourceFile": "Models\\CGTTEMP2\\Gvar.cs"}, {"SourceFile": "Models\\CGTTEMP2\\Ivar.cs"}, {"SourceFile": "Models\\CGTTEMP2\\TempFile.cs"}, {"SourceFile": "Models\\CGTTEMP2\\TempRecord.cs"}, {"SourceFile": "Models\\CGTTEMP2\\TimingLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\Fvar.cs"}, {"SourceFile": "Models\\CGTTemp\\Gvar.cs"}, {"SourceFile": "Models\\CGTTemp\\Ivar.cs"}, {"SourceFile": "Models\\CGTTemp\\TempFile.cs"}, {"SourceFile": "Models\\CGTTemp\\TempRecord.cs"}, {"SourceFile": "Models\\CGTTemp\\TimingLinkage.cs"}, {"SourceFile": "Models\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CobolConstants.cs"}, {"SourceFile": "Models\\CommonLinkage.cs"}, {"SourceFile": "Models\\Common\\CommonUtils.cs"}, {"SourceFile": "Models\\Common\\EquityGlobalParms.cs"}, {"SourceFile": "Models\\Common\\GlobalVars.cs"}, {"SourceFile": "Models\\D133Record.cs"}, {"SourceFile": "Models\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\D13CostElement.cs"}, {"SourceFile": "Models\\D13RecordHolder.cs"}, {"SourceFile": "Models\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\D37DetailRecord.cs"}, {"SourceFile": "Models\\D37HeaderRecord.cs"}, {"SourceFile": "Models\\D37Record.cs"}, {"SourceFile": "Models\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\D8Record.cs"}, {"SourceFile": "Models\\D8WindowsRecord.cs"}, {"SourceFile": "Models\\ELCGGIO\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\ELCGGIO\\CommonLinkage.cs"}, {"SourceFile": "Models\\ELCGGIO\\ElcggioLink1.cs"}, {"SourceFile": "Models\\ELCGGIO\\ElcggioLink2.cs"}, {"SourceFile": "Models\\ELCGGIO\\Gvar.cs"}, {"SourceFile": "Models\\ELCGGIO\\Ivar.cs"}, {"SourceFile": "Models\\ELCGGIO\\LFileRecordArea.cs"}, {"SourceFile": "Models\\EnvironmentSettings.cs"}, {"SourceFile": "Models\\EQTCALC\\EquityParameters.cs"}, {"SourceFile": "Models\\EQTDEBUG\\CommonLinkage.cs"}, {"SourceFile": "Models\\EQTDEBUG\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\EQTDEBUG\\Gvar.cs"}, {"SourceFile": "Models\\EQTDEBUG\\Ivar.cs"}, {"SourceFile": "Models\\EqtEnvnmLinkage.cs"}, {"SourceFile": "Models\\EqtLog\\Gvar.cs"}, {"SourceFile": "Models\\EqtLog\\Ivar.cs"}, {"SourceFile": "Models\\EqtLog\\LinkageArea1.cs"}, {"SourceFile": "Models\\EqtLog\\LinkageArea2.cs"}, {"SourceFile": "Models\\EqtLog\\LogMessage.cs"}, {"SourceFile": "Models\\eqtpath\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\eqtpath\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\eqtpath\\Gvar.cs"}, {"SourceFile": "Models\\eqtpath\\Ivar.cs"}, {"SourceFile": "Models\\eqtpath\\SplitParams.cs"}, {"SourceFile": "Models\\EQTYE\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\EQTYE\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\EQTYE\\CgtsplitLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CommonLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\D8Record.cs"}, {"SourceFile": "Models\\EQTYE\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\EquityErrorMessages.cs"}, {"SourceFile": "Models\\EQTYE\\EquityParameters.cs"}, {"SourceFile": "Models\\EQTYE\\Filler22.cs"}, {"SourceFile": "Models\\EQTYE\\Gvar.cs"}, {"SourceFile": "Models\\EQTYE\\Ivar.cs"}, {"SourceFile": "Models\\EQTYE\\LFileRecordArea.cs"}, {"SourceFile": "Models\\EQTYE\\LkCgtMasterRecord.cs"}, {"SourceFile": "Models\\EQTYE\\ParmInfo.cs"}, {"SourceFile": "Models\\EQTYE\\WLog.cs"}, {"SourceFile": "Models\\EQTYE\\WTest.cs"}, {"SourceFile": "Models\\EquityGlobalParms.cs"}, {"SourceFile": "Models\\EquityGlobal.cs"}, {"SourceFile": "Models\\EquityParameters.cs"}, {"SourceFile": "Models\\EquityRouterLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\CommonLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\D153Record.cs"}, {"SourceFile": "Models\\EQUSECAL\\D4Record.cs"}, {"SourceFile": "Models\\EQUSECAL\\D8Record.cs"}, {"SourceFile": "Models\\EQUSECAL\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\EqusecalLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\Gvar.cs"}, {"SourceFile": "Models\\EQUSECAL\\Ivar.cs"}, {"SourceFile": "Models\\EQUSECAL\\LFileRecordArea.cs"}, {"SourceFile": "Models\\ExporterSettings.cs"}, {"SourceFile": "Models\\FileAction.cs"}, {"SourceFile": "Models\\GeneralParameters.cs"}, {"SourceFile": "Models\\GroupDataLinkage.cs"}, {"SourceFile": "Models\\LinkageArea.cs"}, {"SourceFile": "Models\\LinkageIDs.cs"}, {"SourceFile": "Models\\LinkageRequests.cs"}, {"SourceFile": "Models\\LkPanels.cs"}, {"SourceFile": "Models\\LogMessage.cs"}, {"SourceFile": "Models\\MasterAcquisitionDARecord.cs"}, {"SourceFile": "Models\\MasterBalanceCostDARecord.cs"}, {"SourceFile": "Models\\MasterBalanceDARecord.cs"}, {"SourceFile": "Models\\MasterDADto.cs"}, {"SourceFile": "Models\\MasterDALResult.cs"}, {"SourceFile": "Models\\MasterDisposalDARecord.cs"}, {"SourceFile": "Models\\MasterHoldingDARecord.cs"}, {"SourceFile": "Models\\PathSettings.cs"}, {"SourceFile": "Models\\TimingLinkage.cs"}, {"SourceFile": "Models\\UserFundRecord.cs"}, {"SourceFile": "Models\\UserFundsDARecord.cs"}, {"SourceFile": "Models\\UserInfoDARecord.cs"}, {"SourceFile": "Models\\UserInfoRecord.cs"}, {"SourceFile": "Models\\WTSedolHeaders.cs"}, {"SourceFile": "Models\\WTTCCosts.cs"}, {"SourceFile": "Models\\WTTransactions.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Reports\\Cgtskr1.cs"}, {"SourceFile": "Reports\\Cgtskr2.cs"}, {"SourceFile": "Reports\\Cgtskr3.cs"}, {"SourceFile": "Utils\\Comp3Converter.cs"}, {"SourceFile": "Utils\\Constants.cs"}, {"SourceFile": "Utils\\MoveCorr.cs"}], "References": [{"Reference": "C:\\Windows\\Microsoft.NET\\Framework64\\v2.0.50727\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Reference Assemblies\\Microsoft\\Framework\\v3.5\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.NET\\Framework64\\v2.0.50727\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.NET\\Framework64\\v2.0.50727\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.NET\\Framework64\\v2.0.50727\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Business Logic Layer\\Equity.CobolDataTransformation\\bin\\Debug\\WK.UK.CCH.Equity.CobolDataTransformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Business Logic Layer\\Equity.CobolDataTransformation\\bin\\Debug\\WK.UK.CCH.Equity.CobolDataTransformation.dll"}, {"Reference": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Foundation Layer\\Equity.CommonTypes\\bin\\Debug\\WK.UK.CCH.Equity.CommonTypes.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Foundation Layer\\Equity.CommonTypes\\bin\\Debug\\WK.UK.CCH.Equity.CommonTypes.dll"}, {"Reference": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Foundation Layer\\Equity.Utilities\\bin\\Debug\\WK.UK.CCH.Equity.Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Foundation Layer\\Equity.Utilities\\bin\\Debug\\WK.UK.CCH.Equity.Utilities.dll"}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\GitWorkspace\\Legacy4-WK\\Equity.Net\\Sources\\Business Logic Layer\\Equity.CobMod\\bin\\Debug\\Legacy4.Equity.CobMod.dll", "OutputItemRelativePath": "Legacy4.Equity.CobMod.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}