using System;
using System.Text;
using EquityProject.Cgtsing4DTO;
namespace EquityProject.Cgtsing4PGM
{
    // Cgtsing4 Class Definition

    //Cgtsing4 Class Constructor
    public class Cgtsing4
    {
        /// <summary>
        /// Helper method to check if a string is numeric (COBOL NUMERIC class test)
        /// </summary>
        private bool IsNumeric(string value)
        {
            if (string.IsNullOrEmpty(value)) return false;
            return decimal.TryParse(value, out _);
        }
        // Declare Cgtsing4 Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private string _highValues = "";

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
            Mainline(fvar, gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// Mainline method that implements the COBOL paragraph MAINLINE.
        /// </summary>
        /// <remarks>
        /// The original COBOL logic performs initialization, reads and accumulates data until
        /// not successful or the quit process flag is set, then performs the ending process.
        /// </remarks>
        /// <param name="fvar">Fvar parameter containing COBOL working storage variables</param>
        /// <param name="gvar">Gvar parameter containing COBOL global variables and linkages</param>
        /// <param name="ivar">Ivar parameter containing COBOL input variables</param>
        public void Mainline(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // PERFORM A-INITIALISE
            AInitialise(fvar, gvar, ivar);

            // PERFORM B-READ-ACCUMULATE UNTIL NOT SUCCESSFUL OR QUIT-PROCESS
            while (!(!gvar.GetCgtfilesLinkage().IsSuccessful() ||
                     gvar.IsQuitProcess()))
            {
                BReadAccumulate(fvar, gvar, ivar);
            }

            // PERFORM D-END
            DEnd(fvar, gvar, ivar);

            // COBOL EXIT PROGRAM and STOP RUN would normally terminate the program
            // but in C# we let this method complete naturally
        }
        /// <summary>
        /// AInitialise - Initializes the application by setting up configuration, file paths, and loading required data.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: aInitialise
        /// This method handles:
        /// 1. Configuration setup
        /// 2. File path initialization
        /// 3. Data loading including taper rates
        /// 4. Report/export file setup
        /// 5. Logging initialization
        /// </remarks>
        /// <param name="fvar">Function variables</param>
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar">Instance variables</param>
        public void AInitialise(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Configuration setup
            gvar.GetElcgmioLinkage2().SetElcgmioLinkage2(Gvar.NEW_DERIVATIVE_EXPORT_FORMAT);
            XCallMfHandlerForConfig(fvar, gvar, ivar);
            gvar.SetWNewDerivativeExportFormat(gvar.GetWConfigItem());

            // File path setup for footer text
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
            gvar.GetEqtpathLinkage().SetEqtpathFileName("TaperFooter.txt");
            XCallEqtpath(fvar, gvar, ivar);
            gvar.SetLFileRecordAreaAsString(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // Read footer text file
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.TRANSACTION_FILE);
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_RECORD);
                XCallCgtfiles(fvar, gvar, ivar);
                if (gvar.GetCgtfilesLinkage().IsSuccessful())
                {
                    gvar.GetReportFooter().SetReportFooterText(gvar.GetLFileRecordArea().ToString());
                    gvar.GetReportFooter2().SetReportFooterText2(gvar.GetLFileRecordArea().ToString());
                }
            }
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            // Set user and report numbers across various files
            gvar.GetCommonLinkage().SetLUserNo(ivar.GetCgtsing3Linkage().GetCgtsing3UserNo());
            gvar.GetReportFile().SetReportUserNo(ivar.GetCgtsing3Linkage().GetCgtsing3UserNo().ToString());
            gvar.GetExportFile().SetExportUserNo(ivar.GetCgtsing3Linkage().GetCgtsing3UserNo().ToString());
            gvar.GetLossExportFile().SetLossExportUserNo(ivar.GetCgtsing3Linkage().GetCgtsing3UserNo().ToString());
            gvar.GetCgttempLinkage().SetCgttempUserNo(ivar.GetCgtsing3Linkage().GetCgtsing3UserNo());

            gvar.GetCgtfilesLinkage().SetLReportNo(ivar.GetCgtsing3Linkage().GetCgtsing3ReportNumber().ToString());
            gvar.GetReportFile().SetReportGenNo(ivar.GetCgtsing3Linkage().GetCgtsing3ReportNumber().ToString());
            gvar.GetExportFile().SetExportGenNo(ivar.GetCgtsing3Linkage().GetCgtsing3ReportNumber().ToString());
            gvar.GetCgttempLinkage().SetCgttempReportNumber(ivar.GetCgtsing3Linkage().GetCgtsing3ReportNumber());

            // Set file and report types based on schedule type
            if (ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R")
            {
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.REALISED_DATA_FILE);
                gvar.GetReportFile().SetReportType("RW");
                gvar.GetExportFile().SetExportType("RY");
                gvar.GetReportHeader1().SetReportTitle("SCHEDULE OF REALISED TAPERED GAINS");
            }
            else
            {
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.UNREALISED_DATA_FILE);
                gvar.GetReportFile().SetReportType("RX");
                gvar.GetExportFile().SetExportType("RZ");
                gvar.GetReportHeader1().SetReportTitle("SCHEDULE OF UNREALISED TAPERED GAINS");
            }

            // Set file paths for various output files
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetReportFile().ToString());
            XCallEqtpath(fvar, gvar, ivar);
            gvar.SetReportFileNameAsString(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetExportFile().ToString());
            XCallEqtpath(fvar, gvar, ivar);
            gvar.SetExportFileNameAsString(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetLossExportFile().ToString());
            XCallEqtpath(fvar, gvar, ivar);
            gvar.SetLossExportFileNameAsString(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // Load taper rates
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
            XCallCgtfiles(fvar, gvar, ivar);
            gvar.SetWsTaperRatesLoaded(0);

            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.TAPER_RATE_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);
                gvar.SetWsTaperSub(1);

                while (gvar.GetCgtfilesLinkage().IsSuccessful() &&
                       gvar.GetWsTaperSub() <= Gvar.MAX_TAPER_SUB)
                {
                    XCallCgtfiles(fvar, gvar, ivar);
                    if (gvar.GetCgtfilesLinkage().IsSuccessful())
                    {
                        gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(gvar.GetWsTaperSub()).SetWsTaperRatesDateEffective(
                            gvar.GetD112Record().GetD112Key().GetD112DateEffective());
                        gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(gvar.GetWsTaperSub()).SetWsBusAssetRate(
                            gvar.GetD112Record().GetD112BusAssetRate());
                        gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(gvar.GetWsTaperSub()).SetWsNonBusAssetRate(
                            gvar.GetD112Record().GetD112NonBusAssetRate());
                        gvar.SetWsTaperSub(gvar.GetWsTaperSub() + 1);
                    }
                }
                gvar.SetWsTaperRatesLoaded(gvar.GetWsTaperSub());
            }

            // User fund file setup
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.USER_FUND_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetCgtfilesLinkage().IsFileWasAlreadyOpen())
            {
                gvar.SetWsUserfundOpenFlagAsString("Y");
            }
            else
            {
                gvar.SetWsUserfundOpenFlagAsString("N");
            }

            // Open output files
            // File opening is handled by the file system infrastructure

            // Log initialization
            gvar.GetCgtlogLinkageArea1().SetLLogProgram(gvar.GetProgramName());
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.OPEN_OUTPUT);
            gvar.GetCgtlogLinkageArea1().SetLLogFileNameAsString(" ");
            X4Cgtlog(fvar, gvar, ivar);

            gvar.SetWsMessageNo(0);
            gvar.GetCgtlogLinkageArea2().SetLLogMessageTypeAsString("I");
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.WRITE_RECORD);

            // Set timestamp for log
            gvar.SetTimeStampAsString(DateTime.Now.ToString("HHmmss"));
            gvar.SetDateStampAsString(DateTime.Now.ToString("yyMMdd"));
            gvar.GetWsMessages().GetWsMessage2().SetWsMessDd(int.Parse(gvar.GetDateStamp().ToString().Substring(4, 2)));
            gvar.GetWsMessages().GetWsMessage2().SetWsMessMm(int.Parse(gvar.GetDateStamp().ToString().Substring(2, 2)));
            gvar.GetWsMessages().GetWsMessage2().SetWsMessYy(int.Parse(gvar.GetDateStamp().ToString().Substring(0, 2)));
            gvar.GetWsMessages().GetWsMessage2().SetWsMessHh(int.Parse(gvar.GetTimeStamp().ToString().Substring(0, 2)));
            gvar.GetWsMessages().GetWsMessage2().SetWsMessNn(int.Parse(gvar.GetTimeStamp().ToString().Substring(2, 2)));

            // Prepare and write log message
            gvar.SetWsWhenCompiledAsString(DateTime.Now.ToString("yyyyMMddHHmmss"));
            string logMessage = string.Format("{0}: Version {1} {2} {3}; {4}",
                gvar.GetProgramName(),
                gvar.GetVersionNumber(),
                gvar.GetWsWhenCompiled().GetWsCompDate(),
                gvar.GetWsWhenCompiled().GetWsCompTime(),
                gvar.GetWsMessages().GetWsMessage2());
            gvar.GetCgtlogLinkageArea2().SetLLogMessageAsString(logMessage);
            X4Cgtlog(fvar, gvar, ivar);

            // Parameter file processing
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.PARAMETER_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (!gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                XCallCgtabort(fvar, gvar, ivar);
            }

            gvar.GetD8Record().GetD8Key().SetD8ParamKey("CGT");
            gvar.SetLFileRecordAreaAsString(gvar.GetD8Record().ToString());
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_RECORD);
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.PARAMETER_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (!gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                XCallCgtabort(fvar, gvar, ivar);
            }
            else
            {
                gvar.SetD8RecordAsString(gvar.GetLFileRecordArea().ToString());
            }

            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (!gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                XCallCgtabort(fvar, gvar, ivar);
            }

            // Set report offsets based on parameter file
            if (gvar.GetD8Record().GetD8ReportOffsetFlag() == "Y")
            {
                gvar.GetReportConstants().SetReportHeaderOffsetLines(
                    gvar.GetD8Record().GetD8ReportHeaderOffsetLines());
                gvar.GetReportConstants().SetReportFooterOffsetLines(
                    gvar.GetD8Record().GetD8ReportFooterOffsetLines());
            }
            else
            {
                gvar.GetReportConstants().SetReportHeaderOffsetLines(0);
                gvar.GetReportConstants().SetReportFooterOffsetLines(0);
            }

            // Initialize allowances
            gvar.SetWsAllowancesOccurs(0);
            if (gvar.GetD8Record().GetD8UseLosses() == "Y")
            {
                A1OpenLossAllocationFiles(fvar, gvar, ivar);
            }

            if (gvar.GetWsAllowancesOccurs() == 0)
            {
                gvar.SetWsAllowancesSub(1);
                gvar.SetWsAllowancesOccurs(1);
                gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).SetWsTaxYearEndDateAsString(" ");
                gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).SetWsGainsAllowance(0);
                gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).SetWsTrustAllowance(0);
            }

            // Read user fund record
            gvar.SetLFileRecordAreaAsString(" ");
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_RECORD);
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.USER_FUND_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (!gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                XCallCgtabort(fvar, gvar, ivar);
            }
            else
            {
                gvar.SetD37RecordAsString(gvar.GetLFileRecordArea().ToString());
                gvar.GetReportHeader1().SetReportRef(gvar.GetD37Record().GetD37GlobalCompName());
            }

            // Initialize temp file
            gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_OPEN_OUTPUT);
            XCallCgttemp(fvar, gvar, ivar);
        }
        /// <summary>
        /// Executes the A1 Open Loss Allocation Files procedure from the original COBOL program.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: A1-OPEN-LOSS-ALLOCATION-FILES
        /// This method handles opening and reading allowance and loss files, processing records,
        /// and managing file operations for the CGT (Capital Gains Tax) system.
        /// </remarks>
        public void A1OpenLossAllocationFiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Open and read allowances file
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.ALLOWANCES_FROM_DB_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);
                XCallCgtfiles(fvar, gvar, ivar);
                gvar.SetWsAllowancesOccurs(0);

                while (gvar.GetCgtfilesLinkage().IsSuccessful() &&
                       gvar.GetWsAllowancesOccurs() != Gvar.WS_MAX_ALLOWANCES)
                {
                    XCallCgtfiles(fvar, gvar, ivar);
                    if (gvar.GetCgtfilesLinkage().IsSuccessful())
                    {
                        gvar.SetWsAllowancesOccurs(gvar.GetWsAllowancesOccurs() + 1);
                        // UNSTRING operation
                        string[] parts = gvar.GetLFileRecordArea().ToString().Split(',');
                        gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).SetWsTaxYearEndDateAsString(parts[0]);
                        gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).SetWsGainsAllowance(int.Parse(parts[1]));
                        gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).SetWsTrustAllowance(int.Parse(parts[2]));
                        gvar.SetWsRemainderAsString(parts.Length > 3 ? parts[3] : string.Empty);
                    }
                }
            }

            // Close the allowances file
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            // Open and read losses file
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.LOSSES_FROM_DB_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                gvar.GetWsDbLosses().GetWsDbLossesKey().SetWsClientFundCodeAsString(" ");
                gvar.GetWsDbLosses().GetWsDbLossesKey().SetWsMasterFileYearAsString(" ");
            }
            else
            {
                gvar.GetWsDbLosses().GetWsDbLossesKey().SetWsClientFundCode(_highValues);
                gvar.GetWsDbLosses().GetWsDbLossesKey().SetWsMasterFileYear(_highValues);
            }

            // Process disposal files if schedule type is 'R'
            if (ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R")
            {
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.DISPOSALS_FROM_DB_FILE);
                XCallCgtfiles(fvar, gvar, ivar);

                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);
                XCallCgtfiles(fvar, gvar, ivar);

                if (gvar.GetCgtfilesLinkage().IsSuccessful())
                {
                    gvar.GetD158Record().SetD158ClientFundCode(" ");
                }
                else
                {
                    gvar.GetD158Record().SetD158ClientFundCode(_highValues);
                }

                // Write D157 header record
                // File operations handled by file system infrastructure
                fvar.SetD157RecordAsString(fvar.GetD157HeaderRecord().ToString());
                // Write operation handled by file system infrastructure
            }
        }
        /// <summary>
        /// BReadAccumulate - Reads and accumulates schedule data records from appropriate data file.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: bReadAccumulate
        /// This method reads records from either realized or unrealized data file based on schedule type,
        /// processes the records, and accumulates relevant data into temporary fields.
        /// </remarks>
        public void BReadAccumulate(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Determine the file to read based on schedule type
            if (ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R")
            {
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.REALISED_DATA_FILE);
            }
            else
            {
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.UNREALISED_DATA_FILE);
            }

            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);

            // Call the file operation
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                gvar.SetScheduleDataRecordAsString(gvar.GetLFileRecordArea().ToString());

                switch (gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkRecordType())
                {
                    case "1":
                        if (gvar.GetWsProcessingSale() == "Y" || gvar.GetWsProcessingSale() == "C")
                        {
                            B1WriteTempRecord(fvar, gvar, ivar);
                        }
                        B2StoreHeaderDetails(fvar, gvar, ivar);
                        gvar.SetWsProcessingSaleAsString("N");
                        gvar.SetWsTrancheFlag("N");
                        break;

                    case "2":
                        if (gvar.GetWsPrevScheduleRecDetails().GetWsPrevFundCode() != gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey1().GetSkFundCode() ||
                            gvar.GetWsPrevScheduleRecDetails().GetWsPrevSedolCode() != gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSortSedolCode() ||
                            gvar.GetWsPrevScheduleRecDetails().GetWsPrevAcqContract() != gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdContractNo())
                        {
                            gvar.SetWsTrancheFlag("N");
                            gvar.SetWsTaperDateAsString(" ");
                        }

                        if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdTrancheFlag() == "Y")
                        {
                            gvar.SetWsTrancheFlag("Y");
                        }

                        if (IsNumeric(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdFullTaperDate().GetSdTaperDate()))
                        {
                            gvar.SetWsTaperDateAsString(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdFullTaperDate().GetSdTaperDate());
                        }

                        gvar.SetWsExerciseProcess("N");

                        if ((gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription() == "STOCK PURCHASE" &&
                             (gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "G" ||
                              gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "K" ||
                              gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "L")) ||
                            gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription() == "OPTION LAPSE" ||
                            (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller174().GetFiller176() == "BALANCE" &&
                             gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdSourceCategory() == "WC") ||
                            (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller174().GetFiller176() == "BALANCE" &&
                             gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdSourceCategory() == "EP") ||
                            (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription().Contains("SALE") &&
                             !(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "G" ||
                               gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "K" ||
                               gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "L")))
                        {
                            gvar.SetWsExerciseProcess("Y");
                        }

                        bool evaluateCondition =
                            gvar.GetTempFields().GetTempKey().GetTempFundCode() != gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey1().GetSkFundCode() ||
                            gvar.GetTempFields().GetTempKey().GetTempSedolCode() != gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSortSedolCode() ||
                            gvar.GetTempFields().GetTempDetails().GetTempAcqContract() != gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdContractNo() ||
                            gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller174().GetFiller176() == "BALANCE" ||
                            gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription() == "BALANCE        " ||
                            (gvar.GetWsProcessingSale() == "Y" || gvar.GetWsProcessingSale() == "C") && (
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription().Contains("SALE") ||
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription() == "CAPITAL DISTBTN" ||
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller178().GetFiller179().Contains("TO") ||
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller178().GetFiller179().Contains("TO") ||
                                gvar.GetWsExerciseProcess() == "Y");

                        if (evaluateCondition)
                        {
                            if (gvar.GetWsProcessingSale() == "Y" || gvar.GetWsProcessingSale() == "C")
                            {
                                B1WriteTempRecord(fvar, gvar, ivar);
                            }

                            if ((gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription().Contains("SALE") &&
                                 !(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "G" ||
                                   gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "K" ||
                                   gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "L")) ||
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription() == "CAPITAL DISTBTN" ||
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller178().GetFiller179().Contains("TO") ||
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller178().GetFiller179().Contains("TO") ||
                                gvar.GetWsExerciseProcess() == "Y")
                            {
                                B3NextSale(fvar, gvar, ivar);
                                if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription() == "CAPITAL DISTBTN")
                                {
                                    gvar.SetWsProcessingSaleAsString("C");
                                }
                                else
                                {
                                    gvar.SetWsProcessingSaleAsString("Y");
                                }
                            }
                            else
                            {
                                gvar.SetWsProcessingSaleAsString("N");
                            }
                        }

                        if ((gvar.GetWsProcessingSale() == "Y" || gvar.GetWsProcessingSale() == "C") &&
                            !(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetFiller174().GetFiller176() == "BALANCE"))
                        {
                            gvar.GetTempFields().GetTempDetails().SetTempSecurityType(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType());

                            if (!IsNumeric(gvar.GetWsTaperDate()) &&
                                !(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDescription().Contains("POOL")))
                            {
                                if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdQuantity() != 0 &&
                                    gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdQuantity() < 0 &&
                                    gvar.GetWsProcessingSale() == "Y")
                                {
                                    gvar.GetTempFields().GetTempDetails().SetTempQuantity(
                                        gvar.GetTempFields().GetTempDetails().GetTempQuantity() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdQuantity());
                                }
                                else if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdQuantity() != 0 &&
                                         gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdQuantity() > 0 &&
                                         (gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "G" ||
                                          gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "K" ||
                                          gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecurityType() == "L"))
                                {
                                    gvar.GetTempFields().GetTempDetails().SetTempQuantity(
                                        gvar.GetTempFields().GetTempDetails().GetTempQuantity() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdQuantity());
                                }
                            }

                            if (!IsNumeric(gvar.GetWsTaperDate()))
                            {
                                if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdProceedsX() != " ")
                                {
                                    gvar.GetTempFields().GetTempDetails().SetTempProceeds(
                                        gvar.GetTempFields().GetTempDetails().GetTempProceeds() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdProceeds());
                                }
                            }
                            else
                            {
                                if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdTaperProcX() != " ")
                                {
                                    gvar.GetTempFields().GetTempDetails().SetTempProceeds(
                                        gvar.GetTempFields().GetTempDetails().GetTempProceeds() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperProc());
                                }
                            }

                            if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdProceedsX() != " " &&
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdGainlossX() != " ")
                            {
                                if (IsNumeric(gvar.GetWsTaperDate()))
                                {
                                    if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperUnits() != 0 &&
                                        gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperUnits() < 0 &&
                                        gvar.GetWsProcessingSale() == "Y")
                                    {
                                        gvar.GetTempFields().GetTempDetails().SetTempQuantity(
                                            gvar.GetTempFields().GetTempDetails().GetTempQuantity() - gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperUnits());
                                    }

                                    if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperGain() > 0)
                                    {
                                        gvar.SetWsClientGain(gvar.GetWsClientGain() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperGain());
                                    }
                                    else if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperGain() < 0)
                                    {
                                        gvar.SetWsClientLoss(gvar.GetWsClientLoss() - gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperGain());
                                    }

                                    gvar.GetTempFields().GetTempDetails().SetTempGainLoss(
                                        gvar.GetTempFields().GetTempDetails().GetTempGainLoss() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdTaperGain());
                                }
                                else
                                {
                                    if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdGainloss() > 0)
                                    {
                                        gvar.SetWsClientGain(gvar.GetWsClientGain() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdGainloss());
                                    }
                                    else if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdGainloss() < 0)
                                    {
                                        gvar.SetWsClientLoss(gvar.GetWsClientLoss() - gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdGainloss());
                                    }

                                    gvar.GetTempFields().GetTempDetails().SetTempGainLoss(
                                        gvar.GetTempFields().GetTempDetails().GetTempGainLoss() + gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdGainloss());
                                }
                            }

                            if ("ABC".Contains(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdIndexationLimit()) ||
                                gvar.GetWsHoldingFlag() == "Y" ||
                                gvar.GetWsTrancheFlag() == "Y" ||
                                gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdTrancheFlag() == "Y")
                            {
                                gvar.GetTempFields().GetTempDetails().SetTempEstimatedIndAsString("Y");
                            }

                            if (gvar.GetWsHoldingFlag() == "Y")
                            {
                                gvar.GetTempFields().GetTempDetails().SetTempHoldingIndAsString("Y");
                            }

                            if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdTrancheFlag() == "Y" ||
                                gvar.GetWsTrancheFlag() == "Y")
                            {
                                gvar.GetTempFields().GetTempDetails().SetTempTrancheIndAsString("Y");
                            }

                            if (gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdPercentageBusinessX() != " ")
                            {
                                gvar.GetBusienssUseWorkFields().SetBusPercentUse(
                                    gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdPercentageBusiness9());
                            }

                            if (gvar.GetBusienssUseWorkFields().GetBusPercentUse() == 0)
                            {
                                gvar.GetBusienssUseWorkFields().SetBusPercentUse(0);
                            }
                        }
                        break;
                }

                gvar.GetWsPrevScheduleRecDetails().SetWsPrevFundCode(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey1().GetSkFundCode());
                gvar.GetWsPrevScheduleRecDetails().SetWsPrevSedolCode(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSortSedolCode());

                if (gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkRecordType() == "2")
                {
                    gvar.GetWsPrevScheduleRecDetails().SetWsPrevAcqDate(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdAcquisitionDateX());
                    gvar.GetWsPrevScheduleRecDetails().SetWsPrevAcqContract(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdContractNo());
                }
                else
                {
                    gvar.GetWsPrevScheduleRecDetails().SetWsPrevAcqDateAsString(" ");
                    gvar.GetWsPrevScheduleRecDetails().SetWsPrevAcqContractAsString(" ");
                }

                // File operation completed - success status is determined by the file operation
            }
        }
        /// <summary>
        /// Implements the COBOL paragraph B1-WRITE-TEMP-RECORD which handles:
        /// - Taper rate calculations based on acquisition date
        /// - Business use percentage calculations
        /// - Temporary record writing logic
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: B1-WRITE-TEMP-RECORD
        /// This method performs complex taper rate calculations and handles business use cases
        /// before writing temporary records. It processes acquisition dates, calculates years held,
        /// and applies business rules for taper relief.
        /// </remarks>
        /// <param name="fvar">Function variable container</param>
        /// <param name="gvar">Global variable container</param>
        /// <param name="ivar">Instance variable container</param>
        public void B1WriteTempRecord(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Handle 1982 indicator
            if (gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate().CompareTo("19820401") < 0)
            {
                gvar.GetTempFields().GetTempDetails().SetTemp1982Ind("Y");
            }
            else
            {
                gvar.GetTempFields().GetTempDetails().SetTemp1982Ind("N");
            }

            // Set tranche flag if needed
            if (gvar.GetTempFields().GetTempDetails().GetTempTrancheInd() == "Y")
            {
                gvar.GetTempFields().GetTempDetails().SetTempTrancheFlag("*");
            }

            // Handle acquisition date and extra years
            if (gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate().CompareTo("19980317") < 0)
            {
                gvar.GetTempFields().GetTempDetails().SetTempAcquisitionDate("19980316");
                gvar.SetWsExtraYears(1);
            }
            else
            {
                gvar.SetWsExtraYears(0);
            }

            // Process work acquisition date
            gvar.SetWsWorkAcquisitionDateAsString(gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate());
            if (string.Compare(gvar.GetWsWorkAcquisitionDate().ToString(), "19980406") < 0)
            {
                gvar.SetWsWorkAcquisitionDateAsString("19980406");
            }

            // Calculate years held
            int yearsHeld = 0;
            while (yearsHeld <= 11 && string.Compare(gvar.GetWsWorkAcquisitionDate().ToString(), gvar.GetTempFields().GetTempDetails().GetTempDisposalDate()) <= 0)
            {
                string workAcqDate = gvar.GetWsWorkAcquisitionDate().ToString();
                int currentYear = int.Parse(workAcqDate.Substring(0, 4));
                currentYear++;
                gvar.SetWsWorkAcquisitionDateAsString(currentYear.ToString() + workAcqDate.Substring(4));
                yearsHeld++;
            }

            yearsHeld -= 1;
            yearsHeld += gvar.GetWsExtraYears();
            if (yearsHeld > 10)
            {
                yearsHeld = 10;
            }
            gvar.SetWsNoOfYearsHeld(yearsHeld);

            // Handle taper rate calculations
            if (gvar.GetWFa2008Flag() == 1 || gvar.GetWsTaperRatesLoaded() < 1)
            {
                gvar.SetWsTaperSub(1);
            }

            if (gvar.GetTempFields().GetTempDetails().GetTempGainLoss() < 0)
            {
                gvar.GetTempFields().GetTempDetails().SetTempTaperRate(0);
                gvar.GetTempFields().GetTempKey().SetTempBusinessUseAsString("N");
            }
            else
            {
                gvar.SetWsTaperSub(gvar.GetWsNoOfYearsHeld() + 1);
                gvar.SetWsTaperSub(gvar.GetWsTaperSub() + gvar.GetWsTaperRateOffset());

                decimal busPercent = gvar.GetBusienssUseWorkFields().GetBusPercentUse();
                if (busPercent == 0)
                {
                    gvar.GetTempFields().GetTempKey().SetTempBusinessUseAsString("N");
                    gvar.GetTempFields().GetTempDetails().SetTempTaperRate(gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(gvar.GetWsTaperSub()).GetWsNonBusAssetRate());
                }
                else if (busPercent == 100)
                {
                    gvar.GetTempFields().GetTempKey().SetTempBusinessUseAsString("Y");
                    if (gvar.GetTempFields().GetTempDetails().GetTempDisposalDate().CompareTo(Gvar.CLIENT_PERIOD_START_DATE_00_01) >= 0)
                    {
                        gvar.SetWsTaperSub(gvar.GetWsTaperSub() - gvar.GetWsExtraYears());
                    }
                    gvar.GetTempFields().GetTempDetails().SetTempTaperRate(gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(gvar.GetWsTaperSub()).GetWsBusAssetRate());
                }
                else
                {
                    gvar.GetTempFields().GetTempKey().SetTempBusinessUseAsString("N");
                    gvar.GetTempFields().GetTempDetails().SetTempTaperRate(gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(gvar.GetWsTaperSub()).GetWsNonBusAssetRate());

                    decimal busQuantity = Math.Round(gvar.GetTempFields().GetTempDetails().GetTempQuantity() * busPercent / 100, MidpointRounding.AwayFromZero);
                    gvar.GetBusienssUseWorkFields().SetBusQuantity(busQuantity);
                    gvar.GetTempFields().GetTempDetails().SetTempQuantity(gvar.GetTempFields().GetTempDetails().GetTempQuantity() - busQuantity);

                    decimal busProceeds = Math.Round(gvar.GetTempFields().GetTempDetails().GetTempProceeds() * busPercent / 100, MidpointRounding.AwayFromZero);
                    gvar.GetBusienssUseWorkFields().SetBusProceeds(busProceeds);
                    gvar.GetTempFields().GetTempDetails().SetTempProceeds(gvar.GetTempFields().GetTempDetails().GetTempProceeds() - busProceeds);

                    decimal busGainLoss = Math.Round(gvar.GetTempFields().GetTempDetails().GetTempGainLoss() * busPercent / 100, MidpointRounding.AwayFromZero);
                    gvar.GetBusienssUseWorkFields().SetBusGainLoss(busGainLoss);
                    gvar.GetTempFields().GetTempDetails().SetTempGainLoss(gvar.GetTempFields().GetTempDetails().GetTempGainLoss() - busGainLoss);
                }

                if (gvar.GetWFa2008Flag() == 1 || gvar.GetWsTaperRatesLoaded() < 1)
                {
                    gvar.GetTempFields().GetTempDetails().SetTempTaperRate(100);
                }
            }

            // Process taper rate through CGTINVRT
            gvar.SetCgtinvrtLinkageAsString(gvar.GetTempFields().GetTempDetails().GetTempTaperRate().ToString());
            XCallCgtinvrt(fvar, gvar, ivar);
            gvar.GetTempFields().GetTempKey().SetTempInverseTaperRate(decimal.Parse(gvar.GetCgtinvrtLinkage().ToString()));

            gvar.SetCgtinvrtLinkageAsString(gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate().ToString());
            XCallCgtinvrt(fvar, gvar, ivar);
            gvar.GetTempFields().GetTempKey().SetTempInverseAcquisitionDate(gvar.GetCgtinvrtLinkage().ToString());

            // Prepare and write temporary record
            gvar.GetTempFields().GetTempKey().SetTempSequenceNo(int.Parse(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey7().GetSkSequenceNo()));
            gvar.GetCgttempLinkage().GetCgttempRecord().SetCgttempKey(gvar.GetTempFields().GetTempKey().ToString());

            if (gvar.GetWNewDerivativeExportFormat() == "Y")
            {
                gvar.GetTempFields().GetTempDetails().SetTempSign(gvar.GetTempFields().GetTempDetails().GetTempSecurityType() == "S" ? "+" : "-");
            }

            gvar.GetCgttempLinkage().GetCgttempRecord().SetCgttempDetails(gvar.GetTempFields().GetTempDetails().ToString());
            gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_WRITE);

            if (gvar.GetTempFields().GetTempDetails().GetTempQuantity() != 0 ||
                gvar.GetTempFields().GetTempDetails().GetTempProceeds() != 0 ||
                gvar.GetTempFields().GetTempDetails().GetTempGainLoss() != 0)
            {
                XCallCgttemp(fvar, gvar, ivar);
            }

            // Handle business use case if needed
            decimal busUsePercent = gvar.GetBusienssUseWorkFields().GetBusPercentUse();
            if (busUsePercent > 0 && busUsePercent < 100 && gvar.GetTempFields().GetTempDetails().GetTempGainLoss() >= 0)
            {
                gvar.GetTempFields().GetTempKey().SetTempBusinessUseAsString("Y");
                if (gvar.GetTempFields().GetTempDetails().GetTempDisposalDate().CompareTo(Gvar.CLIENT_PERIOD_START_DATE_00_01) >= 0)
                {
                    gvar.SetWsTaperSub(gvar.GetWsTaperSub() - gvar.GetWsExtraYears());
                }
                gvar.GetTempFields().GetTempDetails().SetTempTaperRate(gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(gvar.GetWsTaperSub()).GetWsBusAssetRate());

                if (gvar.GetWFa2008Flag() == 1 || gvar.GetWsTaperRatesLoaded() < 1)
                {
                    gvar.GetTempFields().GetTempDetails().SetTempTaperRate(100);
                    gvar.SetCgtinvrtLinkageAsString(gvar.GetTempFields().GetTempDetails().GetTempTaperRate().ToString());
                }

                XCallCgtinvrt(fvar, gvar, ivar);
                gvar.GetTempFields().GetTempKey().SetTempInverseTaperRate(decimal.Parse(gvar.GetCgtinvrtLinkage().ToString()));

                gvar.GetTempFields().GetTempDetails().SetTempQuantity(gvar.GetBusienssUseWorkFields().GetBusQuantity());
                gvar.GetTempFields().GetTempDetails().SetTempProceeds(gvar.GetBusienssUseWorkFields().GetBusProceeds());
                gvar.GetTempFields().GetTempDetails().SetTempGainLoss(gvar.GetBusienssUseWorkFields().GetBusGainLoss());
                gvar.GetCgttempLinkage().GetCgttempRecord().SetCgttempKey(gvar.GetTempFields().GetTempKey().ToString());
                gvar.GetCgttempLinkage().GetCgttempRecord().SetCgttempDetails(gvar.GetTempFields().GetTempDetails().ToString());

                if (gvar.GetTempFields().GetTempDetails().GetTempQuantity() != 0 ||
                    gvar.GetTempFields().GetTempDetails().GetTempProceeds() != 0 ||
                    gvar.GetTempFields().GetTempDetails().GetTempGainLoss() != 0)
                {
                    XCallCgttemp(fvar, gvar, ivar);
                }
            }

            // Reset temporary fields
            gvar.SetTempFields(new TempFields());
        }
        /// <summary>
        /// B2StoreHeaderDetails - Converts and stores security header details from schedule data to D3 record
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: b2StoreHeaderDetails
        /// This method transfers security information from schedule data fields to the D3 record,
        /// including setting the holding flag based on the input value, and moving various security attributes.
        /// </remarks>
        public void B2StoreHeaderDetails(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Handle holding flag setting
            if (gvar.GetScheduleDataRecord().GetFiller149().GetUSedolRecord().GetSsHoldingFlag() == "Y")
            {
                gvar.SetWsHoldingFlagAsString("Y");
            }
            else
            {
                gvar.SetWsHoldingFlagAsString("N");
            }

            // Move all security-related fields from schedule data to D3 record
            gvar.GetD3Record().GetD3Key().SetD3SedolCode(gvar.GetScheduleDataRecord().GetFiller149().GetUSedolRecord().GetSsSedolNumber());
            gvar.GetD3Record().SetD3Issuer(gvar.GetScheduleDataRecord().GetFiller149().GetUSedolRecord().GetSsIssuersName());
            gvar.GetD3Record().SetD3Description(gvar.GetScheduleDataRecord().GetFiller149().GetUSedolRecord().GetSsStockDescription());
            gvar.GetD3Record().SetD3SortCode(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSecuritySortCode());
            gvar.GetD3Record().SetD3QuotedUnquoted(gvar.GetScheduleDataRecord().GetFiller149().GetUSedolRecord().GetSsQuotedIndicator());
        }
        /// <summary>
        /// Implements the COBOL B3NEXT-SALE paragraph functionality.
        /// This method processes fund code changes and related operations in a financial transaction system.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: B3NEXT-SALE
        /// This method handles fund code processing, message generation, file operations, and date conversions.
        /// It implements complex conditional logic and performs several nested operations.
        /// </remarks>
        /// <param name="fvar">Fvar instance containing function-specific variables</param>
        /// <param name="gvar">Gvar instance containing global variables</param>
        /// <param name="ivar">Ivar instance containing iteration variables</param>
        public void B3NextSale(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            var wsLastFundCode = gvar.GetWsLastFundCode();
            var skFundCode = gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey1().GetSkFundCode();

            if (!string.IsNullOrEmpty(wsLastFundCode) && !string.IsNullOrEmpty(wsLastFundCode.Trim()) &&
                string.Compare(wsLastFundCode, skFundCode) < 0)
            {
                CProcessClient(fvar, gvar, ivar);
            }

            if (skFundCode != wsLastFundCode)
            {
                gvar.GetWsMessages().GetWsMessage11().SetWsMess11Fund(skFundCode);
                gvar.GetCgtlogLinkageArea2().SetLLogMessageAsString(gvar.GetWsMessages().GetWsMessage11().ToString());
                gvar.GetD37RecordFormat2().GetD37Key().SetD37FundCode(skFundCode);
                gvar.SetLFileRecordAreaAsString(gvar.GetD37RecordFormat2().ToString());
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_RECORD);
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.USER_FUND_FILE);
                XCallCgtfiles(fvar, gvar, ivar);

                if (gvar.GetCgtfilesLinkage().IsSuccessful())
                {
                    // Move the file record area to D37 record - this would be handled by the file I/O operation
                    // gvar.GetD37RecordFormat2().SetFromString(gvar.GetLFileRecordArea());
                }
                else
                {
                    gvar.GetD37RecordFormat2().SetD37Name("*** Missing from User's file ***");
                    // Set default period dates - these would be set through individual field access
                    // gvar.GetD37RecordFormat2().SetD37PeriodStartDate(Gvar.FIRST_PERIOD_DATE_YYMMDD);
                    // gvar.GetD37RecordFormat2().SetD37PeriodEndDate(Gvar.LAST_PERIOD_DATE_YYMMDD);
                }

                B31GetTaperRatesForClient(fvar, gvar, ivar);

                if (gvar.GetD8Record().GetD8UseLosses() == "Y")
                {
                    B32GetAllowancesForClient(fvar, gvar, ivar);
                    B33GetLossesForClient(fvar, gvar, ivar);
                    B34GetDisposalsForClient(fvar, gvar, ivar);
                }
                else
                {
                    gvar.GetWsDbLosses().SetWsDbLossesDetails(string.Empty);
                    gvar.SetWsDbLossTableAsString(string.Empty);
                }
            }

            gvar.SetWsLastFundCodeAsString(skFundCode);
            gvar.GetTempFields().GetTempKey().SetTempFundCode(skFundCode);
            gvar.GetTempFields().GetTempKey().SetTempSedolCode(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey2().GetSkSortSedolCode());

            if (IsNumeric(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdFullTaperDate().GetSdTaperDate()))
            {
                gvar.GetCgtdate2LinkageDate1().SetCgtdate2Ccyymmdd1(int.Parse(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecord9().GetSdFullTaperDate().GetSdTaperDate()));
            }
            else
            {
                gvar.GetCgtdate2LinkageDate1().SetCgtdate2Ccyymmdd1(int.Parse(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdAcquisitionDateX()));
            }

            // Call external program CGTDATE2 - this would be handled by external program call
            // gvar.CallCgtdate2(gvar.GetCgtdate2LinkageDate1());
            gvar.GetTempFields().GetTempDetails().SetTempAcquisitionDate(gvar.GetCgtdate2LinkageDate1().GetCgtdate2Ccyymmdd1().ToString());
            gvar.GetTempFields().GetTempDetails().SetTempAcqContract(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdContractNo());
            gvar.GetCgtdate2LinkageDate1().SetCgtdate2Ccyymmdd1(int.Parse(gvar.GetScheduleDataRecord().GetFiller149().GetUDetailRecordX().GetSdMovementDateX()));
            // Call external program CGTDATE2 - this would be handled by external program call
            // gvar.CallCgtdate2(gvar.GetCgtdate2LinkageDate1());
            gvar.GetTempFields().GetTempDetails().SetTempDisposalDate(gvar.GetCgtdate2LinkageDate1().GetCgtdate2Ccyymmdd1().ToString());

            gvar.GetTempFields().GetTempKey().SetTempSedolCode(gvar.GetD3Record().GetD3Key().GetD3SedolCode());
            gvar.GetTempFields().GetTempDetails().SetTempIssuerName(gvar.GetD3Record().GetD3Issuer());
            gvar.GetTempFields().GetTempDetails().SetTempDescription(gvar.GetD3Record().GetD3Description());
            gvar.GetTempFields().GetTempKey().SetTempSortCode(gvar.GetD3Record().GetD3SortCode());

            if (gvar.GetD3Record().GetD3QuotedUnquoted() == "0")
            {
                gvar.GetTempFields().GetTempDetails().SetTempQuotedIndAsString("Q");
            }
            else
            {
                gvar.GetTempFields().GetTempDetails().SetTempQuotedIndAsString("U");
            }

            gvar.GetTempFields().GetTempDetails().SetTempEstimatedIndAsString("N");
            gvar.GetBusienssUseWorkFields().SetBusPercentUse(0);
        }
        /// <summary>
        /// Original COBOL paragraph: b31GetTaperRatesForClient
        /// Determines the applicable taper rates for a client based on dates.
        /// </summary>
        /// <remarks>
        /// This method handles pre-FA2008 and post-FA2008 scenarios by checking date ranges
        /// and calculating the appropriate taper rate offset for the period.
        /// </remarks>
        /// <param name="fvar">Fvar object containing business data</param>
        /// <param name="gvar">Gvar object containing global variables</param>
        /// <param name="ivar">Ivar object containing input variables</param>
        public void B31GetTaperRatesForClient(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move D37-PERIOD-END-DATE to CGTDATE2-YYMMDD1 and call CGTDATE2
            // Construct date from D37PeriodEndDate components
            string endDateStr = string.Concat(
                gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateYy(),
                gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateMm(),
                gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateDd()
            );
            gvar.GetCgtdate2LinkageDate1().SetCgtdate2Ccyymmdd1(int.Parse(endDateStr));
            // CALL 'CGTDATE2' is treated as a direct method call per constraints
            // Note: Actual CGTDATE2 conversion would be in a separate method not shown here

            // Initialize taper rate offset
            gvar.SetWsTaperRateOffset(0);

            if (gvar.GetCgtdate2LinkageDate1().GetCgtdate2Ccyymmdd1() < Gvar.CREATE_2008_POOL_DATE)
            {
                // Process pre-FA2008 dates
                // Construct date from D37PeriodStartDate components
                string startDateStr = string.Concat(
                    gvar.GetD37RecordFormat2().GetD37PeriodStartDate().GetD37PeriodStartDateYy(),
                    gvar.GetD37RecordFormat2().GetD37PeriodStartDate().GetD37PeriodStartDateMm(),
                    gvar.GetD37RecordFormat2().GetD37PeriodStartDate().GetD37PeriodStartDateDd()
                );
                gvar.GetCgtdate2LinkageDate1().SetCgtdate2Ccyymmdd1(int.Parse(startDateStr));
                // CALL 'CGTDATE2' is treated as a direct method call per constraints
                // Note: Actual CGTDATE2 conversion would be in a separate method not shown here

                gvar.SetWsD37StartDateCcyymmdd(gvar.GetCgtdate2LinkageDate1().GetCgtdate2Ccyymmdd1().ToString());

                // Find applicable taper rate
                for (var wsTaperSub = 1; wsTaperSub <= Gvar.MAX_TAPER_SUB; wsTaperSub += Gvar.TAPER_RATES_PER_YEAR)
                {
                    if (int.Parse(gvar.GetCgtdate2LinkageDate1().GetCgtdate2Ccyymmdd1().ToString()) >=
                        int.Parse(gvar.GetWsTaperRatesTable().GetWsTaperRatesElementAt(wsTaperSub).GetWsTaperRatesDateEffective()))
                    {
                        gvar.SetWsTaperRateOffset(wsTaperSub);
                    }
                }

                // Adjust offset if needed
                if (gvar.GetWsTaperRateOffset() > 0)
                {
                    gvar.SetWsTaperRateOffset(gvar.GetWsTaperRateOffset() - 1);
                }

                gvar.SetWFa2008FlagAsString("0");
            }
            else
            {
                // Process post-FA2008 dates
                gvar.SetWFa2008FlagAsString("1");
            }

            // File operation completed - success status is determined by the file operation
        }
        /// <summary>
        /// Original COBOL paragraph: B32-GET-ALLOWANCES-FOR-CLIENT
        /// </summary>
        /// <remarks>
        /// This method retrieves tax allowances until a specified end date is reached.
        /// It processes allowance dates in a loop, comparing them with the period end date.
        /// </remarks>
        public void B32GetAllowancesForClient(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            gvar.SetWsAllowanceDateCcyymmddAsString(" ");
            // Construct date from D37PeriodEndDate components
            string endDateStr2 = string.Concat(
                gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateYy(),
                gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateMm(),
                gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateDd()
            );
            gvar.GetCgtdate2LinkageDate2().SetCgtdate2Ccyymmdd2(int.Parse(endDateStr2));

            // Call external program CGTDATE2 - this would be handled by external program call
            // CGTDATE2 cgtdate2 = new CGTDATE2();
            // cgtdate2.Run(gvar.GetCgtdate2LinkageDate2());

            gvar.SetWsD37EndDateCcyymmdd(gvar.GetCgtdate2LinkageDate2().GetCgtdate2Ccyymmdd2().ToString());

            for (gvar.SetWsAllowancesSub(1);
                 gvar.GetWsAllowancesSub() != gvar.GetWsAllowancesOccurs() &&
                 string.Compare(gvar.GetWsAllowanceDateCcyymmdd(), gvar.GetWsD37EndDateCcyymmdd()) < 0;
                 gvar.SetWsAllowancesSub(gvar.GetWsAllowancesSub() + 1))
            {
                string allowanceDate = string.Concat(
                    gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).GetWsTaxYearEndDate().GetWsTaxYearEndDateCc(),
                    gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).GetWsTaxYearEndDate().GetWsTaxYearEndDateYy(),
                    gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).GetWsTaxYearEndDate().GetWsTaxYearEndDateMm(),
                    gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).GetWsTaxYearEndDate().GetWsTaxYearEndDateDd()
                );
                gvar.SetWsAllowanceDateCcyymmdd(allowanceDate);

                if (string.Compare(gvar.GetWsAllowanceDateCcyymmdd(), gvar.GetWsD37EndDateCcyymmdd()) >= 0)
                {
                    return;  // Equivalent to GO TO B32-EXIT in COBOL
                }
            }
        }
        /// <summary>
        /// B33GetLossesForClient - Processes losses data for a client from database.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: b33GetLossesForClient
        /// This method retrieves and processes loss details for a specific client fund code.
        /// It handles both regular and YTD loss calculations with adjustments.
        /// </remarks>
        public void B33GetLossesForClient(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Initialize loss-related structures
            gvar.GetWsDbLosses().SetWsDbLossesDetails(string.Empty);
            gvar.SetWsDbLossTableAsString(string.Empty);

            if (gvar.GetD37RecordFormat2().GetD37UseLossesFlag() == "Y")
            {
                // Set up file parameters
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.LOSSES_FROM_DB_FILE);
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);

                // Process records until we find the matching fund code or reach end
                while (string.Compare(gvar.GetWsDbLosses().GetWsDbLossesKey().GetWsClientFundCode(), gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode()) < 0)
                {
                    XCallCgtfiles(fvar, gvar, ivar);
                    if (gvar.GetCgtfilesLinkage().IsSuccessful())
                    {
                        X5GetLossDetails(fvar, gvar, ivar);
                    }
                    else
                    {
                        gvar.GetWsDbLosses().GetWsDbLossesKey().SetWsClientFundCode(_highValues);
                        gvar.GetWsDbLosses().GetWsDbLossesKey().SetWsMasterFileYear(_highValues);
                    }
                }

                // Process matching fund code if found
                if (gvar.GetWsDbLosses().GetWsDbLossesKey().GetWsClientFundCode() == gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode())
                {
                    if (ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R")
                    {
                        // Regular processing path
                        decimal bfLoss9697 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).GetWsBfLoss();
                        decimal lossYtd9697 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).GetWsLossYtd();
                        decimal bfLoss9596 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).GetWsBfLoss();
                        decimal lossYtd9596 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).GetWsLossYtd();
                        decimal incomeLoss = gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).GetWsBfLoss();
                        decimal incomeLossYtd = gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).GetWsLossYtd();

                        // Add base values
                        bfLoss9697 += gvar.GetWsDbLossesN().GetWsBf9697AndLaterLossesN();
                        lossYtd9697 += gvar.GetWsDbLossesN().GetWsBf9697AndLaterLossesN();

                        // Handle adjustment
                        if (gvar.GetWsDbLossesN().GetWsLossesAdjustmentSign() == "-")
                        {
                            bfLoss9697 -= gvar.GetWsDbLossesN().GetWsLossesAdjustmentN();
                            lossYtd9697 -= gvar.GetWsDbLossesN().GetWsLossesAdjustmentN();
                        }
                        else
                        {
                            bfLoss9697 += gvar.GetWsDbLossesN().GetWsLossesAdjustmentN();
                            lossYtd9697 += gvar.GetWsDbLossesN().GetWsLossesAdjustmentN();
                        }

                        // Add other loss categories
                        bfLoss9596 += gvar.GetWsDbLossesN().GetWsBf9596AndEarlierLossesN();
                        lossYtd9596 += gvar.GetWsDbLossesN().GetWsBf9596AndEarlierLossesN();
                        incomeLoss += gvar.GetWsDbLossesN().GetWsIncomeLossesN();
                        incomeLossYtd += gvar.GetWsDbLossesN().GetWsIncomeLossesN();

                        // Save updated values
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).SetWsBfLoss(bfLoss9697);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).SetWsLossYtd(lossYtd9697);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).SetWsBfLoss(bfLoss9596);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).SetWsLossYtd(lossYtd9596);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).SetWsBfLoss(incomeLoss);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).SetWsLossYtd(incomeLossYtd);
                    }
                    else
                    {
                        // YTD processing path
                        decimal bfLoss9697 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).GetWsBfLoss();
                        decimal lossYtd9697 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).GetWsLossYtd();
                        decimal bfLoss9596 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).GetWsBfLoss();
                        decimal lossYtd9596 = gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).GetWsLossYtd();
                        decimal incomeLoss = gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).GetWsBfLoss();
                        decimal incomeLossYtd = gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).GetWsLossYtd();

                        // Add YTD values
                        bfLoss9697 += gvar.GetWsDbLossesN().GetWs9697AndLaterLossesYtdN();
                        lossYtd9697 += gvar.GetWsDbLossesN().GetWs9697AndLaterLossesYtdN();
                        bfLoss9596 += gvar.GetWsDbLossesN().GetWs9596AndEarlierLossesYtdN();
                        lossYtd9596 += gvar.GetWsDbLossesN().GetWs9596AndEarlierLossesYtdN();
                        incomeLoss += gvar.GetWsDbLossesN().GetWsIncomeLossesYtdN();
                        incomeLossYtd += gvar.GetWsDbLossesN().GetWsIncomeLossesYtdN();

                        // Save updated values
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).SetWsBfLoss(bfLoss9697);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).SetWsLossYtd(lossYtd9697);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).SetWsBfLoss(bfLoss9596);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).SetWsLossYtd(lossYtd9596);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).SetWsBfLoss(incomeLoss);
                        gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).SetWsLossYtd(incomeLossYtd);
                    }
                }
            }
        }

        /// <summary>
        /// Original COBOL paragraph: b34GetDisposalsForClient
        /// Processes disposal records for a client when losses are applicable and schedule type is 'R'.
        /// Reads disposal records from database file and populates temporary fields for eligible records.
        /// </summary>
        /// <remarks>
        /// This method handles client disposal records, filtering by fund code and date range.
        /// It calculates gains/losses and maintains running totals for client gains and losses.
        /// </remarks>
        public void B34GetDisposalsForClient(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            if (gvar.GetD37RecordFormat2().GetD37UseLossesFlag() == "Y" &&
                ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R")
            {
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.DISPOSALS_FROM_DB_FILE);
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);

                while (string.Compare(gvar.GetD158Record().GetD158ClientFundCode(), gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode()) > 0)
                {
                    if (gvar.GetD158Record().GetD158ClientFundCode() == gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode() &&
                        string.Compare(gvar.GetD158Record().GetD158DisposalDate().ToString(), gvar.GetWsD37StartDateCcyymmdd()) >= 0 &&
                        string.Compare(gvar.GetD158Record().GetD158DisposalDate().ToString(), gvar.GetWsD37EndDateCcyymmdd()) <= 0)
                    {
                        // Initialize temp details structure
                        gvar.GetTempFields().SetTempDetails(new EquityProject.Cgtsing4DTO.TempFields.TempDetails());
                        gvar.GetTempFields().GetTempKey().SetTempFundCode(gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode());
                        gvar.GetTempFields().GetTempKey().SetTempSedolCode(Gvar.DESCRIPTION_OTHER);
                        gvar.GetTempFields().GetTempKey().SetTempSortCode(gvar.GetD158Record().GetD158DisposalId());
                        gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey7().SetSkSequenceNo(gvar.GetScheduleDataRecord().GetKeyRecord().GetSkSplitKey7().GetSkSequenceNo());
                        gvar.GetTempFields().GetTempKey().SetTempBusinessUseAsString("N");
                        gvar.GetTempFields().GetTempDetails().SetTempQuantity(0m);
                        gvar.GetTempFields().GetTempDetails().SetTempProceeds(gvar.GetD158RecordN().GetD158ProceedsN());

                        if (gvar.GetD158Record().GetD158GainLossSign() == "-")
                        {
                            decimal temp = 0m - gvar.GetD158RecordN().GetD158GainLossN();
                            gvar.GetTempFields().GetTempDetails().SetTempGainLoss(temp);
                            gvar.SetWsClientLoss(gvar.GetWsClientLoss() + gvar.GetD158RecordN().GetD158GainLossN());
                        }
                        else
                        {
                            gvar.GetTempFields().GetTempDetails().SetTempGainLoss(gvar.GetD158RecordN().GetD158GainLossN());
                            gvar.SetWsClientGain(gvar.GetWsClientGain() + gvar.GetD158RecordN().GetD158GainLossN());
                        }

                        gvar.GetTempFields().GetTempDetails().SetTempAcquisitionDate(gvar.GetD158Record().GetD158AcquisitionDate().ToString());
                        gvar.GetTempFields().GetTempDetails().SetTempIssuerName(gvar.GetD158Record().GetD158Description());
                        gvar.GetTempFields().GetTempDetails().SetTempDescription(gvar.GetD158Record().GetD158Description().Substring(34, 220));
                        gvar.GetBusienssUseWorkFields().SetBusPercentUse(gvar.GetD158RecordN().GetD158PercentBusinessN());
                        gvar.GetTempFields().GetTempDetails().SetTempQuotedInd(gvar.GetD158Record().GetD158Quoted());
                        gvar.GetTempFields().GetTempDetails().SetTempDisposalDate(gvar.GetD158Record().GetD158DisposalDate().ToString());

                        gvar.GetTempFields().GetTempDetails().SetTempEstimatedInd(gvar.GetD158Record().GetD158Estimated() == "0" ? "N" : "Y");

                        B1WriteTempRecord(fvar, gvar, ivar);
                    }

                    XCallCgtfiles(fvar, gvar, ivar);
                    if (gvar.GetCgtfilesLinkage().IsSuccessful())
                    {
                        X6GetDisposalDetails(fvar, gvar, ivar);
                    }
                    else
                    {
                        gvar.GetD158Record().SetD158ClientFundCode(_highValues);
                    }
                }
            }
        }
        /// <summary>
        /// CProcessClient - Processes client gains and losses, generates reports, and manages temporary file operations.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: cProcessClient
        /// This method handles the processing flow for calculating and reporting client gains/losses,
        /// including temporary file operations and report generation.
        /// </remarks>
        public void CProcessClient(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            gvar.SetWsReportStatusFlagAsString("G");
            XReportHeadings(fvar, gvar, ivar);

            var cgttempLinkage = gvar.GetCgttempLinkage();
            cgttempLinkage.SetCgttempAction(Gvar.CGTTEMP_CLOSE);
            XCallCgttemp(fvar, gvar, ivar);

            cgttempLinkage.SetCgttempAction(Gvar.CGTTEMP_OPEN_IO);
            XCallCgttemp(fvar, gvar, ivar);

            C3CalculateLosses(fvar, gvar, ivar);
            C4WriteYtdLosses(fvar, gvar, ivar);

            cgttempLinkage.SetCgttempAction(Gvar.CGTTEMP_READ);
            XCallCgttemp(fvar, gvar, ivar);

            while (cgttempLinkage.GetCgttempStatus() == "00")
            {
                var tempFields = gvar.GetTempFields();
                // Set the key and details from the linkage strings
                tempFields.SetTempKeyAsString(cgttempLinkage.GetCgttempRecord().GetCgttempKey());
                tempFields.SetTempDetailsAsString(cgttempLinkage.GetCgttempRecord().GetCgttempDetails());
                gvar.SetWsTempRecordWrittenAsString("N");

                for (int lossSub = 1; lossSub <= Gvar.MAX_LOSS_TYPES; lossSub++)
                {
                    gvar.SetWsLossSub(lossSub);
                    if (gvar.GetFiller13().GetWsLossElementAt(lossSub).GetWsLossUsed() > 0)
                    {
                        C1FormatWriteExport(fvar, gvar, ivar);
                        C2FormatWriteReport(fvar, gvar, ivar);
                        gvar.SetWsTempRecordWrittenAsString("Y");

                        if (gvar.GetD8Record().GetD8UseLosses() != "Y" ||
                            gvar.GetD37RecordFormat2().GetD37UseLossesFlag() != "Y" ||
                            tempFields.GetTempDetails().GetTempGainLoss() <= 0)
                        {
                            gvar.SetWsLossSub(Gvar.MAX_LOSS_TYPES);
                        }

                        gvar.SetWsLastTempSedolAsString(tempFields.GetTempKey().GetTempSedolCode());
                        gvar.SetWsLastTempFundAsString(tempFields.GetTempKey().GetTempFundCode());
                    }
                }

                if (gvar.GetWsTempRecordWritten() == "N" ||
                    tempFields.GetTempDetails().GetTempGainLoss() > 0)
                {
                    gvar.SetWsLossSub(1);
                    C1FormatWriteExport(fvar, gvar, ivar);
                    C2FormatWriteReport(fvar, gvar, ivar);
                    gvar.SetWsLastTempSedolAsString(tempFields.GetTempKey().GetTempSedolCode());
                    gvar.SetWsLastTempFundAsString(tempFields.GetTempKey().GetTempFundCode());
                }

                XCallCgttemp(fvar, gvar, ivar);
            }

            if (gvar.GetWsReportStatusFlag() == "G")
            {
                XFinishSedolDesc(fvar, gvar, ivar);
                gvar.SetWsReportStatusFlagAsString("L");
                XGainTotals(fvar, gvar, ivar);
            }

            if (gvar.GetD8Record().GetD8UseLosses() == "Y" &&
                gvar.GetD37RecordFormat2().GetD37UseLossesFlag() == "Y")
            {
                fvar.GetD110Record().SetD110GainLossSign("-");
                for (int lossSub = 2; lossSub <= Gvar.MAX_LOSS_TYPES; lossSub++)
                {
                    gvar.SetWsLossSub(lossSub);
                    if (gvar.GetFiller13().GetWsLossElementAt(lossSub).GetWsBfLoss() > gvar.GetFiller13().GetWsLossElementAt(lossSub).GetWsLossYtd())
                    {
                        gvar.SetTempFields(new TempFields());
                        var tempFieldsNew = gvar.GetTempFields();
                        tempFieldsNew.GetTempDetails().SetTempIssuerName(gvar.GetFiller14().GetWsLossTextElementAt(lossSub).GetWsLossDescription());
                        tempFieldsNew.GetTempDetails().SetTempGainLoss(gvar.GetFiller13().GetWsLossElementAt(lossSub).GetWsLossYtd() -
                                                     gvar.GetFiller13().GetWsLossElementAt(lossSub).GetWsBfLoss());
                        fvar.GetD110Record().SetD110GainLoss(tempFieldsNew.GetTempDetails().GetTempGainLoss());
                        C2FormatWriteReport(fvar, gvar, ivar);
                    }
                }
            }

            XLossTotals(fvar, gvar, ivar);
            cgttempLinkage.SetCgttempAction(Gvar.CGTTEMP_CLOSE);
            XCallCgttemp(fvar, gvar, ivar);

            cgttempLinkage.SetCgttempAction(Gvar.CGTTEMP_OPEN_OUTPUT);
            XCallCgttemp(fvar, gvar, ivar);

            if (gvar.GetReportFields().GetReportLineCount() >
                gvar.GetReportConstants().GetMaxLines() -
                gvar.GetReportConstants().GetReportFooterOffsetLines())
            {
                XReportHeadings(fvar, gvar, ivar);
            }

            while (gvar.GetReportFields().GetReportLineCount() <=
                   gvar.GetReportConstants().GetMaxLines() -
                   gvar.GetReportConstants().GetReportFooterOffsetLines() - 1)
            {
                XWriteReportDetail(fvar, gvar, ivar);
            }

            if (gvar.GetD8Record().GetD8UseLosses() == "Y" &&
                gvar.GetD37RecordFormat2().GetD37UseLossesFlag() == "Y")
            {
                gvar.SetReportDetailAsString(gvar.GetReportFooter2().ToString());
            }
            else
            {
                gvar.SetReportDetailAsString(gvar.GetReportFooter().ToString());
            }

            XWriteReportDetail(fvar, gvar, ivar);
            gvar.SetWsClientGain(0);
            gvar.SetWsClientLoss(0);
            gvar.SetReportFields(new EquityProject.Cgtsing4DTO.ReportFields());
            gvar.SetTempFields(new EquityProject.Cgtsing4DTO.TempFields());
        }
        /// <summary>
        /// Formats and writes the export record based on temporary field values.
        /// Original COBOL paragraph name: c1FormatWriteExport
        /// </summary>
        /// <remarks>
        /// This method transfers temporary field values to the output record and performs gain/loss calculations.
        /// It handles different cases for gain/loss allocation and writes the final D110 record.
        /// </remarks>
        public void C1FormatWriteExport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move temp fields to D110 record
            fvar.GetD110Record().SetD110FundCode(gvar.GetTempFields().GetTempKey().GetTempFundCode());
            fvar.GetD110Record().SetD110SedolCode(gvar.GetTempFields().GetTempKey().GetTempSedolCode());
            fvar.GetD110Record().SetD110AcquisitionDate(gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate());
            fvar.GetD110Record().SetD110DisposalDate(gvar.GetTempFields().GetTempDetails().GetTempDisposalDate());
            fvar.GetD110Record().SetD110TaperRate(gvar.GetTempFields().GetTempDetails().GetTempTaperRate());
            fvar.GetD110Record().SetD110IssuerName(gvar.GetTempFields().GetTempDetails().GetTempIssuerName());
            fvar.GetD110Record().SetD110Description(gvar.GetTempFields().GetTempDetails().GetTempDescription());
            fvar.GetD110Record().SetD110QuotedInd(gvar.GetTempFields().GetTempDetails().GetTempQuotedInd());
            fvar.GetD110Record().SetD110EstimatedInd(gvar.GetTempFields().GetTempDetails().GetTempEstimatedInd());
            fvar.GetD110Record().SetD1101982Ind(gvar.GetTempFields().GetTempDetails().GetTemp1982Ind());
            fvar.GetD110Record().SetD110HoldingInd(gvar.GetTempFields().GetTempDetails().GetTempHoldingInd());
            fvar.GetD110Record().SetD110TrancheInd(gvar.GetTempFields().GetTempDetails().GetTempTrancheInd());
            fvar.GetD110Record().SetD110SequenceNo(gvar.GetTempFields().GetTempKey().GetTempSequenceNo());
            fvar.GetD110Record().SetD110BusinessUse(gvar.GetTempFields().GetTempKey().GetTempBusinessUse());

            // Handle quantity and proceeds based on record write status
            if (gvar.GetWsTempRecordWritten() == "N")
            {
                fvar.GetD110Record().SetD110Quantity(gvar.GetTempFields().GetTempDetails().GetTempQuantity());
                fvar.GetD110Record().SetD110Proceeds(gvar.GetTempFields().GetTempDetails().GetTempProceeds());
            }
            else
            {
                fvar.GetD110Record().SetD110Quantity(0);
                fvar.GetD110Record().SetD110Proceeds(0);
                gvar.GetTempFields().GetTempDetails().SetTempQuantity(0);
                gvar.GetTempFields().GetTempDetails().SetTempProceeds(0);
            }

            // Handle gain/loss calculation
            decimal tempGainLoss = gvar.GetTempFields().GetTempDetails().GetTempGainLoss();
            if (tempGainLoss <= 0)
            {
                if (tempGainLoss < 0)
                {
                    fvar.GetD110Record().SetD110GainLossSign("-");
                }
                fvar.GetD110Record().SetD110LossAllocated(0);
                fvar.GetD110Record().SetD110GainLessLoss(0);
                fvar.GetD110Record().SetD110TaperedGain(0);
                fvar.GetD110Record().SetD110GainLoss(tempGainLoss);
            }
            else
            {
                fvar.GetD110Record().SetD110GainLossSign("+");
                int lossSub = gvar.GetWsLossSub();
                decimal wsLossUsed = gvar.GetFiller13().GetWsLossElementAt(lossSub).GetWsLossUsed();

                if (wsLossUsed > tempGainLoss)
                {
                    fvar.GetD110Record().SetD110LossAllocated(tempGainLoss);
                    fvar.GetD110Record().SetD110GainLoss(tempGainLoss);
                    gvar.GetFiller13().GetWsLossElementAt(lossSub).SetWsLossUsed(wsLossUsed - tempGainLoss);
                    gvar.GetTempFields().GetTempDetails().SetTempGainLoss(0);
                }
                else if (wsLossUsed > 0)
                {
                    fvar.GetD110Record().SetD110LossAllocated(wsLossUsed);
                    fvar.GetD110Record().SetD110GainLoss(wsLossUsed);
                    gvar.GetTempFields().GetTempDetails().SetTempGainLoss(tempGainLoss - wsLossUsed);
                    gvar.GetFiller13().GetWsLossElementAt(lossSub).SetWsLossUsed(0);
                }
                else
                {
                    fvar.GetD110Record().SetD110GainLoss(tempGainLoss);
                    fvar.GetD110Record().SetD110LossAllocated(0);
                    gvar.GetTempFields().GetTempDetails().SetTempGainLoss(0);
                }

                // Calculate gain less loss and tapered gain
                fvar.GetD110Record().SetD110GainLessLoss(
                    fvar.GetD110Record().GetD110GainLoss() - fvar.GetD110Record().GetD110LossAllocated());
                fvar.GetD110Record().SetD110TaperedGain(
                    fvar.GetD110Record().GetD110GainLessLoss() * (fvar.GetD110Record().GetD110TaperRate() / 100));
            }

            // Set loss type
            if (fvar.GetD110Record().GetD110LossAllocated() == 0)
            {
                fvar.GetD110Record().SetD110LossType(" ");
            }
            else
            {
                fvar.GetD110Record().SetD110LossType(
                    gvar.GetFiller14().GetWsLossTextElementAt(gvar.GetWsLossSub()).GetWsLossLetter());
            }

            // Set remaining fields and write record
            fvar.GetD110Record().SetD110DisposalDate(gvar.GetTempFields().GetTempDetails().GetTempDisposalDate());
            fvar.GetD110Record().SetD110SecuritySign(gvar.GetTempFields().GetTempDetails().GetTempSign());
            fvar.SetD110RecordAsString(fvar.GetD110Record().ToString());
        }
        /// <summary>
        /// Converts and implements the COBOL paragraph C2FORMAT-WRITE-REPORT.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: C2FORMAT-WRITE-REPORT
        /// This method handles report formatting and writing logic with gain/loss calculations.
        /// </remarks>
        public void C2FormatWriteReport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            if (gvar.GetTempFields().GetTempKey().GetTempSedolCode() != gvar.GetWsLastTempSedol() ||
                gvar.GetTempFields().GetTempKey().GetTempFundCode() != gvar.GetWsLastTempFund() ||
                (fvar.GetD110Record().GetD110GainLossSign() == "-" &&
                 gvar.GetWsLastTempGainsLoss() >= 0))
            {
                XFinishSedolDesc(fvar, gvar, ivar);
            }
            else
            {
                gvar.SetWsSedolLines(gvar.GetWsSedolLines() + 1);
            }

            if (gvar.GetTempFields().GetTempKey().GetTempSedolCode() == Gvar.DESCRIPTION_OTHER)
            {
                gvar.SetWsSedolLines(1);
            }

            if (gvar.GetReportFields().GetReportLineCount() >
                gvar.GetReportConstants().GetMaxLines() - gvar.GetReportConstants().GetReportFooterOffsetLines())
            {
                XReportHeadings(fvar, gvar, ivar);
            }

            if (fvar.GetD110Record().GetD110GainLossSign() == "-" &&
                (gvar.GetWsReportStatusFlag() == "G" || gvar.GetWsReportStatusFlag() == "P"))
            {
                gvar.SetWsReportStatusFlagAsString("L");
                XGainTotals(fvar, gvar, ivar);
            }

            gvar.GetReportHeader2().SetReportFundCode(gvar.GetTempFields().GetTempKey().GetTempFundCode());

            switch (gvar.GetWsSedolLines())
            {
                case 1:
                    if (gvar.GetReportFields().GetReportLineCount() >
                        gvar.GetReportConstants().GetMaxLines() - gvar.GetReportConstants().GetReportFooterOffsetLines() - 2)
                    {
                        XReportHeadings(fvar, gvar, ivar);
                    }
                    XWriteReportDetail(fvar, gvar, ivar);
                    gvar.GetReportDetail().SetReportSedolCode(gvar.GetTempFields().GetTempKey().GetTempSedolCode());
                    gvar.GetReportDetail().SetReportText(gvar.GetTempFields().GetTempDetails().GetTempIssuerName());
                    gvar.SetWsLastTempDescriptionAsString(gvar.GetTempFields().GetTempDetails().GetTempDescription());
                    break;
                case 2:
                    gvar.GetReportDetail().SetReportSedolCodeAsString(" ");
                    gvar.GetReportDetail().SetReportText(gvar.GetTempFields().GetTempDetails().GetTempDescription());
                    break;
                default:
                    gvar.GetReportDetail().SetReportSedolCodeAsString(" ");
                    gvar.GetReportDetail().SetReportTextAsString(" ");
                    break;
            }

            string acquisitionDate = $"{gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate().Substring(6, 2)}/" +
                                   $"{gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate().Substring(4, 2)}/" +
                                   $"{gvar.GetTempFields().GetTempDetails().GetTempAcquisitionDate().Substring(2, 2)}";
            gvar.GetReportDetail().SetReportAcquisitionDate(acquisitionDate);

            string disposalDate = $"{gvar.GetTempFields().GetTempDetails().GetTempDisposalDate().Substring(6, 2)}/" +
                                 $"{gvar.GetTempFields().GetTempDetails().GetTempDisposalDate().Substring(4, 2)}/" +
                                 $"{gvar.GetTempFields().GetTempDetails().GetTempDisposalDate().Substring(2, 2)}";
            gvar.GetReportDetail().SetReportDisposalDate(disposalDate);

            gvar.GetReportDetail().SetReportQuotedInd(gvar.GetTempFields().GetTempDetails().GetTempQuotedInd());
            gvar.GetReportDetail().SetReportQuantity(gvar.GetTempFields().GetTempDetails().GetTempQuantity());
            gvar.GetReportDetail().SetReportQuantitySign(gvar.GetTempFields().GetTempDetails().GetTempSign());
            gvar.GetReportDetail().SetReportProceeds(gvar.GetTempFields().GetTempDetails().GetTempProceeds());
            gvar.GetReportDetail().SetReportEstimatedInd(gvar.GetTempFields().GetTempDetails().GetTempEstimatedInd());
            gvar.GetReportDetail().SetReport1982Ind(gvar.GetTempFields().GetTempDetails().GetTemp1982Ind());
            gvar.GetReportDetail().SetReportTrancheFlag(gvar.GetTempFields().GetTempDetails().GetTempTrancheFlag());

            if (fvar.GetD110Record().GetD110GainLossSign() == "+")
            {
                gvar.GetReportDetail().SetReportGainLoss(fvar.GetD110Record().GetD110GainLoss());
                gvar.GetReportDetail().SetReportTaperRate(gvar.GetTempFields().GetTempDetails().GetTempTaperRate());
                gvar.GetReportDetail().SetReportPercentageSign("%");
                gvar.GetReportDetail().SetReportLossUsed(fvar.GetD110Record().GetD110LossAllocated());
                gvar.GetReportDetail().SetReportGainLessLoss(fvar.GetD110Record().GetD110GainLessLoss());
                gvar.GetReportDetail().SetReportTaperedGain(fvar.GetD110Record().GetD110TaperedGain());
                gvar.GetReportFields().SetReportTotalGain(gvar.GetReportFields().GetReportTotalGain() + fvar.GetD110Record().GetD110GainLoss());
                gvar.GetReportFields().SetReportTotalLossUsed(gvar.GetReportFields().GetReportTotalLossUsed() + fvar.GetD110Record().GetD110LossAllocated());
                gvar.GetReportFields().SetReportTotalTaperedGain(gvar.GetReportFields().GetReportTotalTaperedGain() + fvar.GetD110Record().GetD110TaperedGain());
                gvar.SetWsReportStatusFlagAsString("P");
                gvar.GetReportDetail().SetReportLossType(fvar.GetD110Record().GetD110LossType());
            }
            else
            {
                gvar.GetReportDetail().SetReportGainLoss(-fvar.GetD110Record().GetD110GainLoss());
                gvar.GetReportFields().SetReportTotalLoss(gvar.GetReportFields().GetReportTotalLoss() - fvar.GetD110Record().GetD110GainLoss());
                gvar.SetWsReportStatusFlagAsString("L");
            }

            gvar.GetReportDetail().SetReportBusinessUse(gvar.GetTempFields().GetTempKey().GetTempBusinessUse());
            XWriteReportDetail(fvar, gvar, ivar);

            if (fvar.GetD110Record().GetD110GainLossSign() == "-")
            {
                gvar.SetWsLastTempGainsLoss(-fvar.GetD110Record().GetD110GainLoss());
            }
            else
            {
                gvar.SetWsLastTempGainsLoss(fvar.GetD110Record().GetD110GainLoss());
            }
        }
        /// <summary>
        /// Original COBOL paragraph: c3CalculateLosses
        /// </summary>
        /// <remarks>
        /// Calculates client losses by distributing client gains across different loss types.
        /// Handles special case for CON_9697_LOSSES and updates remaining gains and losses.
        /// </remarks>
        public void C3CalculateLosses(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Set initial values from client gain/loss
            gvar.SetWsClientGainLeft(gvar.GetWsClientGain());
            gvar.GetFiller13().GetWsLossElementAt(Gvar.CURYR_LOSSES).SetWsBfLoss(gvar.GetWsClientLoss());
            gvar.GetFiller13().GetWsLossElementAt(Gvar.CURYR_LOSSES).SetWsLossYtd(gvar.GetWsClientLoss());

            // Loop through loss types
            for (int wsLossSub = 1; wsLossSub <= Gvar.MAX_LOSS_TYPES; wsLossSub++)
            {
                gvar.SetWsLossSub(wsLossSub);

                // Special handling for CON_9697_LOSSES
                if (wsLossSub == Gvar.CON_9697_LOSSES)
                {
                    if (gvar.GetWsClientGainLeft() > gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).GetWsGainsAllowance())
                    {
                        gvar.SetWsClientGainLeft(gvar.GetWsClientGainLeft() -
                            gvar.GetWsAllowancesTable().GetWsAllowancesElementAt(gvar.GetWsAllowancesSub()).GetWsGainsAllowance());
                    }
                    else
                    {
                        return; // Exit the method early
                    }
                }

                // Handle current loss type
                if (gvar.GetWsClientGainLeft() < gvar.GetFiller13().GetWsLossElementAt(wsLossSub).GetWsBfLoss())
                {
                    gvar.GetFiller13().GetWsLossElementAt(wsLossSub).SetWsLossUsed(gvar.GetWsClientGainLeft());
                    gvar.GetFiller13().GetWsLossElementAt(wsLossSub).SetWsBfLoss(
                        gvar.GetFiller13().GetWsLossElementAt(wsLossSub).GetWsBfLoss() - gvar.GetWsClientGainLeft());
                    gvar.GetFiller13().GetWsLossElementAt(wsLossSub).SetWsLossYtd(
                        gvar.GetFiller13().GetWsLossElementAt(wsLossSub).GetWsBfLoss());
                    return; // Exit the method early
                }
                else
                {
                    gvar.GetFiller13().GetWsLossElementAt(wsLossSub).SetWsLossUsed(gvar.GetFiller13().GetWsLossElementAt(wsLossSub).GetWsBfLoss());
                    gvar.GetFiller13().GetWsLossElementAt(wsLossSub).SetWsLossYtd(0);
                    gvar.SetWsClientGainLeft(gvar.GetWsClientGainLeft() -
                        gvar.GetFiller13().GetWsLossElementAt(wsLossSub).GetWsBfLoss());
                }
            }
        }
        /// <summary>
        /// C4WriteYtdLosses - Converts from COBOL paragraph C4-WRITE-YTD-LOSSES
        /// </summary>
        /// <remarks>
        /// Original COBOL logic:
        /// Writes YTD losses record if conditions are met
        /// </remarks>
        /// <param name="fvar">File variable container</param>
        /// <param name="gvar">Global variable container</param>
        /// <param name="ivar">Input variable container</param>
        public void C4WriteYtdLosses(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            if (gvar.GetD8Record().GetD8UseLosses() == "Y"
                && gvar.GetD37RecordFormat2().GetD37UseLossesFlag() == "Y"
                && ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R"
                && gvar.GetWsDbLosses().GetWsDbLossesKey().GetWsClientFundCode() == gvar.GetWsLastFundCode())
            {
                fvar.SetD157RecordAsString(" ");
                fvar.GetD157Record().SetD157ClientFundCode(gvar.GetWsDbLosses().GetWsDbLossesKey().GetWsClientFundCode());
                fvar.GetD157Record().SetD157MasterFileYear(gvar.GetWsDbLosses().GetWsDbLossesKey().GetWsMasterFileYear());
                fvar.GetD157Record().SetD157PeriodStartDate(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWsPeriodStartDate());
                fvar.GetD157Record().SetD157PeriodEndDate(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWsPeriodEndDate());

                fvar.GetD157RecordN().SetD1579697AndLaterLossesYtdN(gvar.GetFiller13().GetWsLossElementAt(Gvar.CURYR_LOSSES).GetWsLossYtd());
                fvar.GetD157RecordN().SetD1579697AndLaterLossesYtdN(
                    fvar.GetD157RecordN().GetD1579697AndLaterLossesYtdN() + gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9697_LOSSES).GetWsLossYtd());

                fvar.GetD157RecordN().SetD1579596AndEarlierLossesYtdN(gvar.GetFiller13().GetWsLossElementAt(Gvar.CON_9596_LOSSES).GetWsLossYtd());
                fvar.GetD157RecordN().SetD157IncomeLossesYtdN(gvar.GetFiller13().GetWsLossElementAt(Gvar.INCOME_LOSSES).GetWsLossYtd());

                // WRITE D157-RECORD - Assuming this writes to a file through fvar
                // Implementation of the actual write operation would depend on how fvar handles writes
            }
        }
        /// <summary>
        /// Implements the COBOL dEnd paragraph functionality.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: dEnd
        /// Performs final processing tasks including writing records, closing files, and logging messages.
        /// </remarks>
        public void DEnd(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            if (gvar.GetWsProcessingSale() == "Y")
            {
                B1WriteTempRecord(fvar, gvar, ivar);
            }

            //if (gvar.GetWsLastFundCode() > " ")
            //{
            //    CProcessClient(fvar, gvar, ivar);
            //}

            gvar.GetWsMessages().SetWsMessage1AsString(" ");

            if (ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R")
            {
                gvar.GetWsMessages().SetWsMessage1AsString(
                    $"Realised Tapered Gains Export File: {gvar.GetExportFile()} created");
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.REALISED_DATA_FILE);
            }
            else
            {
                gvar.GetWsMessages().SetWsMessage1AsString(
                    $"Unrealised Tapered Gains Export File: {gvar.GetExportFile()} created");
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.UNREALISED_DATA_FILE);
            }

            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage1());
            X4Cgtlog(fvar, gvar, ivar);

            gvar.GetCgtlogLinkageArea2().SetLLogMessageTypeAsString("F");
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.CLOSE_FILE);
            X4Cgtlog(fvar, gvar, ivar);

            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.TAPER_RATE_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetWsUserfundOpenFlag() == "N")
            {
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.USER_FUND_FILE);
                XCallCgtfiles(fvar, gvar, ivar);
            }

            if (gvar.GetD8Record().GetD8UseLosses() == "Y")
            {
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.LOSSES_FROM_DB_FILE);
                XCallCgtfiles(fvar, gvar, ivar);

                if (ivar.GetCgtsing3Linkage().GetCgtsing3ScheduleType() == "R")
                {
                    gvar.GetCgtfilesLinkage().SetLFileName(Gvar.DISPOSALS_FROM_DB_FILE);
                    XCallCgtfiles(fvar, gvar, ivar);

                    fvar.SetD157RecordAsString(fvar.GetD157TrailerRecord().ToString());
                    // WRITE and CLOSE operations would be handled by the file system in C#
                    // File close operations handled by file system infrastructure
                }
            }

            gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_CLOSE);
            XCallCgttemp(fvar, gvar, ivar);

            // File close operations handled by file system infrastructure
        }
        /// <summary>
        /// XReportHeadings - COBOL paragraph that generates report headings and manages pagination.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: xReportHeadings
        /// This method handles report header generation including page counts, fund information,
        /// date formatting, and column headers based on report type.
        /// </remarks>
        public void XReportHeadings(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Increment page count and set page number
            gvar.GetReportFields().SetReportPageCount(gvar.GetReportFields().GetReportPageCount() + 1);
            gvar.GetReportHeader1().SetReportPageNo(gvar.GetReportFields().GetReportPageCount());

            // Set fund information in report header
            gvar.GetReportHeader2().SetReportFundCode(gvar.GetD37RecordFormat2().GetD37Key().GetD37FundCode());
            gvar.GetReportHeader2().SetReportFundName(gvar.GetD37RecordFormat2().GetD37Name());

            // Write page break and handle header offset
            fvar.SetD111RecordAsString(" ");
            // fvar.WriteD111Record(Gvar.AFTER, Gvar.PAGE); // COBOL write statement - handled by report infrastructure

            if (gvar.GetReportConstants().GetReportHeaderOffsetLines() > 0)
            {
                // fvar.WriteD111Record(Gvar.AFTER, gvar.GetReportConstants().GetReportHeaderOffsetLines(), Gvar.LINES); // COBOL write statement
            }

            // Write main report headers
            // fvar.WriteD111Record(gvar.GetReportHeader1().ToString(), Gvar.AFTER, 1, Gvar.LINE); // COBOL write statement
            int lineCount = gvar.GetReportConstants().GetReportHeaderOffsetLines() + 2;
            gvar.GetReportFields().SetReportLineCount(lineCount);

            // fvar.WriteD111Record(gvar.GetReportHeader2().ToString(), Gvar.AFTER, 3, Gvar.LINES); // COBOL write statement
            lineCount += 3;
            gvar.GetReportFields().SetReportLineCount(lineCount);

            // Format and set report dates
            string fromDate = $"{gvar.GetD37RecordFormat2().GetD37PeriodStartDate().GetD37PeriodStartDateDd()}/" +
                             $"{gvar.GetD37RecordFormat2().GetD37PeriodStartDate().GetD37PeriodStartDateMm()}/" +
                             $"{gvar.GetD37RecordFormat2().GetD37PeriodStartDate().GetD37PeriodStartDateYy()}";
            gvar.GetReportHeader3().SetReportFundFromDate(fromDate);

            string toDate = $"{gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateDd()}/" +
                           $"{gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateMm()}/" +
                           $"{gvar.GetD37RecordFormat2().GetD37PeriodEndDate().GetD37PeriodEndDateYy()}";
            gvar.GetReportHeader3().SetReportFundToDate(toDate);

            // Write additional headers and adjust line count
            // fvar.WriteD111Record(gvar.GetReportHeader3().ToString(), Gvar.AFTER, 1, Gvar.LINE); // COBOL write statement
            lineCount += 1;
            gvar.GetReportFields().SetReportLineCount(lineCount);

            // Conditionally write different column headers based on report type
            if (gvar.GetWsReportStatusFlag() == "L" || gvar.GetWsReportStatusFlag() == "P")
            {
                // fvar.WriteD111Record(gvar.GetReportColHeader3().ToString(), Gvar.AFTER, 1, Gvar.LINE); // COBOL write statement
                lineCount += 1;
                gvar.GetReportFields().SetReportLineCount(lineCount);

                // fvar.WriteD111Record(gvar.GetReportColHeader4().ToString(), Gvar.AFTER, 1, Gvar.LINE); // COBOL write statement
                lineCount += 1;
                gvar.GetReportFields().SetReportLineCount(lineCount);
            }
            else
            {
                // fvar.WriteD111Record(gvar.GetReportColHeader1().ToString(), Gvar.AFTER, 1, Gvar.LINE); // COBOL write statement
                lineCount += 1;
                gvar.GetReportFields().SetReportLineCount(lineCount);

                // fvar.WriteD111Record(gvar.GetReportColHeader2().ToString(), Gvar.AFTER, 1, Gvar.LINE); // COBOL write statement
                lineCount += 1;
                gvar.GetReportFields().SetReportLineCount(lineCount);
            }

            // Perform detail writing and set SEDOL lines
            XWriteReportDetail(fvar, gvar, ivar);
            gvar.SetWsSedolLines(1);
        }
        /// <summary>
        /// Implements the COBOL WRITE-DETAIL paragraph functionality.
        /// Original COBOL paragraph name: xWriteReportDetail
        /// </summary>
        /// <remarks>
        /// Writes the report detail record, increments line count, and clears the detail line.
        /// COBOL equivalent:
        ///   WRITE D111-RECORD FROM REPORT-DETAIL
        ///   ADD 1 TO REPORT-LINE-COUNT
        ///   MOVE SPACES TO REPORT-DETAIL
        /// </remarks>
        public void XWriteReportDetail(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // WRITE D111-RECORD FROM REPORT-DETAIL
            fvar.GetD111Record().SetD111RecordAsString(gvar.GetReportDetail().ToString());

            // ADD 1 TO REPORT-LINE-COUNT
            gvar.GetReportFields().SetReportLineCount(gvar.GetReportFields().GetReportLineCount() + 1);

            // MOVE SPACES TO REPORT-DETAIL
            gvar.SetReportDetailAsString(" ");
        }
        /// <summary>
        /// Implements the COBOL paragraph X-FINISH-SEDOL-DESC logic in C#.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: X-FINISH-SEDOL-DESC
        /// Handles the logic for finishing SEDOL descriptions based on processing status.
        /// If conditions are met, writes a report detail line with the last temp description.
        /// Otherwise, resets the SEDOL line counter.
        /// </remarks>
        public void XFinishSedolDesc(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Check if we have exactly 1 SEDOL line and are processing gains or losses
            if (gvar.GetWsSedolLines() == 1 &&
                (gvar.GetWsReportStatusFlag() == "P" ||
                 gvar.GetWsReportStatusFlag() == "L"))
            {
                // Move last temp description to report text and write detail line
                gvar.GetReportDetail().SetReportText(gvar.GetWsLastTempDescription());
                XWriteReportDetail(fvar, gvar, ivar);
            }
            else
            {
                // Reset SEDOL line counter to 1
                gvar.SetWsSedolLines(1);
            }
        }
        /// <summary>
        /// Original COBOL paragraph: X-GAIN-TOTALS
        /// </summary>
        /// <remarks>
        /// Handles reporting of gain totals, including checking line counts,
        /// writing headers when needed, and processing zero loss cases.
        /// </remarks>
        public void XGainTotals(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Check if we need to print report headings
            if (gvar.GetReportFields().GetReportLineCount() > (56 - gvar.GetReportConstants().GetReportFooterOffsetLines()))
            {
                XReportHeadings(fvar, gvar, ivar);
            }

            // Process zero loss case
            if (gvar.GetReportFields().GetReportTotalLoss() == 0)
            {
                // Store current line count
                gvar.GetReportFields().SetReportLineCountStore(gvar.GetReportFields().GetReportLineCount());

                XFinishSedolDesc(fvar, gvar, ivar);

                // Only write detail if line count hasn't changed
                if (gvar.GetReportFields().GetReportLineCount() == gvar.GetReportFields().GetReportLineCountStore())
                {
                    XWriteReportDetail(fvar, gvar, ivar);
                }

                // Update report status flags and values
                gvar.SetWsReportStatusFlagAsString("T");
                gvar.SetReportDetailAsString(gvar.GetReportGainTotalText().ToString());
                gvar.GetReportDetail().SetReportGainLoss(gvar.GetReportFields().GetReportTotalGain());
                gvar.GetReportDetail().SetReportLossUsed(gvar.GetReportFields().GetReportTotalLossUsed());
                gvar.GetReportDetail().SetReportTaperedGain(gvar.GetReportFields().GetReportTotalTaperedGain());

                XWriteReportDetail(fvar, gvar, ivar);

                // Write column headers with proper line spacing
                fvar.SetD111RecordAsString(gvar.GetReportColHeader3().ToString());
                // Equivalent to AFTER ADVANCING 2 LINES and ADD 2 TO LINE-COUNT
                gvar.GetReportFields().SetReportLineCount(gvar.GetReportFields().GetReportLineCount() + 2);

                fvar.SetD111RecordAsString(gvar.GetReportColHeader4().ToString());
                // Equivalent to AFTER ADVANCING 1 LINE and ADD 1 TO LINE-COUNT
                gvar.GetReportFields().SetReportLineCount(gvar.GetReportFields().GetReportLineCount() + 1);

                // Clear detail line and write it
                gvar.SetReportDetailAsString(" ");
                XWriteReportDetail(fvar, gvar, ivar);
            }
        }
        /// <summary>
        /// Implements the COBOL X-LOSS-TOTALS paragraph functionality.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: X-LOSS-TOTALS
        /// Handles reporting of loss totals and net gains, including:
        /// - Checking if new page headings are needed
        /// - Writing report details for losses and net gains
        /// - Calculating and storing line counts
        /// </remarks>
        public void XLossTotals(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Check if we need new headings (when line count exceeds available space)
            if (gvar.GetReportFields().GetReportLineCount() > 56 - gvar.GetReportConstants().GetReportFooterOffsetLines())
            {
                XReportHeadings(fvar, gvar, ivar);
            }

            // Store current line count
            gvar.GetReportFields().SetReportLineCountStore(gvar.GetReportFields().GetReportLineCount());

            // Process SEDOL description
            XFinishSedolDesc(fvar, gvar, ivar);

            // Write detail line if no page break occurred (line count unchanged)
            if (gvar.GetReportFields().GetReportLineCount() == gvar.GetReportFields().GetReportLineCountStore())
            {
                XWriteReportDetail(fvar, gvar, ivar);
            }

            // Write loss total line
            gvar.SetReportDetailAsString(gvar.GetReportLossTotalText().ToString());
            gvar.GetReportDetail().SetReportGainLoss(gvar.GetReportFields().GetReportTotalLoss());
            XWriteReportDetail(fvar, gvar, ivar);

            // Write net gain total line
            XWriteReportDetail(fvar, gvar, ivar);
            gvar.SetReportDetailAsString(gvar.GetReportNetGainTotalText().ToString());
            gvar.GetReportFields().SetReportTotalGain(gvar.GetReportFields().GetReportTotalGain() + gvar.GetReportFields().GetReportTotalLoss());
            gvar.GetReportDetail().SetReportGainLoss(gvar.GetReportFields().GetReportTotalGain());
            XWriteReportDetail(fvar, gvar, ivar);
        }
        /// <summary>
        /// XCallCgttemp - COBOL paragraph that calls the CGTTEMP program with CGTTEMP-LINKAGE as parameter
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: xCallCgttemp
        /// This method represents a call to an external COBOL program named 'CGTTEMP'
        /// Passes the CGTTEMP-LINKAGE data area as parameter
        /// </remarks>
        public void XCallCgttemp(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the CGTTEMP program
            // CGTTEMP cgttempProgram = new CGTTEMP(); // External program - not implemented
            // Call the Run method with CGTTEMP-LINKAGE parameter as specified in USING clause
            // cgttempProgram.Run(gvar.GetCgttempLinkage());
        }
        /// <summary>
        /// XCallCgtabort - Converts COBOL paragraph xCallCgtabort to C# method.
        /// </summary>
        /// <remarks>
        /// Original COBOL logic:
        /// MOVE L-FILE-RETURN-CODE TO L-ABORT-FILE-STATUS
        /// MOVE PROGRAM-NAME TO L-ABORT-PROGRAM-NAME
        /// MOVE L-FILE-NAME TO L-ABORT-FILE-NAME
        /// CALL 'CGTABORT' USING COMMON-LINKAGE CGTABORT-LINKAGE
        /// </remarks>
        /// <param name="fvar">Fvar instance (not used in this method)</param>
        /// <param name="gvar">Gvar instance containing all shared variables</param>
        /// <param name="ivar">Ivar instance (not used in this method)</param>
        public void XCallCgtabort(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Set L-ABORT-FILE-STATUS from L-FILE-RETURN-CODE
            gvar.GetCgtabortLinkage().SetLAbortFileStatus(gvar.GetCgtfilesLinkage().GetLFileReturnCode());

            // Set L-ABORT-PROGRAM-NAME from PROGRAM-NAME
            gvar.GetCgtabortLinkage().SetLAbortProgramName(gvar.GetProgramName());

            // Set L-ABORT-FILE-NAME from L-FILE-NAME
            gvar.GetCgtabortLinkage().SetLAbortFileName(gvar.GetCgtfilesLinkage().GetLFileName());

            // Call CGTABORT with specified parameters
            // Cgtabort cgtabort = new Cgtabort(); // External program - not implemented
            // cgtabort.Run(gvar.GetCommonLinkage(), gvar.GetCgtabortLinkage());
        }
        /// <summary>
        /// Calls the CGTINVRT external program with the specified linkage area.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: X-CALL-CGTINVRT
        /// This method invokes an external program 'CGTINVRT' with the CGTINVRT-LINKAGE data structure.
        /// The call is synchronous and passes the current linkage area to the external program.
        /// </remarks>
        /// <param name="fvar">Fvar instance containing file-related variables</param>
        /// <param name="gvar">Gvar instance containing global variables</param>
        /// <param name="ivar">Ivar instance containing input variables</param>
        public void XCallCgtinvrt(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program
            // Cgtinvrt externalProgram = new Cgtinvrt(); // External program - not implemented
            // Call the Run method with ONLY the CGTINVRT-LINKAGE parameter as specified in USING
            // externalProgram.Run(gvar.GetCgtinvrtLinkage());
        }
        /// <summary>
        /// Implements the COBOL paragraph X4CGTLOG functionality in C#.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: X4CGTLOG
        /// This method increments the message number, logs it, and checks the message type.
        /// If the message type is 'Q', it sets the process flag and updates the message type.
        /// </remarks>
        /// <param name="fvar">Fvar parameter (not used in this method)</param>
        /// <param name="gvar">Gvar parameter containing all working storage variables</param>
        /// <param name="ivar">Ivar parameter (not used in this method)</param>
        public void X4Cgtlog(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Increment message number (ADD 1 TO WS-MESSAGE-NO)
            gvar.SetWsMessageNo(gvar.GetWsMessageNo() + 1);

            // Move message number to log field (MOVE WS-MESSAGE-NO TO L-LOG-MESSAGE-NO)
            gvar.GetCgtlogLinkageArea2().SetLLogMessageNo(gvar.GetWsMessageNo().ToString());

            // Call external log program (CALL 'CGTLOG' USING...)
            // Cgtlog cgtlog = new Cgtlog(); // External program - not implemented
            // cgtlog.Run(gvar.GetCgtlogLinkageArea1(), gvar.GetCgtlogLinkageArea2());

            // Check message type (IF L-LOG-MESSAGE-TYPE = 'Q')
            if (gvar.GetCgtlogLinkageArea2().GetLLogMessageType() == "Q")
            {
                // Set process flag (MOVE 'Q' TO WS-PROCESS-FLAG)
                gvar.SetWsProcessFlagAsString("Q");

                // Update message type (MOVE 'P' TO L-LOG-MESSAGE-TYPE)
                gvar.GetCgtlogLinkageArea2().SetLLogMessageTypeAsString("P");
            }
        }
        /// <summary>
        /// X5GetLossDetails - Processes loss details from a file record by unstringing and converting values.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: x5GetLossDetails
        /// 
        /// This method performs the following operations:
        /// 1. Unstrings a file record into multiple fields separated by commas
        /// 2. Reformat dates from YYYYMMDD to MMDDYYYY format
        /// 3. Converts string values to numeric formats
        /// 4. Determines sign for losses adjustment field
        /// </remarks>
        public void X5GetLossDetails(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Unstring the file record into individual fields
            var unstringResult = gvar.GetLFileRecordArea().ToString().Split(',');

            // Store unstring results in corresponding fields
            gvar.GetWsDbLosses().GetWsDbLossesKey().SetWsClientFundCodeAsString(unstringResult[0]);
            gvar.GetWsDbLossesN().SetWsMasterFileYearN(int.Parse(unstringResult[1]));
            gvar.SetWsWorkStartDateAsString(unstringResult[2]);
            gvar.SetWsWorkEndDateAsString(unstringResult[3]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsStatusAsString(unstringResult[4]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsIncomeLossesAsString(unstringResult[5]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsBf9697AndLaterLossesAsString(unstringResult[6]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsBf9596AndEarlierLossesAsString(unstringResult[7]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWs9697AndLaterLossesYtdAsString(unstringResult[8]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWs9596AndEarlierLossesYtdAsString(unstringResult[9]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsIncomeLossesYtdAsString(unstringResult[10]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsLossesAdjustmentAsString(unstringResult[11]);
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsOverrideYearEndAsString(unstringResult[12]);
            gvar.SetWsRemainderAsString(string.Join(",", unstringResult, 13, unstringResult.Length - 13));

            // Reformat start and end dates
            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsPeriodStartDate(
                gvar.GetWsWorkStartDate().Substring(6, 4) +
                gvar.GetWsWorkStartDate().Substring(3, 2) +
                gvar.GetWsWorkStartDate().Substring(0, 2));

            gvar.GetWsDbLosses().GetWsDbLossesDetails().SetWsPeriodEndDate(
                gvar.GetWsWorkEndDate().Substring(6, 4) +
                gvar.GetWsWorkEndDate().Substring(3, 2) +
                gvar.GetWsWorkEndDate().Substring(0, 2));

            // Convert string fields to numeric values
            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWsIncomeLosses());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetWsDbLossesN().SetWsIncomeLossesN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWsBf9697AndLaterLosses());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetWsDbLossesN().SetWsBf9697AndLaterLossesN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWsBf9596AndEarlierLosses());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetWsDbLossesN().SetWsBf9596AndEarlierLossesN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWs9697AndLaterLossesYtd());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetWsDbLossesN().SetWs9697AndLaterLossesYtdN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWs9596AndEarlierLossesYtd());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetWsDbLossesN().SetWs9596AndEarlierLossesYtdN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWsIncomeLossesYtd());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetWsDbLossesN().SetWsIncomeLossesYtdN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbLosses().GetWsDbLossesDetails().GetWsLossesAdjustment());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetWsDbLossesN().SetWsLossesAdjustmentN(gvar.GetWsDbDisposals().GetWNumOut9());

            // Set the sign for losses adjustment
            gvar.GetWsDbLossesN().SetWsLossesAdjustmentSign(
                gvar.GetWsDbDisposals().GetWNumSign().ToString().Contains("-") ? "-" : "+");
        }
        /// <summary>
        /// Converts COBOL UNSTRING operation to parse disposal details from a record and populate D158 record fields.
        /// Original COBOL paragraph: x6GetDisposalDetails
        /// </summary>
        /// <remarks>
        /// This method:
        /// 1. Unstrings a comma-delimited record into individual fields
        /// 2. Reformat date fields from YYYYMMDD to proper formats
        /// 3. Converts numeric string fields to decimal values
        /// 4. Determines gain/loss sign based on value
        /// </remarks>
        public void X6GetDisposalDetails(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Split comma-delimited record into component fields
            string[] fields = gvar.GetLFileRecordArea().ToString().Split(',');

            // Assign parsed values to destination fields
            gvar.GetD158RecordN().SetD158DisposalIdN(int.Parse(fields[0]));
            gvar.GetD158Record().SetD158ClientFundCodeAsString(fields[1]);
            gvar.GetWsDbDisposals().SetWDisposalDateAsString(fields[2]);
            gvar.GetWsDbDisposals().SetWAcquisitionDateAsString(fields[3]);
            gvar.GetD158Record().SetD158DescriptionAsString(fields[4]);
            gvar.GetWsDbDisposals().SetWCostAsString(fields[5]);
            gvar.GetWsDbDisposals().SetWProceedsAsString(fields[6]);
            gvar.GetWsDbDisposals().SetWGainLossAsString(fields[7]);
            gvar.GetWsDbDisposals().SetWPercentBusinessAsString(fields[8]);
            gvar.GetD158Record().SetD158QuotedAsString(fields[9]);
            gvar.GetD158Record().SetD158EstimatedAsString(fields[10]);
            if (fields.Length > 11)
            {
                string[] remainingFields = new string[fields.Length - 11];
                Array.Copy(fields, 11, remainingFields, 0, fields.Length - 11);
                gvar.SetWsRemainderAsString(string.Join(",", remainingFields));
            }
            else
            {
                gvar.SetWsRemainderAsString(string.Empty);
            }

            // Reformat dates from YYYYMMDD to proper format
            string disposalDate = gvar.GetWsDbDisposals().GetWDisposalDate();
            string acquisitionDate = gvar.GetWsDbDisposals().GetWAcquisitionDate();

            gvar.GetD158Record().SetD158DisposalDate(
                disposalDate.Substring(7, 4) +
                disposalDate.Substring(4, 2) +
                disposalDate.Substring(1, 2)
            );

            gvar.GetD158Record().SetD158AcquisitionDate(
                acquisitionDate.Substring(7, 4) +
                acquisitionDate.Substring(4, 2) +
                acquisitionDate.Substring(1, 2)
            );

            // Convert string numbers to decimal values
            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbDisposals().GetWCost());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetD158RecordN().SetD158CostN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbDisposals().GetWProceeds());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetD158RecordN().SetD158ProceedsN(gvar.GetWsDbDisposals().GetWNumOut9());

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbDisposals().GetWGainLoss());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetD158RecordN().SetD158GainLossN(gvar.GetWsDbDisposals().GetWNumOut9());

            // Set gain/loss sign based on value
            gvar.GetD158Record().SetD158GainLossSign(
                gvar.GetWsDbDisposals().GetWNumSign().ToString().Contains("-") ? "-" : "+"
            );

            gvar.GetWsDbDisposals().SetWNumIn(gvar.GetWsDbDisposals().GetWPercentBusiness());
            X7ConvertToNumber(fvar, gvar, ivar);
            gvar.GetD158RecordN().SetD158PercentBusinessN(gvar.GetWsDbDisposals().GetWNumOut9());
        }
        /// <summary>
        /// Converts an input string to a formatted number, handling decimal places and sign.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph: x7ConvertToNumber
        /// This method processes the input string (W-NUM-IN-BYTE) to produce a formatted numeric output (W-NUM-OUT-BYTE),
        /// handling negative signs, decimal points, and proper digit placement.
        /// </remarks>
        public void X7ConvertToNumber(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Handle negative/positive sign
            if (gvar.GetWsDbDisposals().GetWNumInTab().GetWNumInByte().Substring(0, 1) == "-")
            {
                gvar.GetWsDbDisposals().SetWNumSignAsString("-");
                gvar.GetWsDbDisposals().SetWStartSub(2);
            }
            else
            {
                gvar.GetWsDbDisposals().SetWNumSignAsString("+");
                gvar.GetWsDbDisposals().SetWStartSub(1);
            }

            // Initialize variables
            gvar.GetWsDbDisposals().SetWDpSub(0);
            gvar.GetWsDbDisposals().SetWNumOut9(0);

            // Process each character in the input string
            for (int i = gvar.GetWsDbDisposals().GetWStartSub(); i <= 16; i++)
            {
                string currentChar = gvar.GetWsDbDisposals().GetWNumInTab().GetWNumInByte().Substring(i - 1, 1);
                if (currentChar == " ")
                {
                    break;
                }

                // Handle decimal point
                if (currentChar == ".")
                {
                    gvar.GetWsDbDisposals().SetWDpSub(i);
                }

                // Handle digits after decimal point
                if (i > gvar.GetWsDbDisposals().GetWDpSub() &&
                    gvar.GetWsDbDisposals().GetWDpSub() > 0 &&
                    currentChar != " ")
                {
                    int outputPosition = 14 + i - gvar.GetWsDbDisposals().GetWDpSub();
                    gvar.GetWsDbDisposals().GetWNumOutTab().SetWNumOutByte(
                        gvar.GetWsDbDisposals().GetWNumOutTab().GetWNumOutByte().Insert(
                            outputPosition - 1,
                            currentChar
                        )
                    );
                }
            }

            // Adjust indices and process non-decimal portion
            int lastIndex = gvar.GetWsDbDisposals().GetWSubI() - 1;
            if (gvar.GetWsDbDisposals().GetWDpSub() > 0)
            {
                lastIndex = gvar.GetWsDbDisposals().GetWDpSub() - 1;
            }

            // Process non-decimal digits
            for (int j = 14; j >= 1; j--)
            {
                if (lastIndex < gvar.GetWsDbDisposals().GetWStartSub())
                {
                    break;
                }

                string currentChar = gvar.GetWsDbDisposals().GetWNumInTab().GetWNumInByte().Substring(lastIndex - 1, 1);
                gvar.GetWsDbDisposals().GetWNumOutTab().SetWNumOutByte(
                    gvar.GetWsDbDisposals().GetWNumOutTab().GetWNumOutByte().Insert(
                        j - 1,
                        currentChar
                    )
                );
                lastIndex--;
            }
        }
        /// <summary>
        /// Executes the EQTPATH external program using the EQTPATH-LINKAGE parameter.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: xCallEqtpath
        /// This method represents a COBOL CALL statement to the EQTPATH program.
        /// </remarks>
        /// <param name="fvar">Fvar object for field variable access</param>
        /// <param name="gvar">Gvar object for global variable access</param>
        /// <param name="ivar">Ivar object for input variable access</param>
        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the EQTPATH program
            // Eqtpath eqtpath = new Eqtpath(); // External program - not implemented
            // Call the Run method with the EQTPATH-LINKAGE parameter
            // eqtpath.Run(gvar.GetEqtpathLinkage());
        }
        /// <summary>
        /// Executes a CALL to the CGTFILES program with the specified parameters.
        /// </summary>
        /// <remarks>
        /// Original COBOL paragraph name: xCallCgtfiles
        /// This method calls an external CGTFILES program passing CGTFILES linkage data.
        /// The CALL statement uses three parameters: CGTFILES-LINKAGE, L-FILE-RECORD-AREA, and COMMON-LINKAGE.
        /// </remarks>
        public void XCallCgtfiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the CGTFILES program
            // CGTFILES cgtfiles = new CGTFILES(); // External program - not implemented

            // Call the Run method with the three specified parameters in order
            // cgtfiles.Run(gvar.GetCgtfilesLinkage(), gvar.GetLFileRecordArea(), gvar.GetCommonLinkage());
        }
        /// <summary>
        /// Original COBOL paragraph: xCallMfHandlerForConfig
        /// </summary>
        /// <remarks>
        /// Converts COBOL CALL statement to C# method invocation.
        /// Moves configuration values between variables and calls external handler.
        /// </remarks>
        public void XCallMfHandlerForConfig(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE GET-CONFIG-VALUE TO L-ACT
            gvar.GetElcgmioLinkage1().SetLAct(Gvar.GET_CONFIG_VALUE);

            // CALL 'ELCGMIO' USING ELCGMIO-LINKAGE-1 ELCGMIO-LINKAGE-2
            // ELCGMIO elcgmio = new ELCGMIO(); // External program - not implemented
            // elcgmio.Run(gvar.GetElcgmioLinkage1(), gvar.GetElcgmioLinkage2());

            // MOVE ELCGMIO-LINKAGE-2 TO W-CONFIG-ITEM
            gvar.SetWConfigItemAsString(gvar.GetElcgmioLinkage2().ToString());
        }

    }
}
