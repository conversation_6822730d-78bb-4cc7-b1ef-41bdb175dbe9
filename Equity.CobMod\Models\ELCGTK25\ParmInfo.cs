using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing ParmInfo Data Structure

public class ParmInfo
{
    private static int _size = 86;
    // [DEBUG] Class: ParmInfo, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ParmLength, is_external=, is_static_class=False, static_prefix=
    private int _ParmLength =0;
    
    
    
    
    // [DEBUG] Field: ParmValues, is_external=, is_static_class=False, static_prefix=
    private ParmValues _ParmValues = new ParmValues();
    
    
    
    
    // [DEBUG] Field: ParmValuesR, is_external=, is_static_class=False, static_prefix=
    private ParmValuesR _ParmValuesR = new ParmValuesR();
    
    
    
    
    
    // Serialization methods
    public string GetParmInfoAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmLength.ToString().PadLeft(4, '0'));
        result.Append(_ParmValues.GetParmValuesAsString());
        result.Append(_ParmValuesR.GetParmValuesRAsString());
        
        return result.ToString();
    }
    
    public void SetParmInfoAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetParmLength(parsedInt);
        }
        offset += 4;
        if (offset + 81 <= data.Length)
        {
            _ParmValues.SetParmValuesAsString(data.Substring(offset, 81));
        }
        else
        {
            _ParmValues.SetParmValuesAsString(data.Substring(offset));
        }
        offset += 81;
        if (offset + 1 <= data.Length)
        {
            _ParmValuesR.SetParmValuesRAsString(data.Substring(offset, 1));
        }
        else
        {
            _ParmValuesR.SetParmValuesRAsString(data.Substring(offset));
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetParmInfoAsString();
    }
    // Set<>String Override function
    public void SetParmInfo(string value)
    {
        SetParmInfoAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetParmLength()
    {
        return _ParmLength;
    }
    
    // Standard Setter
    public void SetParmLength(int value)
    {
        _ParmLength = value;
    }
    
    // Get<>AsString()
    public string GetParmLengthAsString()
    {
        return _ParmLength.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetParmLengthAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _ParmLength = parsed;
    }
    
    // Standard Getter
    public ParmValues GetParmValues()
    {
        return _ParmValues;
    }
    
    // Standard Setter
    public void SetParmValues(ParmValues value)
    {
        _ParmValues = value;
    }
    
    // Get<>AsString()
    public string GetParmValuesAsString()
    {
        return _ParmValues != null ? _ParmValues.GetParmValuesAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmValuesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmValues == null)
        {
            _ParmValues = new ParmValues();
        }
        _ParmValues.SetParmValuesAsString(value);
    }
    
    // Standard Getter
    public ParmValuesR GetParmValuesR()
    {
        return _ParmValuesR;
    }
    
    // Standard Setter
    public void SetParmValuesR(ParmValuesR value)
    {
        _ParmValuesR = value;
    }
    
    // Get<>AsString()
    public string GetParmValuesRAsString()
    {
        return _ParmValuesR != null ? _ParmValuesR.GetParmValuesRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmValuesRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmValuesR == null)
        {
            _ParmValuesR = new ParmValuesR();
        }
        _ParmValuesR.SetParmValuesRAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetParmValues(string value)
    {
        _ParmValues.SetParmValuesAsString(value);
    }
    // Nested Class: ParmValues
    public class ParmValues
    {
        private static int _size = 81;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParmChar1, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar1 ="";
        
        
        
        
        // [DEBUG] Field: ParmMsg, is_external=, is_static_class=False, static_prefix=
        private ParmValues.ParmMsg _ParmMsg = new ParmValues.ParmMsg();
        
        
        
        
    public ParmValues() {}
    
    public ParmValues(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetParmChar1(data.Substring(offset, 1).Trim());
        offset += 1;
        _ParmMsg.SetParmMsgAsString(data.Substring(offset, ParmMsg.GetSize()));
        offset += 80;
        
    }
    
    // Serialization methods
    public string GetParmValuesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmChar1.PadRight(1));
        result.Append(_ParmMsg.GetParmMsgAsString());
        
        return result.ToString();
    }
    
    public void SetParmValuesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar1(extracted);
        }
        offset += 1;
        if (offset + 80 <= data.Length)
        {
            _ParmMsg.SetParmMsgAsString(data.Substring(offset, 80));
        }
        else
        {
            _ParmMsg.SetParmMsgAsString(data.Substring(offset));
        }
        offset += 80;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetParmChar1()
    {
        return _ParmChar1;
    }
    
    // Standard Setter
    public void SetParmChar1(string value)
    {
        _ParmChar1 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar1AsString()
    {
        return _ParmChar1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar1 = value;
    }
    
    // Standard Getter
    public ParmMsg GetParmMsg()
    {
        return _ParmMsg;
    }
    
    // Standard Setter
    public void SetParmMsg(ParmMsg value)
    {
        _ParmMsg = value;
    }
    
    // Get<>AsString()
    public string GetParmMsgAsString()
    {
        return _ParmMsg != null ? _ParmMsg.GetParmMsgAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmMsgAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmMsg == null)
        {
            _ParmMsg = new ParmMsg();
        }
        _ParmMsg.SetParmMsgAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: ParmMsg
    public class ParmMsg
    {
        private static int _size = 80;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParmChar2, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar2 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar3, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar3 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar4, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar4 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar5, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar5 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar6, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar6 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar7, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar7 ="";
        
        
        
        
        // [DEBUG] Field: Filler131, is_external=, is_static_class=False, static_prefix=
        private string _Filler131 ="";
        
        
        
        
        // [DEBUG] Field: ParmFn, is_external=, is_static_class=False, static_prefix=
        private ParmMsg.ParmFn _ParmFn = new ParmMsg.ParmFn();
        
        
        
        
        // [DEBUG] Field: Filler132, is_external=, is_static_class=False, static_prefix=
        private string _Filler132 ="";
        
        
        
        
    public ParmMsg() {}
    
    public ParmMsg(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetParmChar2(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar3(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar4(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar5(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar6(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar7(data.Substring(offset, 1).Trim());
        offset += 1;
        SetFiller131(data.Substring(offset, 2).Trim());
        offset += 2;
        _ParmFn.SetParmFnAsString(data.Substring(offset, ParmFn.GetSize()));
        offset += 8;
        SetFiller132(data.Substring(offset, 64).Trim());
        offset += 64;
        
    }
    
    // Serialization methods
    public string GetParmMsgAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmChar2.PadRight(1));
        result.Append(_ParmChar3.PadRight(1));
        result.Append(_ParmChar4.PadRight(1));
        result.Append(_ParmChar5.PadRight(1));
        result.Append(_ParmChar6.PadRight(1));
        result.Append(_ParmChar7.PadRight(1));
        result.Append(_Filler131.PadRight(2));
        result.Append(_ParmFn.GetParmFnAsString());
        result.Append(_Filler132.PadRight(64));
        
        return result.ToString();
    }
    
    public void SetParmMsgAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar2(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar3(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar4(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar5(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar6(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar7(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller131(extracted);
        }
        offset += 2;
        if (offset + 8 <= data.Length)
        {
            _ParmFn.SetParmFnAsString(data.Substring(offset, 8));
        }
        else
        {
            _ParmFn.SetParmFnAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 64 <= data.Length)
        {
            string extracted = data.Substring(offset, 64).Trim();
            SetFiller132(extracted);
        }
        offset += 64;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetParmChar2()
    {
        return _ParmChar2;
    }
    
    // Standard Setter
    public void SetParmChar2(string value)
    {
        _ParmChar2 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar2AsString()
    {
        return _ParmChar2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar2 = value;
    }
    
    // Standard Getter
    public string GetParmChar3()
    {
        return _ParmChar3;
    }
    
    // Standard Setter
    public void SetParmChar3(string value)
    {
        _ParmChar3 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar3AsString()
    {
        return _ParmChar3.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar3 = value;
    }
    
    // Standard Getter
    public string GetParmChar4()
    {
        return _ParmChar4;
    }
    
    // Standard Setter
    public void SetParmChar4(string value)
    {
        _ParmChar4 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar4AsString()
    {
        return _ParmChar4.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar4 = value;
    }
    
    // Standard Getter
    public string GetParmChar5()
    {
        return _ParmChar5;
    }
    
    // Standard Setter
    public void SetParmChar5(string value)
    {
        _ParmChar5 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar5AsString()
    {
        return _ParmChar5.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar5 = value;
    }
    
    // Standard Getter
    public string GetParmChar6()
    {
        return _ParmChar6;
    }
    
    // Standard Setter
    public void SetParmChar6(string value)
    {
        _ParmChar6 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar6AsString()
    {
        return _ParmChar6.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar6 = value;
    }
    
    // Standard Getter
    public string GetParmChar7()
    {
        return _ParmChar7;
    }
    
    // Standard Setter
    public void SetParmChar7(string value)
    {
        _ParmChar7 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar7AsString()
    {
        return _ParmChar7.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar7 = value;
    }
    
    // Standard Getter
    public string GetFiller131()
    {
        return _Filler131;
    }
    
    // Standard Setter
    public void SetFiller131(string value)
    {
        _Filler131 = value;
    }
    
    // Get<>AsString()
    public string GetFiller131AsString()
    {
        return _Filler131.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller131AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler131 = value;
    }
    
    // Standard Getter
    public ParmFn GetParmFn()
    {
        return _ParmFn;
    }
    
    // Standard Setter
    public void SetParmFn(ParmFn value)
    {
        _ParmFn = value;
    }
    
    // Get<>AsString()
    public string GetParmFnAsString()
    {
        return _ParmFn != null ? _ParmFn.GetParmFnAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmFnAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmFn == null)
        {
            _ParmFn = new ParmFn();
        }
        _ParmFn.SetParmFnAsString(value);
    }
    
    // Standard Getter
    public string GetFiller132()
    {
        return _Filler132;
    }
    
    // Standard Setter
    public void SetFiller132(string value)
    {
        _Filler132 = value;
    }
    
    // Get<>AsString()
    public string GetFiller132AsString()
    {
        return _Filler132.PadRight(64);
    }
    
    // Set<>AsString()
    public void SetFiller132AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler132 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: ParmFn
    public class ParmFn
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParmFn1To5, is_external=, is_static_class=False, static_prefix=
        private string _ParmFn1To5 ="";
        
        
        
        
        // [DEBUG] Field: ParmFnrest, is_external=, is_static_class=False, static_prefix=
        private string _ParmFnrest ="";
        
        
        
        
        // [DEBUG] Field: ParmFnYy, is_external=, is_static_class=False, static_prefix=
        private string _ParmFnYy ="";
        
        
        
        
    public ParmFn() {}
    
    public ParmFn(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetParmFn1To5(data.Substring(offset, 5).Trim());
        offset += 5;
        SetParmFnrest(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmFnYy(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetParmFnAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmFn1To5.PadRight(5));
        result.Append(_ParmFnrest.PadRight(1));
        result.Append(_ParmFnYy.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetParmFnAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetParmFn1To5(extracted);
        }
        offset += 5;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmFnrest(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetParmFnYy(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetParmFn1To5()
    {
        return _ParmFn1To5;
    }
    
    // Standard Setter
    public void SetParmFn1To5(string value)
    {
        _ParmFn1To5 = value;
    }
    
    // Get<>AsString()
    public string GetParmFn1To5AsString()
    {
        return _ParmFn1To5.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetParmFn1To5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmFn1To5 = value;
    }
    
    // Standard Getter
    public string GetParmFnrest()
    {
        return _ParmFnrest;
    }
    
    // Standard Setter
    public void SetParmFnrest(string value)
    {
        _ParmFnrest = value;
    }
    
    // Get<>AsString()
    public string GetParmFnrestAsString()
    {
        return _ParmFnrest.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmFnrestAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmFnrest = value;
    }
    
    // Standard Getter
    public string GetParmFnYy()
    {
        return _ParmFnYy;
    }
    
    // Standard Setter
    public void SetParmFnYy(string value)
    {
        _ParmFnYy = value;
    }
    
    // Get<>AsString()
    public string GetParmFnYyAsString()
    {
        return _ParmFnYy.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetParmFnYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmFnYy = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetParmValuesR(string value)
{
    _ParmValuesR.SetParmValuesRAsString(value);
}
// Nested Class: ParmValuesR
public class ParmValuesR
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: ParmChar, is_external=, is_static_class=False, static_prefix=
    private string[] _ParmChar = new string[81];
    
    
    
    
public ParmValuesR() {}

public ParmValuesR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 81; i++)
    {
        string value = data.Substring(offset, 1);
        _ParmChar[i] = value.Trim();
        offset += 1;
    }
    
}

// Serialization methods
public string GetParmValuesRAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 81; i++)
    {
        result.Append(_ParmChar[i].PadRight(1));
    }
    
    return result.ToString();
}

public void SetParmValuesRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 81; i++)
    {
        if (offset + 1 > data.Length) break;
        string val = data.Substring(offset, 1);
        
        _ParmChar[i] = val.Trim();
        offset += 1;
    }
}

// Getter and Setter methods

// Array Accessors for ParmChar
public string GetParmCharAt(int index)
{
    return _ParmChar[index];
}

public void SetParmCharAt(int index, string value)
{
    _ParmChar[index] = value;
}

public string GetParmCharAsStringAt(int index)
{
    return _ParmChar[index].PadRight(1);
}

public void SetParmCharAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _ParmChar[index] = value;
}

// Flattened accessors (index 0)
public string GetParmChar()
{
    return _ParmChar != null && _ParmChar.Length > 0
    ? _ParmChar[0]
    : default(string);
}

public void SetParmChar(string value)
{
    if (_ParmChar == null || _ParmChar.Length == 0)
    _ParmChar = new string[1];
    _ParmChar[0] = value;
}

public string GetParmCharAsString()
{
    return _ParmChar != null && _ParmChar.Length > 0
    ? _ParmChar[0].ToString()
    : string.Empty;
}

public void SetParmCharAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_ParmChar == null || _ParmChar.Length == 0)
    _ParmChar = new string[1];
    
    _ParmChar[0] = value;
}




public static int GetSize()
{
    return _size;
}

}

}}
