using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WsWhenCompiled Data Structure

public class WsWhenCompiled
{
    private static int _size = 20;
    // [DEBUG] Class: WsWhenCompiled, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsCompTime, is_external=, is_static_class=False, static_prefix=
    private string _WsCompTime ="";
    
    
    
    
    // [DEBUG] Field: WsCompDate, is_external=, is_static_class=False, static_prefix=
    private string _WsCompDate ="";
    
    
    
    
    
    // Serialization methods
    public string GetWsWhenCompiledAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsCompTime.PadRight(8));
        result.Append(_WsCompDate.PadRight(12));
        
        return result.ToString();
    }
    
    public void SetWsWhenCompiledAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsCompTime(extracted);
        }
        offset += 8;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetWsCompDate(extracted);
        }
        offset += 12;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsWhenCompiledAsString();
    }
    // Set<>String Override function
    public void SetWsWhenCompiled(string value)
    {
        SetWsWhenCompiledAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsCompTime()
    {
        return _WsCompTime;
    }
    
    // Standard Setter
    public void SetWsCompTime(string value)
    {
        _WsCompTime = value;
    }
    
    // Get<>AsString()
    public string GetWsCompTimeAsString()
    {
        return _WsCompTime.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsCompTimeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCompTime = value;
    }
    
    // Standard Getter
    public string GetWsCompDate()
    {
        return _WsCompDate;
    }
    
    // Standard Setter
    public void SetWsCompDate(string value)
    {
        _WsCompDate = value;
    }
    
    // Get<>AsString()
    public string GetWsCompDateAsString()
    {
        return _WsCompDate.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetWsCompDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCompDate = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}