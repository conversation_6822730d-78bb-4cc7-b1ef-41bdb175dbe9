using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing Filler107 Data Structure

public class Filler107
{
    private static int _size = 36;
    // [DEBUG] Class: Filler107, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WmtMonth, is_external=, is_static_class=False, static_prefix=
    private string[] _WmtMonth = new string[12];
    
    
    
    
    
    // Serialization methods
    public string GetFiller107AsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 12; i++)
        {
            result.Append(_WmtMonth[i].PadRight(3));
        }
        
        return result.ToString();
    }
    
    public void SetFiller107AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 12; i++)
        {
            if (offset + 3 > data.Length) break;
            string val = data.Substring(offset, 3);
            
            _WmtMonth[i] = val.Trim();
            offset += 3;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetFiller107AsString();
    }
    // Set<>String Override function
    public void SetFiller107(string value)
    {
        SetFiller107AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for WmtMonth
    public string GetWmtMonthAt(int index)
    {
        return _WmtMonth[index];
    }
    
    public void SetWmtMonthAt(int index, string value)
    {
        _WmtMonth[index] = value;
    }
    
    public string GetWmtMonthAsStringAt(int index)
    {
        return _WmtMonth[index].PadRight(3);
    }
    
    public void SetWmtMonthAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WmtMonth[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWmtMonth()
    {
        return _WmtMonth != null && _WmtMonth.Length > 0
        ? _WmtMonth[0]
        : default(string);
    }
    
    public void SetWmtMonth(string value)
    {
        if (_WmtMonth == null || _WmtMonth.Length == 0)
        _WmtMonth = new string[1];
        _WmtMonth[0] = value;
    }
    
    public string GetWmtMonthAsString()
    {
        return _WmtMonth != null && _WmtMonth.Length > 0
        ? _WmtMonth[0].ToString()
        : string.Empty;
    }
    
    public void SetWmtMonthAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WmtMonth == null || _WmtMonth.Length == 0)
        _WmtMonth = new string[1];
        
        _WmtMonth[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
