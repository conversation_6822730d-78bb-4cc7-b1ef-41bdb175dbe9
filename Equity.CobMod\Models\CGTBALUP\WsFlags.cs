using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing WsFlags Data Structure

public class WsFlags
{
    private static int _size = 8;
    // [DEBUG] Class: WsFlags, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsProcessFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsProcessFlag ="";
    
    
    // 88-level condition checks for WsProcessFlag
    public bool IsMainProcess()
    {
        if (this._WsProcessFlag == "'M'") return true;
        return false;
    }
    public bool IsEndProcess()
    {
        if (this._WsProcessFlag == "'E'") return true;
        return false;
    }
    public bool IsQuitProcess()
    {
        if (this._WsProcessFlag == "'Q'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsMasterFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsMasterFlag ="";
    
    
    
    
    // [DEBUG] Field: WsUpdateFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsUpdateFlag ="";
    
    
    
    
    // [DEBUG] Field: WsReadD45Flag, is_external=, is_static_class=False, static_prefix=
    private string _WsReadD45Flag ="";
    
    
    // 88-level condition checks for WsReadD45Flag
    public bool IsReadD45()
    {
        if (this._WsReadD45Flag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsReadD13Flag, is_external=, is_static_class=False, static_prefix=
    private string _WsReadD13Flag ="";
    
    
    // 88-level condition checks for WsReadD13Flag
    public bool IsReadD13()
    {
        if (this._WsReadD13Flag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsEofD45Flag, is_external=, is_static_class=False, static_prefix=
    private string _WsEofD45Flag ="";
    
    
    // 88-level condition checks for WsEofD45Flag
    public bool IsEofD45()
    {
        if (this._WsEofD45Flag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsEofD13Flag, is_external=, is_static_class=False, static_prefix=
    private string _WsEofD13Flag ="";
    
    
    // 88-level condition checks for WsEofD13Flag
    public bool IsEofD13()
    {
        if (this._WsEofD13Flag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsOverRideFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsOverRideFlag ="";
    
    
    // 88-level condition checks for WsOverRideFlag
    public bool IsOverRide()
    {
        if (this._WsOverRideFlag == "'Y'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetWsFlagsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsProcessFlag.PadRight(1));
        result.Append(_WsMasterFlag.PadRight(1));
        result.Append(_WsUpdateFlag.PadRight(1));
        result.Append(_WsReadD45Flag.PadRight(1));
        result.Append(_WsReadD13Flag.PadRight(1));
        result.Append(_WsEofD45Flag.PadRight(1));
        result.Append(_WsEofD13Flag.PadRight(1));
        result.Append(_WsOverRideFlag.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsFlagsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsProcessFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsMasterFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsUpdateFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsReadD45Flag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsReadD13Flag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsEofD45Flag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsEofD13Flag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsOverRideFlag(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsFlagsAsString();
    }
    // Set<>String Override function
    public void SetWsFlags(string value)
    {
        SetWsFlagsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsProcessFlag()
    {
        return _WsProcessFlag;
    }
    
    // Standard Setter
    public void SetWsProcessFlag(string value)
    {
        _WsProcessFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsProcessFlagAsString()
    {
        return _WsProcessFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsProcessFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsProcessFlag = value;
    }
    
    // Standard Getter
    public string GetWsMasterFlag()
    {
        return _WsMasterFlag;
    }
    
    // Standard Setter
    public void SetWsMasterFlag(string value)
    {
        _WsMasterFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsMasterFlagAsString()
    {
        return _WsMasterFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsMasterFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsMasterFlag = value;
    }
    
    // Standard Getter
    public string GetWsUpdateFlag()
    {
        return _WsUpdateFlag;
    }
    
    // Standard Setter
    public void SetWsUpdateFlag(string value)
    {
        _WsUpdateFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsUpdateFlagAsString()
    {
        return _WsUpdateFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsUpdateFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsUpdateFlag = value;
    }
    
    // Standard Getter
    public string GetWsReadD45Flag()
    {
        return _WsReadD45Flag;
    }
    
    // Standard Setter
    public void SetWsReadD45Flag(string value)
    {
        _WsReadD45Flag = value;
    }
    
    // Get<>AsString()
    public string GetWsReadD45FlagAsString()
    {
        return _WsReadD45Flag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsReadD45FlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsReadD45Flag = value;
    }
    
    // Standard Getter
    public string GetWsReadD13Flag()
    {
        return _WsReadD13Flag;
    }
    
    // Standard Setter
    public void SetWsReadD13Flag(string value)
    {
        _WsReadD13Flag = value;
    }
    
    // Get<>AsString()
    public string GetWsReadD13FlagAsString()
    {
        return _WsReadD13Flag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsReadD13FlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsReadD13Flag = value;
    }
    
    // Standard Getter
    public string GetWsEofD45Flag()
    {
        return _WsEofD45Flag;
    }
    
    // Standard Setter
    public void SetWsEofD45Flag(string value)
    {
        _WsEofD45Flag = value;
    }
    
    // Get<>AsString()
    public string GetWsEofD45FlagAsString()
    {
        return _WsEofD45Flag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsEofD45FlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsEofD45Flag = value;
    }
    
    // Standard Getter
    public string GetWsEofD13Flag()
    {
        return _WsEofD13Flag;
    }
    
    // Standard Setter
    public void SetWsEofD13Flag(string value)
    {
        _WsEofD13Flag = value;
    }
    
    // Get<>AsString()
    public string GetWsEofD13FlagAsString()
    {
        return _WsEofD13Flag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsEofD13FlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsEofD13Flag = value;
    }
    
    // Standard Getter
    public string GetWsOverRideFlag()
    {
        return _WsOverRideFlag;
    }
    
    // Standard Setter
    public void SetWsOverRideFlag(string value)
    {
        _WsOverRideFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsOverRideFlagAsString()
    {
        return _WsOverRideFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsOverRideFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOverRideFlag = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}