using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing D000Detail Data Structure

public class D000Detail
{
    private static int _size = 154;
    // [DEBUG] Class: D000Detail, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D001Control, is_external=, is_static_class=False, static_prefix=
    private string _D001Control ="";
    
    
    
    
    // [DEBUG] Field: D002, is_external=, is_static_class=False, static_prefix=
    private string _D002 ="";
    
    
    
    
    // [DEBUG] Field: D003Text, is_external=, is_static_class=False, static_prefix=
    private D003Text _D003Text = new D003Text();
    
    
    
    
    // [DEBUG] Field: D004, is_external=, is_static_class=False, static_prefix=
    private string _D004 ="";
    
    
    
    
    // [DEBUG] Field: D005Date, is_external=, is_static_class=False, static_prefix=
    private string _D005Date ="";
    
    
    
    
    // [DEBUG] Field: D005BTrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _D005BTrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: D006, is_external=, is_static_class=False, static_prefix=
    private string _D006 ="";
    
    
    
    
    // [DEBUG] Field: D007MovementDate, is_external=, is_static_class=False, static_prefix=
    private string _D007MovementDate ="";
    
    
    
    
    // [DEBUG] Field: D008, is_external=, is_static_class=False, static_prefix=
    private string _D008 ="";
    
    
    
    
    // [DEBUG] Field: D009Description, is_external=, is_static_class=False, static_prefix=
    private string _D009Description ="";
    
    
    
    
    // [DEBUG] Field: D010, is_external=, is_static_class=False, static_prefix=
    private string _D010 ="";
    
    
    
    
    // [DEBUG] Field: D011Units, is_external=, is_static_class=False, static_prefix=
    private decimal _D011Units =0;
    
    
    
    
    // [DEBUG] Field: D011UnitsSterling, is_external=, is_static_class=False, static_prefix=
    private decimal _D011UnitsSterling =0;
    
    
    
    
    // [DEBUG] Field: D012, is_external=, is_static_class=False, static_prefix=
    private string _D012 ="";
    
    
    
    
    // [DEBUG] Field: D013Cost, is_external=, is_static_class=False, static_prefix=
    private decimal _D013Cost =0;
    
    
    
    
    // [DEBUG] Field: D014, is_external=, is_static_class=False, static_prefix=
    private string _D014 ="";
    
    
    
    
    // [DEBUG] Field: D015IndexDate, is_external=, is_static_class=False, static_prefix=
    private string _D015IndexDate ="";
    
    
    
    
    // [DEBUG] Field: D016, is_external=, is_static_class=False, static_prefix=
    private string _D016 ="";
    
    
    
    
    // [DEBUG] Field: D017, is_external=, is_static_class=False, static_prefix=
    private D017 _D017 = new D017();
    
    
    
    
    // [DEBUG] Field: D019Limit, is_external=, is_static_class=False, static_prefix=
    private string _D019Limit ="";
    
    
    
    
    // [DEBUG] Field: D020, is_external=, is_static_class=False, static_prefix=
    private D020 _D020 = new D020();
    
    
    
    
    // [DEBUG] Field: D021, is_external=, is_static_class=False, static_prefix=
    private string _D021 ="";
    
    
    
    
    // [DEBUG] Field: D022, is_external=, is_static_class=False, static_prefix=
    private D022 _D022 = new D022();
    
    
    
    
    // [DEBUG] Field: D023, is_external=, is_static_class=False, static_prefix=
    private string _D023 ="";
    
    
    
    
    // [DEBUG] Field: D024, is_external=, is_static_class=False, static_prefix=
    private D024 _D024 = new D024();
    
    
    
    
    // [DEBUG] Field: D025, is_external=, is_static_class=False, static_prefix=
    private string _D025 ="";
    
    
    
    
    // [DEBUG] Field: D026, is_external=, is_static_class=False, static_prefix=
    private D026 _D026 = new D026();
    
    
    
    
    // [DEBUG] Field: D027, is_external=, is_static_class=False, static_prefix=
    private string _D027 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD000DetailAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D001Control.PadRight(1));
        result.Append(_D002.PadRight(1));
        result.Append(_D003Text.GetD003TextAsString());
        result.Append(_D004.PadRight(1));
        result.Append(_D005Date.PadRight(7));
        result.Append(_D005BTrancheFlag.PadRight(1));
        result.Append(_D006.PadRight(1));
        result.Append(_D007MovementDate.PadRight(7));
        result.Append(_D008.PadRight(1));
        result.Append(_D009Description.PadRight(16));
        result.Append(_D010.PadRight(1));
        result.Append(_D011Units.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D011UnitsSterling.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D012.PadRight(1));
        result.Append(_D013Cost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D014.PadRight(1));
        result.Append(_D015IndexDate.PadRight(5));
        result.Append(_D016.PadRight(1));
        result.Append(_D017.GetD017AsString());
        result.Append(_D019Limit.PadRight(1));
        result.Append(_D020.GetD020AsString());
        result.Append(_D021.PadRight(0));
        result.Append(_D022.GetD022AsString());
        result.Append(_D023.PadRight(0));
        result.Append(_D024.GetD024AsString());
        result.Append(_D025.PadRight(0));
        result.Append(_D026.GetD026AsString());
        result.Append(_D027.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD000DetailAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD001Control(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD002(extracted);
        }
        offset += 1;
        if (offset + 16 <= data.Length)
        {
            _D003Text.SetD003TextAsString(data.Substring(offset, 16));
        }
        else
        {
            _D003Text.SetD003TextAsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD004(extracted);
        }
        offset += 1;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD005Date(extracted);
        }
        offset += 7;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD005BTrancheFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD006(extracted);
        }
        offset += 1;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD007MovementDate(extracted);
        }
        offset += 7;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD008(extracted);
        }
        offset += 1;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetD009Description(extracted);
        }
        offset += 16;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD010(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD011Units(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD011UnitsSterling(parsedDec);
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD012(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD013Cost(parsedDec);
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD014(extracted);
        }
        offset += 1;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetD015IndexDate(extracted);
        }
        offset += 5;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD016(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            _D017.SetD017AsString(data.Substring(offset, 0));
        }
        else
        {
            _D017.SetD017AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD019Limit(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            _D020.SetD020AsString(data.Substring(offset, 13));
        }
        else
        {
            _D020.SetD020AsString(data.Substring(offset));
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD021(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            _D022.SetD022AsString(data.Substring(offset, 13));
        }
        else
        {
            _D022.SetD022AsString(data.Substring(offset));
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD023(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            _D024.SetD024AsString(data.Substring(offset, 13));
        }
        else
        {
            _D024.SetD024AsString(data.Substring(offset));
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD025(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            _D026.SetD026AsString(data.Substring(offset, 13));
        }
        else
        {
            _D026.SetD026AsString(data.Substring(offset));
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD027(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD000DetailAsString();
    }
    // Set<>String Override function
    public void SetD000Detail(string value)
    {
        SetD000DetailAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD001Control()
    {
        return _D001Control;
    }
    
    // Standard Setter
    public void SetD001Control(string value)
    {
        _D001Control = value;
    }
    
    // Get<>AsString()
    public string GetD001ControlAsString()
    {
        return _D001Control.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD001ControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D001Control = value;
    }
    
    // Standard Getter
    public string GetD002()
    {
        return _D002;
    }
    
    // Standard Setter
    public void SetD002(string value)
    {
        _D002 = value;
    }
    
    // Get<>AsString()
    public string GetD002AsString()
    {
        return _D002.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD002AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D002 = value;
    }
    
    // Standard Getter
    public D003Text GetD003Text()
    {
        return _D003Text;
    }
    
    // Standard Setter
    public void SetD003Text(D003Text value)
    {
        _D003Text = value;
    }
    
    // Get<>AsString()
    public string GetD003TextAsString()
    {
        return _D003Text != null ? _D003Text.GetD003TextAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD003TextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D003Text == null)
        {
            _D003Text = new D003Text();
        }
        _D003Text.SetD003TextAsString(value);
    }
    
    // Standard Getter
    public string GetD004()
    {
        return _D004;
    }
    
    // Standard Setter
    public void SetD004(string value)
    {
        _D004 = value;
    }
    
    // Get<>AsString()
    public string GetD004AsString()
    {
        return _D004.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD004AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D004 = value;
    }
    
    // Standard Getter
    public string GetD005Date()
    {
        return _D005Date;
    }
    
    // Standard Setter
    public void SetD005Date(string value)
    {
        _D005Date = value;
    }
    
    // Get<>AsString()
    public string GetD005DateAsString()
    {
        return _D005Date.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD005DateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D005Date = value;
    }
    
    // Standard Getter
    public string GetD005BTrancheFlag()
    {
        return _D005BTrancheFlag;
    }
    
    // Standard Setter
    public void SetD005BTrancheFlag(string value)
    {
        _D005BTrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD005BTrancheFlagAsString()
    {
        return _D005BTrancheFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD005BTrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D005BTrancheFlag = value;
    }
    
    // Standard Getter
    public string GetD006()
    {
        return _D006;
    }
    
    // Standard Setter
    public void SetD006(string value)
    {
        _D006 = value;
    }
    
    // Get<>AsString()
    public string GetD006AsString()
    {
        return _D006.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD006AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D006 = value;
    }
    
    // Standard Getter
    public string GetD007MovementDate()
    {
        return _D007MovementDate;
    }
    
    // Standard Setter
    public void SetD007MovementDate(string value)
    {
        _D007MovementDate = value;
    }
    
    // Get<>AsString()
    public string GetD007MovementDateAsString()
    {
        return _D007MovementDate.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD007MovementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D007MovementDate = value;
    }
    
    // Standard Getter
    public string GetD008()
    {
        return _D008;
    }
    
    // Standard Setter
    public void SetD008(string value)
    {
        _D008 = value;
    }
    
    // Get<>AsString()
    public string GetD008AsString()
    {
        return _D008.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD008AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D008 = value;
    }
    
    // Standard Getter
    public string GetD009Description()
    {
        return _D009Description;
    }
    
    // Standard Setter
    public void SetD009Description(string value)
    {
        _D009Description = value;
    }
    
    // Get<>AsString()
    public string GetD009DescriptionAsString()
    {
        return _D009Description.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetD009DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D009Description = value;
    }
    
    // Standard Getter
    public string GetD010()
    {
        return _D010;
    }
    
    // Standard Setter
    public void SetD010(string value)
    {
        _D010 = value;
    }
    
    // Get<>AsString()
    public string GetD010AsString()
    {
        return _D010.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD010AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D010 = value;
    }
    
    // Standard Getter
    public decimal GetD011Units()
    {
        return _D011Units;
    }
    
    // Standard Setter
    public void SetD011Units(decimal value)
    {
        _D011Units = value;
    }
    
    // Get<>AsString()
    public string GetD011UnitsAsString()
    {
        return _D011Units.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD011UnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D011Units = parsed;
    }
    
    // Standard Getter
    public decimal GetD011UnitsSterling()
    {
        return _D011UnitsSterling;
    }
    
    // Standard Setter
    public void SetD011UnitsSterling(decimal value)
    {
        _D011UnitsSterling = value;
    }
    
    // Get<>AsString()
    public string GetD011UnitsSterlingAsString()
    {
        return _D011UnitsSterling.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD011UnitsSterlingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D011UnitsSterling = parsed;
    }
    
    // Standard Getter
    public string GetD012()
    {
        return _D012;
    }
    
    // Standard Setter
    public void SetD012(string value)
    {
        _D012 = value;
    }
    
    // Get<>AsString()
    public string GetD012AsString()
    {
        return _D012.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD012AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D012 = value;
    }
    
    // Standard Getter
    public decimal GetD013Cost()
    {
        return _D013Cost;
    }
    
    // Standard Setter
    public void SetD013Cost(decimal value)
    {
        _D013Cost = value;
    }
    
    // Get<>AsString()
    public string GetD013CostAsString()
    {
        return _D013Cost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD013CostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D013Cost = parsed;
    }
    
    // Standard Getter
    public string GetD014()
    {
        return _D014;
    }
    
    // Standard Setter
    public void SetD014(string value)
    {
        _D014 = value;
    }
    
    // Get<>AsString()
    public string GetD014AsString()
    {
        return _D014.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD014AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D014 = value;
    }
    
    // Standard Getter
    public string GetD015IndexDate()
    {
        return _D015IndexDate;
    }
    
    // Standard Setter
    public void SetD015IndexDate(string value)
    {
        _D015IndexDate = value;
    }
    
    // Get<>AsString()
    public string GetD015IndexDateAsString()
    {
        return _D015IndexDate.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetD015IndexDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D015IndexDate = value;
    }
    
    // Standard Getter
    public string GetD016()
    {
        return _D016;
    }
    
    // Standard Setter
    public void SetD016(string value)
    {
        _D016 = value;
    }
    
    // Get<>AsString()
    public string GetD016AsString()
    {
        return _D016.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD016AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D016 = value;
    }
    
    // Standard Getter
    public D017 GetD017()
    {
        return _D017;
    }
    
    // Standard Setter
    public void SetD017(D017 value)
    {
        _D017 = value;
    }
    
    // Get<>AsString()
    public string GetD017AsString()
    {
        return _D017 != null ? _D017.GetD017AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD017AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D017 == null)
        {
            _D017 = new D017();
        }
        _D017.SetD017AsString(value);
    }
    
    // Standard Getter
    public string GetD019Limit()
    {
        return _D019Limit;
    }
    
    // Standard Setter
    public void SetD019Limit(string value)
    {
        _D019Limit = value;
    }
    
    // Get<>AsString()
    public string GetD019LimitAsString()
    {
        return _D019Limit.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD019LimitAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D019Limit = value;
    }
    
    // Standard Getter
    public D020 GetD020()
    {
        return _D020;
    }
    
    // Standard Setter
    public void SetD020(D020 value)
    {
        _D020 = value;
    }
    
    // Get<>AsString()
    public string GetD020AsString()
    {
        return _D020 != null ? _D020.GetD020AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD020AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D020 == null)
        {
            _D020 = new D020();
        }
        _D020.SetD020AsString(value);
    }
    
    // Standard Getter
    public string GetD021()
    {
        return _D021;
    }
    
    // Standard Setter
    public void SetD021(string value)
    {
        _D021 = value;
    }
    
    // Get<>AsString()
    public string GetD021AsString()
    {
        return _D021.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD021AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D021 = value;
    }
    
    // Standard Getter
    public D022 GetD022()
    {
        return _D022;
    }
    
    // Standard Setter
    public void SetD022(D022 value)
    {
        _D022 = value;
    }
    
    // Get<>AsString()
    public string GetD022AsString()
    {
        return _D022 != null ? _D022.GetD022AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD022AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D022 == null)
        {
            _D022 = new D022();
        }
        _D022.SetD022AsString(value);
    }
    
    // Standard Getter
    public string GetD023()
    {
        return _D023;
    }
    
    // Standard Setter
    public void SetD023(string value)
    {
        _D023 = value;
    }
    
    // Get<>AsString()
    public string GetD023AsString()
    {
        return _D023.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD023AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D023 = value;
    }
    
    // Standard Getter
    public D024 GetD024()
    {
        return _D024;
    }
    
    // Standard Setter
    public void SetD024(D024 value)
    {
        _D024 = value;
    }
    
    // Get<>AsString()
    public string GetD024AsString()
    {
        return _D024 != null ? _D024.GetD024AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD024AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D024 == null)
        {
            _D024 = new D024();
        }
        _D024.SetD024AsString(value);
    }
    
    // Standard Getter
    public string GetD025()
    {
        return _D025;
    }
    
    // Standard Setter
    public void SetD025(string value)
    {
        _D025 = value;
    }
    
    // Get<>AsString()
    public string GetD025AsString()
    {
        return _D025.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD025AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D025 = value;
    }
    
    // Standard Getter
    public D026 GetD026()
    {
        return _D026;
    }
    
    // Standard Setter
    public void SetD026(D026 value)
    {
        _D026 = value;
    }
    
    // Get<>AsString()
    public string GetD026AsString()
    {
        return _D026 != null ? _D026.GetD026AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD026AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D026 == null)
        {
            _D026 = new D026();
        }
        _D026.SetD026AsString(value);
    }
    
    // Standard Getter
    public string GetD027()
    {
        return _D027;
    }
    
    // Standard Setter
    public void SetD027(string value)
    {
        _D027 = value;
    }
    
    // Get<>AsString()
    public string GetD027AsString()
    {
        return _D027.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD027AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D027 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD003Text(string value)
    {
        _D003Text.SetD003TextAsString(value);
    }
    // Nested Class: D003Text
    public class D003Text
    {
        private static int _size = 16;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler84, is_external=, is_static_class=False, static_prefix=
        private string _Filler84 ="";
        
        
        
        
        // [DEBUG] Field: D003ReitDesc, is_external=, is_static_class=False, static_prefix=
        private string _D003ReitDesc ="";
        
        
        
        
        // [DEBUG] Field: D003SecurityType, is_external=, is_static_class=False, static_prefix=
        private string _D003SecurityType ="";
        
        
        
        
    public D003Text() {}
    
    public D003Text(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller84(data.Substring(offset, 15).Trim());
        offset += 15;
        SetD003ReitDesc(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD003SecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetD003TextAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler84.PadRight(15));
        result.Append(_D003ReitDesc.PadRight(0));
        result.Append(_D003SecurityType.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD003TextAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetFiller84(extracted);
        }
        offset += 15;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD003ReitDesc(extracted);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD003SecurityType(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller84()
    {
        return _Filler84;
    }
    
    // Standard Setter
    public void SetFiller84(string value)
    {
        _Filler84 = value;
    }
    
    // Get<>AsString()
    public string GetFiller84AsString()
    {
        return _Filler84.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetFiller84AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler84 = value;
    }
    
    // Standard Getter
    public string GetD003ReitDesc()
    {
        return _D003ReitDesc;
    }
    
    // Standard Setter
    public void SetD003ReitDesc(string value)
    {
        _D003ReitDesc = value;
    }
    
    // Get<>AsString()
    public string GetD003ReitDescAsString()
    {
        return _D003ReitDesc.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD003ReitDescAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D003ReitDesc = value;
    }
    
    // Standard Getter
    public string GetD003SecurityType()
    {
        return _D003SecurityType;
    }
    
    // Standard Setter
    public void SetD003SecurityType(string value)
    {
        _D003SecurityType = value;
    }
    
    // Get<>AsString()
    public string GetD003SecurityTypeAsString()
    {
        return _D003SecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD003SecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D003SecurityType = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD017(string value)
{
    _D017.SetD017AsString(value);
}
// Nested Class: D017
public class D017
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D017IndexFactor, is_external=, is_static_class=False, static_prefix=
    private decimal _D017IndexFactor =0;
    
    
    
    
public D017() {}

public D017(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD017IndexFactor(PackedDecimalConverter.ToDecimal(data.Substring(offset, 0)));
    offset += 0;
    
}

// Serialization methods
public string GetD017AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D017IndexFactor.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD017AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD017IndexFactor(parsedDec);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD017IndexFactor()
{
    return _D017IndexFactor;
}

// Standard Setter
public void SetD017IndexFactor(decimal value)
{
    _D017IndexFactor = value;
}

// Get<>AsString()
public string GetD017IndexFactorAsString()
{
    return _D017IndexFactor.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD017IndexFactorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D017IndexFactor = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD020(string value)
{
    _D020.SetD020AsString(value);
}
// Nested Class: D020
public class D020
{
    private static int _size = 13;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D020IndexedCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D020IndexedCost =0;
    
    
    
    
public D020() {}

public D020(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD020IndexedCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    
}

// Serialization methods
public string GetD020AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D020IndexedCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD020AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD020IndexedCost(parsedDec);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD020IndexedCost()
{
    return _D020IndexedCost;
}

// Standard Setter
public void SetD020IndexedCost(decimal value)
{
    _D020IndexedCost = value;
}

// Get<>AsString()
public string GetD020IndexedCostAsString()
{
    return _D020IndexedCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD020IndexedCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D020IndexedCost = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD022(string value)
{
    _D022.SetD022AsString(value);
}
// Nested Class: D022
public class D022
{
    private static int _size = 13;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D022Proceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D022Proceeds =0;
    
    
    
    
public D022() {}

public D022(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD022Proceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    
}

// Serialization methods
public string GetD022AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D022Proceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD022AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD022Proceeds(parsedDec);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD022Proceeds()
{
    return _D022Proceeds;
}

// Standard Setter
public void SetD022Proceeds(decimal value)
{
    _D022Proceeds = value;
}

// Get<>AsString()
public string GetD022ProceedsAsString()
{
    return _D022Proceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD022ProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D022Proceeds = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD024(string value)
{
    _D024.SetD024AsString(value);
}
// Nested Class: D024
public class D024
{
    private static int _size = 13;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D024Cgt, is_external=, is_static_class=False, static_prefix=
    private decimal _D024Cgt =0;
    
    
    
    
public D024() {}

public D024(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD024Cgt(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    
}

// Serialization methods
public string GetD024AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D024Cgt.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD024AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD024Cgt(parsedDec);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD024Cgt()
{
    return _D024Cgt;
}

// Standard Setter
public void SetD024Cgt(decimal value)
{
    _D024Cgt = value;
}

// Get<>AsString()
public string GetD024CgtAsString()
{
    return _D024Cgt.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD024CgtAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D024Cgt = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD026(string value)
{
    _D026.SetD026AsString(value);
}
// Nested Class: D026
public class D026
{
    private static int _size = 13;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D026Profit, is_external=, is_static_class=False, static_prefix=
    private decimal _D026Profit =0;
    
    
    
    
public D026() {}

public D026(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD026Profit(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    
}

// Serialization methods
public string GetD026AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D026Profit.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD026AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD026Profit(parsedDec);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD026Profit()
{
    return _D026Profit;
}

// Standard Setter
public void SetD026Profit(decimal value)
{
    _D026Profit = value;
}

// Get<>AsString()
public string GetD026ProfitAsString()
{
    return _D026Profit.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD026ProfitAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D026Profit = parsed;
}



public static int GetSize()
{
    return _size;
}

}

}}
