using System;
using System.IO;
using System.Text;
using EquityProject.CgtdelPGM;
using EquityProject.Cgttemp2DTO;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;
namespace EquityProject.Cgttemp2PGM
{
    // Cgttemp2 Class Definition

    //Cgttemp2 Class Constructor
    public class Cgttemp2
    {
        // Declare Cgttemp2 Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private EquityGlobalParms equityGlobalParms;

        // File handling variables
        private FileStream _temporaryFileStream;
        private StreamWriter _temporaryFileWriter;
        private StreamReader _temporaryFileReader;

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            Mainline(fvar, gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// Mainline method implementing the logic from the COBOL paragraph MAINLINE.
        /// This method evaluates the CGTTEMP-ACTION and performs corresponding file operations.
        /// </summary>
        /// <param name="fvar">Instance of Fvar containing relevant variables.</param>
        /// <param name="gvar">Instance of Gvar containing relevant variables.</param>
        /// <param name="ivar">Instance of Ivar containing relevant variables.</param>
        /// <remarks>
        /// This method converts the EVALUATE statement in COBOL to a switch-case statement
        /// in C#. It uses modern C# conventions and best practices, maintaining the original logic flow.
        /// </remarks>
        public void Mainline(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Evaluate the CGTTEMP-ACTION
            string cgttempAction = ivar.GetCgttempLinkage().GetCgttempAction();
            switch (cgttempAction)
            {
                case Ivar.CGTTEMP_OPEN_OUTPUT:
                    // Move values to corresponding variables
                    gvar.GetTempFile().SetReportUserNo(ivar.GetCgttempLinkage().GetCgttempUserNo().ToString());
                    gvar.GetTempFile().SetReportGenNo(ivar.GetCgttempLinkage().GetCgttempReportNumber().ToString());
                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
                    gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetTempFileAsString());

                    // Perform X-CALL-EQTPATH
                    XCallEqtpath(fvar, gvar, ivar);

                    // Move file name to TEMP-FILE-NAME and open the file
                    gvar.SetTempFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                    // Open output file
                    try
                    {
                        _temporaryFileStream = new FileStream(gvar.GetTempFileName(), FileMode.Create, FileAccess.Write);
                        _temporaryFileWriter = new StreamWriter(_temporaryFileStream);
                        gvar.SetWFileReturnCode("00"); // Success
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("35"); // File not found or cannot create
                    }
                    break;

                case Ivar.CGTTEMP_WRITE:
                    // Write the record to the temporary file
                    try
                    {
                        if (_temporaryFileWriter != null)
                        {
                            _temporaryFileWriter.WriteLine(ivar.GetCgttempLinkage().GetCgttempRecordAsString());
                            _temporaryFileWriter.Flush();
                            gvar.SetWFileReturnCode("00"); // Success
                        }
                        else
                        {
                            gvar.SetWFileReturnCode("42"); // File not open
                        }
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("30"); // I/O error
                    }
                    break;

                case Ivar.CGTTEMP_CLOSE:
                    // Close the temporary file
                    try
                    {
                        _temporaryFileWriter?.Close();
                        _temporaryFileReader?.Close();
                        _temporaryFileStream?.Close();
                        _temporaryFileWriter = null;
                        _temporaryFileReader = null;
                        _temporaryFileStream = null;
                        gvar.SetWFileReturnCode("00"); // Success
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("30"); // I/O error
                    }
                    break;

                case Ivar.CGTTEMP_OPEN_IO:
                    // Move values to corresponding variables
                    gvar.GetTempFile().SetReportUserNo(ivar.GetCgttempLinkage().GetCgttempUserNo().ToString());
                    gvar.GetTempFile().SetReportGenNo(ivar.GetCgttempLinkage().GetCgttempReportNumber().ToString());
                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
                    gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetTempFileAsString());

                    // Perform X-CALL-EQTPATH
                    XCallEqtpath(fvar, gvar, ivar);

                    // Move file name to TEMP-FILE-NAME and open the file
                    gvar.SetTempFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                    // Open file for input/output
                    try
                    {
                        if (File.Exists(gvar.GetTempFileName()))
                        {
                            _temporaryFileStream = new FileStream(gvar.GetTempFileName(), FileMode.Open, FileAccess.ReadWrite);
                            _temporaryFileReader = new StreamReader(_temporaryFileStream);
                            _temporaryFileWriter = new StreamWriter(_temporaryFileStream);
                            gvar.SetWFileReturnCode("00"); // Success
                        }
                        else
                        {
                            gvar.SetWFileReturnCode("35"); // File not found
                        }
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("35"); // File not found or cannot open
                    }
                    break;

                case Ivar.CGTTEMP_READ:
                    // Read the next record from the temporary file
                    try
                    {
                        if (_temporaryFileReader != null)
                        {
                            string line = _temporaryFileReader.ReadLine();
                            if (line != null)
                            {
                                ivar.GetCgttempLinkage().SetCgttempRecordAsString(line);
                                gvar.SetWFileReturnCode("00"); // Success
                            }
                            else
                            {
                                gvar.SetWFileReturnCode("10"); // End of file
                            }
                        }
                        else
                        {
                            gvar.SetWFileReturnCode("42"); // File not open
                        }
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("30"); // I/O error
                    }
                    break;

                case Ivar.CGTTEMP_DELETE_FILE:
                    // Move values to corresponding variables for file deletion
                    gvar.GetTempFile().SetReportUserNo(ivar.GetCgttempLinkage().GetCgttempUserNo().ToString());
                    gvar.GetTempFile().SetReportGenNo(ivar.GetCgttempLinkage().GetCgttempReportNumber().ToString());

                    // Set the path and file name for deletion
                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
                    gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetTempFileAsString());

                    // Perform X-CALL-EQTPATH
                    XCallEqtpath(fvar, gvar, ivar);

                    // Move file name to L-DEL-FILE and perform deletion
                    gvar.GetCgtdelLinkage().SetLDelFile(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());
                    X1CallCgtdel(fvar, gvar, ivar);

                    // Set path for index file deletion and perform deletion
                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);

                    // Change file extension to .IDX (equivalent to MOVE '.IDX' TO TEMP-FILE(9:4))
                    string tempFileWithIdx = ModifyTempFileExtension(gvar.GetTempFileAsString(), ".IDX");
                    gvar.GetEqtpathLinkage().SetEqtpathFileName(tempFileWithIdx);
                    XCallEqtpath(fvar, gvar, ivar);

                    // Move file name to L-DEL-FILE and perform deletion
                    gvar.GetCgtdelLinkage().SetLDelFile(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());
                    X1CallCgtdel(fvar, gvar, ivar);

                    // Set path for data file deletion and perform deletion
                    // Change file extension back to .DAT (equivalent to MOVE '.DAT' TO TEMP-FILE(9:4))
                    string tempFileWithDat = ModifyTempFileExtension(gvar.GetTempFileAsString(), ".DAT");
                    gvar.SetWFileReturnCode(gvar.GetCgtdelLinkage().GetLDelResultAsString());
                    break;

                case Ivar.CGTTEMP_REWRITE:
                    // Rewrite the record to the temporary file
                    try
                    {
                        if (_temporaryFileWriter != null)
                        {
                            // For simplicity, we'll treat rewrite as write (in a real implementation,
                            // this would require more complex file positioning logic)
                            _temporaryFileWriter.WriteLine(ivar.GetCgttempLinkage().GetCgttempRecordAsString());
                            _temporaryFileWriter.Flush();
                            gvar.SetWFileReturnCode("00"); // Success
                        }
                        else
                        {
                            gvar.SetWFileReturnCode("42"); // File not open
                        }
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("30"); // I/O error
                    }
                    break;

                case Ivar.CGTTEMP_START_NOT_LESS_THAN:
                    // Move record and start the file reading from the specified key
                    fvar.SetTempRecordAsString(ivar.GetCgttempLinkage().GetCgttempRecordAsString());

                    // For indexed file START operation, we'll simulate by positioning at beginning
                    // In a real implementation, this would require indexed file handling
                    try
                    {
                        if (_temporaryFileReader != null)
                        {
                            _temporaryFileReader.BaseStream.Seek(0, SeekOrigin.Begin);
                            _temporaryFileReader.DiscardBufferedData();
                            gvar.SetWFileReturnCode("00"); // Success
                        }
                        else
                        {
                            gvar.SetWFileReturnCode("42"); // File not open
                        }
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("30"); // I/O error
                    }
                    break;

                case Ivar.CGTTEMP_READ_ON_KEY:
                    // Move record and read the file on the specified key
                    fvar.SetTempRecordAsString(ivar.GetCgttempLinkage().GetCgttempRecordAsString());

                    // For indexed file READ on key operation, we'll simulate by reading next record
                    // In a real implementation, this would require indexed file handling with key lookup
                    try
                    {
                        if (_temporaryFileReader != null)
                        {
                            string line = _temporaryFileReader.ReadLine();
                            if (line != null)
                            {
                                ivar.GetCgttempLinkage().SetCgttempRecordAsString(line);
                                gvar.SetWFileReturnCode("00"); // Success
                            }
                            else
                            {
                                gvar.SetWFileReturnCode("23"); // Record not found
                            }
                        }
                        else
                        {
                            gvar.SetWFileReturnCode("42"); // File not open
                        }
                    }
                    catch (Exception)
                    {
                        gvar.SetWFileReturnCode("30"); // I/O error
                    }
                    break;

                default:
                    // Handle unknown action case
                    break;
            }

            // Set the return code to CGTTEMP-STATUS
            ivar.GetCgttempLinkage().SetCgttempStatus(gvar.GetWFileReturnCode());
        }
        /// <summary>
        /// Calls the external program 'CGTDEL' using the CGTDEL-LINKAGE parameter.
        /// <para>
        /// COBOL Paragraph Name: X1CALLCGTDLS
        /// </para>
        /// </summary>
        /// <param name="fvar">Global variable collection fVar</param>
        /// <param name="gvar">Global variable collection gVar</param>
        /// <param name="ivar">Global variable collection iVar</param>
        /// <remarks>
        ///  1. Create a new instance of the external program 'CGTDEL'.
        ///  2. Call the Run method of the CGTDEL program with the CGTDEL-LINKAGE parameter.
        /// </remarks>
        public void X1CallCgtdel(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program 'CGTDEL'
            Cgtdel cgtdel = new Cgtdel();
            cgtdel.GetIvar().SetCgtdelLinkageAsString(gvar.GetCgtdelLinkageAsString());
            // Call the Run method with the CGTDEL-LINKAGE parameter, passing it strictly as specified in the USING clause
            cgtdel.Run(cgtdel.GetGvar(), cgtdel.GetIvar());
        }
        ///
        /*<summary>
        COBOL to CSharp conversion of paragraph "xCallEqtpath"
        </summary>

        <param name = "fvar" > Fixed variable parameters</param>
        <param name = "gvar" > Global variable parameters</param>
        <param name = "ivar" > Input variable parameters</param>

        <remarks>
            This method converts the COBOL CALL statement to C#.
            - COBOL: CALL 'EQTPATH' USING EQTPATH-LINKAGE.
            - C#: Creates an instance of Eqtpath and calls its Run method with the EQTPATH-LINKAGE parameter.
        </remarks>*/

        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the Eqtpath class
            Eqtpath eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            // Call the Run method with the EQTPATH-LINKAGE parameter
            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(), equityGlobalParms);
        }

        /// <summary>
        /// Helper method to modify the file extension in TEMP-FILE structure
        /// Equivalent to COBOL: MOVE '.IDX' TO TEMP-FILE(9:4) or MOVE '.DAT' TO TEMP-FILE(9:4)
        /// </summary>
        /// <param name="tempFileString">The current temp file string</param>
        /// <param name="newExtension">The new extension to set (.IDX or .DAT)</param>
        /// <returns>Modified temp file string</returns>
        private string ModifyTempFileExtension(string tempFileString, string newExtension)
        {
            // The TEMP-FILE structure is: $9999T2G.DAT (12 characters)
            // Position 9-12 (0-based: 8-11) contains the extension ".DAT"
            if (string.IsNullOrEmpty(tempFileString) || tempFileString.Length < 12)
            {
                return tempFileString;
            }

            // Replace the last 4 characters with the new extension
            return tempFileString.Substring(0, 8) + newExtension;
        }

    }
}
