using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing TimeStamp Data Structure

public class TimeStamp
{
    private static int _size = 8;
    // [DEBUG] Class: TimeStamp, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsHh, is_external=, is_static_class=False, static_prefix=
    private int _WsHh =0;
    
    
    
    
    // [DEBUG] Field: WsNn, is_external=, is_static_class=False, static_prefix=
    private int _WsNn =0;
    
    
    
    
    // [DEBUG] Field: Filler101, is_external=, is_static_class=False, static_prefix=
    private int _Filler101 =0;
    
    
    
    
    // [DEBUG] Field: Filler102, is_external=, is_static_class=False, static_prefix=
    private int _Filler102 =0;
    
    
    
    
    
    // Serialization methods
    public string GetTimeStampAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsHh.ToString().PadLeft(2, '0'));
        result.Append(_WsNn.ToString().PadLeft(2, '0'));
        result.Append(_Filler101.ToString().PadLeft(2, '0'));
        result.Append(_Filler102.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetTimeStampAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsHh(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsNn(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetFiller101(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetFiller102(parsedInt);
        }
        offset += 2;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetTimeStampAsString();
    }
    // Set<>String Override function
    public void SetTimeStamp(string value)
    {
        SetTimeStampAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWsHh()
    {
        return _WsHh;
    }
    
    // Standard Setter
    public void SetWsHh(int value)
    {
        _WsHh = value;
    }
    
    // Get<>AsString()
    public string GetWsHhAsString()
    {
        return _WsHh.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsHhAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsHh = parsed;
    }
    
    // Standard Getter
    public int GetWsNn()
    {
        return _WsNn;
    }
    
    // Standard Setter
    public void SetWsNn(int value)
    {
        _WsNn = value;
    }
    
    // Get<>AsString()
    public string GetWsNnAsString()
    {
        return _WsNn.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWsNnAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsNn = parsed;
    }
    
    // Standard Getter
    public int GetFiller101()
    {
        return _Filler101;
    }
    
    // Standard Setter
    public void SetFiller101(int value)
    {
        _Filler101 = value;
    }
    
    // Get<>AsString()
    public string GetFiller101AsString()
    {
        return _Filler101.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetFiller101AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Filler101 = parsed;
    }
    
    // Standard Getter
    public int GetFiller102()
    {
        return _Filler102;
    }
    
    // Standard Setter
    public void SetFiller102(int value)
    {
        _Filler102 = value;
    }
    
    // Get<>AsString()
    public string GetFiller102AsString()
    {
        return _Filler102.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetFiller102AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Filler102 = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}