using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing NewFields Data Structure

public class NewFields
{
    private static int _size = 160;
    // [DEBUG] Class: NewFields, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsXyMsgNo, is_external=, is_static_class=False, static_prefix=
    private int _WsXyMsgNo =1;
    
    
    
    
    // [DEBUG] Field: WsDispXyMsg, is_external=, is_static_class=False, static_prefix=
    private WsDispXyMsg _WsDispXyMsg = new WsDispXyMsg();
    
    
    
    
    
    // Serialization methods
    public string GetNewFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsXyMsgNo.ToString().PadLeft(5, '0'));
        result.Append(_WsDispXyMsg.GetWsDispXyMsgAsString());
        
        return result.ToString();
    }
    
    public void SetNewFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsXyMsgNo(parsedInt);
        }
        offset += 5;
        if (offset + 155 <= data.Length)
        {
            _WsDispXyMsg.SetWsDispXyMsgAsString(data.Substring(offset, 155));
        }
        else
        {
            _WsDispXyMsg.SetWsDispXyMsgAsString(data.Substring(offset));
        }
        offset += 155;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetNewFieldsAsString();
    }
    // Set<>String Override function
    public void SetNewFields(string value)
    {
        SetNewFieldsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWsXyMsgNo()
    {
        return _WsXyMsgNo;
    }
    
    // Standard Setter
    public void SetWsXyMsgNo(int value)
    {
        _WsXyMsgNo = value;
    }
    
    // Get<>AsString()
    public string GetWsXyMsgNoAsString()
    {
        return _WsXyMsgNo.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWsXyMsgNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsXyMsgNo = parsed;
    }
    
    // Standard Getter
    public WsDispXyMsg GetWsDispXyMsg()
    {
        return _WsDispXyMsg;
    }
    
    // Standard Setter
    public void SetWsDispXyMsg(WsDispXyMsg value)
    {
        _WsDispXyMsg = value;
    }
    
    // Get<>AsString()
    public string GetWsDispXyMsgAsString()
    {
        return _WsDispXyMsg != null ? _WsDispXyMsg.GetWsDispXyMsgAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsDispXyMsgAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsDispXyMsg == null)
        {
            _WsDispXyMsg = new WsDispXyMsg();
        }
        _WsDispXyMsg.SetWsDispXyMsgAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsDispXyMsg(string value)
    {
        _WsDispXyMsg.SetWsDispXyMsgAsString(value);
    }
    // Nested Class: WsDispXyMsg
    public class WsDispXyMsg
    {
        private static int _size = 155;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsDispXyMsgNo, is_external=, is_static_class=False, static_prefix=
        private int _WsDispXyMsgNo =0;
        
        
        
        
        // [DEBUG] Field: WsXy, is_external=, is_static_class=False, static_prefix=
        private string _WsXy =" ";
        
        
        
        
        // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
        private WsDispXyMsg.Filler15 _Filler15 = new WsDispXyMsg.Filler15();
        
        
        
        
    public WsDispXyMsg() {}
    
    public WsDispXyMsg(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsDispXyMsgNo(int.Parse(data.Substring(offset, 5).Trim()));
        offset += 5;
        SetWsXy(data.Substring(offset, 75).Trim());
        offset += 75;
        _Filler15.SetFiller15AsString(data.Substring(offset, Filler15.GetSize()));
        offset += 75;
        
    }
    
    // Serialization methods
    public string GetWsDispXyMsgAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsDispXyMsgNo.ToString().PadLeft(5, '0'));
        result.Append(_WsXy.PadRight(75));
        result.Append(_Filler15.GetFiller15AsString());
        
        return result.ToString();
    }
    
    public void SetWsDispXyMsgAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsDispXyMsgNo(parsedInt);
        }
        offset += 5;
        if (offset + 75 <= data.Length)
        {
            string extracted = data.Substring(offset, 75).Trim();
            SetWsXy(extracted);
        }
        offset += 75;
        if (offset + 75 <= data.Length)
        {
            _Filler15.SetFiller15AsString(data.Substring(offset, 75));
        }
        else
        {
            _Filler15.SetFiller15AsString(data.Substring(offset));
        }
        offset += 75;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWsDispXyMsgNo()
    {
        return _WsDispXyMsgNo;
    }
    
    // Standard Setter
    public void SetWsDispXyMsgNo(int value)
    {
        _WsDispXyMsgNo = value;
    }
    
    // Get<>AsString()
    public string GetWsDispXyMsgNoAsString()
    {
        return _WsDispXyMsgNo.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWsDispXyMsgNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsDispXyMsgNo = parsed;
    }
    
    // Standard Getter
    public string GetWsXy()
    {
        return _WsXy;
    }
    
    // Standard Setter
    public void SetWsXy(string value)
    {
        _WsXy = value;
    }
    
    // Get<>AsString()
    public string GetWsXyAsString()
    {
        return _WsXy.PadRight(75);
    }
    
    // Set<>AsString()
    public void SetWsXyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsXy = value;
    }
    
    // Standard Getter
    public Filler15 GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(Filler15 value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15 != null ? _Filler15.GetFiller15AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler15 == null)
        {
            _Filler15 = new Filler15();
        }
        _Filler15.SetFiller15AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Filler15
    public class Filler15
    {
        private static int _size = 75;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsXyMsgType, is_external=, is_static_class=False, static_prefix=
        private string _WsXyMsgType ="";
        
        
        
        
        // [DEBUG] Field: WsXyMsg, is_external=, is_static_class=False, static_prefix=
        private string _WsXyMsg ="";
        
        
        
        
    public Filler15() {}
    
    public Filler15(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsXyMsgType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsXyMsg(data.Substring(offset, 74).Trim());
        offset += 74;
        
    }
    
    // Serialization methods
    public string GetFiller15AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsXyMsgType.PadRight(1));
        result.Append(_WsXyMsg.PadRight(74));
        
        return result.ToString();
    }
    
    public void SetFiller15AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsXyMsgType(extracted);
        }
        offset += 1;
        if (offset + 74 <= data.Length)
        {
            string extracted = data.Substring(offset, 74).Trim();
            SetWsXyMsg(extracted);
        }
        offset += 74;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsXyMsgType()
    {
        return _WsXyMsgType;
    }
    
    // Standard Setter
    public void SetWsXyMsgType(string value)
    {
        _WsXyMsgType = value;
    }
    
    // Get<>AsString()
    public string GetWsXyMsgTypeAsString()
    {
        return _WsXyMsgType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsXyMsgTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsXyMsgType = value;
    }
    
    // Standard Getter
    public string GetWsXyMsg()
    {
        return _WsXyMsg;
    }
    
    // Standard Setter
    public void SetWsXyMsg(string value)
    {
        _WsXyMsg = value;
    }
    
    // Get<>AsString()
    public string GetWsXyMsgAsString()
    {
        return _WsXyMsg.PadRight(74);
    }
    
    // Set<>AsString()
    public void SetWsXyMsgAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsXyMsg = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}
