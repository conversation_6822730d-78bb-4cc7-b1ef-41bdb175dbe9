using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// <Section> Class for Gvar
    public class Gvar
    {
        public Gvar() { }

        // Fields in the class


        // [DEBUG] Field: ProgramName, is_external=, is_static_class=False, static_prefix=
        private string _ProgramName = "CGTBALUP";




        // [DEBUG] Field: VersionNumber, is_external=, is_static_class=False, static_prefix=
        private string _VersionNumber = "1.3";




        // [DEBUG] Field: MAX_NO_OF_COSTS, is_external=, is_static_class=False, static_prefix=
        public const int MAX_NO_OF_COSTS = 200;




        // [DEBUG] Field: MAX_MASTER_RECORD_LEN, is_external=, is_static_class=False, static_prefix=
        public const int MAX_MASTER_RECORD_LEN = 16270;




        // [DEBUG] Field: WRecordStore, is_external=, is_static_class=False, static_prefix=
        private WRecordStore _WRecordStore = new WRecordStore();




        // [DEBUG] Field: D45Record, is_external=, is_static_class=False, static_prefix=
        private D45Record _D45Record = new D45Record();




        // [DEBUG] Field: D45BalAcqDispRecord, is_external=, is_static_class=False, static_prefix=
        private D45BalAcqDispRecord _D45BalAcqDispRecord = new D45BalAcqDispRecord();




        // [DEBUG] Field: D13Record, is_external=, is_static_class=False, static_prefix=
        private string _D13Record = "";




        // [DEBUG] Field: D13SedolHeaderRecord, is_external=, is_static_class=False, static_prefix=
        private D13SedolHeaderRecord _D13SedolHeaderRecord = new D13SedolHeaderRecord();




        // [DEBUG] Field: D13BalAcqDispRecord, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispRecord _D13BalAcqDispRecord = new D13BalAcqDispRecord();




        // [DEBUG] Field: D37Record, is_external=, is_static_class=False, static_prefix=
        private D37Record _D37Record = new D37Record();




        // [DEBUG] Field: D37RecordFormat2, is_external=, is_static_class=False, static_prefix=
        private D37RecordFormat2 _D37RecordFormat2 = new D37RecordFormat2();




        // [DEBUG] Field: D4Record, is_external=, is_static_class=False, static_prefix=
        private D4Record _D4Record = new D4Record();




        // [DEBUG] Field: Cgtdate2LinkageDate1, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate1 _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();




        // [DEBUG] Field: Cgtdate2LinkageDate2, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate2 _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();




        // [DEBUG] Field: Cgtdate2LinkageDate3, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate3 _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();




        // [DEBUG] Field: Cgtdate2LinkageDate4, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate4 _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();




        // [DEBUG] Field: Cgtdate2LinkageDate5, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate5 _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();




        // [DEBUG] Field: Cgtdate2LinkageDate6, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate6 _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();




        // [DEBUG] Field: Cgtdate2LinkageDate7, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate7 _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();




        // [DEBUG] Field: Cgtdate2LinkageDate8, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate8 _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();




        // [DEBUG] Field: Cgtdate2LinkageDate9, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate9 _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();




        // [DEBUG] Field: Cgtdate2LinkageDate10, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2LinkageDate10 _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();




        // [DEBUG] Field: GetCurrencyLinkage, is_external=, is_static_class=False, static_prefix=
        private GetCurrencyLinkage _GetCurrencyLinkage = new GetCurrencyLinkage();




        // [DEBUG] Field: WsSub, is_external=, is_static_class=False, static_prefix=
        private int _WsSub = 0;




        // [DEBUG] Field: Sub, is_external=, is_static_class=False, static_prefix=
        private int _Sub = 0;




        // [DEBUG] Field: WsMessageNo, is_external=, is_static_class=False, static_prefix=
        private int _WsMessageNo = 0;




        // [DEBUG] Field: WsPreviousFund, is_external=, is_static_class=False, static_prefix=
        private string _WsPreviousFund = "";




        // [DEBUG] Field: WsCrt, is_external=, is_static_class=False, static_prefix=
        private WsCrt _WsCrt = new WsCrt();




        // [DEBUG] Field: TimeStamp, is_external=, is_static_class=False, static_prefix=
        private TimeStamp _TimeStamp = new TimeStamp();




        // [DEBUG] Field: DateStamp, is_external=, is_static_class=False, static_prefix=
        private DateStamp _DateStamp = new DateStamp();




        // [DEBUG] Field: WsSedol, is_external=, is_static_class=False, static_prefix=
        private WsSedol _WsSedol = new WsSedol();




        // [DEBUG] Field: WsWhenCompiled, is_external=, is_static_class=False, static_prefix=
        private WsWhenCompiled _WsWhenCompiled = new WsWhenCompiled();




        // [DEBUG] Field: WsMessages, is_external=, is_static_class=False, static_prefix=
        private WsMessages _WsMessages = new WsMessages();




        // [DEBUG] Field: WsFlags, is_external=, is_static_class=False, static_prefix=
        private WsFlags _WsFlags = new WsFlags();




        // [DEBUG] Field: PROCESS_ALL_FUNDS, is_external=, is_static_class=False, static_prefix=
        public const string PROCESS_ALL_FUNDS = "Y";




        // [DEBUG] Field: PROCESS_USER_FUNDS_ONLY, is_external=, is_static_class=False, static_prefix=
        public const string PROCESS_USER_FUNDS_ONLY = "N";




        // [DEBUG] Field: ReportFileName, is_external=, is_static_class=False, static_prefix=
        private string _ReportFileName = "";




        // [DEBUG] Field: ReportFile, is_external=, is_static_class=False, static_prefix=
        private ReportFile _ReportFile = new ReportFile();




        // [DEBUG] Field: RPageCount, is_external=, is_static_class=False, static_prefix=
        private int _RPageCount = 0;




        // [DEBUG] Field: RLineCount, is_external=, is_static_class=False, static_prefix=
        private int _RLineCount = 0;




        // [DEBUG] Field: RAdv, is_external=, is_static_class=False, static_prefix=
        private int _RAdv = 0;




        // [DEBUG] Field: WorkAmount, is_external=, is_static_class=False, static_prefix=
        private decimal _WorkAmount = 0M;




        // [DEBUG] Field: WorkUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WorkUnits = 0M;




        // [DEBUG] Field: WorkSub, is_external=, is_static_class=False, static_prefix=
        private int _WorkSub = 0;




        // [DEBUG] Field: ReportBalancesAdded, is_external=, is_static_class=False, static_prefix=
        private int _ReportBalancesAdded = 0;




        // [DEBUG] Field: ReportBalancesUpdated, is_external=, is_static_class=False, static_prefix=
        private int _ReportBalancesUpdated = 0;




        // [DEBUG] Field: ReportBalancesDeleted, is_external=, is_static_class=False, static_prefix=
        private int _ReportBalancesDeleted = 0;




        // [DEBUG] Field: ReportTransactionsAdded, is_external=, is_static_class=False, static_prefix=
        private int _ReportTransactionsAdded = 0;




        // [DEBUG] Field: ReportTransactionsUpdated, is_external=, is_static_class=False, static_prefix=
        private int _ReportTransactionsUpdated = 0;




        // [DEBUG] Field: WEditCount, is_external=, is_static_class=False, static_prefix=
        private decimal _WEditCount = 0;




        // [DEBUG] Field: WEditQuantity, is_external=, is_static_class=False, static_prefix=
        private decimal _WEditQuantity = 0;




        // [DEBUG] Field: WEditValue, is_external=, is_static_class=False, static_prefix=
        private decimal _WEditValue = 0;




        // [DEBUG] Field: WReport1StTime, is_external=, is_static_class=False, static_prefix=
        private string _WReport1StTime = "Y";

        private int _returnCode = 0;



        // Getter and Setter methods

        public int GetReturnCode()
        {
            return _returnCode;
        }

        public void SetReturnCode(int code)
        {
            this._returnCode = code;
        }

        // 88-level condition checks for WReport1StTime
        public bool IsReport1StTime()
        {
            if (this._WReport1StTime == "'Y'") return true;
            return false;
        }


        // [DEBUG] Field: WReport1StDetail, is_external=, is_static_class=False, static_prefix=
        private string _WReport1StDetail = "";


        // 88-level condition checks for WReport1StDetail
        public bool IsReport1StDetail()
        {
            if (this._WReport1StDetail == "'Y'") return true;
            return false;
        }


        // [DEBUG] Field: WReportOverride, is_external=, is_static_class=False, static_prefix=
        private string _WReportOverride = "";


        // 88-level condition checks for WReportOverride
        public bool IsReportOverride()
        {
            if (this._WReportOverride == "'Y'") return true;
            return false;
        }


        // [DEBUG] Field: WStoredFundSedol, is_external=, is_static_class=False, static_prefix=
        private WStoredFundSedol _WStoredFundSedol = new WStoredFundSedol();




        // [DEBUG] Field: WReportMode, is_external=, is_static_class=False, static_prefix=
        private string _WReportMode = "";




        // [DEBUG] Field: NEW_SEDOL, is_external=, is_static_class=False, static_prefix=
        public const string NEW_SEDOL = "N";




        // [DEBUG] Field: OLD_SEDOL, is_external=, is_static_class=False, static_prefix=
        public const string OLD_SEDOL = "O";




        // [DEBUG] Field: WReportAction, is_external=, is_static_class=False, static_prefix=
        private string _WReportAction = "";




        // [DEBUG] Field: LEAVE_HEADER, is_external=, is_static_class=False, static_prefix=
        public const string LEAVE_HEADER = "LH";




        // [DEBUG] Field: REWRITE_HEADER, is_external=, is_static_class=False, static_prefix=
        public const string REWRITE_HEADER = "RH";




        // [DEBUG] Field: REWRITE_BALANCE_ACQUISITION, is_external=, is_static_class=False, static_prefix=
        public const string REWRITE_BALANCE_ACQUISITION = "RB";




        // [DEBUG] Field: REWRITE_DISPOSAL, is_external=, is_static_class=False, static_prefix=
        public const string REWRITE_DISPOSAL = "RD";




        // [DEBUG] Field: WRITE_HEADER, is_external=, is_static_class=False, static_prefix=
        public const string WRITE_HEADER = "WH";




        // [DEBUG] Field: WRITE_BALANCE_ACQUISITION, is_external=, is_static_class=False, static_prefix=
        public const string WRITE_BALANCE_ACQUISITION = "WB";




        // [DEBUG] Field: WRITE_DISPOSAL, is_external=, is_static_class=False, static_prefix=
        public const string WRITE_DISPOSAL = "WD";




        // [DEBUG] Field: DELETE_BALANCE, is_external=, is_static_class=False, static_prefix=
        public const string DELETE_BALANCE = "DB";




        // [DEBUG] Field: OVERRIDDEN_BALANCE, is_external=, is_static_class=False, static_prefix=
        public const string OVERRIDDEN_BALANCE = "OB";




        // [DEBUG] Field: OVERWRITE_BALANCE, is_external=, is_static_class=False, static_prefix=
        public const string OVERWRITE_BALANCE = "OW";




        // [DEBUG] Field: ReportHeading1, is_external=, is_static_class=False, static_prefix=
        private ReportHeading1 _ReportHeading1 = new ReportHeading1();




        // [DEBUG] Field: ReportHeading2, is_external=, is_static_class=False, static_prefix=
        private ReportHeading2 _ReportHeading2 = new ReportHeading2();




        // [DEBUG] Field: ReportHeading3, is_external=, is_static_class=False, static_prefix=
        private ReportHeading3 _ReportHeading3 = new ReportHeading3();




        // [DEBUG] Field: ReportColumnHeading1, is_external=, is_static_class=False, static_prefix=
        private ReportColumnHeading1 _ReportColumnHeading1 = new ReportColumnHeading1();




        // [DEBUG] Field: ReportColumnHeading2, is_external=, is_static_class=False, static_prefix=
        private ReportColumnHeading2 _ReportColumnHeading2 = new ReportColumnHeading2();




        // [DEBUG] Field: ReportLastLine, is_external=, is_static_class=False, static_prefix=
        private ReportLastLine _ReportLastLine = new ReportLastLine();




        // [DEBUG] Field: ReportDetailLine1, is_external=, is_static_class=False, static_prefix=
        private ReportDetailLine1 _ReportDetailLine1 = new ReportDetailLine1();




        // [DEBUG] Field: WReportSedol, is_external=, is_static_class=False, static_prefix=
        private string _WReportSedol = "";




        // [DEBUG] Field: ReportDetailLine2, is_external=, is_static_class=False, static_prefix=
        private ReportDetailLine2 _ReportDetailLine2 = new ReportDetailLine2();




        // [DEBUG] Field: ReportDetailLine3, is_external=, is_static_class=False, static_prefix=
        private ReportDetailLine3 _ReportDetailLine3 = new ReportDetailLine3();




        // [DEBUG] Field: Cgtbalup00, is_external=, is_static_class=False, static_prefix=
        private Cgtbalup00 _Cgtbalup00 = new Cgtbalup00();




        // [DEBUG] Field: ReportOpt01, is_external=, is_static_class=False, static_prefix=
        private ReportOpt01 _ReportOpt01 = new ReportOpt01();




        // [DEBUG] Field: ReportOptTable, is_external=, is_static_class=False, static_prefix=
        private ReportOptTable _ReportOptTable = new ReportOptTable();




        // [DEBUG] Field: ReportOptLineOccurs, is_external=, is_static_class=False, static_prefix=
        private int _ReportOptLineOccurs = 8;




        // [DEBUG] Field: CgtabortLinkage, is_external=, is_static_class=False, static_prefix=
        private CgtabortLinkage _CgtabortLinkage = new CgtabortLinkage();




        // [DEBUG] Field: CgtfilesLinkage, is_external=, is_static_class=False, static_prefix=
        private CgtfilesLinkage _CgtfilesLinkage = new CgtfilesLinkage();




        // [DEBUG] Field: LFileRecordArea, is_external=, is_static_class=False, static_prefix=
        private LFileRecordArea _LFileRecordArea = new LFileRecordArea();




        // [DEBUG] Field: ElcgmioLinkage1, is_external=, is_static_class=False, static_prefix=
        private ElcgmioLinkage1 _ElcgmioLinkage1 = new ElcgmioLinkage1();




        // [DEBUG] Field: FULL_COMP, is_external=, is_static_class=False, static_prefix=
        public const int FULL_COMP = 6;




        // [DEBUG] Field: PARTIAL_COMP, is_external=, is_static_class=False, static_prefix=
        public const int PARTIAL_COMP = 9;




        // [DEBUG] Field: SEDOL_WHATIF_COMP, is_external=, is_static_class=False, static_prefix=
        public const int SEDOL_WHATIF_COMP = 7;




        // [DEBUG] Field: SINGLE_SEDOL_COMP, is_external=, is_static_class=False, static_prefix=
        public const int SINGLE_SEDOL_COMP = 7;




        // [DEBUG] Field: YEAR_END_COMP, is_external=, is_static_class=False, static_prefix=
        public const int YEAR_END_COMP = 8;




        // [DEBUG] Field: ElcgmioLinkage2, is_external=, is_static_class=False, static_prefix=
        private ElcgmioLinkage2 _ElcgmioLinkage2 = new ElcgmioLinkage2();




        // [DEBUG] Field: CgtmessLinkage, is_external=, is_static_class=False, static_prefix=
        private CgtmessLinkage _CgtmessLinkage = new CgtmessLinkage();




        // [DEBUG] Field: CREATE_MESSAGE_PANEL, is_external=, is_static_class=False, static_prefix=
        public const int CREATE_MESSAGE_PANEL = 0;




        // [DEBUG] Field: DISABLE_MESSAGE_PANEL, is_external=, is_static_class=False, static_prefix=
        public const int DISABLE_MESSAGE_PANEL = 9999;




        // [DEBUG] Field: CgtlogLinkageArea1, is_external=, is_static_class=False, static_prefix=
        private CgtlogLinkageArea1 _CgtlogLinkageArea1 = new CgtlogLinkageArea1();




        // [DEBUG] Field: CLEAR_RUN_LOG, is_external=, is_static_class=False, static_prefix=
        public const string CLEAR_RUN_LOG = "CRL";




        // [DEBUG] Field: CgtlogLinkageArea2, is_external=, is_static_class=False, static_prefix=
        private CgtlogLinkageArea2 _CgtlogLinkageArea2 = new CgtlogLinkageArea2();




        // [DEBUG] Field: EqtpathLinkage, is_external=, is_static_class=False, static_prefix=
        private EqtpathLinkage _EqtpathLinkage = new EqtpathLinkage();




        // [DEBUG] Field: ADMIN_DATA_PATH, is_external=, is_static_class=False, static_prefix=
        public const string ADMIN_DATA_PATH = "EQADMIN";




        // [DEBUG] Field: USER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
        public const string USER_DATA_PATH = "EQUSER";




        // [DEBUG] Field: MASTER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
        public const string MASTER_DATA_PATH = "EQMASTER";




        // [DEBUG] Field: WIds, is_external=, is_static_class=False, static_prefix=
        private WIds _WIds = new WIds();




        // [DEBUG] Field: HeaderIds, is_external=, is_static_class=False, static_prefix=
        private HeaderIds _HeaderIds = new HeaderIds();




        // [DEBUG] Field: BalanceIds, is_external=, is_static_class=False, static_prefix=
        private BalanceIds _BalanceIds = new BalanceIds();




        // [DEBUG] Field: AcquisitionIds, is_external=, is_static_class=False, static_prefix=
        private AcquisitionIds _AcquisitionIds = new AcquisitionIds();




        // [DEBUG] Field: DisposalIds, is_external=, is_static_class=False, static_prefix=
        private DisposalIds _DisposalIds = new DisposalIds();




        // [DEBUG] Field: Filler202, is_external=, is_static_class=False, static_prefix=
        private Filler202 _Filler202 = new Filler202();




        // [DEBUG] Field: CheckCatLinkage, is_external=, is_static_class=False, static_prefix=
        private CheckCatLinkage _CheckCatLinkage = new CheckCatLinkage();




        // [DEBUG] Field: WTransactionCategory, is_external=, is_static_class=False, static_prefix=
        private string _WTransactionCategory = "";




        // [DEBUG] Field: WttOutputRecord, is_external=, is_static_class=False, static_prefix=
        private WttOutputRecord _WttOutputRecord = new WttOutputRecord();




        // [DEBUG] Field: WsStoreD13Ids, is_external=, is_static_class=False, static_prefix=
        private string _WsStoreD13Ids = "";




        // [DEBUG] Field: WsNewHoldingId, is_external=, is_static_class=False, static_prefix=
        private string _WsNewHoldingId = "";




        // [DEBUG] Field: LkFundSedol, is_external=, is_static_class=False, static_prefix=
        private LkFundSedol _LkFundSedol = new LkFundSedol();




        // [DEBUG] Field: LForceWrite, is_external=, is_static_class=False, static_prefix=
        private string _LForceWrite = "";


        // 88-level condition checks for LForceWrite
        public bool IsForceWrite()
        {
            if (this._LForceWrite == "'Y'") return true;
            return false;
        }
        public bool IsDontForceWrite()
        {
            if (this._LForceWrite == "'N'") return true;
            return false;
        }


        // [DEBUG] Field: EquityParameters, is_external=, is_static_class=False, static_prefix=
        private EquityParameters _EquityParameters = new EquityParameters();




        // [DEBUG] Field: EQUITY_SUCCESSFUL, is_external=, is_static_class=False, static_prefix=
        public const int EQUITY_SUCCESSFUL = 0;




        // [DEBUG] Field: SUCCESSFUL_PREVIOUS_CRASH, is_external=, is_static_class=False, static_prefix=
        public const int SUCCESSFUL_PREVIOUS_CRASH = 1;




        // [DEBUG] Field: USERID_ALREADY_IN_USE, is_external=, is_static_class=False, static_prefix=
        public const int USERID_ALREADY_IN_USE = 2;




        // [DEBUG] Field: USERID_DOESNT_EXIST, is_external=, is_static_class=False, static_prefix=
        public const int USERID_DOESNT_EXIST = 3;




        // [DEBUG] Field: INVALID_USERID_PW, is_external=, is_static_class=False, static_prefix=
        public const int INVALID_USERID_PW = 4;




        // [DEBUG] Field: SEE_ERROR_LOG, is_external=, is_static_class=False, static_prefix=
        public const int SEE_ERROR_LOG = 5;




        // [DEBUG] Field: LOAD_RECORDS_REJECTED, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_RECORDS_REJECTED = 6;




        // [DEBUG] Field: SEE_OUTPUT_REPORTS, is_external=, is_static_class=False, static_prefix=
        public const int SEE_OUTPUT_REPORTS = 7;




        // [DEBUG] Field: SEE_RUN_LOG, is_external=, is_static_class=False, static_prefix=
        public const int SEE_RUN_LOG = 8;




        // [DEBUG] Field: USER_NOT_LOGGED_ON, is_external=, is_static_class=False, static_prefix=
        public const int USER_NOT_LOGGED_ON = 9;




        // [DEBUG] Field: TOO_MANY_USERS, is_external=, is_static_class=False, static_prefix=
        public const int TOO_MANY_USERS = 10;




        // [DEBUG] Field: VERSION_2_1_OR_LOWER, is_external=, is_static_class=False, static_prefix=
        public const int VERSION_2_1_OR_LOWER = 11;




        // [DEBUG] Field: LOAD_FUNDS, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_FUNDS = 3001;




        // [DEBUG] Field: LOAD_COUNTRIES, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_COUNTRIES = 3002;




        // [DEBUG] Field: LOAD_GROUPS, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_GROUPS = 3003;




        // [DEBUG] Field: LOAD_RPI, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_RPI = 3004;




        // [DEBUG] Field: LOAD_STOCKS, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_STOCKS = 3005;




        // [DEBUG] Field: LOAD_PRICES, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_PRICES = 3006;




        // [DEBUG] Field: LOAD_BALANCES, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_BALANCES = 3007;




        // [DEBUG] Field: LOAD_TRANSACTIONS, is_external=, is_static_class=False, static_prefix=
        public const int LOAD_TRANSACTIONS = 3008;




        // [DEBUG] Field: FULL_CALCULATION, is_external=, is_static_class=False, static_prefix=
        public const int FULL_CALCULATION = 3009;




        // [DEBUG] Field: ERROR_REPORT, is_external=, is_static_class=False, static_prefix=
        public const int ERROR_REPORT = 3010;




        // [DEBUG] Field: SCHEDULE, is_external=, is_static_class=False, static_prefix=
        public const int SCHEDULE = 3011;




        // [DEBUG] Field: YEAR_END, is_external=, is_static_class=False, static_prefix=
        public const int YEAR_END = 3012;




        // [DEBUG] Field: PARTIAL_CALCULATION, is_external=, is_static_class=False, static_prefix=
        public const int PARTIAL_CALCULATION = 3013;




        // [DEBUG] Field: SEDOL_WHATIF_CALCULATION, is_external=, is_static_class=False, static_prefix=
        public const int SEDOL_WHATIF_CALCULATION = 3014;




        // [DEBUG] Field: XFUND_WHATIF_CALCULATION, is_external=, is_static_class=False, static_prefix=
        public const int XFUND_WHATIF_CALCULATION = 3015;




        // [DEBUG] Field: ON_LINE_CALCULATION, is_external=, is_static_class=False, static_prefix=
        public const int ON_LINE_CALCULATION = 3016;




        // [DEBUG] Field: CALCULATION_ERROR, is_external=, is_static_class=False, static_prefix=
        public const int CALCULATION_ERROR = 3017;




        // [DEBUG] Field: FORMAT_SCHEDULE, is_external=, is_static_class=False, static_prefix=
        public const int FORMAT_SCHEDULE = 3018;




        // [DEBUG] Field: TAPERED_GAINS_SCHD, is_external=, is_static_class=False, static_prefix=
        public const int TAPERED_GAINS_SCHD = 3019;




        // [DEBUG] Field: U_PERCENTAGE_GAIN_LOSS, is_external=, is_static_class=False, static_prefix=
        public const int U_PERCENTAGE_GAIN_LOSS = 3020;




        // [DEBUG] Field: U_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
        public const int U_EXPORT_FILE = 3021;




        // [DEBUG] Field: U_FULL_EXTRACT, is_external=, is_static_class=False, static_prefix=
        public const int U_FULL_EXTRACT = 3022;




        // [DEBUG] Field: TRANCHE_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int TRANCHE_EXPORT = 3023;




        // [DEBUG] Field: GAIN_LOSS_SUMMARY, is_external=, is_static_class=False, static_prefix=
        public const int GAIN_LOSS_SUMMARY = 3024;




        // [DEBUG] Field: CH_GAIN_LOSS_SUMMARY, is_external=, is_static_class=False, static_prefix=
        public const int CH_GAIN_LOSS_SUMMARY = 3025;




        // [DEBUG] Field: SCHEDULE_D_BF_GAINS, is_external=, is_static_class=False, static_prefix=
        public const int SCHEDULE_D_BF_GAINS = 3026;




        // [DEBUG] Field: GAIN_LOSS_EXTRACT, is_external=, is_static_class=False, static_prefix=
        public const int GAIN_LOSS_EXTRACT = 3027;




        // [DEBUG] Field: REALISED_REPORT_CG01, is_external=, is_static_class=False, static_prefix=
        public const int REALISED_REPORT_CG01 = 3028;




        // [DEBUG] Field: CONFIGURABLE_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
        public const int CONFIGURABLE_REALISED_REPORT = 3029;




        // [DEBUG] Field: GAINS_LOSSES_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
        public const int GAINS_LOSSES_REALISED_REPORT = 3030;




        // [DEBUG] Field: UNREALISED_REPORT_CG02, is_external=, is_static_class=False, static_prefix=
        public const int UNREALISED_REPORT_CG02 = 3031;




        // [DEBUG] Field: FULL_UNREALISED_REPORT, is_external=, is_static_class=False, static_prefix=
        public const int FULL_UNREALISED_REPORT = 3032;




        // [DEBUG] Field: HOLDINGS_ANALYSIS, is_external=, is_static_class=False, static_prefix=
        public const int HOLDINGS_ANALYSIS = 3033;




        // [DEBUG] Field: INDEXATION_ANALYSIS, is_external=, is_static_class=False, static_prefix=
        public const int INDEXATION_ANALYSIS = 3034;




        // [DEBUG] Field: TRANSACTION_SUMMARY, is_external=, is_static_class=False, static_prefix=
        public const int TRANSACTION_SUMMARY = 3035;




        // [DEBUG] Field: GAINS_TODAY_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int GAINS_TODAY_EXPORT = 3036;




        // [DEBUG] Field: HOLDINGS_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int HOLDINGS_EXPORT = 3037;




        // [DEBUG] Field: SF_GAINLOSS_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int SF_GAINLOSS_EXPORT = 3038;




        // [DEBUG] Field: TSB_GAINLOSS_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int TSB_GAINLOSS_EXPORT = 3039;




        // [DEBUG] Field: ACQUISITION_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int ACQUISITION_EXPORT = 3040;




        // [DEBUG] Field: PRICING_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int PRICING_EXPORT = 3041;




        // [DEBUG] Field: MASTERFILE_EXPORTER, is_external=, is_static_class=False, static_prefix=
        public const int MASTERFILE_EXPORTER = 3042;




        // [DEBUG] Field: TRANSACTION_HISTORY_EXPORTER, is_external=, is_static_class=False, static_prefix=
        public const int TRANSACTION_HISTORY_EXPORTER = 3043;




        // [DEBUG] Field: ADMIN_EXPORTER, is_external=, is_static_class=False, static_prefix=
        public const int ADMIN_EXPORTER = 3044;




        // [DEBUG] Field: FULL_HOLDINGS_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int FULL_HOLDINGS_EXPORT = 3045;




        // [DEBUG] Field: RESULTS_DB, is_external=, is_static_class=False, static_prefix=
        public const int RESULTS_DB = 3055;




        // [DEBUG] Field: INTERCONNECTED_FUNDS, is_external=, is_static_class=False, static_prefix=
        public const int INTERCONNECTED_FUNDS = 3056;




        // [DEBUG] Field: DAILY_TRANSACTION_EXPORT, is_external=, is_static_class=False, static_prefix=
        public const int DAILY_TRANSACTION_EXPORT = 3060;




        // [DEBUG] Field: GAIN_LOSS_SUMMARY_LR, is_external=, is_static_class=False, static_prefix=
        public const int GAIN_LOSS_SUMMARY_LR = 3061;




        // Getter and Setter methods

        // Standard Getter
        public string GetProgramName()
        {
            return _ProgramName;
        }

        // Standard Setter
        public void SetProgramName(string value)
        {
            _ProgramName = value;
        }

        // Get<>AsString()
        public string GetProgramNameAsString()
        {
            return _ProgramName.PadRight(8);
        }

        // Set<>AsString()
        public void SetProgramNameAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _ProgramName = value;
        }

        // Standard Getter
        public string GetVersionNumber()
        {
            return _VersionNumber;
        }

        // Standard Setter
        public void SetVersionNumber(string value)
        {
            _VersionNumber = value;
        }

        // Get<>AsString()
        public string GetVersionNumberAsString()
        {
            return _VersionNumber.PadRight(3);
        }

        // Set<>AsString()
        public void SetVersionNumberAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _VersionNumber = value;
        }

        // Standard Getter
        public WRecordStore GetWRecordStore()
        {
            return _WRecordStore;
        }

        // Standard Setter
        public void SetWRecordStore(WRecordStore value)
        {
            _WRecordStore = value;
        }

        // Get<>AsString()
        public string GetWRecordStoreAsString()
        {
            return _WRecordStore != null ? _WRecordStore.GetWRecordStoreAsString() : "";
        }

        // Set<>AsString()
        public void SetWRecordStoreAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WRecordStore == null)
            {
                _WRecordStore = new WRecordStore();
            }
            _WRecordStore.SetWRecordStoreAsString(value);
        }

        // Standard Getter
        public D45Record GetD45Record()
        {
            return _D45Record;
        }

        // Standard Setter
        public void SetD45Record(D45Record value)
        {
            _D45Record = value;
        }

        // Get<>AsString()
        public string GetD45RecordAsString()
        {
            return _D45Record != null ? _D45Record.GetD45RecordAsString() : "";
        }

        // Set<>AsString()
        public void SetD45RecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_D45Record == null)
            {
                _D45Record = new D45Record();
            }
            _D45Record.SetD45RecordAsString(value);
        }

        // Standard Getter
        public D45BalAcqDispRecord GetD45BalAcqDispRecord()
        {
            return _D45BalAcqDispRecord;
        }

        // Standard Setter
        public void SetD45BalAcqDispRecord(D45BalAcqDispRecord value)
        {
            _D45BalAcqDispRecord = value;
        }

        // Get<>AsString()
        public string GetD45BalAcqDispRecordAsString()
        {
            return _D45BalAcqDispRecord != null ? _D45BalAcqDispRecord.GetD45BalAcqDispRecordAsString() : "";
        }

        // Set<>AsString()
        public void SetD45BalAcqDispRecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_D45BalAcqDispRecord == null)
            {
                _D45BalAcqDispRecord = new D45BalAcqDispRecord();
            }
            _D45BalAcqDispRecord.SetD45BalAcqDispRecordAsString(value);
        }

        // Standard Getter
        public string GetD13Record()
        {
            return _D13Record;
        }

        // Standard Setter
        public void SetD13Record(string value)
        {
            _D13Record = value;
        }

        // Get<>AsString()
        public string GetD13RecordAsString()
        {
            return _D13Record.PadRight(1870);
        }

        // Set<>AsString()
        public void SetD13RecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _D13Record = value;
        }

        // Standard Getter
        public D13SedolHeaderRecord GetD13SedolHeaderRecord()
        {
            return _D13SedolHeaderRecord;
        }

        // Standard Setter
        public void SetD13SedolHeaderRecord(D13SedolHeaderRecord value)
        {
            _D13SedolHeaderRecord = value;
        }

        // Get<>AsString()
        public string GetD13SedolHeaderRecordAsString()
        {
            return _D13SedolHeaderRecord != null ? _D13SedolHeaderRecord.GetD13SedolHeaderRecordAsString() : "";
        }

        // Set<>AsString()
        public void SetD13SedolHeaderRecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_D13SedolHeaderRecord == null)
            {
                _D13SedolHeaderRecord = new D13SedolHeaderRecord();
            }
            _D13SedolHeaderRecord.SetD13SedolHeaderRecordAsString(value);
        }

        // Standard Getter
        public D13BalAcqDispRecord GetD13BalAcqDispRecord()
        {
            return _D13BalAcqDispRecord;
        }

        // Standard Setter
        public void SetD13BalAcqDispRecord(D13BalAcqDispRecord value)
        {
            _D13BalAcqDispRecord = value;
        }

        // Get<>AsString()
        public string GetD13BalAcqDispRecordAsString()
        {
            return _D13BalAcqDispRecord != null ? _D13BalAcqDispRecord.GetD13BalAcqDispRecordAsString() : "";
        }

        // Set<>AsString()
        public void SetD13BalAcqDispRecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_D13BalAcqDispRecord == null)
            {
                _D13BalAcqDispRecord = new D13BalAcqDispRecord();
            }
            _D13BalAcqDispRecord.SetD13BalAcqDispRecordAsString(value);
        }

        // Standard Getter
        public D37Record GetD37Record()
        {
            return _D37Record;
        }

        // Standard Setter
        public void SetD37Record(D37Record value)
        {
            _D37Record = value;
        }

        // Get<>AsString()
        public string GetD37RecordAsString()
        {
            return _D37Record != null ? _D37Record.GetD37RecordAsString() : "";
        }

        // Set<>AsString()
        public void SetD37RecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_D37Record == null)
            {
                _D37Record = new D37Record();
            }
            _D37Record.SetD37RecordAsString(value);
        }

        // Standard Getter
        public D37RecordFormat2 GetD37RecordFormat2()
        {
            return _D37RecordFormat2;
        }

        // Standard Setter
        public void SetD37RecordFormat2(D37RecordFormat2 value)
        {
            _D37RecordFormat2 = value;
        }

        // Get<>AsString()
        public string GetD37RecordFormat2AsString()
        {
            return _D37RecordFormat2 != null ? _D37RecordFormat2.GetD37RecordFormat2AsString() : "";
        }

        // Set<>AsString()
        public void SetD37RecordFormat2AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_D37RecordFormat2 == null)
            {
                _D37RecordFormat2 = new D37RecordFormat2();
            }
            _D37RecordFormat2.SetD37RecordFormat2AsString(value);
        }

        // Standard Getter
        public D4Record GetD4Record()
        {
            return _D4Record;
        }

        // Standard Setter
        public void SetD4Record(D4Record value)
        {
            _D4Record = value;
        }

        // Get<>AsString()
        public string GetD4RecordAsString()
        {
            return _D4Record != null ? _D4Record.GetD4RecordAsString() : "";
        }

        // Set<>AsString()
        public void SetD4RecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_D4Record == null)
            {
                _D4Record = new D4Record();
            }
            _D4Record.SetD4RecordAsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate1 GetCgtdate2LinkageDate1()
        {
            return _Cgtdate2LinkageDate1;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate1(Cgtdate2LinkageDate1 value)
        {
            _Cgtdate2LinkageDate1 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate1AsString()
        {
            return _Cgtdate2LinkageDate1 != null ? _Cgtdate2LinkageDate1.GetCgtdate2LinkageDate1AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate1AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate1 == null)
            {
                _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();
            }
            _Cgtdate2LinkageDate1.SetCgtdate2LinkageDate1AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate2 GetCgtdate2LinkageDate2()
        {
            return _Cgtdate2LinkageDate2;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate2(Cgtdate2LinkageDate2 value)
        {
            _Cgtdate2LinkageDate2 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate2AsString()
        {
            return _Cgtdate2LinkageDate2 != null ? _Cgtdate2LinkageDate2.GetCgtdate2LinkageDate2AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate2AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate2 == null)
            {
                _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();
            }
            _Cgtdate2LinkageDate2.SetCgtdate2LinkageDate2AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate3 GetCgtdate2LinkageDate3()
        {
            return _Cgtdate2LinkageDate3;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate3(Cgtdate2LinkageDate3 value)
        {
            _Cgtdate2LinkageDate3 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate3AsString()
        {
            return _Cgtdate2LinkageDate3 != null ? _Cgtdate2LinkageDate3.GetCgtdate2LinkageDate3AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate3AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate3 == null)
            {
                _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();
            }
            _Cgtdate2LinkageDate3.SetCgtdate2LinkageDate3AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate4 GetCgtdate2LinkageDate4()
        {
            return _Cgtdate2LinkageDate4;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate4(Cgtdate2LinkageDate4 value)
        {
            _Cgtdate2LinkageDate4 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate4AsString()
        {
            return _Cgtdate2LinkageDate4 != null ? _Cgtdate2LinkageDate4.GetCgtdate2LinkageDate4AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate4AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate4 == null)
            {
                _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();
            }
            _Cgtdate2LinkageDate4.SetCgtdate2LinkageDate4AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate5 GetCgtdate2LinkageDate5()
        {
            return _Cgtdate2LinkageDate5;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate5(Cgtdate2LinkageDate5 value)
        {
            _Cgtdate2LinkageDate5 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate5AsString()
        {
            return _Cgtdate2LinkageDate5 != null ? _Cgtdate2LinkageDate5.GetCgtdate2LinkageDate5AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate5AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate5 == null)
            {
                _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();
            }
            _Cgtdate2LinkageDate5.SetCgtdate2LinkageDate5AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate6 GetCgtdate2LinkageDate6()
        {
            return _Cgtdate2LinkageDate6;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate6(Cgtdate2LinkageDate6 value)
        {
            _Cgtdate2LinkageDate6 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate6AsString()
        {
            return _Cgtdate2LinkageDate6 != null ? _Cgtdate2LinkageDate6.GetCgtdate2LinkageDate6AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate6AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate6 == null)
            {
                _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();
            }
            _Cgtdate2LinkageDate6.SetCgtdate2LinkageDate6AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate7 GetCgtdate2LinkageDate7()
        {
            return _Cgtdate2LinkageDate7;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate7(Cgtdate2LinkageDate7 value)
        {
            _Cgtdate2LinkageDate7 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate7AsString()
        {
            return _Cgtdate2LinkageDate7 != null ? _Cgtdate2LinkageDate7.GetCgtdate2LinkageDate7AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate7AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate7 == null)
            {
                _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();
            }
            _Cgtdate2LinkageDate7.SetCgtdate2LinkageDate7AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate8 GetCgtdate2LinkageDate8()
        {
            return _Cgtdate2LinkageDate8;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate8(Cgtdate2LinkageDate8 value)
        {
            _Cgtdate2LinkageDate8 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate8AsString()
        {
            return _Cgtdate2LinkageDate8 != null ? _Cgtdate2LinkageDate8.GetCgtdate2LinkageDate8AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate8AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate8 == null)
            {
                _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();
            }
            _Cgtdate2LinkageDate8.SetCgtdate2LinkageDate8AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate9 GetCgtdate2LinkageDate9()
        {
            return _Cgtdate2LinkageDate9;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate9(Cgtdate2LinkageDate9 value)
        {
            _Cgtdate2LinkageDate9 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate9AsString()
        {
            return _Cgtdate2LinkageDate9 != null ? _Cgtdate2LinkageDate9.GetCgtdate2LinkageDate9AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate9AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate9 == null)
            {
                _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();
            }
            _Cgtdate2LinkageDate9.SetCgtdate2LinkageDate9AsString(value);
        }

        // Standard Getter
        public Cgtdate2LinkageDate10 GetCgtdate2LinkageDate10()
        {
            return _Cgtdate2LinkageDate10;
        }

        // Standard Setter
        public void SetCgtdate2LinkageDate10(Cgtdate2LinkageDate10 value)
        {
            _Cgtdate2LinkageDate10 = value;
        }

        // Get<>AsString()
        public string GetCgtdate2LinkageDate10AsString()
        {
            return _Cgtdate2LinkageDate10 != null ? _Cgtdate2LinkageDate10.GetCgtdate2LinkageDate10AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtdate2LinkageDate10AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtdate2LinkageDate10 == null)
            {
                _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();
            }
            _Cgtdate2LinkageDate10.SetCgtdate2LinkageDate10AsString(value);
        }

        // Standard Getter
        public GetCurrencyLinkage GetGetCurrencyLinkage()
        {
            return _GetCurrencyLinkage;
        }

        // Standard Setter
        public void SetGetCurrencyLinkage(GetCurrencyLinkage value)
        {
            _GetCurrencyLinkage = value;
        }

        // Get<>AsString()
        public string GetGetCurrencyLinkageAsString()
        {
            return _GetCurrencyLinkage != null ? _GetCurrencyLinkage.GetGetCurrencyLinkageAsString() : "";
        }

        // Set<>AsString()
        public void SetGetCurrencyLinkageAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_GetCurrencyLinkage == null)
            {
                _GetCurrencyLinkage = new GetCurrencyLinkage();
            }
            _GetCurrencyLinkage.SetGetCurrencyLinkageAsString(value);
        }

        // Standard Getter
        public int GetWsSub()
        {
            return _WsSub;
        }

        // Standard Setter
        public void SetWsSub(int value)
        {
            _WsSub = value;
        }

        // Get<>AsString()
        public string GetWsSubAsString()
        {
            return _WsSub.ToString().PadLeft(2, '0');
        }

        // Set<>AsString()
        public void SetWsSubAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _WsSub = parsed;
        }

        // Standard Getter
        public int GetSub()
        {
            return _Sub;
        }

        // Standard Setter
        public void SetSub(int value)
        {
            _Sub = value;
        }

        // Get<>AsString()
        public string GetSubAsString()
        {
            return _Sub.ToString().PadLeft(1, '0');
        }

        // Set<>AsString()
        public void SetSubAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _Sub = parsed;
        }

        // Standard Getter
        public int GetWsMessageNo()
        {
            return _WsMessageNo;
        }

        // Standard Setter
        public void SetWsMessageNo(int value)
        {
            _WsMessageNo = value;
        }

        // Get<>AsString()
        public string GetWsMessageNoAsString()
        {
            return _WsMessageNo.ToString().PadLeft(4, '0');
        }

        // Set<>AsString()
        public void SetWsMessageNoAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _WsMessageNo = parsed;
        }

        // Standard Getter
        public string GetWsPreviousFund()
        {
            return _WsPreviousFund;
        }

        // Standard Setter
        public void SetWsPreviousFund(string value)
        {
            _WsPreviousFund = value;
        }

        // Get<>AsString()
        public string GetWsPreviousFundAsString()
        {
            return _WsPreviousFund.PadRight(4);
        }

        // Set<>AsString()
        public void SetWsPreviousFundAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WsPreviousFund = value;
        }

        // Standard Getter
        public WsCrt GetWsCrt()
        {
            return _WsCrt;
        }

        // Standard Setter
        public void SetWsCrt(WsCrt value)
        {
            _WsCrt = value;
        }

        // Get<>AsString()
        public string GetWsCrtAsString()
        {
            return _WsCrt != null ? _WsCrt.GetWsCrtAsString() : "";
        }

        // Set<>AsString()
        public void SetWsCrtAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WsCrt == null)
            {
                _WsCrt = new WsCrt();
            }
            _WsCrt.SetWsCrtAsString(value);
        }

        // Standard Getter
        public TimeStamp GetTimeStamp()
        {
            return _TimeStamp;
        }

        // Standard Setter
        public void SetTimeStamp(TimeStamp value)
        {
            _TimeStamp = value;
        }

        // Get<>AsString()
        public string GetTimeStampAsString()
        {
            return _TimeStamp != null ? _TimeStamp.GetTimeStampAsString() : "";
        }

        // Set<>AsString()
        public void SetTimeStampAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_TimeStamp == null)
            {
                _TimeStamp = new TimeStamp();
            }
            _TimeStamp.SetTimeStampAsString(value);
        }

        // Standard Getter
        public DateStamp GetDateStamp()
        {
            return _DateStamp;
        }

        // Standard Setter
        public void SetDateStamp(DateStamp value)
        {
            _DateStamp = value;
        }

        // Get<>AsString()
        public string GetDateStampAsString()
        {
            return _DateStamp != null ? _DateStamp.GetDateStampAsString() : "";
        }

        // Set<>AsString()
        public void SetDateStampAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_DateStamp == null)
            {
                _DateStamp = new DateStamp();
            }
            _DateStamp.SetDateStampAsString(value);
        }

        // Standard Getter
        public WsSedol GetWsSedol()
        {
            return _WsSedol;
        }

        // Standard Setter
        public void SetWsSedol(WsSedol value)
        {
            _WsSedol = value;
        }

        // Get<>AsString()
        public string GetWsSedolAsString()
        {
            return _WsSedol != null ? _WsSedol.GetWsSedolAsString() : "";
        }

        // Set<>AsString()
        public void SetWsSedolAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WsSedol == null)
            {
                _WsSedol = new WsSedol();
            }
            _WsSedol.SetWsSedolAsString(value);
        }

        // Standard Getter
        public WsWhenCompiled GetWsWhenCompiled()
        {
            return _WsWhenCompiled;
        }

        // Standard Setter
        public void SetWsWhenCompiled(WsWhenCompiled value)
        {
            _WsWhenCompiled = value;
        }

        // Get<>AsString()
        public string GetWsWhenCompiledAsString()
        {
            return _WsWhenCompiled != null ? _WsWhenCompiled.GetWsWhenCompiledAsString() : "";
        }

        // Set<>AsString()
        public void SetWsWhenCompiledAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WsWhenCompiled == null)
            {
                _WsWhenCompiled = new WsWhenCompiled();
            }
            _WsWhenCompiled.SetWsWhenCompiledAsString(value);
        }

        // Standard Getter
        public WsMessages GetWsMessages()
        {
            return _WsMessages;
        }

        // Standard Setter
        public void SetWsMessages(WsMessages value)
        {
            _WsMessages = value;
        }

        // Get<>AsString()
        public string GetWsMessagesAsString()
        {
            return _WsMessages != null ? _WsMessages.GetWsMessagesAsString() : "";
        }

        // Set<>AsString()
        public void SetWsMessagesAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WsMessages == null)
            {
                _WsMessages = new WsMessages();
            }
            _WsMessages.SetWsMessagesAsString(value);
        }

        // Standard Getter
        public WsFlags GetWsFlags()
        {
            return _WsFlags;
        }

        // Standard Setter
        public void SetWsFlags(WsFlags value)
        {
            _WsFlags = value;
        }

        // Get<>AsString()
        public string GetWsFlagsAsString()
        {
            return _WsFlags != null ? _WsFlags.GetWsFlagsAsString() : "";
        }

        // Set<>AsString()
        public void SetWsFlagsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WsFlags == null)
            {
                _WsFlags = new WsFlags();
            }
            _WsFlags.SetWsFlagsAsString(value);
        }

        // Standard Getter
        public string GetReportFileName()
        {
            return _ReportFileName;
        }

        // Standard Setter
        public void SetReportFileName(string value)
        {
            _ReportFileName = value;
        }

        // Get<>AsString()
        public string GetReportFileNameAsString()
        {
            return _ReportFileName.PadRight(256);
        }

        // Set<>AsString()
        public void SetReportFileNameAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _ReportFileName = value;
        }

        // Standard Getter
        public ReportFile GetReportFile()
        {
            return _ReportFile;
        }

        // Standard Setter
        public void SetReportFile(ReportFile value)
        {
            _ReportFile = value;
        }

        // Get<>AsString()
        public string GetReportFileAsString()
        {
            return _ReportFile != null ? _ReportFile.GetReportFileAsString() : "";
        }

        // Set<>AsString()
        public void SetReportFileAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportFile == null)
            {
                _ReportFile = new ReportFile();
            }
            _ReportFile.SetReportFileAsString(value);
        }

        // Standard Getter
        public int GetRPageCount()
        {
            return _RPageCount;
        }

        // Standard Setter
        public void SetRPageCount(int value)
        {
            _RPageCount = value;
        }

        // Get<>AsString()
        public string GetRPageCountAsString()
        {
            return _RPageCount.ToString().PadLeft(3, '0');
        }

        // Set<>AsString()
        public void SetRPageCountAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _RPageCount = parsed;
        }

        // Standard Getter
        public int GetRLineCount()
        {
            return _RLineCount;
        }

        // Standard Setter
        public void SetRLineCount(int value)
        {
            _RLineCount = value;
        }

        // Get<>AsString()
        public string GetRLineCountAsString()
        {
            return _RLineCount.ToString().PadLeft(3, '0');
        }

        // Set<>AsString()
        public void SetRLineCountAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _RLineCount = parsed;
        }

        // Standard Getter
        public int GetRAdv()
        {
            return _RAdv;
        }

        // Standard Setter
        public void SetRAdv(int value)
        {
            _RAdv = value;
        }

        // Get<>AsString()
        public string GetRAdvAsString()
        {
            return _RAdv.ToString().PadLeft(3, '0');
        }

        // Set<>AsString()
        public void SetRAdvAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _RAdv = parsed;
        }

        // Standard Getter
        public decimal GetWorkAmount()
        {
            return _WorkAmount;
        }

        // Standard Setter
        public void SetWorkAmount(decimal value)
        {
            _WorkAmount = value;
        }

        // Get<>AsString()
        public string GetWorkAmountAsString()
        {
            return _WorkAmount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
        }

        // Set<>AsString()
        public void SetWorkAmountAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            decimal parsed;
            if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WorkAmount = parsed;
        }

        // Standard Getter
        public decimal GetWorkUnits()
        {
            return _WorkUnits;
        }

        // Standard Setter
        public void SetWorkUnits(decimal value)
        {
            _WorkUnits = value;
        }

        // Get<>AsString()
        public string GetWorkUnitsAsString()
        {
            return _WorkUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
        }

        // Set<>AsString()
        public void SetWorkUnitsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            decimal parsed;
            if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WorkUnits = parsed;
        }

        // Standard Getter
        public int GetWorkSub()
        {
            return _WorkSub;
        }

        // Standard Setter
        public void SetWorkSub(int value)
        {
            _WorkSub = value;
        }

        // Get<>AsString()
        public string GetWorkSubAsString()
        {
            return _WorkSub.ToString().PadLeft(15, '0');
        }

        // Set<>AsString()
        public void SetWorkSubAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _WorkSub = parsed;
        }

        // Standard Getter
        public int GetReportBalancesAdded()
        {
            return _ReportBalancesAdded;
        }

        // Standard Setter
        public void SetReportBalancesAdded(int value)
        {
            _ReportBalancesAdded = value;
        }

        // Get<>AsString()
        public string GetReportBalancesAddedAsString()
        {
            return _ReportBalancesAdded.ToString().PadLeft(15, '0');
        }

        // Set<>AsString()
        public void SetReportBalancesAddedAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _ReportBalancesAdded = parsed;
        }

        // Standard Getter
        public int GetReportBalancesUpdated()
        {
            return _ReportBalancesUpdated;
        }

        // Standard Setter
        public void SetReportBalancesUpdated(int value)
        {
            _ReportBalancesUpdated = value;
        }

        // Get<>AsString()
        public string GetReportBalancesUpdatedAsString()
        {
            return _ReportBalancesUpdated.ToString().PadLeft(15, '0');
        }

        // Set<>AsString()
        public void SetReportBalancesUpdatedAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _ReportBalancesUpdated = parsed;
        }

        // Standard Getter
        public int GetReportBalancesDeleted()
        {
            return _ReportBalancesDeleted;
        }

        // Standard Setter
        public void SetReportBalancesDeleted(int value)
        {
            _ReportBalancesDeleted = value;
        }

        // Get<>AsString()
        public string GetReportBalancesDeletedAsString()
        {
            return _ReportBalancesDeleted.ToString().PadLeft(15, '0');
        }

        // Set<>AsString()
        public void SetReportBalancesDeletedAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _ReportBalancesDeleted = parsed;
        }

        // Standard Getter
        public int GetReportTransactionsAdded()
        {
            return _ReportTransactionsAdded;
        }

        // Standard Setter
        public void SetReportTransactionsAdded(int value)
        {
            _ReportTransactionsAdded = value;
        }

        // Get<>AsString()
        public string GetReportTransactionsAddedAsString()
        {
            return _ReportTransactionsAdded.ToString().PadLeft(15, '0');
        }

        // Set<>AsString()
        public void SetReportTransactionsAddedAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _ReportTransactionsAdded = parsed;
        }

        // Standard Getter
        public int GetReportTransactionsUpdated()
        {
            return _ReportTransactionsUpdated;
        }

        // Standard Setter
        public void SetReportTransactionsUpdated(int value)
        {
            _ReportTransactionsUpdated = value;
        }

        // Get<>AsString()
        public string GetReportTransactionsUpdatedAsString()
        {
            return _ReportTransactionsUpdated.ToString().PadLeft(15, '0');
        }

        // Set<>AsString()
        public void SetReportTransactionsUpdatedAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _ReportTransactionsUpdated = parsed;
        }

        // Standard Getter
        public decimal GetWEditCount()
        {
            return _WEditCount;
        }

        // Standard Setter
        public void SetWEditCount(decimal value)
        {
            _WEditCount = value;
        }

        // Get<>AsString()
        public string GetWEditCountAsString()
        {
            return _WEditCount.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
        }

        // Set<>AsString()
        public void SetWEditCountAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            decimal parsed;
            if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WEditCount = parsed;
        }

        // Standard Getter
        public decimal GetWEditQuantity()
        {
            return _WEditQuantity;
        }

        // Standard Setter
        public void SetWEditQuantity(decimal value)
        {
            _WEditQuantity = value;
        }

        // Get<>AsString()
        public string GetWEditQuantityAsString()
        {
            return _WEditQuantity.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
        }

        // Set<>AsString()
        public void SetWEditQuantityAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            decimal parsed;
            if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WEditQuantity = parsed;
        }

        // Standard Getter
        public decimal GetWEditValue()
        {
            return _WEditValue;
        }

        // Standard Setter
        public void SetWEditValue(decimal value)
        {
            _WEditValue = value;
        }

        // Get<>AsString()
        public string GetWEditValueAsString()
        {
            return _WEditValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
        }

        // Set<>AsString()
        public void SetWEditValueAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            decimal parsed;
            if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WEditValue = parsed;
        }

        // Standard Getter
        public string GetWReport1StTime()
        {
            return _WReport1StTime;
        }

        // Standard Setter
        public void SetWReport1StTime(string value)
        {
            _WReport1StTime = value;
        }

        // Get<>AsString()
        public string GetWReport1StTimeAsString()
        {
            return _WReport1StTime.PadRight(1);
        }

        // Set<>AsString()
        public void SetWReport1StTimeAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WReport1StTime = value;
        }

        // Standard Getter
        public string GetWReport1StDetail()
        {
            return _WReport1StDetail;
        }

        // Standard Setter
        public void SetWReport1StDetail(string value)
        {
            _WReport1StDetail = value;
        }

        // Get<>AsString()
        public string GetWReport1StDetailAsString()
        {
            return _WReport1StDetail.PadRight(1);
        }

        // Set<>AsString()
        public void SetWReport1StDetailAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WReport1StDetail = value;
        }

        // Standard Getter
        public string GetWReportOverride()
        {
            return _WReportOverride;
        }

        // Standard Setter
        public void SetWReportOverride(string value)
        {
            _WReportOverride = value;
        }

        // Get<>AsString()
        public string GetWReportOverrideAsString()
        {
            return _WReportOverride.PadRight(1);
        }

        // Set<>AsString()
        public void SetWReportOverrideAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WReportOverride = value;
        }

        // Standard Getter
        public WStoredFundSedol GetWStoredFundSedol()
        {
            return _WStoredFundSedol;
        }

        // Standard Setter
        public void SetWStoredFundSedol(WStoredFundSedol value)
        {
            _WStoredFundSedol = value;
        }

        // Get<>AsString()
        public string GetWStoredFundSedolAsString()
        {
            return _WStoredFundSedol != null ? _WStoredFundSedol.GetWStoredFundSedolAsString() : "";
        }

        // Set<>AsString()
        public void SetWStoredFundSedolAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WStoredFundSedol == null)
            {
                _WStoredFundSedol = new WStoredFundSedol();
            }
            _WStoredFundSedol.SetWStoredFundSedolAsString(value);
        }

        // Standard Getter
        public string GetWReportMode()
        {
            return _WReportMode;
        }

        // Standard Setter
        public void SetWReportMode(string value)
        {
            _WReportMode = value;
        }

        // Get<>AsString()
        public string GetWReportModeAsString()
        {
            return _WReportMode.PadRight(1);
        }

        // Set<>AsString()
        public void SetWReportModeAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WReportMode = value;
        }

        // Standard Getter
        public string GetWReportAction()
        {
            return _WReportAction;
        }

        // Standard Setter
        public void SetWReportAction(string value)
        {
            _WReportAction = value;
        }

        // Get<>AsString()
        public string GetWReportActionAsString()
        {
            return _WReportAction.PadRight(2);
        }

        // Set<>AsString()
        public void SetWReportActionAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WReportAction = value;
        }

        // Standard Getter
        public ReportHeading1 GetReportHeading1()
        {
            return _ReportHeading1;
        }

        // Standard Setter
        public void SetReportHeading1(ReportHeading1 value)
        {
            _ReportHeading1 = value;
        }

        // Get<>AsString()
        public string GetReportHeading1AsString()
        {
            return _ReportHeading1 != null ? _ReportHeading1.GetReportHeading1AsString() : "";
        }

        // Set<>AsString()
        public void SetReportHeading1AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportHeading1 == null)
            {
                _ReportHeading1 = new ReportHeading1();
            }
            _ReportHeading1.SetReportHeading1AsString(value);
        }

        // Standard Getter
        public ReportHeading2 GetReportHeading2()
        {
            return _ReportHeading2;
        }

        // Standard Setter
        public void SetReportHeading2(ReportHeading2 value)
        {
            _ReportHeading2 = value;
        }

        // Get<>AsString()
        public string GetReportHeading2AsString()
        {
            return _ReportHeading2 != null ? _ReportHeading2.GetReportHeading2AsString() : "";
        }

        // Set<>AsString()
        public void SetReportHeading2AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportHeading2 == null)
            {
                _ReportHeading2 = new ReportHeading2();
            }
            _ReportHeading2.SetReportHeading2AsString(value);
        }

        // Standard Getter
        public ReportHeading3 GetReportHeading3()
        {
            return _ReportHeading3;
        }

        // Standard Setter
        public void SetReportHeading3(ReportHeading3 value)
        {
            _ReportHeading3 = value;
        }

        // Get<>AsString()
        public string GetReportHeading3AsString()
        {
            return _ReportHeading3 != null ? _ReportHeading3.GetReportHeading3AsString() : "";
        }

        // Set<>AsString()
        public void SetReportHeading3AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportHeading3 == null)
            {
                _ReportHeading3 = new ReportHeading3();
            }
            _ReportHeading3.SetReportHeading3AsString(value);
        }

        // Standard Getter
        public ReportColumnHeading1 GetReportColumnHeading1()
        {
            return _ReportColumnHeading1;
        }

        // Standard Setter
        public void SetReportColumnHeading1(ReportColumnHeading1 value)
        {
            _ReportColumnHeading1 = value;
        }

        // Get<>AsString()
        public string GetReportColumnHeading1AsString()
        {
            return _ReportColumnHeading1 != null ? _ReportColumnHeading1.GetReportColumnHeading1AsString() : "";
        }

        // Set<>AsString()
        public void SetReportColumnHeading1AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportColumnHeading1 == null)
            {
                _ReportColumnHeading1 = new ReportColumnHeading1();
            }
            _ReportColumnHeading1.SetReportColumnHeading1AsString(value);
        }

        // Standard Getter
        public ReportColumnHeading2 GetReportColumnHeading2()
        {
            return _ReportColumnHeading2;
        }

        // Standard Setter
        public void SetReportColumnHeading2(ReportColumnHeading2 value)
        {
            _ReportColumnHeading2 = value;
        }

        // Get<>AsString()
        public string GetReportColumnHeading2AsString()
        {
            return _ReportColumnHeading2 != null ? _ReportColumnHeading2.GetReportColumnHeading2AsString() : "";
        }

        // Set<>AsString()
        public void SetReportColumnHeading2AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportColumnHeading2 == null)
            {
                _ReportColumnHeading2 = new ReportColumnHeading2();
            }
            _ReportColumnHeading2.SetReportColumnHeading2AsString(value);
        }

        // Standard Getter
        public ReportLastLine GetReportLastLine()
        {
            return _ReportLastLine;
        }

        // Standard Setter
        public void SetReportLastLine(ReportLastLine value)
        {
            _ReportLastLine = value;
        }

        // Get<>AsString()
        public string GetReportLastLineAsString()
        {
            return _ReportLastLine != null ? _ReportLastLine.GetReportLastLineAsString() : "";
        }

        // Set<>AsString()
        public void SetReportLastLineAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportLastLine == null)
            {
                _ReportLastLine = new ReportLastLine();
            }
            _ReportLastLine.SetReportLastLineAsString(value);
        }

        // Standard Getter
        public ReportDetailLine1 GetReportDetailLine1()
        {
            return _ReportDetailLine1;
        }

        // Standard Setter
        public void SetReportDetailLine1(ReportDetailLine1 value)
        {
            _ReportDetailLine1 = value;
        }

        // Get<>AsString()
        public string GetReportDetailLine1AsString()
        {
            return _ReportDetailLine1 != null ? _ReportDetailLine1.GetReportDetailLine1AsString() : "";
        }

        // Set<>AsString()
        public void SetReportDetailLine1AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportDetailLine1 == null)
            {
                _ReportDetailLine1 = new ReportDetailLine1();
            }
            _ReportDetailLine1.SetReportDetailLine1AsString(value);
        }

        // Standard Getter
        public string GetWReportSedol()
        {
            return _WReportSedol;
        }

        // Standard Setter
        public void SetWReportSedol(string value)
        {
            _WReportSedol = value;
        }

        // Get<>AsString()
        public string GetWReportSedolAsString()
        {
            return _WReportSedol.PadRight(11);
        }

        // Set<>AsString()
        public void SetWReportSedolAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WReportSedol = value;
        }

        // Standard Getter
        public ReportDetailLine2 GetReportDetailLine2()
        {
            return _ReportDetailLine2;
        }

        // Standard Setter
        public void SetReportDetailLine2(ReportDetailLine2 value)
        {
            _ReportDetailLine2 = value;
        }

        // Get<>AsString()
        public string GetReportDetailLine2AsString()
        {
            return _ReportDetailLine2 != null ? _ReportDetailLine2.GetReportDetailLine2AsString() : "";
        }

        // Set<>AsString()
        public void SetReportDetailLine2AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportDetailLine2 == null)
            {
                _ReportDetailLine2 = new ReportDetailLine2();
            }
            _ReportDetailLine2.SetReportDetailLine2AsString(value);
        }

        // Standard Getter
        public ReportDetailLine3 GetReportDetailLine3()
        {
            return _ReportDetailLine3;
        }

        // Standard Setter
        public void SetReportDetailLine3(ReportDetailLine3 value)
        {
            _ReportDetailLine3 = value;
        }

        // Get<>AsString()
        public string GetReportDetailLine3AsString()
        {
            return _ReportDetailLine3 != null ? _ReportDetailLine3.GetReportDetailLine3AsString() : "";
        }

        // Set<>AsString()
        public void SetReportDetailLine3AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportDetailLine3 == null)
            {
                _ReportDetailLine3 = new ReportDetailLine3();
            }
            _ReportDetailLine3.SetReportDetailLine3AsString(value);
        }

        // Standard Getter
        public Cgtbalup00 GetCgtbalup00()
        {
            return _Cgtbalup00;
        }

        // Standard Setter
        public void SetCgtbalup00(Cgtbalup00 value)
        {
            _Cgtbalup00 = value;
        }

        // Get<>AsString()
        public string GetCgtbalup00AsString()
        {
            return _Cgtbalup00 != null ? _Cgtbalup00.GetCgtbalup00AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtbalup00AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Cgtbalup00 == null)
            {
                _Cgtbalup00 = new Cgtbalup00();
            }
            _Cgtbalup00.SetCgtbalup00AsString(value);
        }

        // Standard Getter
        public ReportOpt01 GetReportOpt01()
        {
            return _ReportOpt01;
        }

        // Standard Setter
        public void SetReportOpt01(ReportOpt01 value)
        {
            _ReportOpt01 = value;
        }

        // Get<>AsString()
        public string GetReportOpt01AsString()
        {
            return _ReportOpt01 != null ? _ReportOpt01.GetReportOpt01AsString() : "";
        }

        // Set<>AsString()
        public void SetReportOpt01AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportOpt01 == null)
            {
                _ReportOpt01 = new ReportOpt01();
            }
            _ReportOpt01.SetReportOpt01AsString(value);
        }

        // Standard Getter
        public ReportOptTable GetReportOptTable()
        {
            return _ReportOptTable;
        }

        // Standard Setter
        public void SetReportOptTable(ReportOptTable value)
        {
            _ReportOptTable = value;
        }

        // Get<>AsString()
        public string GetReportOptTableAsString()
        {
            return _ReportOptTable != null ? _ReportOptTable.GetReportOptTableAsString() : "";
        }

        // Set<>AsString()
        public void SetReportOptTableAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ReportOptTable == null)
            {
                _ReportOptTable = new ReportOptTable();
            }
            _ReportOptTable.SetReportOptTableAsString(value);
        }

        // Standard Getter
        public int GetReportOptLineOccurs()
        {
            return _ReportOptLineOccurs;
        }

        // Standard Setter
        public void SetReportOptLineOccurs(int value)
        {
            _ReportOptLineOccurs = value;
        }

        // Get<>AsString()
        public string GetReportOptLineOccursAsString()
        {
            return _ReportOptLineOccurs.ToString().PadLeft(2, '0');
        }

        // Set<>AsString()
        public void SetReportOptLineOccursAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            int parsed;
            if (int.TryParse(value.Trim(), out parsed)) _ReportOptLineOccurs = parsed;
        }

        // Standard Getter
        public CgtabortLinkage GetCgtabortLinkage()
        {
            return _CgtabortLinkage;
        }

        // Standard Setter
        public void SetCgtabortLinkage(CgtabortLinkage value)
        {
            _CgtabortLinkage = value;
        }

        // Get<>AsString()
        public string GetCgtabortLinkageAsString()
        {
            return _CgtabortLinkage != null ? _CgtabortLinkage.GetCgtabortLinkageAsString() : "";
        }

        // Set<>AsString()
        public void SetCgtabortLinkageAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_CgtabortLinkage == null)
            {
                _CgtabortLinkage = new CgtabortLinkage();
            }
            _CgtabortLinkage.SetCgtabortLinkageAsString(value);
        }

        // Standard Getter
        public CgtfilesLinkage GetCgtfilesLinkage()
        {
            return _CgtfilesLinkage;
        }

        // Standard Setter
        public void SetCgtfilesLinkage(CgtfilesLinkage value)
        {
            _CgtfilesLinkage = value;
        }

        // Get<>AsString()
        public string GetCgtfilesLinkageAsString()
        {
            return _CgtfilesLinkage != null ? _CgtfilesLinkage.GetCgtfilesLinkageAsString() : "";
        }

        // Set<>AsString()
        public void SetCgtfilesLinkageAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_CgtfilesLinkage == null)
            {
                _CgtfilesLinkage = new CgtfilesLinkage();
            }
            _CgtfilesLinkage.SetCgtfilesLinkageAsString(value);
        }

        // Standard Getter
        public LFileRecordArea GetLFileRecordArea()
        {
            return _LFileRecordArea;
        }

        // Standard Setter
        public void SetLFileRecordArea(LFileRecordArea value)
        {
            _LFileRecordArea = value;
        }

        // Get<>AsString()
        public string GetLFileRecordAreaAsString()
        {
            return _LFileRecordArea != null ? _LFileRecordArea.GetLFileRecordAreaAsString() : "";
        }

        // Set<>AsString()
        public void SetLFileRecordAreaAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_LFileRecordArea == null)
            {
                _LFileRecordArea = new LFileRecordArea();
            }
            _LFileRecordArea.SetLFileRecordAreaAsString(value);
        }

        // Standard Getter
        public ElcgmioLinkage1 GetElcgmioLinkage1()
        {
            return _ElcgmioLinkage1;
        }

        // Standard Setter
        public void SetElcgmioLinkage1(ElcgmioLinkage1 value)
        {
            _ElcgmioLinkage1 = value;
        }

        // Get<>AsString()
        public string GetElcgmioLinkage1AsString()
        {
            return _ElcgmioLinkage1 != null ? _ElcgmioLinkage1.GetElcgmioLinkage1AsString() : "";
        }

        // Set<>AsString()
        public void SetElcgmioLinkage1AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ElcgmioLinkage1 == null)
            {
                _ElcgmioLinkage1 = new ElcgmioLinkage1();
            }
            _ElcgmioLinkage1.SetElcgmioLinkage1AsString(value);
        }

        // Standard Getter
        public ElcgmioLinkage2 GetElcgmioLinkage2()
        {
            return _ElcgmioLinkage2;
        }

        // Standard Setter
        public void SetElcgmioLinkage2(ElcgmioLinkage2 value)
        {
            _ElcgmioLinkage2 = value;
        }

        // Get<>AsString()
        public string GetElcgmioLinkage2AsString()
        {
            return _ElcgmioLinkage2 != null ? _ElcgmioLinkage2.GetElcgmioLinkage2AsString() : "";
        }

        // Set<>AsString()
        public void SetElcgmioLinkage2AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_ElcgmioLinkage2 == null)
            {
                _ElcgmioLinkage2 = new ElcgmioLinkage2();
            }
            _ElcgmioLinkage2.SetElcgmioLinkage2AsString(value);
        }

        // Standard Getter
        public CgtmessLinkage GetCgtmessLinkage()
        {
            return _CgtmessLinkage;
        }

        // Standard Setter
        public void SetCgtmessLinkage(CgtmessLinkage value)
        {
            _CgtmessLinkage = value;
        }

        // Get<>AsString()
        public string GetCgtmessLinkageAsString()
        {
            return _CgtmessLinkage != null ? _CgtmessLinkage.GetCgtmessLinkageAsString() : "";
        }

        // Set<>AsString()
        public void SetCgtmessLinkageAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_CgtmessLinkage == null)
            {
                _CgtmessLinkage = new CgtmessLinkage();
            }
            _CgtmessLinkage.SetCgtmessLinkageAsString(value);
        }

        // Standard Getter
        public CgtlogLinkageArea1 GetCgtlogLinkageArea1()
        {
            return _CgtlogLinkageArea1;
        }

        // Standard Setter
        public void SetCgtlogLinkageArea1(CgtlogLinkageArea1 value)
        {
            _CgtlogLinkageArea1 = value;
        }

        // Get<>AsString()
        public string GetCgtlogLinkageArea1AsString()
        {
            return _CgtlogLinkageArea1 != null ? _CgtlogLinkageArea1.GetCgtlogLinkageArea1AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtlogLinkageArea1AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_CgtlogLinkageArea1 == null)
            {
                _CgtlogLinkageArea1 = new CgtlogLinkageArea1();
            }
            _CgtlogLinkageArea1.SetCgtlogLinkageArea1AsString(value);
        }

        // Standard Getter
        public CgtlogLinkageArea2 GetCgtlogLinkageArea2()
        {
            return _CgtlogLinkageArea2;
        }

        // Standard Setter
        public void SetCgtlogLinkageArea2(CgtlogLinkageArea2 value)
        {
            _CgtlogLinkageArea2 = value;
        }

        // Get<>AsString()
        public string GetCgtlogLinkageArea2AsString()
        {
            return _CgtlogLinkageArea2 != null ? _CgtlogLinkageArea2.GetCgtlogLinkageArea2AsString() : "";
        }

        // Set<>AsString()
        public void SetCgtlogLinkageArea2AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_CgtlogLinkageArea2 == null)
            {
                _CgtlogLinkageArea2 = new CgtlogLinkageArea2();
            }
            _CgtlogLinkageArea2.SetCgtlogLinkageArea2AsString(value);
        }

        // Standard Getter
        public EqtpathLinkage GetEqtpathLinkage()
        {
            return _EqtpathLinkage;
        }

        // Standard Setter
        public void SetEqtpathLinkage(EqtpathLinkage value)
        {
            _EqtpathLinkage = value;
        }

        // Get<>AsString()
        public string GetEqtpathLinkageAsString()
        {
            return _EqtpathLinkage != null ? _EqtpathLinkage.GetEqtpathLinkageAsString() : "";
        }

        // Set<>AsString()
        public void SetEqtpathLinkageAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_EqtpathLinkage == null)
            {
                _EqtpathLinkage = new EqtpathLinkage();
            }
            _EqtpathLinkage.SetEqtpathLinkageAsString(value);
        }

        // Standard Getter
        public WIds GetWIds()
        {
            return _WIds;
        }

        // Standard Setter
        public void SetWIds(WIds value)
        {
            _WIds = value;
        }

        // Get<>AsString()
        public string GetWIdsAsString()
        {
            return _WIds != null ? _WIds.GetWIdsAsString() : "";
        }

        // Set<>AsString()
        public void SetWIdsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WIds == null)
            {
                _WIds = new WIds();
            }
            _WIds.SetWIdsAsString(value);
        }

        // Standard Getter
        public HeaderIds GetHeaderIds()
        {
            return _HeaderIds;
        }

        // Standard Setter
        public void SetHeaderIds(HeaderIds value)
        {
            _HeaderIds = value;
        }

        // Get<>AsString()
        public string GetHeaderIdsAsString()
        {
            return _HeaderIds != null ? _HeaderIds.GetHeaderIdsAsString() : "";
        }

        // Set<>AsString()
        public void SetHeaderIdsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_HeaderIds == null)
            {
                _HeaderIds = new HeaderIds();
            }
            _HeaderIds.SetHeaderIdsAsString(value);
        }

        // Standard Getter
        public BalanceIds GetBalanceIds()
        {
            return _BalanceIds;
        }

        // Standard Setter
        public void SetBalanceIds(BalanceIds value)
        {
            _BalanceIds = value;
        }

        // Get<>AsString()
        public string GetBalanceIdsAsString()
        {
            return _BalanceIds != null ? _BalanceIds.GetBalanceIdsAsString() : "";
        }

        // Set<>AsString()
        public void SetBalanceIdsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_BalanceIds == null)
            {
                _BalanceIds = new BalanceIds();
            }
            _BalanceIds.SetBalanceIdsAsString(value);
        }

        // Standard Getter
        public AcquisitionIds GetAcquisitionIds()
        {
            return _AcquisitionIds;
        }

        // Standard Setter
        public void SetAcquisitionIds(AcquisitionIds value)
        {
            _AcquisitionIds = value;
        }

        // Get<>AsString()
        public string GetAcquisitionIdsAsString()
        {
            return _AcquisitionIds != null ? _AcquisitionIds.GetAcquisitionIdsAsString() : "";
        }

        // Set<>AsString()
        public void SetAcquisitionIdsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_AcquisitionIds == null)
            {
                _AcquisitionIds = new AcquisitionIds();
            }
            _AcquisitionIds.SetAcquisitionIdsAsString(value);
        }

        // Standard Getter
        public DisposalIds GetDisposalIds()
        {
            return _DisposalIds;
        }

        // Standard Setter
        public void SetDisposalIds(DisposalIds value)
        {
            _DisposalIds = value;
        }

        // Get<>AsString()
        public string GetDisposalIdsAsString()
        {
            return _DisposalIds != null ? _DisposalIds.GetDisposalIdsAsString() : "";
        }

        // Set<>AsString()
        public void SetDisposalIdsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_DisposalIds == null)
            {
                _DisposalIds = new DisposalIds();
            }
            _DisposalIds.SetDisposalIdsAsString(value);
        }

        // Standard Getter
        public Filler202 GetFiller202()
        {
            return _Filler202;
        }

        // Standard Setter
        public void SetFiller202(Filler202 value)
        {
            _Filler202 = value;
        }

        // Get<>AsString()
        public string GetFiller202AsString()
        {
            return _Filler202 != null ? _Filler202.GetFiller202AsString() : "";
        }

        // Set<>AsString()
        public void SetFiller202AsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_Filler202 == null)
            {
                _Filler202 = new Filler202();
            }
            _Filler202.SetFiller202AsString(value);
        }

        // Standard Getter
        public CheckCatLinkage GetCheckCatLinkage()
        {
            return _CheckCatLinkage;
        }

        // Standard Setter
        public void SetCheckCatLinkage(CheckCatLinkage value)
        {
            _CheckCatLinkage = value;
        }

        // Get<>AsString()
        public string GetCheckCatLinkageAsString()
        {
            return _CheckCatLinkage != null ? _CheckCatLinkage.GetCheckCatLinkageAsString() : "";
        }

        // Set<>AsString()
        public void SetCheckCatLinkageAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_CheckCatLinkage == null)
            {
                _CheckCatLinkage = new CheckCatLinkage();
            }
            _CheckCatLinkage.SetCheckCatLinkageAsString(value);
        }

        // Standard Getter
        public string GetWTransactionCategory()
        {
            return _WTransactionCategory;
        }

        // Standard Setter
        public void SetWTransactionCategory(string value)
        {
            _WTransactionCategory = value;
        }

        // Get<>AsString()
        public string GetWTransactionCategoryAsString()
        {
            return _WTransactionCategory.PadRight(0);
        }

        // Set<>AsString()
        public void SetWTransactionCategoryAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WTransactionCategory = value;
        }

        // Standard Getter
        public WttOutputRecord GetWttOutputRecord()
        {
            return _WttOutputRecord;
        }

        // Standard Setter
        public void SetWttOutputRecord(WttOutputRecord value)
        {
            _WttOutputRecord = value;
        }

        // Get<>AsString()
        public string GetWttOutputRecordAsString()
        {
            return _WttOutputRecord != null ? _WttOutputRecord.GetWttOutputRecordAsString() : "";
        }

        // Set<>AsString()
        public void SetWttOutputRecordAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_WttOutputRecord == null)
            {
                _WttOutputRecord = new WttOutputRecord();
            }
            _WttOutputRecord.SetWttOutputRecordAsString(value);
        }

        // Standard Getter
        public string GetWsStoreD13Ids()
        {
            return _WsStoreD13Ids;
        }

        // Standard Setter
        public void SetWsStoreD13Ids(string value)
        {
            _WsStoreD13Ids = value;
        }

        // Get<>AsString()
        public string GetWsStoreD13IdsAsString()
        {
            return _WsStoreD13Ids.PadRight(0);
        }

        // Set<>AsString()
        public void SetWsStoreD13IdsAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WsStoreD13Ids = value;
        }

        // Standard Getter
        public string GetWsNewHoldingId()
        {
            return _WsNewHoldingId;
        }

        // Standard Setter
        public void SetWsNewHoldingId(string value)
        {
            _WsNewHoldingId = value;
        }

        // Get<>AsString()
        public string GetWsNewHoldingIdAsString()
        {
            return _WsNewHoldingId.PadRight(0);
        }

        // Set<>AsString()
        public void SetWsNewHoldingIdAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _WsNewHoldingId = value;
        }

        // Standard Getter
        public LkFundSedol GetLkFundSedol()
        {
            return _LkFundSedol;
        }

        // Standard Setter
        public void SetLkFundSedol(LkFundSedol value)
        {
            _LkFundSedol = value;
        }

        // Get<>AsString()
        public string GetLkFundSedolAsString()
        {
            return _LkFundSedol != null ? _LkFundSedol.GetLkFundSedolAsString() : "";
        }

        // Set<>AsString()
        public void SetLkFundSedolAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_LkFundSedol == null)
            {
                _LkFundSedol = new LkFundSedol();
            }
            _LkFundSedol.SetLkFundSedolAsString(value);
        }

        // Standard Getter
        public string GetLForceWrite()
        {
            return _LForceWrite;
        }

        // Standard Setter
        public void SetLForceWrite(string value)
        {
            _LForceWrite = value;
        }

        // Get<>AsString()
        public string GetLForceWriteAsString()
        {
            return _LForceWrite.PadRight(0);
        }

        // Set<>AsString()
        public void SetLForceWriteAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            _LForceWrite = value;
        }

        // Standard Getter
        public EquityParameters GetEquityParameters()
        {
            return _EquityParameters;
        }

        // Standard Setter
        public void SetEquityParameters(EquityParameters value)
        {
            _EquityParameters = value;
        }

        // Get<>AsString()
        public string GetEquityParametersAsString()
        {
            return _EquityParameters != null ? _EquityParameters.GetEquityParametersAsString() : "";
        }

        // Set<>AsString()
        public void SetEquityParametersAsString(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            if (_EquityParameters == null)
            {
                _EquityParameters = new EquityParameters();
            }
            _EquityParameters.SetEquityParametersAsString(value);
        }


        public void SyncCgtbalup00FromReportOpt01()
        {
            this.SetCgtbalup00AsString(this.GetReportOpt01AsString());
        }

        public void SyncReportOpt01FromCgtbalup00()
        {
            this.SetReportOpt01AsString(this.GetCgtbalup00AsString());
        }
        public void SyncCgtbalup00FromReportOptTable()
        {
            this.SetCgtbalup00AsString(this.GetReportOptTableAsString());
        }

        public void SyncReportOptTableFromCgtbalup00()
        {
            this.SetReportOptTableAsString(this.GetCgtbalup00AsString());
        }
        public void SyncWIdsFromHeaderIds()
        {
            this.SetWIdsAsString(this.GetHeaderIdsAsString());
        }

        public void SyncHeaderIdsFromWIds()
        {
            this.SetHeaderIdsAsString(this.GetWIdsAsString());
        }
        public void SyncWIdsFromBalanceIds()
        {
            this.SetWIdsAsString(this.GetBalanceIdsAsString());
        }

        public void SyncBalanceIdsFromWIds()
        {
            this.SetBalanceIdsAsString(this.GetWIdsAsString());
        }
        public void SyncWIdsFromAcquisitionIds()
        {
            this.SetWIdsAsString(this.GetAcquisitionIdsAsString());
        }

        public void SyncAcquisitionIdsFromWIds()
        {
            this.SetAcquisitionIdsAsString(this.GetWIdsAsString());
        }
        public void SyncWIdsFromDisposalIds()
        {
            this.SetWIdsAsString(this.GetDisposalIdsAsString());
        }

        public void SyncDisposalIdsFromWIds()
        {
            this.SetDisposalIdsAsString(this.GetWIdsAsString());
        }
        public void SyncWIdsFromFiller202()
        {
            this.SetWIdsAsString(this.GetFiller202AsString());
        }

        public void SyncFiller202FromWIds()
        {
            this.SetFiller202AsString(this.GetWIdsAsString());
        }
    }
}