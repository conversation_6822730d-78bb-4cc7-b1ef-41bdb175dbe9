C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\obj\Debug\Equity.CobMod.csproj.AssemblyReference.cache
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\obj\Debug\Equity.CobMod.csproj.CoreCompileInputs.cache
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\Legacy4.Equity.CobMod.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\Legacy4.Equity.CobMod.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.CobolDataTransformation.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.CommonTypes.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Utilities.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Exceptions.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Common.PDFComponent.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.LicenceModule.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Configuration.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.DataAccess.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.DataModel.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\Microsoft.Practices.EnterpriseLibrary.Data.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.BusinessEntities.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\itextsharp.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Audit.DataModel.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Pending.DataModel.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\Microsoft.Practices.EnterpriseLibrary.Common.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\Microsoft.Practices.ObjectBuilder.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Audit.BusinessEntities.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Validations.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Audit.DataAccess.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.CommonTypes.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Utilities.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.CobolDataTransformation.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Exceptions.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.LicenceModule.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Configuration.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.DataAccess.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.DataModel.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.DataModel.dll.config
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.BusinessEntities.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Audit.DataModel.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Audit.DataModel.dll.config
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Pending.DataModel.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Pending.DataModel.dll.config
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Audit.BusinessEntities.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Validations.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\bin\Debug\WK.UK.CCH.Equity.Audit.DataAccess.pdb
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\obj\Debug\Equity.C.B0CD9DED.Up2Date
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\obj\Debug\Legacy4.Equity.CobMod.dll
C:\GitWorkspace\Legacy4-WK\Equity.Net\Sources\Business Logic Layer\Equity.CobMod\obj\Debug\Legacy4.Equity.CobMod.pdb
C:\Elfonze\Mclaren\AugTest\Equity.CobMod\obj\Debug\Equity.CobMod.csproj.AssemblyReference.cache
C:\Elfonze\Mclaren\AugTest\Equity.CobMod\obj\Debug\Equity.CobMod.csproj.CoreCompileInputs.cache
