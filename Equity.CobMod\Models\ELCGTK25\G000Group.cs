using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing G000Group Data Structure

public class G000Group
{
    private static int _size = 44;
    // [DEBUG] Class: G000Group, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: G001GroupCode, is_external=, is_static_class=False, static_prefix=
    private string _G001GroupCode ="";
    
    
    
    
    // [DEBUG] Field: G002Description, is_external=, is_static_class=False, static_prefix=
    private string _G002Description ="";
    
    
    
    
    // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
    private string _Filler17 ="";
    
    
    
    
    
    // Serialization methods
    public string GetG000GroupAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_G001GroupCode.PadRight(3));
        result.Append(_G002Description.PadRight(40));
        result.Append(_Filler17.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetG000GroupAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetG001GroupCode(extracted);
        }
        offset += 3;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetG002Description(extracted);
        }
        offset += 40;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller17(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetG000GroupAsString();
    }
    // Set<>String Override function
    public void SetG000Group(string value)
    {
        SetG000GroupAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetG001GroupCode()
    {
        return _G001GroupCode;
    }
    
    // Standard Setter
    public void SetG001GroupCode(string value)
    {
        _G001GroupCode = value;
    }
    
    // Get<>AsString()
    public string GetG001GroupCodeAsString()
    {
        return _G001GroupCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetG001GroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G001GroupCode = value;
    }
    
    // Standard Getter
    public string GetG002Description()
    {
        return _G002Description;
    }
    
    // Standard Setter
    public void SetG002Description(string value)
    {
        _G002Description = value;
    }
    
    // Get<>AsString()
    public string GetG002DescriptionAsString()
    {
        return _G002Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetG002DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _G002Description = value;
    }
    
    // Standard Getter
    public string GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(string value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler17 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
