using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing N000Country Data Structure

public class N000Country
{
    private static int _size = 44;
    // [DEBUG] Class: N000Country, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: N001CountryCode, is_external=, is_static_class=False, static_prefix=
    private string _N001CountryCode ="";
    
    
    
    
    // [DEBUG] Field: N002CountryName, is_external=, is_static_class=False, static_prefix=
    private string _N002CountryName ="";
    
    
    
    
    // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
    private string _Filler16 ="";
    
    
    
    
    
    // Serialization methods
    public string GetN000CountryAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_N001CountryCode.PadRight(3));
        result.Append(_N002CountryName.PadRight(40));
        result.Append(_Filler16.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetN000CountryAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetN001CountryCode(extracted);
        }
        offset += 3;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetN002CountryName(extracted);
        }
        offset += 40;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller16(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetN000CountryAsString();
    }
    // Set<>String Override function
    public void SetN000Country(string value)
    {
        SetN000CountryAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetN001CountryCode()
    {
        return _N001CountryCode;
    }
    
    // Standard Setter
    public void SetN001CountryCode(string value)
    {
        _N001CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetN001CountryCodeAsString()
    {
        return _N001CountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetN001CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _N001CountryCode = value;
    }
    
    // Standard Getter
    public string GetN002CountryName()
    {
        return _N002CountryName;
    }
    
    // Standard Setter
    public void SetN002CountryName(string value)
    {
        _N002CountryName = value;
    }
    
    // Get<>AsString()
    public string GetN002CountryNameAsString()
    {
        return _N002CountryName.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetN002CountryNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _N002CountryName = value;
    }
    
    // Standard Getter
    public string GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(string value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler16 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
