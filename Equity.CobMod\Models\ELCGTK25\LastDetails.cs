using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgtk25DTO
{// DTO class representing LastDetails Data Structure

public class LastDetails
{
    private static int _size = 27;
    // [DEBUG] Class: LastDetails, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LastFundCode, is_external=, is_static_class=False, static_prefix=
    private string _LastFundCode ="";
    
    
    
    
    // [DEBUG] Field: LastSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _LastSedolCode ="";
    
    
    
    
    // [DEBUG] Field: LastAcquisitionDate, is_external=, is_static_class=False, static_prefix=
    private string _LastAcquisitionDate ="";
    
    
    
    
    // [DEBUG] Field: LastMovementDescription, is_external=, is_static_class=False, static_prefix=
    private string _LastMovementDescription ="";
    
    
    
    
    // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
    private Filler8 _Filler8 = new Filler8();
    
    
    
    
    
    // Serialization methods
    public string GetLastDetailsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LastFundCode.PadRight(0));
        result.Append(_LastSedolCode.PadRight(0));
        result.Append(_LastAcquisitionDate.PadRight(0));
        result.Append(_LastMovementDescription.PadRight(16));
        result.Append(_Filler8.GetFiller8AsString());
        
        return result.ToString();
    }
    
    public void SetLastDetailsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLastFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLastSedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLastAcquisitionDate(extracted);
        }
        offset += 0;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetLastMovementDescription(extracted);
        }
        offset += 16;
        if (offset + 11 <= data.Length)
        {
            _Filler8.SetFiller8AsString(data.Substring(offset, 11));
        }
        else
        {
            _Filler8.SetFiller8AsString(data.Substring(offset));
        }
        offset += 11;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetLastDetailsAsString();
    }
    // Set<>String Override function
    public void SetLastDetails(string value)
    {
        SetLastDetailsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLastFundCode()
    {
        return _LastFundCode;
    }
    
    // Standard Setter
    public void SetLastFundCode(string value)
    {
        _LastFundCode = value;
    }
    
    // Get<>AsString()
    public string GetLastFundCodeAsString()
    {
        return _LastFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLastFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LastFundCode = value;
    }
    
    // Standard Getter
    public string GetLastSedolCode()
    {
        return _LastSedolCode;
    }
    
    // Standard Setter
    public void SetLastSedolCode(string value)
    {
        _LastSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetLastSedolCodeAsString()
    {
        return _LastSedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLastSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LastSedolCode = value;
    }
    
    // Standard Getter
    public string GetLastAcquisitionDate()
    {
        return _LastAcquisitionDate;
    }
    
    // Standard Setter
    public void SetLastAcquisitionDate(string value)
    {
        _LastAcquisitionDate = value;
    }
    
    // Get<>AsString()
    public string GetLastAcquisitionDateAsString()
    {
        return _LastAcquisitionDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLastAcquisitionDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LastAcquisitionDate = value;
    }
    
    // Standard Getter
    public string GetLastMovementDescription()
    {
        return _LastMovementDescription;
    }
    
    // Standard Setter
    public void SetLastMovementDescription(string value)
    {
        _LastMovementDescription = value;
    }
    
    // Get<>AsString()
    public string GetLastMovementDescriptionAsString()
    {
        return _LastMovementDescription.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetLastMovementDescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LastMovementDescription = value;
    }
    
    // Standard Getter
    public Filler8 GetFiller8()
    {
        return _Filler8;
    }
    
    // Standard Setter
    public void SetFiller8(Filler8 value)
    {
        _Filler8 = value;
    }
    
    // Get<>AsString()
    public string GetFiller8AsString()
    {
        return _Filler8 != null ? _Filler8.GetFiller8AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler8 == null)
        {
            _Filler8 = new Filler8();
        }
        _Filler8.SetFiller8AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller8(string value)
    {
        _Filler8.SetFiller8AsString(value);
    }
    // Nested Class: Filler8
    public class Filler8
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: LastMovementDescription15, is_external=, is_static_class=False, static_prefix=
        private string _LastMovementDescription15 ="";
        
        
        // 88-level condition checks for LastMovementDescription15
        public bool IsLastMovtDescOptionExerciseTo()
        {
            if (this._LastMovementDescription15 == "'EX TO'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
        private string _Filler9 ="";
        
        
        
        
    public Filler8() {}
    
    public Filler8(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetLastMovementDescription15(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller9(data.Substring(offset, 11).Trim());
        offset += 11;
        
    }
    
    // Serialization methods
    public string GetFiller8AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LastMovementDescription15.PadRight(0));
        result.Append(_Filler9.PadRight(11));
        
        return result.ToString();
    }
    
    public void SetFiller8AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLastMovementDescription15(extracted);
        }
        offset += 0;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetFiller9(extracted);
        }
        offset += 11;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLastMovementDescription15()
    {
        return _LastMovementDescription15;
    }
    
    // Standard Setter
    public void SetLastMovementDescription15(string value)
    {
        _LastMovementDescription15 = value;
    }
    
    // Get<>AsString()
    public string GetLastMovementDescription15AsString()
    {
        return _LastMovementDescription15.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLastMovementDescription15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LastMovementDescription15 = value;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
