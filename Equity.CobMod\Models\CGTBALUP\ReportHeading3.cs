using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtbalupDTO
{// DTO class representing ReportHeading3 Data Structure

public class ReportHeading3
{
    private static int _size = 132;
    // [DEBUG] Class: ReportHeading3, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler133, is_external=, is_static_class=False, static_prefix=
    private string _Filler133 ="";
    
    
    
    
    // [DEBUG] Field: Filler134, is_external=, is_static_class=False, static_prefix=
    private string _Filler134 ="    ______________________";
    
    
    
    
    
    // Serialization methods
    public string GetReportHeading3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler133.PadRight(52));
        result.Append(_Filler134.PadRight(80));
        
        return result.ToString();
    }
    
    public void SetReportHeading3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 52 <= data.Length)
        {
            string extracted = data.Substring(offset, 52).Trim();
            SetFiller133(extracted);
        }
        offset += 52;
        if (offset + 80 <= data.Length)
        {
            string extracted = data.Substring(offset, 80).Trim();
            SetFiller134(extracted);
        }
        offset += 80;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportHeading3AsString();
    }
    // Set<>String Override function
    public void SetReportHeading3(string value)
    {
        SetReportHeading3AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller133()
    {
        return _Filler133;
    }
    
    // Standard Setter
    public void SetFiller133(string value)
    {
        _Filler133 = value;
    }
    
    // Get<>AsString()
    public string GetFiller133AsString()
    {
        return _Filler133.PadRight(52);
    }
    
    // Set<>AsString()
    public void SetFiller133AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler133 = value;
    }
    
    // Standard Getter
    public string GetFiller134()
    {
        return _Filler134;
    }
    
    // Standard Setter
    public void SetFiller134(string value)
    {
        _Filler134 = value;
    }
    
    // Get<>AsString()
    public string GetFiller134AsString()
    {
        return _Filler134.PadRight(80);
    }
    
    // Set<>AsString()
    public void SetFiller134AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler134 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}